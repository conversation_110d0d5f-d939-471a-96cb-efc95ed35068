﻿using System;
using System.Net.Http;
using Microsoft.Xrm.Sdk;
using Newtonsoft.Json.Linq;
namespace XiaoMi.Crm.CS.ApiCommon.Helper
{
    public static class SendNotifyHelper
    { private static readonly HttpClient httpClient = new HttpClient();

        /// <summary>
        /// 发送Notify消息通用接口
        /// </summary>
        /// <param name="param">消息体</param>
        /// <param name="project">主题</param>
        /// <param name="index">消息索引</param>
        /// <param name="OrganizationService">组织服务</param>
        /// <param name="log">日志服务</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static void SendToNotify(object param, string project, string index, IOrganizationService OrganizationService)
        {
            try
            {
                string appId = CommonHelper.GetSystemParameter(OrganizationService, "notify_cs_appid");
                string appKey = CommonHelper.GetSystemParameter(OrganizationService, "notify_cs_appkey");
                string inappId = CommonHelper.GetSystemParameter(OrganizationService, "Not_inappId");
                string inappKey = CommonHelper.GetSystemParameter(OrganizationService, "Not_inappkey");
                string url = CommonHelper.GetSystemParameter(OrganizationService, "Not_notifyUrl");
                string method = "post";
                if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appKey) || string.IsNullOrEmpty(inappId) || string.IsNullOrEmpty(inappKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请联系管理员维护notify参数");
                }
                CommonHelper.X5PostForNotify(param, appId, appKey, inappId, inappKey, url, project, method, index);
               
            }
            catch (Exception ex)
            {
                throw new Exception("发送数据给Notify异常:" + ex.Message);
            }
        }

        /// <summary>
        /// xms 三包数据notify ，新的appid 
        /// </summary>
        /// <param name="param"></param>
        /// <param name="project"></param>
        /// <param name="index"></param>
        /// <param name="OrganizationService"></param>
        /// <exception cref="Exception"></exception>
        public static void SendToNotifyWarrantyData(object param, string project, string index, IOrganizationService OrganizationService)
        {
            try
            {
                string appId = CommonHelper.GetSystemParameter(OrganizationService, "notify_cs_appid");
                string appKey = CommonHelper.GetSystemParameter(OrganizationService, "notify_cs_appkey");
                string url = CommonHelper.GetSystemParameter(OrganizationService, "Not_notifyUrl");

                var xmsWarrantyDataQuerySys = CommonHelper.GetSystemParameter(OrganizationService, "sys_xmsWarrantyDataQuery");//三包数据的appid和key
                JObject parsedData = JObject.Parse(xmsWarrantyDataQuerySys);
                string inappId = parsedData["appId"]?.ToString();
                string inappKey = parsedData["appKey"]?.ToString();
                string method = "post";
                string inMethod = "ispWarranty";
                if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appKey) || string.IsNullOrEmpty(inappId) || string.IsNullOrEmpty(inappKey) || string.IsNullOrEmpty(url)||string.IsNullOrEmpty(inMethod))
                {
                    throw new Exception("请联系管理员维护三包数据同步notify参数");
                }
                CommonHelper.X5PostForNotify(param, appId, appKey, inappId, inappKey, url, project, method, index, inMethod);

            }
            catch (Exception ex)
            {
                throw new Exception("发送数据给Notify异常:" + ex.Message);
            }
        }


    }
}