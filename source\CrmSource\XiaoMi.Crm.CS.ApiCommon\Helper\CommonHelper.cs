﻿using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;

namespace XiaoMi.Crm.CS.ApiCommon.Helper
{
    public static class CommonHelper
    {
        /// <summary>
        /// 根据id获取唯一编码
        /// </summary>
        /// <param name="id">guid</param>
        /// <param name="service">组织服务</param>
        /// <param name="tracingService">trace</param>
        /// <param name="entityName">实体名</param>
        /// <param name="attributeName">要获取的属性名</param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public static string GetCodeById(Guid id, IOrganizationService service, ITracingService tracingService,
            string entityName, string attributeName)
        {
            try
            {
                var entity = service.RetrieveWithBypassPluginAndFlow(entityName, id, new ColumnSet(attributeName));
                if (entity != null && entity.TryGetAttributeValue<string>(attributeName, out string code))
                {
                    return code;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception e)
            {
                tracingService.Trace($"查询{entityName}的{attributeName}失败{e}");
                throw new InvalidPluginExecutionException(e.Message);
            }
        }
        
        /// <summary>
        /// 将对象序列化为 JSON，并进行 Base64 编码
        /// </summary>
        public static string EncodeObjectToBase64(object obj)
       {
           string json = JsonConvert.SerializeObject(obj);
           byte[] bytes = Encoding.UTF8.GetBytes(json);
           return Convert.ToBase64String(bytes);
       }

 
        /// <summary>
        /// X5Post请求
        /// </summary>
        /// <param name="param">接口方法</param>
        /// <param name="AppId">appid</param>
        /// <param name="AppKey">appkey</param>
        /// <param name="url">请求参数</param>
        /// <param name="tokenUrl">header 里添加method</param>
        /// <returns></returns>
        public static string X5PostForNotify(object param, string AppId, string AppKey, string inAppId, string inAppKey, string url, string project,string method = null,string index = "", string inmethod = null)
        {
            var innerBody = JsonConvert.SerializeObject(param);
            string insign = MD5Encrypt(inAppId + innerBody + inAppKey).ToUpper();
            var innerHeader = new { project = project, index = index, appid = inAppId, sign = insign, method=inmethod };
            var innerParam = new { header = innerHeader, body = innerBody };
            string sign = MD5Encrypt(AppId + JsonConvert.SerializeObject(innerParam) + AppKey).ToUpper();
            var outHeader = new { appid = AppId, sign = sign, method = method };
            var outBody = JsonConvert.SerializeObject(innerParam);
            var outParam = new { header = outHeader, body = outBody };
            string data = EncodeBase64(JsonConvert.SerializeObject(outParam));
            using (HttpClient httpClient = new HttpClient())
            {
                HttpContent httpContent = new StringContent($"data={WebUtility.UrlEncode(data)}");
                httpContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
                var response = httpClient.PostAsync(url, httpContent).Result;
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    return response.Content.ReadAsStringAsync().Result;
                }
                throw new Exception(response.ToString());
            }
        }

        /// <summary>
        /// Base64加密
        /// add Hyacinth 2021-11-18
        /// </summary>
        /// <param name="source">加密字串</param>
        /// <param name="result">返回成功加密后的字符串</param>
        /// <returns>是否加密成功</returns>
        private static string EncodeBase64(string source)
        {
            Encoding encode = Encoding.UTF8;
            string result = "";
            byte[] bytes = encode.GetBytes(source);
            try
            {
                result = Convert.ToBase64String(bytes);
            }
            catch
            {
                result = source;
            }
            return result;
        }

        /// <summary>
        /// MD5加密
        /// add Hyacinth 2021-11-18
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        private static string MD5Encrypt(string source)
        {
            string strResult = string.Empty;
            byte[] result = Encoding.UTF8.GetBytes(source);
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] output = md5.ComputeHash(result);
            strResult = BitConverter.ToString(output).Replace("-", "");
            return strResult;
        }

 

        /// <summary>
        /// 获取系统参数
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="paramName"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public static string GetSystemParameter(IOrganizationService organizationService, string paramName)
        {
            var query = new QueryExpression("new_systemparameter");
            query.ColumnSet = new ColumnSet("new_name", "new_value");
            query.Criteria = new FilterExpression(LogicalOperator.And);
            query.Criteria.AddCondition("new_name", ConditionOperator.Equal, paramName);
            var ret = organizationService.RetrieveMultiple(query);

            if (ret == null || ret.Entities.Count == 0)
            {
                throw new InvalidPluginExecutionException("缺少系统参数:" + paramName + "，请添加！");
            }
            if (ret.Entities.Count > 1)
            {
                throw new InvalidPluginExecutionException(string.Format("存在多个名称为{0}的系统参数", paramName));
            }
            var systemparameter = ret.Entities.FirstOrDefault();
            return systemparameter.GetAttributeValue<string>("new_value");
        }

        /// <summary>
        ///单条查询跳过插件和工作流
        /// </summary>
        /// <param name="service"></param>
        /// <param name="entityName"></param>
        /// <param name="id"></param>
        /// <param name="columnSet"></param>
        /// <param name="passCustomPluginExecution"></param>
        /// <returns></returns>
        public static Entity RetrieveWithBypassPluginAndFlow(this IOrganizationService service, string entityName, Guid id, ColumnSet columnSet, bool passCustomPluginExecution = true)
        {
            RetrieveRequest retrieveRequest = new RetrieveRequest()
            {
                Target = new EntityReference(entityName, id),
                ColumnSet = columnSet
            };
            retrieveRequest.Parameters.Add("BypassCustomPluginExecution", passCustomPluginExecution);
            retrieveRequest.Parameters.Add("BypassBusinessLogicExecution", "CustomSync,CustomAsync");
            RetrieveResponse retrieveResponse = (RetrieveResponse)service.Execute(retrieveRequest);
            return retrieveResponse.Entity;

        }


    }
}