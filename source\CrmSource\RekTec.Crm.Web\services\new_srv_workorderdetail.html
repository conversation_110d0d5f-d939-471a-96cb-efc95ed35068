﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>订单建单</title>
    <meta charset="utf-8" />
    <script type="text/javascript" src="../../ClientGlobalContext.js.aspx"></script>
    <script src="../js/es6_promise.min.js"></script>
    <script src="../js/new_vue.js"></script>
    <script src="../js/rtcrm.min.js"></script>
    <script src="../../xm_common"></script>
    <!-- 引入日志js -->
    <script src="../../xm_ailogging"></script>

    <!-- 引入样式 -->
    <link rel="stylesheet" href="../css/element_index.css">
    <!-- 引入组件库 -->
    <script src="../js/element_index.js"></script>
    <script src="../js/elementLanguage.js"></script>
    <link href="../css/rtcrm.min.css" rel="stylesheet">
    <script src="../components/rtcrm_lookup_dialog.js"></script>
    <script src="../components/rtcrm_lookup.js"></script> 
    <style type="text/css">
        html {
            overflow-y: auto;
        }

        html, body {
            background-color: #ffffff;
            /* height: auto; */
            max-height: none;
        }

        .inputButton {
            margin-left: 4px;
            width: 80px;
            height: 24px;
            font-size: 12px;
            border: 1px solid #ddd;
            background-color: rgb(17,96,183);
            color: #fff;
        }

        .search-style {
            padding-bottom: 0;
        }

        .ul-style {
            justify-content: space-between;
            margin-bottom: 20px;
            margin-right: 20px;
            padding-bottom: 0;
            padding-right: 0;
        }

            .ul-style li {
                flex: 0 !important;
                padding-right: 0 !important;
                margin-right: 40px;
            }


        .rt-crm-GridBody .rt-crm-GridCell {
            text-align: center;
        }

        /*.rt-crm-GridRow:hover {
                    background-color: #c4e8ff !important;
                    width: fit-content !important;
                }*/

        .rt-crm-GridRow {
            height: 30px;
            line-height: 30px;
            border-bottom: 1px solid #e1e1e1;
            padding: 10px 0;
        }

        .rt-crm-GridCell {
            white-space: normal;
            text-align: center;
            overflow: unset;
        }

        .rt-crm-Grid {
            height: auto;
        }

        .btn-time {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .imei-time {
            color: #000;
            font-size: 12px;
            margin-right: 60px;
        }

        .rt-crm-List-SearchContainer {
            padding: 0 20px;
        }

        .rt-crm-List-SearchContainer2 {
            padding: 0 20px 0 0;
        }

        .rt-crm-List-SearchContainer li {
            /*flex: none !important;*/
            /*width: 33%;*/
            justify-content: flex-end;
            padding-right: 0;
        }

        .rt-crm-List-SearchContainer2 li {
            /*flex: none !important;*/
            /*width: 33%;*/
            justify-content: flex-end;
            padding-right: 0;
        }

        .save-btn {
            position: absolute;
            right: 3%;
            top: 65%;
            left: unset;
            width: 100px;
        }

        .rt-crm-GridBody .rt-crm-GridCell {
            border-right: 1px solid transparent;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .rt-crm-Section ul {
            height: 32px;
        }

        .rt-crm-Section li {
            /*flex: auto;
                    width: 30%;
                    justify-content: left;
                    white-space: unset;*/
            justify-content: flex-end;
            padding-right: 0;
            white-space: unset;
        }

        .sectionClass li {
            padding-left: 40px;
            padding-right: 0;
        }

        /*        .sectionClass .rt-crm-Edit {
                    flex: auto;
                }*/

        .rt-crm-Edit {
            flex: none;
            font-size: 13px;
        }

        .rt-crm-Input {
            background: #fff;
            font-size: 13px;
            color: #666;
        }

        .rt-crm-Edit > span {
            padding-left: 7px;
            color: #444;
            /*            overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;*/
        }

        .rt-crm-GridBody {
            border: none;
            /*position: unset;
            overflow: unset;*/
        }

        .rt-crm-Select {
            width: 80px;
        }

        .el-input__inner {
            height: 32px !important;
            line-height: 32px;
            font-size: 14px;
        }

        .el-select {
            width: 100%;
        }

        /* .el-input__prefix, .el-input__suffix {

                    top: 4px;
                }*/

        .rt-crm-List-SearchContainer ul {
            padding: 20px 0 0;
            justify-content: space-between;
            width: 100%;
        }

        .rt-crm-List-SearchContainer2 ul {
            padding: 20px 0 0;
            justify-content: space-between;
            width: 100%;
        }

        .timeclass {
            width: 166px;
        }

        .rt-crm-TabContent {
            padding-bottom: 0;
            overflow: auto;
            max-height: 300px;
        }

        .rt-crm-TabTitle {
            color: #000;
            font-weight: inherit;
            font-size: 20px;
            margin-bottom: 20px;
            margin-left: 10px;
        }

        .rt-crm-Form-ContentContainer {
            border: 1px solid #d8d8d8;
            padding: 20px;
            margin: 25px 40px 25px 20px;
        }

        .rt-crm-InfoTab {
            padding-right: 0;
            padding-bottom: 20px;
        }

        .rt-crm-GridHeader {
            border: 0;
            border-bottom: 1px solid #e1e1e1;
            font-weight: bold;
            padding: 10px 0;
            background: #fff;
            overflow: unset;
        }

        .rt-crm-Label {
            width: 100px;
            font-size: 13px;
            color: #666;
            margin-right: 0;
            text-align: right;
        }

        .orderClass {
            width: 50%;
            /* overflow: hidden; */
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .selectClass {
            width: 40%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .rt-crm-GridHeader .rt-crm-GridCell {
            border-right: 0;
            text-align: center;
        }

        .rt-crm-ListPage {
            background: #fff;
            height: calc(100vh) !important;
        }

        .rt-crm-List-HeaderContainer {
            padding: 22px;
        }

        .rt-crm-List-ContentContainer {
            padding: 0;
        }

        .rt-crm-Tab {
            padding-right: 0;
        }
        /*-------------------ele组件----------------------------*/
        .el-tabs__nav-wrap {
            box-shadow: 1px 2px 5px 0 #f2f2f2;
        }

            .el-tabs__nav-wrap::after {
                height: 1px;
                background-color: #fff;
            }

        .el-tabs__item {
            font-size: 16px;
        }

        .el-tabs__content {
            padding: 10px 0;
        }

        .el-tabs {
            border-bottom: 1px dashed #d8d8d8;
            padding-bottom: 12px;
        }



        .rt-crm-Button {
            text-align: center;
            font-size: 14px;
        }

        .el-tab-pane {
            display: flex;
            align-items: center;
        }

        .el-table tr input[type=checkbox] {
            display: none;
        }

        .table-orderLine {
            border: 1px solid #d8d8d8;
            padding: 15px;
            margin-top: 20px;
        }

        .cell {
            font-size: 14px;
            color: #000;
            white-space: pre-wrap !important;
        }

        .el-tooltip {
            color: #666;
        }

        .el-button--danger {
            background-color: #0078d4;
            border-color: #0078d4;
        }

            .el-button--danger:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
            }
        /*底部样式*/
        .con-Bottom {
            padding: 20px 0;
            margin: 25px 40px 20px 20px;
            border-top: 1px dashed #d8d8d8;
        }

        .bottom-work {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .createBtn {
            padding: 15px 0;
            width: 100%;
            position: fixed;
            bottom: 0;
            display: flex;
            justify-content: center;
            z-index: 10;
            background: #fff;
            border-top: 1px solid #d8d8d8;
            box-shadow: 1px 3px 4px 4px #f2f2f2;
        }

        .checkOrderLabel {
            color: red;
        }

        .btn {
            margin: auto;
        }
        /*input边框聚焦改色，去除基本色*/
        .rt-crm-Edit input:focus {
            border-color: #0078d4;
            outline: none;
        }

        .rt-crm-Input input:focus {
            border-color: #0078d4;
            outline: none;
        }

        .el-input__icon {
            line-height: 32px;
        }

        .borderhidden {
            border: none;
        }

        /*        .box-bottom {
                    margin-bottom: 65px;
                }*/

        .splitLine {
            height: 1px;
            border-top: 1px dashed #d8d8d8;
            margin: 20px 40px 0 20px;
        }

        .rt-crm-SubGridHeader {
            width: 33%;
            margin-top: 20px;
            margin-bottom: 30px;
        }

            .rt-crm-SubGridHeader > span {
                font-size: 14px;
                color: #666;
                line-height: 30px;
                width: 40%;
                text-align: right;
            }

        .SubGridHeader {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .rt-crm-UploadBtn {
            padding: 3px;
            font-size: 12px;
            border-radius: 3px;
            color: #fff;
            background-color: #0078d4;
            border-color: #0078d4;
            width: 60px;
            text-decoration: none;
            text-align: center;
        }

            .rt-crm-UploadBtn input[type*='file'] {
                width: 60px;
            }

        .flexClass {
            flex: 1;
        }

        .upload-title {
            font-size: 12px;
            color: #606266;
            margin-top: 7px;
        }

        .upload-file {
            margin-top: 10px;
            cursor: pointer;
        }

            .upload-file:hover {
                color: #0078d4;
            }
        /*.rt-crm-UploadBtn:hover {
                        background: #66b1ff;
                    }*/
        .el-upload-list {
            width: auto !important;
            display: block !important;
            padding: 0 !important;
        }

        .el-upload-list__item {
            justify-content: space-between !important;
            line-height: 24px !important;
            width: auto !important;
        }

            .el-upload-list__item .el-icon-upload-success {
                color: #0078d4;
            }

        .el-upload-list__item-status-label {
            top: 7px;
        }
        /*隐藏全选按钮*/
        .el-table__header-wrapper .el-checkbox {
            display: none;
        }

        .no-serial-number {
            width: 250px;
            border-radius: 4px;
            margin-right: 10px;
        }

            .no-serial-number::placeholder {
                color: #c0c4cc;
            }

        .addresslong {
            /*justify-content: flex-end !important;
                    padding-right: 0 !important;
                    white-space: unset !important;
                    flex: unset !important;*/
            /*width: 100% !important;*/
            /*overflow: hidden !important;*/
            /*white-space: nowrap !important;*/
            /*text-overflow: ellipsis !important;*/
        }


        .el-row {
            line-height: 32px;
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .el-col {
            border-radius: 4px;
        }

        .grid-content {
            border-radius: 4px;
            min-height: 36px;
        }

        .row-bg {
            /*  padding: 10px 0;
                    background-color: #f9fafc; */
        }

        .tabListPage {
            text-align: right;
            margin-top: 15px;
        }

        .rt-crm-LookupInput {
            height: 32px;
        }

        .xm-label-left {
            width: 120px;
        }

        .rt-crm-Edit-mult {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
        /* 多行文本框样式 */
        .rt-crm-Edit .multi-line {
            white-space: pre-wrap; /* 保留换行符 */
            word-wrap: break-word; /* 自动换行 */
            min-height: 40px; /* 最小高度 */
            line-height: 1.5; /* 行高 */
            padding: 5px 10px;
            resize: none;
        }

        /* 移除边框 */
        .borderhidden {
            border: none;
            outline: none;
        }

        /* 保持原有输入框样式 */
        .rt-crm-Input {
            width: 100%;
            box-sizing: border-box;
        }

        /* 新增自定义上传样式 */
        .rt-crm-Edit-mult .custom-upload {
            display: flex;
            align-items: flex-start;
            gap: 30px; /* 控制按钮区域与文件列表的横向间距 */
        }

        /* 按钮区域保持垂直排列 */
        .rt-crm-Edit-mult .custom-upload__button-area {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex-shrink: 0; /* 防止按钮区域被压缩 */
        }

        /* 文件列表横向排列 */
        .rt-crm-Edit-mult .custom-upload .el-upload-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 0 !important;
            align-items: center;
        }

        /* 其他保持原有样式 */
        .rt-crm-Edit-mult .custom-upload .el-upload-list__item {
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border-radius: 4px;
            padding: 0 8px;
            max-width: 200px;
        }

        .rt-crm-Edit-mult .custom-upload .el-icon-close {
            position: static;
            margin-left: 8px;
        }



        .XM-info .rt-crm-query {
            top: -1px;
        }

        .XM-info .rt-crm-Label {
            width: 100%;
            line-height: 18px;
            overflow-wrap: break-word;
            word-break: break-all;
            justify-content: flex-end;
        }

        .XM-info .rt-crm-Edit {
            width: 100%
        }

        .XM-info-lab {
            text-align: right;
            display: flex;
            align-content: center;
            align-items: center; 
            overflow: hidden;
        }

        .XM-textarea {
            min-height: 40px;
            transition: all 0.3s ease;
        }

            .XM-textarea:hover, .XM-textarea:focus {
                border: 1px solid #ccc;
                border-radius: 5px;
                min-height: 60px; 
            }

        .el-collapse {
            width: calc(100% - 40px);
            margin: 0 auto;
            margin-bottom: 20px
        }
            .el-collapse .rt-crm-Form-ContentContainer {
                margin:0px
            }
        .XM-textarea::-webkit-scrollbar {
            width: 0px;
        }

        .XM-textarea::-webkit-scrollbar-track {
            background: transparent;
        }

        .XM-textarea::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 6px;
        }
        .XM-H2 { 
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            height: 48px;
            line-height: 48px;
            background-color: #FFF;
            color: #303133; 
            border-bottom: 1px solid #EBEEF5;
            font-size: 13px;
            font-weight: 500;
            -webkit-transition: border-bottom-color .3s;
            transition: border-bottom-color .3s;
            outline: 0;
                padding-left: 20px;
                font-weight: 700;  
        }
        .col-height{
            height: 53px;
            display: flex;
            align-items: center;
        }
        .el-collapse-item__header {
            font-weight: 700;    
        } 
        .el-cascader-menu:nth-child(1)::before,.el-cascader-menu:nth-child(2)::before,.el-cascader-menu:nth-child(3)::before,.el-cascader-menu:nth-child(4)::before {
            position: absolute;
            top: 0px;
            width: 100%;
            text-align: center;
            height: 26px;
            padding-top: 2px;
            font-size: 14px;
            font-weight: 500;
            color: #606266;
            white-space: nowrap;
            background-color: #f1f7ff;
        }
        .el-cascader-menu {
            position: relative;
            padding-top: 28px;
            min-width: 120px;
        }

        .el-cascader-menu__list {
            margin-top: 4px;
        }
        .el-cascader-menu__empty-text {
            top:43%
        }
        .el-cascader-menu__list {
  margin-top: 4px; 
} 
    </style>
</head>
<body>
    <div id="app" class="rt-crm-ListPage"> 
        <div class="rt-crm-List-HeaderContainer">
            <div class="rt-crm-ListTitle" v-cloak>{{$t("createworkorder.tab.create","创建工单")}}</div>
        </div>
        <div class="rt-crm-List-SearchContainer" style="padding-right:0">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane v-cloak :label="`${$t('createworkorder.tab.createchuanhao','串号工单创建')}`" name="first">
                    <div class="rt-crm-Edit">
                        <input class="rt-crm-Input" style=" width: 250px" type="text" v-cloak :placeholder="`${$t('createworkorder.tab.pleaceinputimei','请输入IMEI/SN/FSN/订单号')}`" v-on:keyup.enter="reloadData" v-model="suppliername" v-bind:disabled="new_isreadnum==1" />
                    </div>
                    <div class="rt-crm-Label" style="width:auto;margin-right:5px;margin-left: 20px;">
                        <span v-cloak>{{$t("createworkorder.tab.isreadchuanhao","是否支持读取串号：")}}</span>
                    </div>
                    <div class="rt-crm-Edit">
                        <el-select v-model="new_isreadnum" @change="isreadnumselect" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <el-button @click="searchImei" v-show="new_isreadnum==1" type="primary" plain size="small" v-cloak>{{$t("new_service_handing.imeiread","IMEI读取")}}</el-button>
                    <el-button @click="reloadData" type="primary" size="small" style="margin-left: 30px;" v-cloak>{{$t("common.btn.query","查询")}}</el-button>
                </el-tab-pane>
                <el-tab-pane v-cloak :label="`${$t('createworkorder.tab.createfeichuanhao','无串号工单创建')}`" name="second">
                    <div class="rt-crm-Edit" style="display: flex;">
                        <input class="rt-crm-Input no-serial-number" type="text" :placeholder="`${$t('createworkorder.tab.pleaceinputgoodsid','请输入商品编码')}`" v-on:keyup.enter="reloadData" v-model="suppliername" />
                        <el-date-picker v-model="new_invoice_time"
                                        type="date"
                                        value-format="yyyy-MM-dd"
                                        :placeholder="`${$t('Select_Please_InvoiceTime','请输入发票时间')}`"
                                        :picker-options="pickerOptions">
                        </el-date-picker>
                    </div>
                    <el-button @click="reloadData" type="primary" size="small" style="margin-left: 30px;" v-cloak>{{$t("common.btn.query","查询")}}</el-button>
                </el-tab-pane>
                <el-tab-pane v-cloak :label="`${$t('blurform4959.blurcreateorder','串号工单模糊创建')}`" name="third">
                </el-tab-pane>
            </el-tabs>
        </div>
        <div v-if="showOrderContent" v-cloak v-bind:style="boxBottom">
            <el-collapse v-model="activeNames" @change="activeNamesChange">
                <el-collapse-item :title='$t("orderDataDetail.Tab","订单信息")' name="1" v-if="(showNumber || showImei) &&!showOrder&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer"
                         v-loading="orderDetailLoading"
                         :element-loading-background="loadingBackground"
                         :spinner="loadingSpinner">
                        <div class="rt-crm-InfoTab" v-if="orderDataDetail">
                            <!--<h2 class="rt-crm-TabTitle">{{$t("orderDataDetail.Tab","订单信息")}}</h2>-->
                            <div class="rt-crm-TabContent">
                                <div class="rt-crm-Section sectionClass">
                                    <el-row :gutter="0">
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.order_id","订单编号")'>{{$t("order.label.order_id","订单编号")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <input class="rt-crm-Input borderhidden" type="text" v-model="orderDataDetail.order_id">
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.order_status_describe","订单状态")'>{{$t("order.label.order_status_describe","订单状态")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <input class="rt-crm-Input borderhidden" type="text" v-model="orderDataDetail.order_status_describe">
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.country","国家/地区")'>{{$t("order.label.country","国家/地区")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <span>{{orderDataDetail.countryname?orderDataDetail.countryname:orderDataDetail.country}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="0">
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.consignee_c","收货人")'>{{$t("order.label.consignee_c","收货人")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <input class="rt-crm-Input borderhidden" type="text" v-model="orderDataDetail.consignee_c" disabled="">
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("orderDataDetail.tel_c","收货电话")'>{{$t("orderDataDetail.tel_c","收货电话")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <input class="rt-crm-Input borderhidden" type="text" v-model="orderDataDetail.tel_c" disabled="">
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("searchDate.new_street.name","街道")'>{{$t("searchDate.new_street.name","街道")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <span>{{(orderDataDetail.searchDate&&orderDataDetail.searchDate.new_street)?orderDataDetail.searchDate.new_street.name:orderDataDetail.area}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="0">
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.total_price","订单金额")'>{{$t("order.label.total_price","订单金额")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <input class="rt-crm-Input borderhidden" type="text" v-model="orderDataDetail.goods_amount" disabled="">
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("orderDataDetail.new_isfenqi","是否分期")'>{{$t("orderDataDetail.new_isfenqi","是否分期")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit timeclass">
                                                <span>{{orderDataDetail.new_isfenqi?$t("common.true","是"):$t("common.false", "否")}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("searchDate.new_county.name","区县")'>{{$t("searchDate.new_county.name","区县")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit timeclass">
                                                <span>{{orderDataDetail.districtname?orderDataDetail.districtname:orderDataDetail.district}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="0">
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.add_time","下单时间")'>{{$t("order.label.add_time","下单时间")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit timeclass">
                                                <span v-if="orderDataDetail.pay_time>0">{{dateTime(orderDataDetail.pay_time)}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("orderDataDetail.invoice_company_code","销售主体")'>{{$t("orderDataDetail.invoice_company_code","销售主体")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit timeclass">
                                                <span v-if="orderDataDetail.invoice_company_code">{{orderDataDetail.invoice_company_code}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.city","城市")'>{{$t("order.label.city","城市")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit timeclass">
                                                <span>{{orderDataDetail.cityname?orderDataDetail.cityname:orderDataDetail.city}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="0">
                                        <el-col :span="16">
                                            <div class="rt-crm-Label orderClass" style="width:25%;">
                                                <span>{{$t("order.label.sale_channel","销售渠道")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit" style="white-space: nowrap; position: absolute;padding-left:5px;padding-top:1px;">
                                                <span>{{(orderDataDetail.sale_channel||"") +"-"+ orderDataDetail.sale_channel_describe}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="rt-crm-Label orderClass">
                                                <span :title='$t("order.label.province","省份")'>{{$t("order.label.province","省份")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <span>{{orderDataDetail.provincename?orderDataDetail.provincename:orderDataDetail.province}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="0">
                                        <el-col :span="16">
                                            <div class="rt-crm-Label orderClass" style="width:25%;">
                                                <span>{{$t("orderDataDetail.address_c","具体地址")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit" style="white-space: nowrap; position: absolute;padding-left:5px;padding-top:1px;">
                                                <span>{{orderDataDetail.address_c}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <!-- add by p-huyan9 2023/04/17 增加销售类型描述-->
                                            <div class="rt-crm-Label orderClass">
                                                <span>{{$t("order.label.sales_type","销售类型")+"："}}</span>
                                            </div>
                                            <div class="rt-crm-Edit">
                                                <span>{{orderDataDetail.sales_type_describe}}</span>
                                            </div>
                                            <!-- end add by p-huyan9 2023/04/17 增加销售类型描述-->
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                            <div class="table-orderLine" v-show="(showNumber || showImei) &&!showOrder">
                                <el-table ref="multipleTable"
                                          :data="stations"
                                          v-show="stations.length>0"
                                          tooltip-effect="dark"
                                          style="width: 100%"
                                          max-height="450"
                                          @selection-change="handleSelectionChange">
                                    <el-table-column type="selection"
                                                     :selectable="selectState"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="goods_id"
                                                     min-width="150"
                                                     :label="`${$t('orderline.label.goodsid','商品编码')}`"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="mode"
                                                     min-width="200"
                                                     :label="`${$t('orderline.label.mode','商品名称')}`"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="new_airconditioningtypedesc"
                                                     min-width="200"
                                                     :label="`${$t('orderline.label.airconditioningtype','内机/外机')}`"
                                                     v-if="showAirconditioningType"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="new_repairrightsname"
                                                     min-width="150"
                                                     :label="`${$t('orderline.label.repairrightsname','权益')}`"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="new_iszengpin"
                                                     min-width="150"
                                                     :label="`${$t('orderline.label.new_iszengpin','是否赠品')}`"
                                                     show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            {{ scope.row.new_iszengpin?$t("common.true","是"):$t("common.false", "否") }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column min-width="150" :label="`${$t('orderline.label.batchgoodsid','大礼包ID')}`">
                                        <template slot-scope="scope" v-if="scope.row.batch_goods_id>0">
                                            {{ scope.row.batch_goods_id }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="new_isserialnum"
                                                     :label="`${$t('orderline.label.new_isserialnum','是否串号管理')}`"
                                                     min-width="150"
                                                     show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            {{ scope.row.new_isserialnum?$t("common.true","是"):$t("common.false", "否") }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="new_sn"
                                                     label="SN"
                                                     min-width="200"
                                                     show-overflow-tooltip>
                                    </el-table-column>
                                    <el-table-column prop="goods_count"
                                                     :label="`${$t('orderline.label.goods_count','数量')}`"
                                                     min-width="100">
                                    </el-table-column>
                                    <el-table-column prop="signed_time"
                                                     :label="`${$t('orderline.label.signed_time','妥投时间')}`"
                                                     min-width="200">
                                    </el-table-column>
                                </el-table>
                                <el-empty v-show="stations.length==0" description=" "
                                          v-loading="orderItemLoading"
                                          :element-loading-background="loadingBackground"></el-empty>
                            </div>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title='$t("Maintenances.Tab","IMEI维保信息")' name="2" v-if="(showImei || showOrder)&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer">
                        <div class="rt-crm-Tab">
                            <!--<h2 class="rt-crm-TabTitle">{{$t("Maintenances.Tab","IMEI维保信息")}}</h2>-->
                            <el-table :data="maintenances"
                                      tooltip-effect="dark"
                                      v-show="maintenances.length>0"
                                      style="width: 100%"
                                      max-height="450">
                                ="
                                <el-table-column prop="new_projectcode"
                                                 :label="`${$t('orderline.label.new_projectcode','项目编码')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_commoditycode"
                                                 :label="`${$t('orderline.label.goodsid','商品编码')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_commodityname"
                                                 :label="`${$t('orderline.label.mode','商品名称')}`"
                                                 min-width="300">
                                </el-table-column>
                                <el-table-column prop="imei"
                                                 label="IMEI"
                                                 v-if="!showOrder"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="new_sn"
                                                 label="SN"
                                                 v-if="!showOrder"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="fsn"
                                                 label="FSN"
                                                 v-if="!showOrder"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="new_packetsstarttime"
                                                 :label="`${$t('maintenances.label.new_packetsstarttime','三包开始日期')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_maintenanceendtime"
                                                 :label="`${$t('maintenances.label.new_maintenanceendtime','维修截止日期')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_returnendtime"
                                                 :label="`${$t('maintenances.label.new_returnendtime','退货截止日期')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_exchangeendtime"
                                                 :label="`${$t('maintenances.label.new_exchangeendtime','换货截止日期')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_factorytimeaddtime"
                                                 :label="`${$t('orderline.label.factorytimeaddtime','出厂时间+90天')}`"
                                                 v-if="caseid || !showOrder"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_activate_date"
                                                 :label="`${$t('orderline.label.activatedate','激活时间')}`"
                                                 v-if="caseid ||!showOrder"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="b2b_country"
                                                 v-if="!showOrder"
                                                 :label="`${$t('maintenances.label.b2b_country','销售地')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="active_country"
                                                 v-if="!showOrder"
                                                 :label="`${$t('maintenances.label.active_country','激活地')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_isparallelgoods"
                                                 :label="`${$t('maintenances.label.new_isparallelgoods','是否非行货')}`"
                                                 min-width="150"
                                                 show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ scope.row.new_isparallelgoods? $t("common.true","是"):$t("common.false", "否") }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="isInternationalWarranty"
                                                 :label="`${$t('maintenances.label.isinternationalwarranty','是否国际联保')}`"
                                                 min-width="150"
                                                 show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ scope.row.new_isinternationalwarranty? $t("common.true","是"):$t("common.false", "否") }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="new_maintenanceresult"
                                                 :label="`${$t('maintenances.label.code','维保code')}`"
                                                 fixed="right"
                                                 v-if="!showOrder"
                                                 width="200">
                                </el-table-column>
                                <el-table-column prop="new_maintenancereason"
                                                 v-if="!showOrder"
                                                 :label="`${$t('maintenances.label.describe','维保描述')}`"
                                                 fixed="right"
                                                 min-width="200">
                                </el-table-column>
                            </el-table>
                            <el-empty v-show="maintenances.length==0"
                                      description=" "
                                      v-loading="iMeiLoading"
                                      :element-loading-background="loadingBackground"></el-empty>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title='$t("SearchOldAndNewImeiList.Tab","历史服务记录")' name="3" v-if="showImei &&!showOrder&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer" v-show="showImei &&!showOrder&&!showBlurOrder">
                        <div class="rt-crm-Tab">

                            <el-table :data="searchOldAndNewImeiList"
                                      tooltip-effect="dark"
                                      v-show="searchOldAndNewImeiList.length>0"
                                      style="width: 100%"
                                      max-height="450"
                                      :spinner="loadingSpinner">
                                <el-table-column prop="new_name"
                                                 :label="`${$t('searchOldAndNewImeiList.label.new_name','服务单号')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_servicemode"
                                                 :label="`${$t('Workorder.new_servicemode','服务方式')}`"
                                                 min-width="120">
                                </el-table-column>
                                <el-table-column prop="new_type"
                                                 :label="`${$t('Workorder.new_type','服务类型')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_sn"
                                                 label="SN"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="new_imei"
                                                 label="IMEI"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="new_ordercode"
                                                 :label="`${$t('order.label.order_id','订单号')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_dealstatus"
                                                 :label="`${$t('searchOldAndNewImeiList.label.new_dealstatus','工单状态')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_goodsfiles_idname"
                                                 :label="`${$t('searchOldAndNewImeiList.label.new_goodsfiles_idname','商品档案')}`"
                                                 min-width="200"
                                                 show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="new_station_idname"
                                                 :label="`${$t('searchOldAndNewImeiList.label.new_station_idname','所属网点')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="createdon"
                                                 :label="`${$t('new_attachment.label.createdon','创建时间')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column fixed="right"
                                                 :label="`${$t('searchOldAndNewImeiList.Operation','操作')}`"
                                                 width="100">
                                    <template slot-scope="scope">
                                        <el-button size="mini"
                                                   type="danger"
                                                   @click="jumpRibbon(scope.row.new_srv_workorderid)">{{$t("common.btn.query","查询")}}</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div class="tabListPage" v-show="searchOldAndNewImeiList.length > 0">
                                <el-pagination @size-change="oldImeiHandleSizeChange"
                                               @current-change="oldImeiHandleCurrentChange"
                                               :current-page="oldImeiCurrentPage"
                                               :page-sizes="oldImeiPageSizes"
                                               :page-size="oldImeiPageSize" layout="total, sizes, prev, pager, next"
                                               :total="oldImeiTotalCount">
                                </el-pagination>
                            </div>
                            <el-empty v-show="searchOldAndNewImeiList.length==0"
                                      description=" "
                                      v-loading="oldImeiLoading"
                                      :element-loading-background="loadingBackground"></el-empty>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title='$t("Profilerights.Tab","可用的权益信息")' name="4" v-if="showImei &&!showOrder&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer">
                        <div class="rt-crm-Tab">
                            <el-table :data="profilerights"
                                      tooltip-effect="dark"
                                      v-show="profilerights.length>0"
                                      style="width: 100%"
                                      v-loading="profilerightsLoading"
                                      max-height="450">
                                <el-table-column prop="new_code"
                                                 :label="`${$t('Profilerights.label.new_code','权益编号')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_classtype"
                                                 :label="`${$t('Profilerights.label.new_classtype','权益类别')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_imei"
                                                 label="IMEI"
                                                 min-width="150">
                                </el-table-column>
                                <el-table-column prop="new_sku"
                                                 label="SKU"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_type"
                                                 :label="`${$t('Profilerights.label.new_type','权益类型')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_usestatus"
                                                 :label="`${$t('Profilerights.label.new_usestatus','使用状态')}`"
                                                 min-width="100">
                                </el-table-column>
                                <el-table-column prop="new_ordercode"
                                                 :label="`${$t('order.label.order_id','订单号')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_buytime"
                                                 :label="`${$t('Profilerights.label.new_buytime','购买时间')}`"
                                                 min-width="200">
                                </el-table-column>
                                <el-table-column prop="new_endtime"
                                                 fixed="right"
                                                 :label="`${$t('Profilerights.label.new_endtime','权益结束时间')}`"
                                                 width="200">
                                </el-table-column>
                            </el-table>
                            <el-empty v-show="profilerights.length==0" description=" "></el-empty>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title='$t("Activities.Tab","可参与的服务活动")' name="5" v-if="(showImei || showOrder) &&!showNumber&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer">
                        <div class="rt-crm-Tab">
                            <el-table :data="activities"
                                      v-show="activities.length>0"
                                      tooltip-effect="dark"
                                      style="width: 100%"
                                      max-height="450"
                                      :spinner="loadingSpinner">
                                <el-table-column prop="actioncode"
                                                 :label="`${$t('Activities.label.actioncode','活动号')}`">
                                </el-table-column>
                                <el-table-column prop="actionstring"
                                                 :label="`${$t('Activities.label.actionstring','一级活动项目')}`">
                                </el-table-column>
                                <el-table-column prop="actiontypename"
                                                 :label="`${$t('Activities.label.actiontypename','二级活动项目')}`">
                                </el-table-column>
                                <el-table-column prop="servicename"
                                                 :label="`${$t('Activities.label.servicename','服务活动名称')}`">
                                </el-table-column>
                                <el-table-column prop="new_activityName"
                                                 :label="`${$t('Activities.label.new_activityName','活动类型')}`">
                                </el-table-column>
                                <el-table-column prop="starttime"
                                                 :label="`${$t('Activities.label.starttime','三包/建单活动开始时间')}`">
                                </el-table-column>
                                <el-table-column prop="endtime"
                                                 :label="`${$t('Activities.label.endtime','三包/建单活动结束时间')}`">
                                </el-table-column>
                                <el-table-column prop="activeperiod"
                                                 :label="`${$t('Activities.label.activeperiod','活动有效期（天）')}`">
                                </el-table-column>
                            </el-table>
                            <el-empty v-show="activities.length==0" description=" " v-loading="serviceActionLoading"
                                      :element-loading-background="loadingBackground"></el-empty>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title='$t("OrderActivity.Tab","已参与的服务活动")' name="6" v-if="showImei &&!showOrder&&!showBlurOrder">
                    <div class="rt-crm-Form-ContentContainer">
                        <div class="rt-crm-Tab">
                            <!-- <h2 class="rt-crm-TabTitle">{{$t("OrderActivity.Tab","已参与的服务活动")}}</h2> -->
                            <!-- height="230" -->
                            <el-table :data="OrderActivity"
                                      tooltip-effect="dark"
                                      v-show="OrderActivity.length>0"
                                      style="width: 100%"
                                      :spinner="loadingSpinner">
                                mn
                                <el-table-column prop="new_arconfiguration_id"
                                                 :label="`${$t('Activities.label.actioncode','活动号')}`">
                                </el-table-column>
                                <el-table-column prop="new_projectinfo_first_id"
                                                 :label="`${$t('Activities.label.actionstring','一级活动项目')}`">
                                </el-table-column>
                                <el-table-column prop="new_projectinfo_second_id"
                                                 :label="`${$t('Activities.label.actiontypename','二级活动项目')}`">
                                </el-table-column>
                                <el-table-column prop="new_servicename"
                                                 :label="`${$t('Activities.label.servicename','服务活动名称')}`">
                                </el-table-column>
                                <el-table-column prop="new_workorder_id"
                                                 :label="`${$t('OrderActivity.new_workorder_id','所属服务单')}`">
                                </el-table-column>
                                <el-table-column prop="createdon"
                                                 :label="`${$t('OrderActivity.createdon','活动参与时间')}`">
                                </el-table-column>
                            </el-table>
                            <el-empty v-show="OrderActivity.length==0" description=" " v-loading="orderActivityLoading"
                                      :element-loading-background="loadingBackground"></el-empty>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <!-- 新的订单详情页面 -->






            <div class="rt-crm-Form-ContentContainer" v-show="false">
                <div class="rt-crm-Tab">
                    <h2 class="rt-crm-TabTitle">{{$t("Activeservicerecords.Tab","主动服务登记记录")}}</h2>
                    <el-table :data="activeservicerecords"
                              tooltip-effect="dark"
                              v-show="activeservicerecords.length>0"
                              style="width: 100%"
                              height="auto"
                              @selection-change="activeservicerecordChange">
                        <el-table-column type="selection"
                                         width="55">
                        </el-table-column>
                        <el-table-column prop="new_name"
                                         :label="`${$t('Activeservicerecords.label.new_name','主动服务登记编码')}`"
                                         min-width="200">
                        </el-table-column>
                        <el-table-column prop="new_type"
                                         :label="`${$t('Activeservicerecords.label.new_type','主动服务类型')}`"
                                         min-width="150">
                        </el-table-column>
                        <el-table-column prop="new_accountname"
                                         :label="`${$t('Activeservicerecords.label.new_accountname','用户姓名')}`"
                                         min-width="150">
                        </el-table-column>
                        <el-table-column prop="new_phone"
                                         :label="`${$t('Activeservicerecords.label.new_phone','电话')}`"
                                         min-width="150">
                        </el-table-column>
                        <el-table-column prop="new_imei"
                                         label="IMEI"
                                         min-width="150">
                        </el-table-column>
                        <el-table-column prop="new_sn"
                                         label="SN"
                                         min-width="150">
                        </el-table-column>
                        <el-table-column prop="new_buytime"
                                         :label="`${$t('Profilerights.label.new_buytime','购买时间')}`"
                                         min-width="200">
                        </el-table-column>
                        <el-table-column prop="new_recordtime"
                                         fixed="right"
                                         :label="`${$t('Activeservicerecords.label.new_recordtime','信息登记时间')}`"
                                         width="200">
                        </el-table-column>
                    </el-table>
                    <el-empty v-show="activeservicerecords.length==0" description=" "></el-empty>
                </div>
            </div>
            <div class="splitLine" v-show="!showBlurOrder&&!isIncident"></div>
            <div class="rt-crm-List-SearchContainer" v-show="!showBlurOrder&&!isIncident">
                <div class="rt-crm-Section">
                    <ul>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_servicemode","服务方式")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-select v-model="new_servicemode" @change="selectTypeDone" v-show="caseid==null" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="service in servicemodeOption" :key="service.id" :label="service.name" :value="service.id">
                                    </el-option>
                                </el-select>
                                <el-select v-model="new_servicemode" @change="selectTypeDone" v-show="caseid!=null" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="caseservicemode in caseservicemodeOption" :key="caseservicemode.id" :label="caseservicemode.name" :value="caseservicemode.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_type","服务类型")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-select v-model="new_type" @change="selectTypeDone" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="itemtype in typeOption" :key="itemtype.id" :label="itemtype.name" :value="itemtype.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span :style="{visibility:(new_type==3?'unset':'hidden')}">
                                    <span style="color:red">*</span>
                                    {{$t("Workorder.new_returntype","退货类型")+"："}}
                                </span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-select :style="{visibility:(new_type==3?'unset':'hidden')}" v-model="new_returntype" @change="selectTypeDone" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="typeoption in returntypeOption" :key="typeoption.id" :label="typeoption.name" :value="typeoption.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>
                    </ul>
                    <ul>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span>{{$t("Workorder.serviceSelectInvoice","是否有发票")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-select v-model="serviceSelectInvoice" :disabled="showOrder" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span v-show="serviceSelectInvoice==1">{{$t("Workorder.new_invoice_num","发票号码")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <input class="el-input__inner" style="padding: 0 30px 0 15px;" v-show="serviceSelectInvoice==1" :placeholder="`${$t('Select_Please_InvoiceNum','请输入发票号码')}`" v-model="new_invoice_num">
                            </div>
                        </li>
                        <li>
                            <div class="rt-crm-Label selectClass" v-show="serviceSelectInvoice==1">
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_invoice_time","发票时间")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-date-picker v-model="new_invoice_time"
                                                v-show="serviceSelectInvoice==1"
                                                style="width:100%"
                                                type="date"
                                                value-format="yyyy-MM-dd"
                                                :placeholder="`${$t('Select_Please_InvoiceTime','请输入发票时间')}`"
                                                :picker-options="pickerOptions">
                                </el-date-picker>
                            </div>
                        </li>
                    </ul>
                    <ul>
                        <li>
                            <div class="rt-crm-Label selectClass">
                                <span>{{$t("Workorder.new_isprototype","是否样机")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-select v-model="new_isprototype" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="itemtype in isprototypeList" :key="itemtype.id" :label="itemtype.name" :value="itemtype.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>
                        <li>
                            <div class="rt-crm-Label flexClass">
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_send_questionnaire","同意售后回访")+"："}}</span>
                            </div>
                            <div class="rt-crm-Edit flexClass">
                                <el-radio v-model="new_send_questionnaire" label="true">{{$t("common.isTrue","是")}}</el-radio>
                                <el-radio v-model="new_send_questionnaire" label="false">{{$t("common.isFalse","否")}}</el-radio>
                            </div>
                        </li>
                        <li></li>
                    </ul>
                    <div class="rt-crm-SubGridHeader" v-show="(serviceSelectInvoice==1||!showOrder)&&!isIncident">
                        <span>{{$t("Workorder.fujian","上传附件")+"："}}</span>
                        <el-upload class="upload-demo"
                                   action=""
                                   :on-change="handleChange"
                                   :before-remove="beforeRemove"
                                   :auto-upload="false"
                                   :file-list="fileList">
                            <el-button size="small" type="primary">{{$t("Workorder.selectwenjian","选取文件")}}</el-button>

                            <div slot="tip" class="el-upload__tip">{{$t("new_attachment.fileSizeCheck", "文件大小必须小于10MB")}}</div>
                        </el-upload>
                    </div>
                </div>
            </div>


            <h2 class="XM-H2" v-show="!showBlurOrder&&isIncident">{{$t("orderDataDetail.Tab.Fault","故障信息")}}</h2>
            <div class="rt-crm-List-SearchContainer2" v-show="!showBlurOrder&&isIncident">
                <div class="rt-crm-Section XM-info" style="margin-top: 15px;">
                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height">
                                <span style="color:red">*</span>
                                <span>{{$t("new_srv_workorder.FaultDescription","故障描述")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="10">
                            <div class="rt-crm-Edit-mult  ">
                                <textarea class="rt-crm-Input borderhidden multi-line XM-textarea" type="text" placeholder="---" v-model="texterro" style="resize: none; padding: 1em 0.5em; "></textarea>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass  col-height">
                                <span>{{$t("new_srv_workorder.Description","备注")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="10">
                            <div class="rt-crm-Edit-mult col-height " style="    align-content: flex-start;    flex-wrap: wrap;">
                                <textarea class="rt-crm-Input borderhidden multi-line XM-textarea" type="text" placeholder="---" v-model="textarea" style="resize: none; padding: 1em 0.5em;"></textarea>
                            </div>
                        </el-col>
                    </el-row>

                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height">
                                <span>{{$t("Workorder.fujian","上传附件")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="10">
                            <div class="rt-crm-Edit-mult">
                                <el-upload class="custom-upload"
                                           action=""
                                           :on-change="handleChange"
                                           :before-remove="beforeRemove"
                                           :auto-upload="false"
                                           :file-list="fileList">

                                    <!-- 新增按钮区域容器 -->
                                    <div class="custom-upload__button-area">
                                        <el-button size="small" type="primary">{{$t("Workorder.selectwenjian","选取文件")}}</el-button>
                                        <div slot="tip" class="el-upload__tip">{{$t("new_attachment.fileSizeCheck", "文件大小必须小于10MB")}}</div>
                                    </div>
                                </el-upload>
                            </div>
                        </el-col>
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass  col-height">
                                &nbsp;
                            </div>
                        </el-col>
                        <el-col :span="10">
                            <div class="rt-crm-Edit-mult col-height">
                                &nbsp;
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <h2 class="XM-H2" v-show="!showBlurOrder&&isIncident">{{$t("orderDataDetail.Tab.Information","受理信息")}}</h2>
            <div class="rt-crm-List-SearchContainer2" v-show="!showBlurOrder&&isIncident">
                <div class="rt-crm-Section XM-info">
                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.customer_name","反馈人") + "："'>
                                <span>{{$t("service.label.feedbackPerson","反馈人") + "："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <input class="el-input__inner XM-input" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="customer_name">
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.new_phone","反馈人电话") + "："'>
                                <span style="color:red">*</span>
                                <span>{{$t("service.label.feedbackPersonPhone","反馈人电话") + "："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <input class="el-input__inner XM-input" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="new_phone">
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.new_phone2","反馈人电话2" ) + "："'>
                                <span>{{$t("service.label.feedbackPersonPhone2","反馈人电话2" ) + "："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <input class="el-input__inner XM-input" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="new_phone2">
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.email","邮箱" ) + "："'>
                                <span style="color:red">*</span>
                                <span>{{$t("logisticsinfo.email","邮箱" ) + "："}}</span>
                            </div>
                        </el-col>
                      
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <input class="el-input__inner XM-input" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="email">
                            </div>
                        </el-col>
                    </el-row>

                    <el-row :gutter="1">          
                             <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("order.label.country","国家/地区" ) + "："'>
                                <span v-if="isCountryRequiredByServiceMode" style="color:red">*</span>
                                <span>{{$t("order.label.country","国家/地区" ) + "："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <rtcrm-lookup mode="dialog"
                                              entity-name="new_country"
                                              entityset-name="new_countries"
                                              name-field="new_name"
                                              key-field="new_countryid"
                                              :query-fields="['new_name','new_code']"
                                              :display-fields="[{name:'new_code',text:`${$t('lookup.code','编码')}`,width:'200px'},{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                              :order-fields="[{name:'new_name', descending: true}]"
                                              v-model="blurform.new_country_id"
                                              filter="<condition attribute='statuscode' operator='eq' value='1' />"
                                              :disabled="countryLocked">
                                </rtcrm-lookup>
                            </div>
                        </el-col>         
                            <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("order.label.country","国家/地区" ) + "："'>
                                <span v-if="isProvinceRequiredByServiceMode" style="color:red">*</span>
                                 <span>{{$t("order.label.detail_address","详细地址" ) + "："}}</span>   
                            </div>
                        </el-col>
                          <el-col :span="10">
                            <div class="rt-crm-Edit col-height">
                               <el-cascader v-model="countySelect" :suffixtext="suffixtext" :notselecttext="notselecttext"  ref="cascader2" @visible-change="cascaderhandleVisibleChange"
            :clearable="true" :placeholder="$t('common.Pleaseenterprovincecity','请输入省份/城市')" :options="countyOptions" filterable
            style="width: 100%;  " @change="cascaderhandleChange">
            <template slot-scope="{ node, data }">
                <span style="display:block" @click="countyonClick(node,$event,2)">{{ data.label }}</span>
            </template>
        </el-cascader>
                            </div>
                        </el-col>
   
    
 

                        <!-- <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("logisticsinfo.province","省份") + "："'>
                                <span v-if="isProvinceRequiredByServiceMode" style="color:red">*</span>
                                <span>{{$t("logisticsinfo.province","省份") + "："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <rtcrm-lookup mode="dialog"
                                              entity-name="new_province"
                                              entityset-name="new_provinces"
                                              name-field="new_name"
                                              key-field="new_provinceid"
                                              :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                              :query-fields="['new_name']"
                                              :order-fields="[{name:'new_name', descending: true}]"
                                              v-model="blurform.new_province_id"
                                              :disabled="(blurform.new_country_id == null ||blurform.new_country_id.id == null) && (blurform.new_province_id == null ||blurform.new_province_id.id == null)"
                                              :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_country_id' operator='eq' value='${blurform.new_country_id.id}' />`">
                                </rtcrm-lookup>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("logisticsinfo.city","城市")+"："'>
                                <span v-if="isCityRequiredByServiceMode" style="color:red">*</span>
                                <span>{{$t("logisticsinfo.city","城市")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <rtcrm-lookup mode="dialog"
                                              entity-name="new_city"
                                              entityset-name="new_cities"
                                              name-field="new_name"
                                              key-field="new_cityid"
                                              :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                              :query-fields="['new_name']"
                                              :order-fields="[{name:'new_name', descending: true}]"
                                              v-model="blurform.new_city_id"
                                              :disabled="(blurform.new_province_id == null ||blurform.new_province_id.id == null) && (blurform.new_city_id == null || blurform.new_city_id.id == null)"
                                              :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_province_id' operator='eq' value='${blurform.new_province_id.id}' />`">
                                </rtcrm-lookup>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("logisticsinfo.county","区县")+"："'>
                                <span>{{$t("logisticsinfo.county","区县")+"："}}</span>
                            </div>
                        </el-col>

                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <rtcrm-lookup mode="dialog"
                                              entity-name="new_county"
                                              entityset-name="new_counties"
                                              name-field="new_name"
                                              key-field="new_countyid"
                                              :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                              :query-fields="['new_name']"
                                              :order-fields="[{name:'new_name', descending: true}]"
                                              v-model="blurform.new_county"
                                              :disabled="(blurform.new_city_id == null || blurform.new_city_id.id == null) && (blurform.new_county == null || blurform.new_county.id == null)"
                                              :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_city_id' operator='eq' value='${blurform.new_city_id.id}' />`">
                                </rtcrm-lookup>
                            </div>
                        </el-col> -->
                    </el-row>

                    <el-row :gutter="1">

                        <!-- <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("searchDate.new_street.name","街道")+"："'>
                                <span>{{$t("searchDate.new_street.name","街道")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <rtcrm-lookup mode="dialog"
                                              entity-name="new_street"
                                              entityset-name="new_streets"
                                              name-field="new_name"
                                              key-field="new_streetid"
                                              :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                              :query-fields="['new_name']"
                                              :order-fields="[{name:'new_name', descending: true}]"
                                              v-model="blurform.new_street"
                                              :disabled="(blurform.new_county == null || blurform.new_county.id == null) && (blurform.new_street == null || blurform.new_street.id == null)"
                                              :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_county_id' operator='eq' value='${blurform.new_county.id}' />`">
                                </rtcrm-lookup>
                            </div>
                        </el-col> -->
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.zip_code","邮政编码")+"："'>
                                <!--<span style="color:red">*</span>-->
                                <span>{{$t("Workorder.xms.zip_code","邮政编码")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <input class="el-input__inner" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="zip_code">
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("orderDataDetail.address_c","具体地址")+"："'>
                                <span v-if="isAddressRequiredByServiceMode" style="color:red">*</span>
                                <span>{{$t("orderDataDetail.address_c","具体地址")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="10">
                            <div class="rt-crm-Edit-mult col-height">
                                <input class="el-input__inner" style="padding: 0 30px 0 15px;" :placeholder="`${$t('please.enter','请输入')}`" v-model="address">
                            </div>
                        </el-col>
                    </el-row>

                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.customertype","客户类型")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.xms.customertype","客户类型")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">

                            <div class="rt-crm-Edit flexClass col-height">
                                <el-select v-model="customertype" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="customer in customertypeOption" :key="customer.id" :label="customer.name" :value="customer.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-col>
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.new_send_questionnaire2","同意回访")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_send_questionnaire2","同意回访")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <!--<el-select v-model="new_send_questionnaire" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                <el-option v-for="item in questionnaireOptions" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>-->
                                <div style="padding-left:10px">
                                    <el-radio v-model="new_send_questionnaire" label="true">{{$t("common.isTrue","是")}}</el-radio>
                                    <el-radio v-model="new_send_questionnaire" label="false">{{$t("common.isFalse","否")}}</el-radio>
                                </div>
                            </div>
                        </el-col>
                    </el-row>

                </div>
            </div>

            <h2 class="XM-H2" v-show="!showBlurOrder&&isIncident">{{$t("orderDataDetail.Tab.Application","申请信息")}}</h2>
            <div class="rt-crm-List-SearchContainer2" v-show="!showBlurOrder&&isIncident">
                <div class="rt-crm-Section XM-info">
                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.new_servicemode","服务方式")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_servicemode","服务方式")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <el-select v-model="new_servicemode" @change="selectTypeDone" v-show="caseid==null" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="service in servicemodeOption" :key="service.id" :label="service.name" :value="service.id">
                                    </el-option>
                                </el-select>
                                <el-select v-model="new_servicemode" @change="selectTypeDone" v-show="caseid!=null" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="caseservicemode in caseservicemodeOption" :key="caseservicemode.id" :label="caseservicemode.name" :value="caseservicemode.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.new_type","服务类型")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_type","服务类型")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <el-select v-model="new_type" @change="selectTypeDone" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="itemtype in typeOption" :key="itemtype.id" :label="itemtype.name" :value="itemtype.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.serviceSelectInvoice","是否有发票")+"："'>
                                <span>{{$t("Workorder.serviceSelectInvoice","是否有发票")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <el-radio-group v-model="serviceSelectInvoice" :disabled="showOrder">
                                    <el-radio v-for="item in options"
                                              :key="item.value"
                                              :label="item.value">
                                        {{ item.label }}
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label xm-label-left col-height" :title='$t("Workorder.new_isprototype","是否样机")+"："'>
                                <span>{{$t("Workorder.new_isprototype","是否样机")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit col-height">
                                <el-radio-group v-model="blurform.new_ifinvoice">
                                    <el-radio v-for="item in options"
                                              :key="item.value"
                                              :label="item.value">
                                        {{ item.label }}
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                    </el-row>

                    <el-row :gutter="1">
                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.xms.new_srv_station","派单网点")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.xms.new_srv_station","派单网点")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="rt-crm-Edit flexClass col-height">
                                <input class="el-input__inner" style="padding: 0 30px 0 15px;" :placeholder="`${$t('Select_Please_Choose','请选择')}`" v-model="new_srv_station" @click="DispatchStation">
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="col-height">
                                <el-button @click="AutoDispatchStation" type="primary" size="small" style="margin-left: 3px;" v-cloak>{{$t("common.btn.autoDispatchStation","自动派单")}}</el-button>

                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" :title='$t("Workorder.new_returntype","退货类型")+"："'>
                                <span :style="{visibility:(new_type==3?'unset':'hidden')}">
                                    <span style="color:red">*</span>
                                    {{$t("Workorder.new_returntype","退货类型")+"："}}
                                </span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <el-select :style="{visibility:(new_type==3?'unset':'hidden')}" v-model="new_returntype" @change="selectTypeDone" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                    <el-option v-for="typeoption in returntypeOption" :key="typeoption.id" :label="typeoption.name" :value="typeoption.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label flexClass col-height" v-show="serviceSelectInvoice==1" :title='$t("Workorder.new_invoice_time","发票时间")+"："'>
                                <span style="color:red">*</span>
                                <span>{{$t("Workorder.new_invoice_time","发票时间")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <el-date-picker v-model="new_invoice_time"
                                                v-show="serviceSelectInvoice==1"
                                                style="width:100%"
                                                type="date"
                                                value-format="yyyy-MM-dd"
                                                :placeholder="`${$t('Select_Please_InvoiceTime','请输入发票时间')}`"
                                                :picker-options="pickerOptions">
                                </el-date-picker>
                            </div>
                        </el-col>

                        <el-col :span="2" class="XM-info-lab">
                            <div class="rt-crm-Label xm-label-left col-height" :title='$t("Workorder.new_invoice_num","发票号码")+"："'>
                                <span v-show="serviceSelectInvoice==1">{{$t("Workorder.new_invoice_num","发票号码")+"："}}</span>
                            </div>
                        </el-col>
                        <el-col :span="4">
                            <div class="rt-crm-Edit flexClass col-height">
                                <input class="el-input__inner" style="padding: 0 30px 0 15px;" v-show="serviceSelectInvoice==1" :placeholder="`${$t('Select_Please_InvoiceNum','请输入发票号码')}`" v-model="new_invoice_num">
                            </div>
                        </el-col>

                    </el-row>



                </div>
            </div>




            <div class="rt-crm-TabContent" v-show="!showBlurOrder&&isIncident">
                <br><br><br><br>
            </div>

            <!--模糊建单-->
            <div class="rt-crm-Form-ContentContainer" v-show="showBlurOrder"
                 v-loading="orderDetailLoading"
                 :element-loading-background="loadingBackground"
                 :spinner="loadingSpinner">
                <div class="rt-crm-InfoTab">
                    <h2 class="rt-crm-TabTitle">{{$t("blurform4959.goodsInfo","商品信息")}}</h2>
                    <div class="rt-crm-TabContent">
                        <div class="rt-crm-Section sectionClass">
                            <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span>
                                        <span>{{$t("imei.label.goodsName","商品名称")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="new_goodsfiles"
                                                      entityset-name="new_goodsfileses"
                                                      name-field="new_projectcode"
                                                      key-field="new_goodsfilesid"
                                                      :display-fields="[{name:'new_commoditycode', text:`${$t('lookup.code','编码')}`, width:'200px'},{name:'new_projectcode', text:`${$t('lookup.name','商品名称')}`, width:'200px'}]"
                                                      :query-fields="['new_projectcode','new_commoditycode']"
                                                      :order-fields="[{name:'new_projectcode', descending: true}]"
                                                      v-model="blurform.new_goodsfiles_id"
                                                      filter="<condition attribute='statuscode' operator='eq' value='1' />">
                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span>
                                        <span>{{$t("new_srv_workorder.FaultDescription","故障描述")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" placeholder="---" v-model="blurform.new_faultdescription">
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </div>
                <div class="rt-crm-InfoTab">
                    <h2 class="rt-crm-TabTitle">{{$t("new_srv_workorder.CustomerInformation","客户信息")}}</h2>
                    <div class="rt-crm-TabContent">
                        <div class="rt-crm-Section sectionClass">
                            <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span>{{$t("blurform4959.new_customerid","客户")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="account"
                                                      entityset-name="accounts"
                                                      name-field="name"
                                                      key-field="accountid"
                                                      @change="accountChange"
                                                      :display-fields="[{name:'accountnumber', text:`${$t('lookup.code','编码')}`, width:'200px'},{name:'name', text:`${$t('lookup.name','名称')}`, width:'230px'}]"
                                                      :query-fields="['name']"
                                                      :order-fields="[{name:'name', descending: true}]"
                                                      v-model="blurform.new_customerid"
                                                      filter="<condition attribute='statuscode' operator='eq' value='1' />">
                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span>
                                        <span>{{$t("blurform4959.customername","客户姓名")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" placeholder="---" v-model="blurform.customername">
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span>
                                        <span>{{$t("blurform4959.new_email","客户邮箱")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" placeholder="---" v-model="blurform.new_email">
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="0">
                             
                                    <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-if="isCustomerAddressRequiredByServiceMode" style="color:red">*</span>
                                      
                                 <span>{{$t("order.label.detail_address","详细地址" ) + "："}}</span>   
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <el-cascader v-model="countySelect" :suffixtext="suffixtext" :notselecttext="notselecttext" ref="cascader1" @visible-change="cascaderhandleVisibleChange" :clearable="true"
                                           :placeholder="$t('common.Pleaseenterprovincecity','请输入省份/城市')" :options="countyOptions" filterable style="width: 100%;"
                                            @change="cascaderhandleChange"  
                                            >
                                            <template slot-scope="{ node, data }">
                                                <span style="display:block" @click="countyonClick(node,$event,1)">{{ data.label }}</span>
                                            </template>
                                        </el-cascader>
                                        </div>
                                </el-col> 
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-if="isCustomerAddressRequiredByServiceMode" style="color:red">*</span>
                                        <span>{{$t("orderDataDetail.address_c","具体地址")+"："}} </span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" placeholder="---" v-model="blurform.customeraddress">
                                    </div>
                                </el-col>
                              
   <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span>{{$t("blurform4959.customerphone","客户电话")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" placeholder="---" v-model="blurform.customerphone">
                                    </div>
                                </el-col>
                            </el-row>
                            
                            <!-- <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-if="isProvinceRequiredByServiceMode" style="color:red">*</span>
                                        <span>{{$t("logisticsinfo.province","省份")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="new_province"
                                                      entityset-name="new_provinces"
                                                      name-field="new_name"
                                                      key-field="new_provinceid"
                                                      :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                                      :query-fields="['new_name']"
                                                      :order-fields="[{name:'new_name', descending: true}]"
                                                      v-model="blurform.new_province_id"
                                                      :disabled="(blurform.new_country_id == null ||blurform.new_country_id.id == null) && (blurform.new_province_id == null ||blurform.new_province_id.id == null)"
                                                      :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_country_id' operator='eq' value='${blurform.new_country_id.id}' />`">
                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-if="isCityRequiredByServiceMode" style="color:red">*</span>
                                        <span>{{$t("logisticsinfo.city","城市")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="new_city"
                                                      entityset-name="new_cities"
                                                      name-field="new_name"
                                                      key-field="new_cityid"
                                                      :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                                      :query-fields="['new_name']"
                                                      :order-fields="[{name:'new_name', descending: true}]"
                                                      v-model="blurform.new_city_id"
                                                      :disabled="(blurform.new_province_id == null ||blurform.new_province_id.id == null) && (blurform.new_city_id == null || blurform.new_city_id.id == null)"
                                                      :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_province_id' operator='eq' value='${blurform.new_province_id.id}' />`">
                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span>{{$t("logisticsinfo.county","区县")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="new_county"
                                                      entityset-name="new_counties"
                                                      name-field="new_name"
                                                      key-field="new_countyid"
                                                      :display-fields="[{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                                      :query-fields="['new_name']"
                                                      :order-fields="[{name:'new_name', descending: true}]"
                                                      v-model="blurform.new_county"
                                                      :disabled="(blurform.new_city_id == null || blurform.new_city_id.id == null) && (blurform.new_county == null || blurform.new_county.id == null)"
                                                      :filter="`<condition attribute='statuscode' operator='eq' value='1' /><condition attribute='new_city_id' operator='eq' value='${blurform.new_city_id.id}' />`">
                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                            </el-row> -->
                            <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span><span>{{$t("Workorder.new_servicemode","服务方式")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <el-select v-model="blurform.new_servicemode" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                            <el-option v-for="service in blurServicemodeOption" :key="service.id" :label="service.name" :value="service.id">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span style="color:red">*</span><span>{{$t("Workorder.new_type","服务类型")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <el-select v-model="blurform.new_type" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                            <el-option v-for="itemtype in blurTypeOption" :key="itemtype.id" :label="itemtype.name" :value="itemtype.id">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </el-col>
                                  <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-if="isCountryRequiredByServiceMode" style="color:red">*</span>
                                        <span>{{$t("order.label.country","国家/地区")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <rtcrm-lookup mode="dialog"
                                                      entity-name="new_country"
                                                      entityset-name="new_countries"
                                                      name-field="new_name"
                                                      key-field="new_countryid"
                                                      :query-fields="['new_name','new_code']"
                                                      :display-fields="[{name:'new_code',text:`${$t('lookup.code','编码')}`,width:'200px'},{name:'new_name',text:`${$t('lookup.name','名称')}`,width:'230px'}]"
                                                      :order-fields="[{name:'new_name', descending: true}]"
                                                      v-model="blurform.new_country_id"
                                                      filter="<condition attribute='statuscode' operator='eq' value='1' />"
                                                      :disabled="countryLocked">

                                        </rtcrm-lookup>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left" v-show="blurform.new_type==3">
                                        <span style="color:red">*</span>  <span>{{$t("Workorder.new_returntype","退货类型")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit flexClass">
                                        <el-select :style="{visibility:(blurform.new_type==3?'unset':'hidden')}" v-model="blurform.new_returntype" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                            <el-option v-for="typeoption in returntypeOption" :key="typeoption.id" :label="typeoption.name" :value="typeoption.id">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </el-col>

                            </el-row>
                            <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label flexClass xm-label-left">
                                        <span style="color:red">*</span>
                                        <span>{{$t("Workorder.new_send_questionnaire","同意售后回访")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit flexClass">
                                        <el-radio v-model="blurform.new_send_questionnaire" label="true">{{$t("common.true","是")}}</el-radio>
                                        <el-radio v-model="blurform.new_send_questionnaire" label="false">{{$t("common.false","否")}}</el-radio>
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="0">
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span>{{$t("Workorder.serviceSelectInvoice","是否有发票")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <el-select v-model="blurform.new_ifinvoice" :placeholder="`${$t('Select_Please_Choose','请选择')}`">
                                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span>{{$t("Workorder.new_invoice_num","发票号码")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <input class="rt-crm-Input borderhidden" type="text" :placeholder="`${$t('Select_Please_InvoiceNum','请输入发票号码')}`" v-model="blurform.new_invoice_num">
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="rt-crm-Label xm-label-left">
                                        <span v-show="blurform.new_ifinvoice==1" style="color:red">*</span><span>{{$t("Workorder.new_invoice_time","发票时间")}}</span>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="rt-crm-Edit">
                                        <el-date-picker v-model="blurform.new_invoice_time"
                                                        style="width:100%"
                                                        type="date"
                                                        value-format="yyyy-MM-dd"
                                                        :placeholder="`${$t('Select_Please_InvoiceTime','请输入发票时间')}`"
                                                        :picker-options="pickerOptions">
                                        </el-date-picker>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </div>
            </div>
            <div class="createBtn">
                <el-button v-show="showCreatebtn" @click="ConfirmRibbon" :disabled="isCreateBtnDisable" type="primary" size="small">{{$t("Select_Please_Create","创建")}}</el-button>
                <label class="checkOrderLabel" v-show="!showCreatebtn">{{checkResultTxt}}</label>
            </div>
        </div>
        <div style="height: 50px;"></div>
        <!-- :description="`${$t('common.noData','暂无数据')}`" -->
        <div v-else style="width: 100%;height: 100%;"
             v-loading="orderDetailLoading"
             :element-loading-background="loadingBackground"
             :spinner="loadingSpinner">
            <el-empty style="margin-top:100px" description=" ">
            </el-empty>
        </div>


    </div>

    <script>

        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                  suffixtext: "_000FXX",
                    notselecttext :$t("common.notSelect","暂不选择"), 
                   countyOptions:[] , 
                    countySelect:"",
                    /*
                    systemParams: {
                        Sys_Area: '' // 区县必填系统参数
                    },
                    isCountyRequired: false, // 区县是否必填
                    */
                    activeNames: ['1'],
                    createOrderModel:{
                        //returnImeiDatalist:[],
                        orderDataDetail:{},
                        serviceOrder:{},
                        incidentModel:{},
                        accountModel:{},
                        orderItemList:[],
                        srvworkerModel:""
                    },//创建Model
                    getWorkOrder:[],//查询服务单据信息
                    afterSelectTypeCheckOrder:null,//选择服务类型之后
                    serviceOrder:{},//创建数据对象
                    afterSelectType:false,//是否已经勾选完所有类型
                    isDeliveryInstallation: false,//是否送装一体
                    showAirconditioningType: false,//是否显示内外机
                    selectCount:0,//勾选次数
                    selectClickModel:{},//勾选判断对象
                    checkCustomModel:{
                        createaccount:0,
                        accountid:"",
                        accountname:""
                    },//返回sapID客户查询信息
                    getimeitime:[],//列表返回对象
                    isCreateBtnDisable:false,//创建按钮是否禁用
                    ifPreCheckDone:false,//数据是否检验完毕
                    requestId:"",
                    loadingSpinner:"el-icon-loading",
                    loadingBackground:"rgba(0, 0, 0, 0.1)",
                    // 默认显示第几页
                    oldImeiCurrentPage: 1,
                    // 总条数，根据接口获取数据长度(注意：这里不能为空)
                    oldImeiTotalCount: 1,
                    // 个数选择器（可修改）
                    oldImeiPageSizes: [5, 10, 15],
                    // 默认每页显示的条数（可修改）
                    oldImeiPageSize: 5,
                    timeoutId: "",
                    fileList: [],
                    //校验信息文本
                    checkResultTxt: "",
                    //创建按钮是否展示
                    showCreatebtn: false,
                    activeName: 'first',
                    servicemodeOption: [],//服务方式
                    caseservicemodeOption: [],//客服建单服务方式
                    typeOption: [],//服务类型
                    initTypeOption: [],//初始服务类型
                    returntypeOption: [],//退货方式
                    customer_name: "",
                    new_phone: "",
                    new_phone2: "",
                    email: "",
                    zip_code: "",
                    address: "",
                    textarea: "",
                    texterro: "",
                    new_srv_station: "",
                    new_station: {},
                    customertype: '',
                    customertypeOption: [],
                    isIncident: true,
                    options: [{
                        label: $t("common.true", "是"),
                        value: 1
                    },
                    {
                        label: $t("common.false", "否"),
                        value: 2
                        }],
                    questionnaireOptions: [{
                        label: $t("common.true", "是"),
                        value: "true"
                    },
                    {
                        label: $t("common.false", "否"),
                        value: "false"
                    }],
                    //IMei维保信息
                    iMeiLoading: false,
                    //可用的权益信息
                    profilerightsLoading: false,
                    //已参与的服务活动
                    orderActivityLoading: false,
                    //可参与的服务活动
                    serviceActionLoading: false,
                    //工单详情loading
                    orderDetailLoading: false,
                    //工单明细loading
                    orderItemLoading: false,
                    //历史服务单loading
                    oldImeiLoading: false,
                    num1: 0,//数据总条数
                    suppliername: '',
                    isNoOrderSearch: false,//是否非订单查询
                    new_invoice_num: '',
                    new_invoice_time: '',
                    stations: [],//OC订单数据
                    orderItemList: [],
                    valorderItemList: [],
                    searchOldAndNewImeiList: [],//历史服务记录
                    pageSearchOldAndNewImeiList: [],//历史服务记录-分页
                    serviceSelectInvoice: 2,//是否有发票（下拉）
                    new_isreadnum: 2,//是否支持读取串号（下拉）
                    orderDataDetail: [],//订单详情
                    new_type: '',//服务类型
                    new_servicemode: '',//服务方式
                    new_returntype: '',//退货类型
                    new_isprototype: '',//是否样机
                    isprototypeList: [],//是否样机
                    new_send_questionnaire: '',//同意售后回访
                    new_expvisitdate: '',//期望上门时间--本地时间
                    activities: [],//可参与的服务活动
                    //IMEI维保
                    ImeiDataList: [],
                    maintenances: [],//IMEI维保信息
                    profilerights: [],//权益信息
                    activeservicerecords: [],//主动服务登记活动
                    IMEIServiceReturnData: [],//imei服务接口
                    ActiveReturnData: [],//imei激活服务接口
                    OrderActivity: [],//已参与的服务活动
                    //案例转入数据
                    caseid: '',//案例id
                    pleaseuploadfile: '',
                    showOrderContent: false,
                    factory: '',
                    workOrderSearchRetun: {},
                    showImei: true,//imei、sn、fsn查询情况显示
                    unchecked: false,
                    showOrder: false,//无串号建单情况下显示
                    showNumber: false,//订单号查询情况显示
                    new_activeservicerecordid: '',//主档服务登记勾选id
                    searchDate: {
                        new_city: {},
                        new_country: {},
                        new_province: {},
                        new_region: {},
                        new_county: {}
                    },
                    isSearch: false,
                    //时间选择
                    pickerOptions: {
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    disChecked: false,
                    //模糊建单Form
                    blurTypeOption: [],//服务类型
                    blurServicemodeOption:[],//服务方式
                    showBlurOrder: false,//模糊建单显隐
                    testid: '',//测试用
                    accountTypeDict: {// 客户上的客户类型 映射到工单上客户类型
                        1: "2",//终端用户
                        2: "3",//经销商
                        3: "4",//零售商
                        4: "5",//KA客户
                        6: "6",//运营商
                        7: "1"//其他
                    },
                    blurform: {
                        new_goodsfiles_id: "",//商品名称
                        new_faultdescription: "",//描述
                        new_customerid: {},//客户
                        customername: "",//客户姓名
                        new_email: "",//客户邮箱
                        customerphone: "",//客户电话
                        customeraddress: "",//客户地址
                        new_street: {},//街道
                        new_county: {},//区县
                        new_city_id: {},//城市
                        new_province_id: {},//省份
                        new_country_id: {},//国家/地区
                        new_servicemode: "",//服务方式
                        new_type: "",//服务类型
                        new_returntype: "",//退货方式
                        new_ifinvoice: "",//是否有发票
                        new_invoice_num: "",//发票号
                        new_invoice_time: "",//发票时间
                        new_send_questionnaire: "",//满意度回访
                        caseid: "" //案例ID
                    },
                    countryLocked: true,
                }
            },
            computed: {
                boxBottom() {
                    return {
                        'margin-bottom': 25 + this.fileList.length * 25 + "px"
                    }
                },
                //地址信息的必填红*展示
                isCountryRequiredByServiceMode() {
                    // 1=到店，2=上门，3=寄修
                    let servicemode = this.showBlurOrder ? this.blurform.new_servicemode : this.new_servicemode;
                    return servicemode == 1 || servicemode == 2 || servicemode == 3;
                },
                isProvinceRequiredByServiceMode() {
                    let servicemode = this.showBlurOrder ? this.blurform.new_servicemode : this.new_servicemode;
                    return servicemode == 3 || servicemode == 2;
                },
                isCityRequiredByServiceMode() {
                    let servicemode = this.showBlurOrder ? this.blurform.new_servicemode : this.new_servicemode;
                    return servicemode == 3 || servicemode == 2;
                },
                isAddressRequiredByServiceMode() {
                    // 非串号工单模糊创建页面：具体地址必填
                    return !this.showBlurOrder && (this.new_servicemode == 3 || this.new_servicemode == 2);
                },
                isCustomerAddressRequiredByServiceMode() {
                    // 串号工单模糊创建页面：客户地址必填
                    return this.showBlurOrder && (this.new_servicemode == 3 || this.new_servicemode == 2);
                }
            },
            mounted: function () { 
            const  provincecityList  =    [$t("order.label.province","省份"),
                        $t("logisticsinfo.city","城市"),
                        $t("logisticsinfo.county","区县"),
                        $t("searchDate.new_street.name","街道")];
                        let cssStyle =[];
                        provincecityList.forEach((item, index) => {
                            cssStyle.push(`.el-cascader-menu:nth-child(${index + 1})::before { content: "${item}"; }`);
                        });
                   const style = document.createElement('style');
    style.textContent =  cssStyle.join(""); 
    document.head.appendChild(style);  
                parent.document.title = this.$t("workorder.workorderAddNewPage", "服务单无订单新建页面");
                this._loadData();
            },
            watch: {
                serviceSelectInvoice(oldValue) {
                    if (oldValue != 1) {
                        this.dunscode = "";
                        this.pancode = "";
                    }
                }
            },
            methods: {
                activeNamesChange(){
                    //此方法为激活页签的事件，后续逻辑可以写在此处
                },
                dateTime(time) {
                    return rtcrm.formatDate(new Date(time * 1000), "yyyy-MM-dd hh:mm:ss")
                },
                GetPickList() {
                    try {
                        let that = this;
                        XM.ActionAsync('new_ServiceOrderQueryAction', {
                            inputPara: JSON.stringify({}),
                            funcType: "serviceOrderPickList",
                            tempPara1: this.requestId
                        }).then((res) => {
                            try {
                                var result = JSON.parse(res.output);
                                if (result.code == 200) {
                                    var data = result.data;
                                    console.log("服务方式、服务类型:", res);
                                    this.servicemodeOption = data.wayList;//服务方式
                                    this.typeOption = data.srctypeList;//服务类型
                                    this.returntypeOption = data.returntypeList.filter(x => x.id != 3);//退货方式 排除退货方式3 维修特批退货
                                    this.isprototypeList = data.isprototypeList;//是否样机
                                    var new_isprototypeList = this.isprototypeList.filter(x => x.id == 1);
                                    this.new_isprototype = new_isprototypeList[0].id;
                                    this.caseid = rtcrm.getWebResourceParam("incidentid");
                                    this.initTypeOption = data.srctypeList;//服务类型
                                    var isIncidentWorker = data.isIncidentWorker;
                                    this.customertypeOption = Object.assign([], data.customertypeList);//用户类型

                                    if (this.caseid) {
                                        this.caseservicemodeOption = data.wayList.filter(x => x.id != 4);//客服建单服务类型
                                        this.blurServicemodeOption = data.wayList.filter(x => x.id != 4);//客服建单服务类型
                                        this.blurTypeOption = data.srctypeList;//服务类型
                                        var casesource = rtcrm.getWebResourceParam("new_casesource");//案例对应的来源字段
                                        if (casesource && casesource == 7) {
                                            this.isDeliveryInstallation = true;//来源为送装一体
                                            this.new_send_questionnaire = "true";//同意售后回访
                                        }
                                    } else {
                                        this.isIncident = isIncidentWorker;
                                    }
                                    //新增判断，如果服务方式只有一个可选项，则默认赋值
                                    if (this.caseid == null && this.servicemodeOption != null && this.servicemodeOption.length == 1) {
                                        this.new_servicemode = this.servicemodeOption[0].id;
                                    }
                                    if (this.caseid != null && this.caseservicemodeOption != null
                                        && this.caseservicemodeOption.length == 1) {
                                        this.new_servicemode = this.caseservicemodeOption[0].id;
                                    }
                                    //新增判断，如果服务类型只有一个可选项，则默认赋值
                                    if (this.typeOption != null && this.typeOption.length == 1) {
                                        this.new_type = this.typeOption[0].id;
                                    }

                                    if (this.caseid) {
                                        let value = parent.fieldGetAttribute('new_servicemode')?.getValue();
                                        this.new_servicemode = value != null ? value + '' : '';
                                    }
                                } else if (result.msg) {
                                    XM.openAlertDialog(result.msg);
                                }
                            } catch (e) { }
                        }).catch((err) => {
                            XM.openAlertDialog(err);
                        });
                    }
                    catch (e) {
                        Xrm.Utility.alertDialog(e);
                    }
                },
                cascaderhandleChange(value) {
                    debugger
                    //如果是清除所有的数据
                    if(value.length ==  0){
                        this.blurform.new_province_id ={};
                         this.blurform.new_city_id ={};
                         this.blurform.new_county ={};
                         this.blurform.new_street ={};
                         this.countySelect ="";
                    }
                    else{
                         //先找出省份
                        var new_province= this.countyOptions.find(v=>v.value == value[0]); 
                        this.blurform.new_province_id  ={id  : new_province.value,name:new_province.label};
                        //在找城市
                       var new_city_id =  new_province.children.find(v=>v.value == value[1]);
                       this.blurform.new_city_id  ={id  : new_city_id.value,name:new_city_id.label};
                        var new_county ;
                        //县是否为暂不选择
                        if(value[2].endsWith(this.suffixtext))
                        {
                            this.blurform.new_county ={};
                        }
                        else
                        {
                            new_county =  new_city_id.children.find(v=>v.value == value[2]);
                            this.blurform.new_county ={id  : new_county.value,name:new_county.label};
                             
                        }
                        //街道
                        if(value[3].endsWith(this.suffixtext))
                        {
                            this.blurform.new_street ={};
                        }
                        else
                        {
                            var new_street = new_county.children.find(v=>v.value == value[3]);
                             this.blurform.new_street={id  : new_street.value,name:new_street.label};
                        } 
                       
                        
                    }
                 
                     
                },
            countyonClick(node,t,index) { 
    //县选择了暂不选择则隐藏选择面板，直接定位下级      
 if(node.level ==3 && node.value.endsWith(this.suffixtext)) {  
     this.countySelect  = node.value + this.suffixtext
     this.$refs["cascader" + index].dropDownVisible = false;
    var selectData =[...node.path,this.countySelect]; 
    this.cascaderhandleChange(selectData);
 } 
    },
                //tab切换
                handleClick: function () {
                    //切换tab的时候，如果是禁用，恢复为正常状态
                    if (this.isCreateBtnDisable)
                        this.isCreateBtnDisable = false;
                    if (this.activeName == "second") {
                        this.new_invoice_time = '';
                        this.checkResultTxt = "";
                        //显示创建按钮
                        this.showCreatebtn = true;
                        this.showOrder = true;
                        this.showOrderContent = true;
                        this.serviceSelectInvoice = 1;
                        this.showBlurOrder = false;
                        this.countryLocked = false;
                        this.new_servicemode = ''; 
                        if (this.isIncident) {
                            //案例地址信息填充
                            this.blurform["new_province_id"]= {id : parent.fieldGetAttribute("new_province_id")?.getValue()};
                            this.blurform["new_city_id"]={id : parent.fieldGetAttribute("new_city_id")?.getValue()};
                            this.blurform["new_county_id"]= {id : parent.fieldGetAttribute("new_county_id")?.getValue()};
                            this.blurform["new_county"]= {id : parent.fieldGetAttribute("new_county_id")?.getValue()};
                            this.blurform["new_street"]={id:parent.fieldGetAttribute("new_street_id")?.getValue()};
                            this.countySelect ="";
                            this.initBlurForm();
                        }
                    }
                    if (this.activeName == "first") {
                        if (this.isIncident) {
                            this.countryLocked = true;
                        }
                        this.showOrderContent = false;
                        this.showOrder = false;
                        this.serviceSelectInvoice = 2;
                        this.showBlurOrder = false;
                    }
                    if (this.activeName == "third") {
                        if (this.isIncident) {
                            this.countryLocked = true;
                        }
                        this.blurform.new_servicemode = '';
                        this.showOrderContent = true;
                        this.showBlurOrder = true;
                        this.showCreatebtn = true;
                        if (this.caseid) {
                            this.showOrder = false;
                            this.initBlurForm();
                        }
                    }
                    this.suppliername = '';
                    this.new_station = null;
                    this.new_srv_station  = null;
                    this.activities = [];
                    this.maintenances = [];
                    this.new_type = "";
                    this.new_servicemode = "";
                    //新增判断，如果服务方式只有一个可选项，则默认赋值
                    if (this.caseid == null && this.servicemodeOption != null && this.servicemodeOption.length == 1) {
                        this.new_servicemode = this.servicemodeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    if (this.caseid != null && this.caseservicemodeOption != null && this.caseservicemodeOption.length == 1) {
                        this.new_servicemode = this.caseservicemodeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    this.customertypeOption = Object.assign([], this.customertypeOption);//用户类型
                    this.typeOption = Object.assign([], this.initTypeOption);
                    //新增判断，如果服务类型只有一个可选项，则默认赋值
                    if (this.typeOption != null && this.typeOption.length == 1) {
                        this.new_type = this.typeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    if (this.caseid) {
                        let value = parent.fieldGetAttribute('new_servicemode')?.getValue();
                        this.new_servicemode = value != null ? value + '' : '';
                    }
                    var new_isprototypeList = this.isprototypeList.filter(x => x.id == 1);
                    this.new_isprototype = new_isprototypeList[0].id;
                    //没国家放开国家锁
                    if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                        this.countryLocked = false;
                    }
                },
                initBlurForm() {
                    var self = this;
                    var fields = [{//客户id
                        from: 'customerid', to: 'new_customerid', type: 'lookup',
                    }, {//联系电话
                        from: 'new_phone', to: 'customerphone', type: 'string',
                    }, {//电子邮件地址
                        from: 'emailaddress', to: 'new_email', type: 'string',
                    }, {//详细地址
                        from: 'new_custaddress', to: 'customeraddress', type: 'string',
                    }, {//国家
                        from: 'new_country_id', to: 'new_country_id', type: 'lookup',
                    }, {//所属区县
                        from: 'new_county_id', to: 'new_county', type: 'lookup',
                    }, {//所属城市
                        from: 'new_city_id', to: 'new_city_id', type: 'lookup',
                    }, {//所属街道
                        from: 'new_street_id', to: 'new_street', type: 'lookup',
                    },{//所属省份
                        from: 'new_province_id', to: 'new_province_id', type: 'lookup',
                    }, {//服务方式
                        from: 'new_servicemode', to: 'new_servicemode', type: 'optionset',
                    }];

                    fields.forEach(function (field) {
                        switch (field.type) {
                            case 'string':
                                self.blurform[field.to] = parent.fieldGetAttribute(field.from)?.getValue() || '';
                                break;
                            case 'optionset':
                                let val = parent.fieldGetAttribute(field.from)?.getValue();
                                self.blurform[field.to] = (val == null || val === undefined) ? '' : (val + '');
                                break;
                            case 'lookup':
                                var refVal = parent.fieldGetAttribute(field.from)?.getValue();
                                if (refVal && refVal.length) {
                                    self.blurform[field.to] = {
                                        id: refVal[0].id,
                                        name: refVal[0].name,
                                    }
                                }
                                break;
                            default:
                                self.blurform[field.to] = parent.fieldGetAttribute(field.from)?.getValue();
                                break;
                        }
                    });
                     this.initCascaderCounty()
                },
                //table订单明细选中
                async handleSelectionChange(val) {
                    //当勾选数据的时候预查询
                    if (val.length > this.valorderItemList.length) {
                        this.selectCount++;
                        this.valorderItemList = val;
                        // var resp = await this.PreCreateDataByHandleSelect();
                        // //全部执行成功
                        // if (resp != null && resp["returnImeiDatalist"].length == this.valorderItemList.length) {
                        //     this.selectClickModel["selectImeiListResult" + this.selectCount] = true;
                        // }
                        //当时订单查询的时候，不自动勾选数据校验处理
                        if (!this.showNumber || (this.showNumber && this.stations.length == 1)) {
                            //创建数据预处理
                            await this.PreCreateDataByHandleSelect();
                        }
                    }
                    else {
                        this.valorderItemList = val;
                    }
                },
                //选择服务类型后的数据校验
                async selectTypeDone()
                {
                    try {
                        var ifcheck = false;
                        // 服务类型变更 清空派单网点
                        this.new_station = null;
                        this.new_srv_station = null;
                        if (this.new_servicemode != null && this.new_servicemode != "") {
                            if (this.new_type != null && this.new_type != "") {
                                if (this.new_type == 3) {
                                    if (this.new_returntype != null && this.new_returntype != "") {
                                        ifcheck = true;
                                    }
                                }
                                else {
                                    ifcheck = true;
                                }
                            }
                        }
                        if (ifcheck) {
                            //数据校验
                            this.afterSelectType = true;
                            await this.getOrderInfoBySelectType();
                        }
                        else{
                            this.afterSelectType = false;
                        }
                    } catch (e) {
                        this.ifCheckQueryDone = true;//数据校验完毕
                        this.selectClickModel["checkOrderError" + this.selectCount] = "selectTypeDone Check Error," + e;
                    }
                },
                //当选择服务类型后的数据加载
                async getOrderInfoBySelectType()
                {
                    if (!this.showOrder) {
                        //订单查询的时候，需要查询下勾选信息
                        if (this.showNumber && this.orderDataDetail["order_id"] != "") {
                            //查询勾选信息
                            await this.PreCreateDataByHandleSelect();
                        }
                        //等待勾选明细的数据校验
                        if (this.valorderItemList.length > 0
                            && this.selectClickModel != null
                            && this.selectClickModel["selectImeiListResult" + this.selectCount] != undefined) {
                            let selectTypeTimer = setInterval(async () => {
                                if (this.selectClickModel["selectImeiListResult" + this.selectCount]) {
                                    result = true;
                                    console.log('checkIfPreCheckDone');
                                    clearInterval(selectTypeTimer);
                                    selectTypeTimer = null;
                                    if (this.selectClickModel["checkOrderError" + this.selectCount] != undefined
                                        && this.selectClickModel["checkOrderError" + this.selectCount] != null
                                        && this.selectClickModel["checkOrderError" + this.selectCount] != "") {
                                        this.ifCheckQueryDone = true;//数据校验完毕
                                        selectTypeTimer = null;
                                        return;
                                    }
                                    var checkOrder = await this.checkOrderByCreate();
                                    if (checkOrder.code != 200) {
                                        if (this.selectClickModel["checkOrderError" + this.selectCount] == undefined
                                            || this.selectClickModel["checkOrderError" + this.selectCount] == null
                                            || this.selectClickModel["checkOrderError" + this.selectCount] == "") {
                                            this.selectClickModel["checkOrderError" + this.selectCount] = checkOrder.msg;
                                        }
                                    }
                                    else {
                                        var checkOrderRlt = checkOrder.data;
                                        this.selectClickModel["checkOrderError" + this.selectCount] = "";
                                        this.afterSelectTypeCheckOrder = checkOrderRlt;
                                    }
                                    this.ifCheckQueryDone = true;//数据校验完毕
                                }
                                else {
                                    console.log('ifPreCheckDone checking...');
                                }
                            }, 500);
                            // 3秒后停止执行
                            setTimeout(() => {
                                clearInterval(selectTypeTimer);
                                selectTypeTimer = null;
                                //当选择服务类型后的数据加载异常埋点
                                // var aiLogDataModel = new top.AILogging.aiLogDataModelV2(this.requestId, "ServiceOrder", "ServiceOrder", "getOrderInfoBySelectType",
                                //     0, 'getOrderInfoBySelectType_TimeOut', 0, new Date().getUTCDate());
                                // this.writeLog(new Date(), null, aiLogDataModel, 2);
                            }, 40000);
                        }else if(this.orderItemList.length == 0 && this.orderDataDetail["goodid"] != "")
                        {
                            var checkOrder = await this.checkOrderByCreate();
                            if (checkOrder.code != 200) {
                                if (this.selectClickModel["checkOrderError" + this.selectCount] == undefined
                                    || this.selectClickModel["checkOrderError" + this.selectCount] == null
                                    || this.selectClickModel["checkOrderError" + this.selectCount] == "") {
                                    this.selectClickModel["checkOrderError" + this.selectCount] = checkOrder.msg;
                                }
                            }
                            else {
                                var checkOrderRlt = checkOrder.data;
                                this.selectClickModel["checkOrderError" + this.selectCount] = "";
                                this.afterSelectTypeCheckOrder = checkOrderRlt;
                            }
                            //无订单明细时候，检查完毕
                            this.ifCheckQueryDone = true;//数据校验完毕
                        }
                    }
                },
                //勾选明细后创建数据准备
                async PreCreateDataByHandleSelect()
                {
                    var imeiRltArr = [];
                    try {
                        this.selectClickModel["selectImeiListResult" + this.selectCount] = false;
                        //检查数据开始
                        this.ifCheckQueryDone = false;
                        //是否订单号查询
                        if (this.showNumber && this.orderDataDetail["order_id"] != "") {
                            //勾选明细需要查询Imei信息
                            imeiRltArr = await this.getImeiMaintenances(this.queryWorkOrder, this.orderDataDetail, this.valorderItemList, this.getimeitime);
                        }
                        //数据校验
                        //this.afterSelectType = false;
                        //未选择服务类型前的校验
                        var checkOrder = await this.checkOrderByCreate();
                        if (checkOrder.code != 200) {
                            //勾选错误文本
                            this.selectClickModel["checkOrderError" + this.selectCount] = checkOrder.msg;
                            this.ifCheckQueryDone = true;
                            if (this.afterSelectType) {
                                //包含选择type的错误文本
                                this.selectClickModel["checkOrderError" + this.selectCount] = checkOrder.msg;
                                this.ifCheckQueryDone = true;
                            }
                            return;
                        }
                        else {
                            this.selectClickModel["checkOrderError" + this.selectCount] = "";
                            if (this.afterSelectType) {
                                this.afterSelectTypeCheckOrder = checkOrder.data;
                            }
                        }
                        //获取客户是否创建
                        var checkCustom = await this.checkCustomIfNeedCreate();
                        //案例
                        if (this.caseid != null) {
                            this.createOrderModel["incidentModel"] = this.queryWorkOrder["incdentModel"];
                        }
                        this.createOrderModel["SearchDate"] = this.searchDate;
                        //网点信息
                        this.createOrderModel["srvworkerModel"] = this.queryWorkOrder["srvworkerModel"];
                        //勾选明细
                        this.createOrderModel["orderItemList"] = this.valorderItemList;
                        //客户
                        if (checkCustom != null) {
                            var accountModel = {};
                            accountModel["createaccount"] = this.checkCustomModel["createaccount"];
                            accountModel["accountid"] = this.checkCustomModel["accountid"];
                            accountModel["accountname"] = this.checkCustomModel["accountname"];
                            this.createOrderModel["serviceOrder"]["customerid"] = this.checkCustomModel["accountid"];
                            if (this.createOrderModel["incdentModel"] != null) {
                                accountModel["emailaddress1"] = this.createOrderModel["incdentModel"]["emailaddress"];
                                //地址
                                if (this.createOrderModel["incdentModel"]["new_custaddress"] != null) {
                                    accountModel["new_address"] = this.createOrderModel["incdentModel"]["new_custaddress"];
                                    this.createOrderModel["serviceOrder"]["address"] = this.createOrderModel["incdentModel"]["new_custaddress"];
                                }
                                //电话
                                if (this.createOrderModel["incdentModel"]["new_phone"] != null) {
                                    accountModel["telephone1"] = this.createOrderModel["incdentModel"]["new_phone"];
                                }
                            }
                            else {
                                accountModel["new_address"] = this.orderDataDetail["address_c"];
                                accountModel["telephone1"] = this.orderDataDetail["tel_c"];
                                this.createOrderModel["serviceOrder"]["address"] = this.orderDataDetail["address_c"];
                            }
                            if (this.searchDate != null) {
                                if (this.searchDate["new_region"] != null)
                                    accountModel["new_region_id"] = this.searchDate["new_region"]["id"];
                                if (this.searchDate["new_province"] != null)
                                    accountModel["new_province_id"] = this.searchDate["new_province"]["id"];
                                if (this.searchDate["new_country"] != null)
                                    accountModel["new_country"] = this.searchDate["new_country"]["id"];
                                if (this.searchDate["new_city"] != null)
                                    accountModel["new_city"] = this.searchDate["new_city"]["id"];
                                if (this.searchDate["new_county"] != null)
                                    accountModel["new_county"] = this.searchDate["new_county"]["id"];
                            }
                            accountModel["name"] = this.orderDataDetail["consignee_c"];
                            this.createOrderModel["accountModel"] = accountModel;
                        }
                        //Imei信息
                        this.createOrderModel["returnImeiDataList"] = this.maintenances;
                        if (this.caseid != null) {
                            this.createOrderModel["serviceOrder"]["incidentId"] = this.caseid;
                            this.createOrderModel["incidentModel"]["incidentId"] = this.caseid;
                            if (this.createOrderModel["incidentModel"]["regionid"] != null)
                                this.createOrderModel["serviceOrder"]["region_id"] = this.createOrderModel["incidentModel"]["regionid"];
                            if (this.createOrderModel["incidentModel"]["countryid"] != null)
                                this.createOrderModel["serviceOrder"]["country_id"] = this.createOrderModel["incidentModel"]["countryid"];
                            if (this.createOrderModel["incidentModel"]["provinceid"] != null)
                                this.createOrderModel["serviceOrder"]["province_id"] = this.createOrderModel["incidentModel"]["provinceid"];
                            if (this.createOrderModel["incidentModel"]["cityid"] != null)
                                this.createOrderModel["serviceOrder"]["city_id"] = this.createOrderModel["incidentModel"]["cityid"];
                            if (this.createOrderModel["incidentModel"]["countyid"] != null)
                                this.createOrderModel["serviceOrder"]["county_id"] = this.createOrderModel["incidentModel"]["countyid"];
                            if (this.createOrderModel["incidentModel"]["new_miliao"] != null)
                                this.createOrderModel["serviceOrder"]["miliao"] = this.createOrderModel["incidentModel"]["new_miliao"];
                            this.createOrderModel["serviceOrder"]["origin"] = 30;
                        }

                        if ((this.createOrderModel["serviceOrder"]["region_id"] == null || this.createOrderModel["serviceOrder"]["region_id"] == "")
                            && this.searchDate != null && this.searchDate["new_region"] != null) {
                            this.createOrderModel["serviceOrder"]["region_id"] = this.createOrderModel["SearchDate"]["new_region"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["country_id"] == null || this.createOrderModel["serviceOrder"]["country_id"] == "")
                            && this.searchDate != null && this.searchDate["new_country"] != null) {
                            this.createOrderModel["serviceOrder"]["country_id"] = this.createOrderModel["SearchDate"]["new_country"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["province_id"] == null || this.createOrderModel["serviceOrder"]["province_id"] == "")
                            && this.searchDate != null && this.searchDate["new_province"] != null) {
                            this.createOrderModel["serviceOrder"]["province_id"] = this.createOrderModel["SearchDate"]["new_province"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["city_id"] == null || this.createOrderModel["serviceOrder"]["city_id"] == "")
                            && this.searchDate != null && this.searchDate["new_city"] != null) {
                            this.createOrderModel["serviceOrder"]["city_id"] = this.createOrderModel["SearchDate"]["new_city"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["county_id"] == null || this.createOrderModel["serviceOrder"]["county_id"] == "")
                            && this.searchDate != null && this.searchDate["new_county"] != null) {
                            this.createOrderModel["serviceOrder"]["county_id"] = this.createOrderModel["SearchDate"]["new_county"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["street_id"] == null || this.createOrderModel["serviceOrder"]["street_id"] == "")
                            && this.searchDate != null && this.searchDate["new_street"] != null) {
                            this.createOrderModel["serviceOrder"]["street_id"] = this.createOrderModel["SearchDate"]["new_street"]["id"];
                        }
                        if ((this.createOrderModel["serviceOrder"]["miliao"] == null || this.createOrderModel["serviceOrder"]["miliao"] == "")) {
                            this.createOrderModel["serviceOrder"]["miliao"] = this.orderDataDetail["user_id"];
                        }
                        this.createOrderModel["serviceOrder"]["origin"] = 10;
                        this.createOrderModel["serviceOrder"]["occurrency"] = this.orderDataDetail["currencyId"];
                        this.createOrderModel["serviceOrder"]["deliverylogisticscompany"] = this.orderDataDetail["config_type"];
                        this.createOrderModel["serviceOrder"]["zip_code"] = this.orderDataDetail["zipcode"];
                        this.createOrderModel["serviceOrder"]["sales_type"] = this.orderDataDetail["sales_type"];
                        this.createOrderModel["orderDataDetail"] = this.orderDataDetail;
                        //判断是否已经选择服务类型等信息,勾选明细后校验成功
                        if (this.afterSelectType) {
                            this.selectClickModel["checkOrderError" + this.selectCount] = "";
                            this.ifCheckQueryDone = true;
                        }
                        this.selectClickModel["selectImeiListResult" + this.selectCount] = true;
                        return imeiRltArr;
                    } catch (e) {
                        this.ifCheckQueryDone = true;
                        this.selectClickModel["checkOrderError" + this.selectCount] = "PreCreateDataByHandleSelect Check Error," + e;
                        //勾选数据异常埋点
                        // var aiLogDataModel = new top.AILogging.aiLogDataModelV2(this.requestId, "ServiceOrder", "ServiceOrder", "PreCreateDataByHandleSelect",
                        //     0, 'PreCreateDataByHandleSelect_error' + e, 0, new Date().getUTCDate());
                        // this.writeLog(new Date(), null, aiLogDataModel, 2);
                        return imeiRltArr;
                    }
                },
                //建单校验
                async checkOrderByCreate()
                {
                    var result = null;
                    try {
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify({
                                workOrderSearchRetun:this.queryWorkOrder["workOrderSearchRetun"],
                                orderItemList:this.valorderItemList,
                                allOrderItemList:this.orderItemList,
                                getImeiTimeList:(this.getimeitime == null || this.getimeitime.length < 1) ? [] : [this.getimeitime],
                                returnImeiDataList:this.maintenances,
                                srvworkerModel:this.queryWorkOrder["srvworkerModel"],
                                caseid:this.caseid,
                                uniquecode:this.maintenances.length > 0 ? this.maintenances[0]["new_sn"] : "",
                                showNumber:this.showNumber,
                                new_returntype:this.new_returntype,
                                new_type:this.new_type,
                                new_servicemode:this.new_servicemode,
                                afterSelectType:this.afterSelectType,
                                isDeliveryInstallation: this.isDeliveryInstallation,
                                IMEIServiceReturnData: (this.IMEIServiceReturnData == null || this.IMEIServiceReturnData.length < 1) ? [] : [this.IMEIServiceReturnData],//imei服务接口
                                ActiveReturnData: (this.ActiveReturnData == null || this.ActiveReturnData.length < 1) ? [] : [this.ActiveReturnData]//imei激活服务接口
                            }),
                            funcType: "CheckOrderByCreate",
                            tempPara1:this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para);
                        var result = JSON.parse(res.output);
                        return result;
                    }
                    catch (e) {
                        Xrm.Utility.alertDialog('checkOrderByCreate:' + e);
                        //建单校验异常埋点
                        // var aiLogDataModel = new top.AILogging.aiLogDataModelV2(this.requestId, "ServiceOrder", "ServiceOrder", "checkOrderByCreate",
                        //     0, 'checkOrderByCreate_error' + e, 0, new Date().getUTCDate());
                        // this.writeLog(new Date(), null, aiLogDataModel, 2);
                    }
                },
                //table主动服务登记记录
                activeservicerecordChange(selection) {
                    if (selection.length > 1) {
                        let del_row = selection.shift();
                        console.log(del_row);
                        this.$nextTick(() => {
                            this.$refs.activeservicerecords.toggleRowSelection(del_row, false);
                        })
                    }
                    this.new_activeservicerecordid = selection.length > 0 ? selection[0].new_activeservicerecordid : "";
                },
                selectState(row, index) {
                    if (this.stations.length == 1) {
                        return false;
                    }
                    //是否为虚拟商品
                    if (row.hasOwnProperty('new_isVirtual')&&row.new_isVirtual) {
                        return false;
                    }
                    //送装一体可勾选
                    if (this.isDeliveryInstallation) {
                        return true;
                    }
                    //查询的是订单，并且满足条件的就禁用
                    if (this.disChecked && this.stations.length > 1) {
                        if (row.stat == 24 || row.status != 6000 || row.new_isVirtual) {
                            return false;
                        } else {
                            return true;
                        }
                    }
                },
                handleChange(file, fileList) {
                    const isLt2M = file.size / 1024 / 1024 < 10;
                    if (!isLt2M) {
                        rtcrm.alertDialog(this.$t("new_attachment.fileSizeCheck", "文件大小必须小于10MB！"));
                        fileList.splice(-1, 1);
                        return;
                    }
                    if (file.size < 1) {
                        rtcrm.alertDialog(this.$t("new_attachment.nodata", "文件内容不可以为空！"));
                        fileList.splice(-1, 1);
                        return;
                    }
                    // if (this.caseid) {
                    //     var fileType = rtcrm.getUrlParam("data");
                    // } else {
                    //     var fileType = "";
                    // }
                    var fileType = "";
                    var info = {
                        //new_entityid: entityId.replace('{', '').replace('}', ''),
                        new_entityname: "new_srv_workorder",
                        new_filetitle: "工单附件",
                        new_filename: file.name,
                        new_mimetype: file.raw.type,
                        new_filetype: fileType,
                        name: file.name
                    };
                    var reader = new FileReader();
                    reader.readAsDataURL(file.raw);
                    reader.onload = function (f) {
                        info.documentbody = f.target.result.substring(f.target.result.indexOf(",") + 1);
                    }
                    this.fileList.push(info);
                },
                beforeRemove(file, fileList) {
                    this.fileList.forEach((ele, index) => {
                        if (ele.uid == file.uid) {
                            this.fileList.splice(index, 1);
                            throw new Error("停止");
                        }
                    })
                },
                uploadEnclosure(entityId) {
                    this.fileList.map(item => item.new_entityid = entityId.replace('{', '').replace('}', ''));
                    rtcrm.invokeHiddenApi("new_comattachment", "Attachment/UploadFile", { "fileModels": this.fileList });
                },
                onFileChange: function (e) {
                    var files = document.getElementById("upload").files;
                    this.pleaseuploadfile = files[0].name;
                },
                //之前的单独上传文件方法
                uploadInvoice: function (entityId) {
                    var uploadFile = document.getElementById("upload");
                    //if (this.caseid) {
                    //    var fileType = rtcrm.getUrlParam("data");
                    //} else {
                    //    var fileType = "";
                    //}
                    var fileType = "";
                    var file = uploadFile.files[0];
                    var info = {
                        new_entityid: entityId.replace('{', '').replace('}', ''),
                        new_entityname: "new_srv_workorder",
                        new_filetitle: "工单附件",
                        new_filename: file.name,
                        new_mimetype: file.type,
                        new_filetype: fileType,
                    };
                    if (file.size / (1024 * 1024) > 10) {
                        Xrm.Utility.alertDialog(this.$t("new_attachment.fileSizeCheck", "文件大小必须小于10MB！"));
                        return;
                    }
                    var reader = new FileReader();
                    reader.onload = function (f) {
                        try {
                            if (f.target.result !== "data:") {
                                info.documentbody = f.target.result.substring(f.target.result.indexOf(",") + 1);
                            }
                            var fileModels = [];
                            fileModels.push(info)
                            rtcrm.invokeHiddenApi("new_comattachment", "Attachment/UploadFile", { "fileModels": fileModels });
                            Xrm.Utility.alertDialog($t("new_service_handing.uploadsuccess", "附件上传成功"));
                        } catch (e) {
                            Xrm.Utility.alertDialog($t("new_service_handing.uploaderror", "附件上传失败") + e);
                        }
                        vm.pleaseuploadfile = '';
                    }
                    reader.readAsDataURL(file);
                },
                //加载后台数据函数
                _loadData: function () {
                    //加载选项集数据
                    this.caseid = rtcrm.getWebResourceParam("incidentid");
                    this.GetPickList();
                    this.getBlurPickList();
                    //新增判断，如果服务方式只有一个可选项，则默认赋值
                    if (this.caseid == null && this.servicemodeOption != null && this.servicemodeOption.length == 1) {
                        this.new_servicemode = this.servicemodeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    if (this.caseid != null && this.caseservicemodeOption != null && this.caseservicemodeOption.length == 1) {
                        this.new_servicemode = this.caseservicemodeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    //新增判断，如果服务类型只有一个可选项，则默认赋值
                    if (this.typeOption != null && this.typeOption.length == 1) {
                        this.new_type = this.typeOption[0].id;
                        //手动触发change事件
                        this.selectTypeDone();
                    }
                    var new_imei = rtcrm.getWebResourceParam("new_imei");
                    var new_sn = rtcrm.getWebResourceParam("new_sn");
                    var new_miordercode = rtcrm.getWebResourceParam("new_miordercode");
                    if (this.caseid) {
                        try {
                            var caseQuery = "incidents(" + this.caseid + ")?$select=new_imei,new_sn,new_miordercode,new_casesource,emailaddress,new_phone, _customerid_value,new_probdescrib,new_servicemode,new_postal_code,new_custaddress,new_invoicenum,new_invoicetime,new_isinvoice,new_expvisitdate";
                            var caseEntity = rtcrm.retrieve(caseQuery, true);
                            
                            var casesource = caseEntity["new_casesource"];//案例对应的来源字段

                            if (casesource && casesource == 7) {
                                this.isDeliveryInstallation = true;//来源为送装一体
                                if(caseEntity.new_expvisitdate){
                                   this.new_expvisitdate = this.formatLocalTime(new Date(caseEntity.new_expvisitdate)); 
                                }
                            }
                            var new_miordercode = caseEntity["new_miordercode"];//案例对应的工单字段
                            if (new_miordercode) {
                                this.suppliername = new_miordercode;
                            }

                            var ishow = rtcrm.isRibbonEnabledAccordingToRules("createWorkOrderBtn");
                            if (ishow) {
                                this.showCreatebtn = true;
                            } else {
                                this.showCreatebtn = false;
                            }
                            this.initBlurForm();
                            this.new_phone = caseEntity["new_phone"];
                            this.texterro = caseEntity["new_probdescrib"];
                            this.zip_code = caseEntity["new_postal_code"];
                            this.address = caseEntity["new_custaddress"];
                            this.email = caseEntity["emailaddress"];
                            new_imei = caseEntity["new_imei"];
                            new_sn = caseEntity["new_sn"];
                            new_miordercode = caseEntity["new_miordercode"];
                            if (caseEntity._customerid_value) {
                                var accountEntity = rtcrm.retrieve("accounts(" + caseEntity._customerid_value + ")?$select=name,new_accounttype", true);
                                accountEntity.id = accountEntity.accountid;
                                this.blurform.new_customerid = accountEntity;
                                this.customer_name = this.blurform.new_customerid.name;
                                if (accountEntity.new_accounttype && accountEntity.new_accounttype != 5) {
                                    this.customertype = this.accountTypeDict[accountEntity.new_accounttype];
                                }
                            }
                        } catch (e) {
                            alert(e);
                            return;
                        }
                    }
                    if (new_sn) {
                        this.suppliername = new_sn;
                    } else if (new_imei) {
                        this.suppliername = new_imei;
                    } else if (new_miordercode) {
                        this.suppliername = new_miordercode;
                    }
                    if (rtcrm.getWebResourceParam("new_isinvoice") == "true") {
                        this.serviceSelectInvoice = 1;
                    } else {
                        this.serviceSelectInvoice = 2;
                    }
                    this.new_invoice_num = rtcrm.getWebResourceParam("new_invoicenum");
                    this.new_invoice_time = rtcrm.getWebResourceParam("new_invoicetime");
                    if (this.suppliername) {
                        this.reloadData();
                    }
                    //this.getRequiredCountry();
                },
                gridBodyScroll: function () {
                    this.$refs.gridHeader.scrollLeft = this.$refs.gridBody.scrollLeft;
                },
                gridRowMouseover: function (index) {
                    this.$refs.gridRow[index].style.width = this.$refs.gridRow[index].parentNode.scrollWidth + 'px';
                },
                isreadnumselect: function () {
                    if (this.new_isreadnum == 1) {
                        this.suppliername = '';
                    }
                },
                generateUUID() {
                    var d = new Date().getTime();
                    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                        var r = (d + Math.random() * 16) % 16 | 0;
                        d = Math.floor(d / 16);
                        return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
                    });
                    return uuid;
                },
                //清空数据
                clearData() {
                    this.showCreatebtn = false;
                    this.checkResultTxt = "";
                    this.orderDataDetail = null;
                    this.stations = [];//订单商品明细
                    this.valorderItemList = [];//勾选明细
                    this.orderItemList = [];
                    this.searchDate = {
                        new_city: {},
                        new_country: {},
                        new_province: {},
                        new_region: {},
                        new_county: {}
                    };
                    this.activities = [];//可参与的服务活动
                    this.maintenances = [];//IMEI维保信息
                    this.searchOldAndNewImeiList = [];//历史服务记录
                    this.profilerights = [];//可用的权益信息
                    this.IMEIServiceReturnData = [];//imei服务接口
                    this.ActiveReturnData = [];//imei激活服务接口
                    this.OrderActivity = [];//已参与的服务活动
                    this.showOrderContent = false;
                    this.$forceUpdate();
                },
                //查询按钮
                reloadData: async function () {
                    try {
                        //------------时间戳开始----------------//
                        var timeStart = new Date().getTime();
                        //------------时间戳----------------//
                        //串号的时候清空
                        if (!this.showOrder) {
                            //清空数据
                            this.clearData();
                        }

                        this.isSearch = true;
                        if (this.showOrder) {
                            if (!this.suppliername) {
                                rtcrm.alertDialog(this.$t("createworkorder.tab.pleaceinputgoodsid", "请输入商品编码！"));
                                return;
                            } else {
                                if (!(this.suppliername.length == 4 || this.suppliername.length == 5)) {
                                    rtcrm.alertDialog(this.$t("new_service_handing.goodsnum", "商品编码请输入4位或5位！"));
                                    return;
                                }
                            }
                        }

                        if (this.suppliername == '' || this.suppliername == null) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_SearchNonum", "请输入IMEI/SN/FSN/订单号"));
                            return;
                        }
                        else {
                            this.suppliername = this.suppliername.replace(/\s+/g, "");
                            if (this.showOrder) {
                                this.showNumber = false;
                                this.showImei = false;
                                this.selectOption();
                                return;
                            }
                            var prestring = this.suppliername.substring(0, 2);
                            if (this.suppliername.length == 18 && prestring == "SA") {
                                var poststring = this.suppliername.substring(2, 18);
                                var regex = /^\d{16}$/;
                                var reg = new RegExp(regex);
                                if (prestring != "SA" || !reg.test(poststring)) {
                                    rtcrm.alertDialog(this.$t("CreateWorkorder_Miorderid", "请输入正确的米家订单号！"));
                                    return;
                                }
                            } else {
                                //var regexp = /^([\d]{4,}\/([A-Z0-9]{8,14})$|^[\d]{12}|[0-9a-zA-Z]{15}|[0-9a-zA-Z]{16})$/;
                                //var regexp = /^([\d]{4,}\/([A-Z0-9]{8,14})$|^[0-9a-zA-Z]{15}|[0-9a-zA-Z]{16})$/;
                                //var re = new RegExp(regexp);
                                //if (!re.test(this.suppliername)) {
                                //    rtcrm.alertDialog(this.$t("", "请输入正确的IMEI/SN/FSN/订单号！"));
                                //    return;
                                //}
                            }
                            //rtcrm.layerLoading();
                            var regNumber = /^[0-9]*$/; //验证0-9的任意数字最少出现1次。
                            var regString = /[a-zA-Z]+/; //验证大小写26个字母任意字母最少出现1次。
                            //rtcrm.layerLoading();
                            var parameters = {};
                            //IMEI转sn
                            if (this.suppliername.length == 15 && regNumber.test(this.suppliername)) {//IMIE
                                parameters = {
                                    imei: this.suppliername,
                                };
                                this.isNoOrderSearch = true;
                                this.showImei = true;
                                this.showNumber = false;
                            } else if (this.suppliername.includes("/")) {//SN
                                parameters = {
                                    uniquecode: this.suppliername,
                                };
                                this.isNoOrderSearch = true;
                                this.showImei = true;
                                this.showNumber = false;
                            }
                            //else if (this.suppliername.length == 12) {//FSN
                            //    //regNumber.test(this.suppliername) && regString.test(this.suppliername)
                            //    parameters = {
                            //        fsn: this.suppliername,
                            //    };
                            //    this.showImei = true;
                            //    this.showNumber = false;
                            //}
                            else if (this.suppliername.length == 14) {
                                parameters = {
                                    externalId: this.suppliername,
                                };
                                this.disChecked = true; //订单查询检测是否可以勾选
                                this.showNumber = true;
                                this.showImei = false;
                            }
                            else if (regNumber.test(this.suppliername) && this.suppliername.length == 16) {//订单号
                                parameters = {
                                    order_id: this.suppliername,
                                };
                                this.disChecked = true; //订单查询检测是否可以勾选
                                this.showNumber = true;
                                this.showImei = false;
                            } else if (this.suppliername.length == 18 && prestring == "SA") {//米家订单号
                                parameters = {
                                    miorderid: this.suppliername,
                                };
                                this.disChecked = true; //订单查询检测是否可以勾选
                                this.showNumber = true;
                                this.showImei = false;
                            } else {
                                parameters = {
                                    fsn: this.suppliername,
                                };
                                this.showImei = true;
                                this.showNumber = false;
                                if (this.showOrder) {
                                    rtcrm.alertDialog(this.$t("createworkorder.tab.pleaceinputgoodsid", "请输入商品编码！"));
                                    return;
                                }
                            }
                            //加载订单详情
                            this.orderDetailLoading = true;
                            //生成requestId
                            this.requestId = this.generateUUID();
                            var orderDetailResp = await this.getOrderDetail({ model: parameters, caseid: this.caseid });
                            var orderDetailRlt = JSON.parse(orderDetailResp.output);
                            if (orderDetailRlt.code == 200) {
                                var queryWorkOrder  = orderDetailRlt.data;
                                this.queryWorkOrder  = queryWorkOrder;
                                this.queryWorkOrder["caseid"] = this.caseid;
                                console.log("工单详情:", queryWorkOrder);
                                var workOrderSearchRetun = queryWorkOrder["workOrderSearchRetun"];
                                this.orderDetailLoading = false;

                                var orderFrom = queryWorkOrder["OrderDataData"]?.["order_from"];
                                var orderType = queryWorkOrder["OrderDataData"]?.["order_type"];
                                //未查询到详情数据,直接返回
                                if (workOrderSearchRetun == undefined || workOrderSearchRetun == null) {
                                    this.showOrderContent = true;
                                    this.orderDetailLoading = false;
                                    this.orderDataDetail = [];
                                    this.$forceUpdate();
                                    return;
                                }
                                var orderDetailData = workOrderSearchRetun["orderDataDetail"];
                                if (queryWorkOrder["res"] != null) {
                                    var resModel = JSON.parse(queryWorkOrder["res"]);
                                    if (resModel["order"] == null) {
                                        if(orderDetailData["goodid"] == "")
                                        {
                                            this.showOrderContent = false;
                                            return;
                                        }
                                        else{
                                            if (!this.showNumber){
                                                this.getImeiMaintenances(queryWorkOrder, orderDetailData, [], null);//Imei信息
                                            }
                                        }
                                    }
                                }

                                this.showOrderContent = true;
                                this.orderDataDetail = orderDetailData;

                                //当前订单的【订单来源】！=“国际CRM” 或 【订单类型】！=“团购”，且订单[国家]与<案例>国家相同时，获取订单“收货地址”自动填入建单页的“地址信息” ；若案例已有地址，则以案例为主
                                if (this.activeName == "first"&&
                                    orderFrom !== 68 && orderType !== 6 && this.caseid && (!this.blurform.new_province_id || this.blurform.new_province_id.id === undefined) &&
                                    (!this.blurform.new_city_id || this.blurform.new_city_id.id === undefined) && (!this.blurform.new_county || this.blurform.new_county.id === undefined)) {
                                    var countryId = queryWorkOrder["OrderDataData"]?.countryId;
                                    var provinceId = queryWorkOrder["OrderDataData"]?.provinceId;
                                    var cityId = queryWorkOrder["OrderDataData"]?.cityId;
                                    var districtId = queryWorkOrder["OrderDataData"]?.districtId;
                                    if (countryId && countryId.toLowerCase() === this.blurform.new_country_id.id.replace(/[{}]/g, '').toLowerCase()
                                    ) {

                                        if (provinceId) {
                                            this.blurform.new_province_id = {
                                                id: provinceId
                                            };
                                        }
                                        if (cityId) {
                                            this.blurform.new_city_id = {
                                                id: cityId
                                            };
                                        }
                                        if (districtId) {
                                            this.blurform.new_county = {
                                                id: districtId
                                            };
                                        }
                                    }
                                    this.initCascaderCounty();
                                }

                                //送装一体不用数据校验
                                if (!this.isDeliveryInstallation) {
                                    this.PreCheckOrderByCreate(queryWorkOrder, orderDetailData);//数据校验
                                }
                                this.getStations(queryWorkOrder, orderDetailData);//订单明细
                                //送装一体只用为安装
                                if (this.isDeliveryInstallation) {
                                    console.info("服务类型如下：" + this.typeOption);
                                    this.typeOption = this.typeOption.filter(x => x.id == 5);//安装
                                    this.new_type = this.typeOption[0].id;
                                    this.showCreatebtn = true;//显示建单按钮
                                    this.isCreateBtnDisable = false;//建单按钮不被禁用
                                    this.serviceSelectInvoice = 2;//是否有发票（下拉）
                                    this.new_servicemode = this.servicemodeOption[1].id;
                                    return;
                                }
                                this.getSearchDate(queryWorkOrder);
                                //当订单号查询，不查询如下数据
                                if (!this.showNumber) {
                                    this.getImeiMaintenances(queryWorkOrder, orderDetailData,[],null);//Imei信息
                                    this.getSearchOldAndNewImeiList(orderDetailData.uniquecode);//历史服务记录
                                    this.getOrderActivity(orderDetailData.uniquecode);//已参与的服务活动
                                }
                                if (this.caseid == null) {
                                    this.servicemodeOption = workOrderSearchRetun["wayList"];//服务方式
                                    this.typeOption = workOrderSearchRetun["srctypeList"];//服务类型
                                }
                                //新增判断，如果服务方式只有一个可选项，则默认赋值
                                if (this.caseid == null && this.servicemodeOption != null && this.servicemodeOption.length == 1) {
                                    this.new_servicemode = this.servicemodeOption[0].id;
                                    //手动触发change事件
                                    this.selectTypeDone();
                                }
                                if (this.caseid != null && this.caseservicemodeOption != null && this.caseservicemodeOption.length == 1) {
                                    this.new_servicemode = this.caseservicemodeOption[0].id;
                                    //手动触发change事件
                                    this.selectTypeDone();
                                }
                                //新增判断，如果服务类型只有一个可选项，则默认赋值
                                if (this.typeOption != null && this.typeOption.length == 1) {
                                    this.new_type = this.typeOption[0].id;
                                    //手动触发change事件
                                    this.selectTypeDone();
                                }
                            } else if (orderDetailRlt.msg) {
                                //详情查询异常埋点
                                // var aiLogDataModel = new top.AILogging.aiLogDataModelV2(this.requestId, "ServiceOrder", "workorderDetail", "reloadData",
                                //     0, 'Web工单详情查询失败-' + orderDetailRlt.msg, 0, new Date().getUTCDate());
                                // this.writeLog(new Date(), null, aiLogDataModel, 2);

                                this.orderDetailLoading = false;
                                this.showOrderContent = false;
                                this.showOrder = false;
                                this.serviceSelectInvoice = 2;
                                this.activities = [];
                                //------------时间戳结束----------------//
                                var timeEnd = new Date().getTime();
                                console.log(new Date(timeEnd - timeStart) / 1000 + "s");
                                //------------时间戳----------------//
                                XM.openAlertDialog(orderDetailRlt.msg);
                                return;
                            }
                        }

                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }
                },
                AutoDispatchStation: async function() {
                    try {
                        var goods ="";
                        if(this.activeName == "first"){
                            if (this.orderDataDetail["order_id"] != "" && (this.valorderItemList == null || this.valorderItemList.length <= 0)) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoCheckOrder", "请至少勾选一条订单明细"));
                                return;
                            }
                            goods = this.valorderItemList[0].goods_id;
                        } else {
                            if (!this.suppliername || this.suppliername == "") {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoGoods", "查询服务网点前请输入商品信息"));
                                return;
                            }
                            goods = this.suppliername;
                        }
                        if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoCountry", "查询服务网点前国家/区域必填！"));
                            return false;
                        }
                        if (!this.new_servicemode) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoServicemode", "查询服务网点前请先选择服务方式"));
                            return;
                        }
                        if (!this.new_type) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoselecttype", "查询服务网点前请先选择服务类型"));
                            return;
                        } 

                        var XMSCreateGetStationModel = {
                            new_servicemode: this.new_servicemode,
                            new_type: this.new_type,
                            new_country_id: this.blurform.new_country_id.id,
                            new_province_id: this.blurform.new_province_id.id,
                            new_city_id: this.blurform.new_city_id.id,
                            new_county_id: this.blurform.new_county.id,
                            goodsid: goods
                        };
                        var inputPara = JSON.stringify({ XMSCreateGetStationModel });
                        var param = {
                            "WorkOrderId": this.blurform.new_country_id.id,//随便传一个值 为了跳过DispatchOrderAction方法的必填校验
                            "isXMS": true,
                            "inputPara": inputPara
                        };
                        var result = rtcrm.invokeHiddenApi("new_service", "WorkOrder/DispatchOrderAction", param);
                        var obj = result;
                        //var station = "new_srv_stations(" +
                        //    obj.list[0].id + ")?$select=new_name,new_code,new_xms"
                        //this.new_station = rtcrm.retrieve(station, true);
                        this.new_station = obj.list[0];
                        this.new_srv_station = this.new_station["new_name"];
                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }
                },



                DispatchStation: async function () {
                    try {
                        var goods ="";
                        if(this.activeName == "first"){
                            if (this.orderDataDetail["order_id"] != "" && (this.valorderItemList == null || this.valorderItemList.length <= 0)) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoCheckOrder", "请至少勾选一条订单明细"));
                                return;
                            }
                            goods = this.valorderItemList[0].goods_id;
                        } else {
                            if (!this.suppliername || this.suppliername == "") {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoGoods", "查询服务网点前请输入商品信息"));
                                return;
                            }
                            goods = this.suppliername;
                        }
                        if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoCountry", "查询服务网点前国家/区域必填！"));
                            return false;
                        }
                        if (!this.new_servicemode) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoServicemode", "查询服务网点前请先选择服务方式"));
                            return;
                        }
                        if (!this.new_type) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_DispatchNoselecttype", "查询服务网点前请先选择服务类型"));
                            return;
                        }

                        var XMSCreateGetStationModel = {
                            new_servicemode: this.new_servicemode,
                            new_type: this.new_type,
                            new_country_id: this.blurform.new_country_id.id,
                            new_province_id: this.blurform.new_province_id.id,
                            new_city_id: this.blurform.new_city_id.id,
                            new_county_id: this.blurform.new_county.id,
                            goodsid: goods
                        };
                        var inputPara = JSON.stringify({ XMSCreateGetStationModel });
                        var encodeStr = "inputPara=" + inputPara + "&WorkOrderId=" + this.blurform.new_country_id.id + "&new_type=" + this.new_type + "&isDeliveryInstallation=" + this.isDeliveryInstallation;
                        if (this.new_station && this.new_station.id) {
                            encodeStr += "&stationId=" + this.new_station.id;
                        }
                        var pageInput = {
                            pageType: "webresource",
                            webresourceName: "new_/Service/createworkorder_dispatchstation.html",
                            data: encodeURIComponent(encodeStr),
                        }
                        var navigationOptions = {
                            title: $t("dispatchstation.work", "派单"),
                            target: 2,
                            width: 1200,
                            height: 600,
                            position: 1
                        };
                        var sidepanel = top.document.getElementById("FullPageWebResource");
                        if (sidepanel) sidepanel.id = "SidePanelWebResource";
                        window.parent.Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                            (res) => {
                                if (res.returnValue) {
                                    //var station = "new_srv_stations(" +
                                    //res.returnValue.stationid + ")?$select=new_name,new_code,new_xms"
                                    //this.new_station = rtcrm.retrieve(station, true);
                                    this.new_station = res.returnValue.station;
                                    this.onloadStation();
                                }
                            },
                            function error(e) {
                                errorCallback(e);
                            });

                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }
                },
                onloadStation: async function () {
                    try {
                        if (this.new_station) {
                            this.new_srv_station = this.new_station["new_name"];
                        }
                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }

                },

                //日志埋点
                writeLog(startTime, endTime, aiLogDataModel, logType) {
                    var utc_startTime = XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S");
                    // aiLogDataModel.DURATION = 0;
                    if (endTime != null) {
                        var utc_endTime = XM.FormatUTCDate(endTime, "yyyy-MM-dd hh:mm:ss.S");
                        // aiLogDataModel.DURATION = new Date(utc_endTime) - new Date(utc_startTime);
                    }
                    // top.AILogging.writeLogToAppInsightsV2(aiLogDataModel,(logType == 1 ? top.AILogging.appInsightLogType().trace : top.AILogging.appInsightLogType().exception));
                },
                // 每页显示的条数
                oldImeiHandleSizeChange(pageSize) {
                    // 改变每页显示的条数
                    this.oldImeiPageSize = pageSize
                    // 注意：在改变每页显示的条数时，要将页码显示到第一页
                    this.oldImeiHandleCurrentChange(1);
                },
                // 显示第几页
                oldImeiHandleCurrentChange(currentPage) {
                    // 改变默认的页数
                    this.currentPage = currentPage
                    this.oldImeiCurrentChangePage(currentPage);
                },
                //分页方法
                oldImeiCurrentChangePage(currentPage) {
                    let from = (currentPage - 1) * this.oldImeiPageSize;
                    let to = currentPage * this.oldImeiPageSize;
                    this.searchOldAndNewImeiList = [];
                    this.searchOldAndNewImeiList = this.pageSearchOldAndNewImeiList.slice(from, to);
                },
                //查询校验
                async PreCheckOrderByCreate(inputPara, orderDetailData) {
                    var result = null;
                    try {
                        let that = this;
                        if (this.isDeliveryInstallation) {
                            inputPara["isDeliveryInstallation"] = "true"
                        } else {
                            inputPara["isDeliveryInstallation"] = "false";
                        }
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "PreCheckOrderByCreate",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para);
                        var checkOrderRlt = JSON.parse(res.output);
                        if (checkOrderRlt.code != 200) {
                            this.showCreatebtn = false;
                            this.checkResultTxt = checkOrderRlt.msg;
                        }
                        else{
                            if (this.showNumber) {
                                // this.showCreatebtn = true;
                                // this.isCreateBtnDisable = false;
                                this.checkResultTxt = "";
                            }
                        }
                        this.ifPreCheckDone = true;//数据校验完毕
                        return result;
                    }
                    catch (e) {
                        this.ifPreCheckDone = true;//数据校验完毕
                        Xrm.Utility.alertDialog('PreCheckOrderByCreate:' + e);
                    }
                },
               async cascaderhandleVisibleChange(Visible) {
                
                    if (!this.blurform.new_country_id?.id)
                        return;
                    if (Visible) {
                        if (this.countyOptions.length == 0) {
                            const notselecttext = this.notselecttext
                            var country_id = this.blurform.new_country_id.id.replace('{', '').replace('}', '');
                            var countryRegionOptions = rtcrm.invokeHiddenApi("new_service", "WorkOrder/GetCountryRegionOptions", { param: JSON.stringify({ countryid: country_id }) });
                            if (countryRegionOptions.Value?.options.length > 0 && countryRegionOptions.Value?.options[0].children.length > 0)
                                countryRegionOptions.Value?.options[0].children.forEach(province => { 
                                    province.children.forEach(city => {
                                        if (!city.children) {
                                            city.children = [];
                                        }
                                       
                                        city.children.unshift({ value: city.value + this.suffixtext, label: notselecttext, children: [] });
                                        city.children.forEach(county => {
                                            const countyObj = { value: county.value + this.suffixtext, label: notselecttext }
                                            if (county.children) {
                                                county.children.unshift(countyObj);
                                            }
                                            else {
                                                county.children = [countyObj];
                                            }
                                        });
                                    });

                                });
                            this.countyOptions = countryRegionOptions.Value?.options[0].children;
                        } 
                        if (!this.countySelect) {
                            this.$nextTick(() => {
                                var panel2 = document.querySelectorAll('.el-cascader-panel > .el-scrollbar.el-cascader-menu');
                                if (panel2.length > 1)
                                    return;
                                // 级联选择器面板容器
                                const panel = document.querySelector('.el-cascader-panel');
                                if (!panel) return;

                                // 1. 处理第一级节点（展开后才会渲染第二级）
                                const level1Node = panel.querySelector('.el-cascader-menu:nth-child(1) .el-cascader-node:first-child');
                                if (level1Node && (level1Node.getAttribute('aria-expanded') == null) || level1Node.getAttribute('aria-expanded') === 'false') {
                                    level1Node.click(); // 未展开则点击

                                    // 2. 第一级展开后，处理第二级节点
                                    this.$nextTick(() => {
                                        const level2Node = panel.querySelector('.el-cascader-menu:nth-child(2) .el-cascader-node:first-child');
                                        if (level2Node && (level2Node.getAttribute('aria-expanded') === 'false' || level2Node.getAttribute('aria-expanded') == null)) {
                                            level2Node.click(); // 未展开则点击
 
                                        }
                                    });
                                }
                            });

                        } else {
                        }
                    }
                },
                //当有发票时间的时候获取最新IMEI信息
                async getImeiMaintenancesByInvoiceTime(inputPara,orderDetailData,orderItemList,getimeitime) {
                    var result = null;
                    try {
                        //判断详情是否为空&当订单号查询，不查询如下数据
                        if ((inputPara == null || inputPara["res"] == "null") && orderDetailData["goodid"] == "")
                            return;
                        let that = this;
                        var newInput  = Object.assign({},inputPara);
                        //去除多余属性
                        delete newInput["incdentModel"];
                        delete newInput["srvworkerModel"];
                        var para = {
                            inputPara: JSON.stringify({
                                queryWorkOrder:newInput,
                                orderItemList:orderItemList,
                                getimeitime:getimeitime,
                                isDeliveryInstallation: this.isDeliveryInstallation
                            }),
                            funcType: "GetImeiServiceData",
                            tempPara1: this.requestId
                        }
                        result = await XM.ActionAsync('new_ServiceOrderQueryAction', para);
                        return result;
                    }
                    catch (e) {
                        console.log('getImeiMaintenancesByInvoiceTime' + e);
                    }
                },
                //Imei维保信息
                //,orderItemList,getimeitime
                async getImeiMaintenances(inputPara,orderDetailData,orderItemList,getimeitime) {
                    var result = [];
                    try {
                        //判断详情是否为空&当订单号查询，不查询如下数据
                        if ((inputPara == null || inputPara["res"] == "null") && orderDetailData["goodid"] == "")
                            return;
                        this.iMeiLoading = true;
                        let that = this;
                        var newInput  = Object.assign({},inputPara)
                        //去除多余属性
                        delete newInput["incdentModel"];
                        delete newInput["srvworkerModel"];
                        var para = {
                            inputPara: JSON.stringify({
                                queryWorkOrder:newInput,
                                orderItemList:orderItemList,
                                getimeitime:getimeitime
                            }),
                            funcType: "GetImeiServiceData",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para);
                        var maintenancesRlt = JSON.parse(res.output);
                        if (maintenancesRlt.code == 200) {
                            result = maintenancesRlt.data;
                            this.maintenances = result.returnImeiDatalist;//IMEI维保信息
                            this.IMEIServiceReturnData = result.IMEIServiceReturnData;//imei服务接口
                            this.ActiveReturnData = result.ActiveReturnData;//imei激活服务接口
                            this.iMeiLoading = false;
                            //加载可参与的活动&非勾选的情况去加载
                            if (that.maintenances.length > 0 && orderItemList.length < 1) {
                                if (!this.showNumber && this.stations.length == 1) {
                                    this.$nextTick(() => {
                                        this.$refs.multipleTable.toggleRowSelection(this.stations[0], true);
                                    })
                                    this.stations.map(item => item.checked = true);
                                    this.unchecked = true;
                                } else {
                                    this.unchecked = false;
                                }
                                this.getServiceAction({
                                    order_from: orderDetailData["order_from"],
                                    station_country_id: orderDetailData["country_id"],
                                    new_type: orderDetailData["new_stietype"],
                                    new_commoditycode: that.maintenances[0]["new_commoditycode"],
                                    new_packetsstarttime: that.maintenances[0]["new_packetsstarttime"],
                                    sn: orderDetailData["miorderid"],
                                    imei: that.maintenances[0]["imei"]
                                });//可参与的服务活动
                            }
                            let maintenancesTimerId = setInterval(() => {
                                if (this.ifPreCheckDone) {
                                    result = true;
                                    console.log('checkIfPreCheckDone');
                                    clearInterval(maintenancesTimerId);
                                    maintenancesTimerId = null;
                                    //非订单查询,数据检验完毕
                                    if (!this.showNumber && this.checkResultTxt == "") {
                                        //屏蔽创建按钮
                                        this.showCreatebtn = true;
                                        this.isCreateBtnDisable = false;
                                    }
                                }
                                else {
                                    console.log('ifPreCheckDone checking...');
                                }
                            }, 500);
                            // 3秒后停止执行
                            setTimeout(() => {
                                clearInterval(maintenancesTimerId);
                                maintenancesTimerId = null;
                            }, 20000);
                        }
                        else {
                            this.iMeiLoading = false;
                            console.log('getImeiMaintenances_Error' + maintenancesRlt.msg);
                            let timerId = setInterval(() => {
                                if (this.ifPreCheckDone) {
                                    result = true;
                                    console.log('checkIfPreCheckDone');
                                    clearInterval(timerId);
                                    timerId = null;
                                    //数据检验完毕,但是查询IMei报错
                                    if (this.checkResultTxt == "") {
                                        //屏蔽创建按钮
                                        this.showCreatebtn = false;
                                        Xrm.Utility.alertDialog(maintenancesRlt.msg);
                                    }
                                }
                                else {
                                    console.log('ifPreCheckDone checking...');
                                }
                            }, 500);
                            // 3秒后停止执行
                            setTimeout(() => {
                                clearInterval(timerId);
                                timerId = null;
                            }, 20000);
                        }
                        return result;
                    }
                    catch (e) {
                        this.iMeiLoading = false;
                        Xrm.Utility.alertDialog('getImeiMaintenances:' + e);
                    }
                },
                //查询客户是否创建
                async checkCustomIfNeedCreate() {
                    var result = null;
                    try {
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify({
                                orderDataDetail:this.orderDataDetail,
                                returnImeiDataList:this.maintenances,
                                caseid: this.caseid
                            }),
                            funcType: "checkCustomIfNeedCreate",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para);
                        var checkCustomRlt = JSON.parse(res.output);
                        if (checkCustomRlt.code == 200) {
                            result = checkCustomRlt.data;
                            this.checkCustomModel = result;
                        }
                        else {
                            console.log('checkCustomIfNeedCreate:' + checkCustomRlt.msg);
                        }
                        return result;
                    }
                    catch (e) {
                        Xrm.Utility.alertDialog('checkCustomIfNeedCreate:' + e);
                    }
                },
                //searchDate
                async getSearchDate(inputPara) {
                    var result = [];
                    try {
                        if (inputPara == "" || inputPara == null)
                            return;
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "searchDate",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var searchDateRlt = JSON.parse(res.output);
                        if (searchDateRlt.code == 200) {
                            result = searchDateRlt.data;
                        }
                        else {
                            console.log('getSearchDate:' + searchDateRlt.msg);
                            Xrm.Utility.alertDialog(searchDateRlt.msg);
                        }
                        this.searchDate = result;
                        return result;
                    }
                    catch (e) {
                        Xrm.Utility.alertDialog('getSearchDate:' + e);
                    }
                },
                //获取【订单商品明细】
                async getStations(inputPara, orderDetailData) {
                    var result = [];
                    try {
                        this.orderItemLoading = true;
                        //判断详情是否为空
                        if (inputPara == null || inputPara["res"] == "null" || inputPara.OrderDataData == null) {
                            this.orderItemLoading = false;
                            return;
                        } else {
                            //是否送装一体赋值
                            if (this.isDeliveryInstallation) {
                                inputPara["isDeliveryInstallation"] = "true";
                            } else {
                                inputPara["isDeliveryInstallation"] = "false";
                            }
                        }
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "SearchOrderItemList",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var stationsRlt = JSON.parse(res.output);
                        if (stationsRlt.code == 200) {
                            var  queryResult = stationsRlt.data;
                            this.getimeitime = queryResult["getimeitime"];
                            result = queryResult["orderItemList"] || [];
                            this.stations = result;
                            this.orderItemList = result;
                            if (this.showNumber) {
                                if(this.stations.length == 1){
                                    this.$nextTick(() => {
                                        this.$refs.multipleTable.toggleRowSelection(this.stations[0], true);
                                    })
                                    this.stations.map(item => item.checked = true);
                                    this.unchecked = true;
                                    this.showCreatebtn = true;
                                    this.isCreateBtnDisable = false;
                                }
                                //没有妥投的订单之前试隐藏创建按钮
                                else if(this.stations.length < 1){
                                    let stationTimerId = setInterval(() => {
                                        if (this.ifPreCheckDone) {
                                            result = true;
                                            console.log('checkIfPreCheckDone');
                                            clearInterval(stationTimerId);
                                            stationTimerId = null;
                                            this.showCreatebtn = false;
                                        }
                                        else {
                                            console.log('ifPreCheckDone checking...');
                                        }
                                    }, 500);
                                    // 3秒后停止执行
                                    setTimeout(() => {
                                        clearInterval(stationTimerId);
                                        stationTimerId = null;
                                    }, 20000);
                                }
                                else{
                                    this.showCreatebtn = true;
                                    this.isCreateBtnDisable = false;
                                }
                            }
                            else {
                                this.unchecked = false;
                            }
                            if (this.stations != null && this.stations.length > 0) {
                                //可用的权益信息
                                if (!this.showNumber) {
                                    this.getProfilerights({ orderCode: orderDetailData["order_id"], orderItemListCount: result.length, uniquecode: orderDetailData.uniquecode, goodid: orderDetailData.goodid });
                                }
                                this.servicerecordData();//主动服务登记记录
                                //当空调内外机存在时显示到界面上
                                var airconditioningType = this.stations.filter(x => x.new_airconditioningtype == 1 || x.new_airconditioningtype == 2);
                                if (airconditioningType && airconditioningType.length > 0)
                                    this.showAirconditioningType = true;
                            }
                        }
                        else {
                            Xrm.Utility.alertDialog('getStations_Error:' + stationsRlt.msg);
                        }
                        this.orderItemLoading = false;
                        return result;
                    }
                    catch (e) {
                        this.orderItemLoading = false;
                        Xrm.Utility.alertDialog('getStations:' + e);
                    }
                },
                //获取【已参与的服务活动】
                async getOrderActivity(inputPara) {
                    var result = [];
                    try {
                        if (inputPara == "" || inputPara == null)
                            return;
                        this.orderActivityLoading = true;
                        let that = this;
                        var para = {
                            inputPara: inputPara,
                            funcType: "OrderActivity",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var OrderActivityRlt = JSON.parse(res.output);
                        if (OrderActivityRlt.code == 200) {
                            result = OrderActivityRlt.data;
                        }
                        else {
                            console.log('getOrderActivity:' + OrderActivityRlt.msg);
                        }
                        this.OrderActivity = result;
                        this.orderActivityLoading = false;
                        return result;
                    }
                    catch (e) {
                        this.orderActivityLoading = false;
                        Xrm.Utility.alertDialog('getOrderActivity:' + e);
                    }
                },
                //获取【可参与的服务活动】
                async getServiceAction(inputPara)
                {
                    var result = [];
                    try {
                        if (inputPara == "" || inputPara == null)
                            return;
                        this.serviceActionLoading = true;
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "serviceAction",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var serviceActionRlt = JSON.parse(res.output);
                        if (serviceActionRlt.code == 200) {
                            result = serviceActionRlt.data;
                        }
                        else {
                            console.log('getServiceAction:' + serviceActionRlt.msg);
                        }
                        this.activities = result;
                        this.serviceActionLoading = false;
                        return result;
                    }
                    catch (e) {
                        this.serviceActionLoading = false;
                        Xrm.Utility.alertDialog('getServiceAction:' + e);
                    }
                },
                //获取【可用的权益信息】
                async getProfilerights(inputPara) {
                    var result = [];
                    try {
                        if (inputPara == "" || inputPara == null || inputPara.uniquecode == "" || inputPara.uniquecode == null)
                            return;
                        this.profilerightsLoading = true;
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "profilerights",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var profilerightsRlt = JSON.parse(res.output);
                        if (profilerightsRlt.code == 200) {
                            result = profilerightsRlt.data;
                        }
                        else {
                            console.log('getProfilerights:' + profilerightsRlt.msg);
                        }
                        this.profilerights = result;
                        this.profilerightsLoading = false;
                        return result;
                    }
                    catch (e) {
                        this.profilerightsLoading = false;
                        Xrm.Utility.alertDialog('getProfilerights:' + e);
                    }
                },
                //获取【历史数据服务】
                async getSearchOldAndNewImeiList(inputPara) {
                    var result = [];
                    try {
                        if (inputPara == "" || inputPara == null)
                            return;
                        this.oldImeiLoading = true;
                        let that = this;
                        var para = {
                            inputPara: inputPara,
                            funcType: "searchOldAndNewImeiList",
                            tempPara1: this.requestId
                        }
                        var res = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        var searchOldAndNewImeiRlt = JSON.parse(res.output);
                        if (searchOldAndNewImeiRlt.code == 200) {
                            result = searchOldAndNewImeiRlt.data;
                        }
                        else {
                            console.log('searchOldAndNewImeiList:' + searchOldAndNewImeiRlt.msg);
                        }
                        this.searchOldAndNewImeiList = result;
                        this.pageSearchOldAndNewImeiList = result;
                        // 将数据的长度赋值给totalCount
                        this.oldImeiTotalCount = this.pageSearchOldAndNewImeiList.length;
                        //默认当前第一页
                        this.oldImeiCurrentChangePage(1);
                        this.oldImeiLoading = false;
                        return result;
                    }
                    catch (e) {
                        this.oldImeiLoading = false;
                        Xrm.Utility.alertDialog(e);
                    }
                },
                //获取工单详情
                async getOrderDetail(inputPara) {
                    var result = null;
                    try {
                        let that = this;
                        var para = {
                            inputPara: JSON.stringify(inputPara),
                            funcType: "SearchOrderInfo",
                            tempPara1: this.requestId
                        }
                        result = await XM.ActionAsync('new_ServiceOrderQueryAction', para)
                        return result;
                    }
                    catch (e) {
                        Xrm.Utility.alertDialog(e);
                    }
                    return result;
                },
                //日期格式化
                formatDate: function (now) {
                    var year = now.getFullYear();
                    var month = now.getMonth() + 1;
                    var date = now.getDate();
                    var hour = now.getHours();
                    var minute = now.getMinutes();
                    var second = now.getSeconds();
                    return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
                },
                //年月日时间
                formatYearDate: function (now) {
                    var year = now.getFullYear();
                    var month = now.getMonth() + 1;
                    var date = now.getDate();
                    return year + "-" + month + "-" + date;
                },
                //产品明细选择
                selectLine: function (index) {
                    //如果是imei带/则不允许勾选
                    if (this.unchecked) {
                        return;
                    }
                    var orderline = this.stations[index];
                    if (rtcrm.isNull(orderline.checked) || !orderline.checked) {
                        orderline.checked = true;
                    } else {
                        orderline.checked = false;
                    }
                    Vue.set(this.stations, index, orderline);
                },
                //主动服务登记记录选择
                selectRecord: function (index) {
                    var orderline = this.activeservicerecords[index];
                    if (rtcrm.isNull(orderline.checked)) {
                        orderline.checked = true;
                        this.new_activeservicerecordid = orderline.new_activeservicerecordid;
                    } else {
                        orderline.checked = !orderline.checked;
                        this.new_activeservicerecordid = '';
                    }
                    Vue.set(this.activeservicerecords, index, orderline);
                },
                //IMEI读取按钮
                searchImei: function () {
                    rtcrm.invokeHiddenApiAsync("new_service", "CreateWorkorder/GetImeiTempMsg").then(res => {
                        console.log(res);
                        rtcrm.closeLayerLoading();
                        if (res != null) {
                            this.suppliername = res[0];
                        }
                    }).catch(e => {
                        rtcrm.closeLayerLoading();
                        rtcrm.alertDialog(e.message);
                    })
                },
                //主动服务登记记录
                servicerecordData: function () {
                    try {
                        var servicerecordsFetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
<entity name = 'new_activeservicerecord' >\
<attribute name='new_activeservicerecordid' />\
<attribute name='new_name' />\
<attribute name='new_buytime' />\
<attribute name='new_phone' />\
<attribute name='new_accountname' />\
<attribute name='new_recordtime' />\
<attribute name='new_type' />\
<attribute name='new_sn' />\
<attribute name='new_imei' />\
<order attribute='new_name' descending='false' />\
<filter type='and'>\
<condition attribute='new_srv_workorder_id' operator='null' />\
<condition attribute='statecode' operator='eq' value='0' />\
<condition attribute='new_sn' operator='eq' value='" + this.suppliername + "' />\
</filter>\
</entity>\
</fetch>";
                        var servicerecords = rtcrm.fetch("new_activeservicerecords", servicerecordsFetchXml, true);
                        this.activeservicerecords = [];
                        for (var i = 0; i < servicerecords.length; i++) {
                            var obj = {};
                            obj.checked = servicerecords[i].Checked;;//声明标志位，表示该列是否被选中，默认为false
                            obj.new_name = servicerecords[i]["new_name"];//主动服务登记编码
                            obj.new_type = servicerecords[i]["<EMAIL>"];;  //主动服务类型
                            obj.new_accountname = servicerecords[i]["new_accountname"];;  //用户姓名
                            obj.new_phone = servicerecords[i]["new_phone"];;  //电话
                            obj.new_imei = servicerecords[i]["new_imei"];;  //IMEI
                            obj.new_sn = servicerecords[i]["new_sn"];;  //SN
                            obj.new_activeservicerecordid = servicerecords[i]["new_activeservicerecordid"];;  //主动服务id
                            //查询活动类型
                            var buytime = new Date(servicerecords[i]["new_buytime"]);
                            obj.new_buytime = this.formatDate(buytime)//购买时间
                            var recordtime = new Date(servicerecords[i]["new_recordtime"]);
                            obj.new_recordtime = this.formatDate(recordtime)//信息登记时间
                            this.activeservicerecords.push(obj);
                        }
                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }
                },
                selectOption: function () {
                    var goodsidCreateWorkorderModel = {
                        new_servicemode: this.new_servicemode,
                        new_type: this.new_type,
                        new_invoice_num: this.new_invoice_num,
                        new_invoice_time: this.new_invoice_time,
                        goodsid: this.suppliername,
                    };
                    rtcrm.layerLoading();
                    rtcrm.invokeHiddenApiAsync("new_service", "CreateWorkorder/GoodsidWorkOrderSearch", { goodsidCreateWorkorderModel: goodsidCreateWorkorderModel, caseid: this.caseid })
                        .then(res => {
                            rtcrm.closeLayerLoading();
                            console.log(res);
                            this.servicemodeOption = res.wayList;
                            //新增判断，如果服务方式只有一个可选项，则默认赋值
                            if (this.caseid == null && this.servicemodeOption != null && this.servicemodeOption.length == 1) {
                                this.new_servicemode = this.servicemodeOption[0].id;
                                //手动触发change事件
                                this.selectTypeDone();
                            }
                            if (this.caseid != null && this.caseservicemodeOption != null && this.caseservicemodeOption.length == 1) {
                                this.new_servicemode = this.caseservicemodeOption[0].id;
                                //手动触发change事件
                                this.selectTypeDone();
                            }
                            if (this.caseid == null) {
                                this.typeOption = res.srctypeList;
                            }
                            //新增判断，如果服务类型只有一个可选项，则默认赋值
                            if (this.typeOption != null && this.typeOption.length == 1) {
                                this.new_type = this.typeOption[0].id;
                                //手动触发change事件
                                this.selectTypeDone();
                            }
                            this.maintenances = res.returnImeiDatalist;
                            this.activities = res.serviceActionModel;//可参与的服务活动
                            this.$forceUpdate();
                        })
                        .catch(e => {
                            rtcrm.closeLayerLoading();
                            rtcrm.alertDialog(e.message);
                            this.isSearch = false;
                        })
                },
                stringFormat(strTxt,arguments){
                    if (arguments.length == 0) return this;
                    for (var s = strTxt, i = 0; i < arguments.length; i++)
                        s = s.replace(new RegExp("\\{" + i + "\\}", "g"), arguments[i]);
                    return s;
                },
                //确认按钮
                ConfirmRibbon: function () {
                    if (this.showBlurOrder) {
                        if (!this.blurCheck()) { return; }
                    } else {

                        if (!this.new_send_questionnaire) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty", "同意售后回访必填！"));
                            return;
                        }
                        if (!this.isSearch) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.clickquery", "请先点击查询按钮"));
                            return;
                        }

                        if (!this.suppliername) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_SearchNonum", "请输入IMEI/SN/FSN/订单号!"));
                            return;
                        }
                        if (!this.new_servicemode) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_SelectServicemode", "请选择服务方式"));
                            return;
                        }
                        if (!this.new_type) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.selecttype", "请选择服务类型"));
                            return;
                        }
                        if (this.new_type == 3 && !this.new_returntype) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_selectreturntype", "请选择退货类型"));
                            return;
                        }
                        //是否有发票校验发票号码，发票时间
                        if (this.serviceSelectInvoice == 1) {
                            if (this.new_invoice_time == null) {
                                rtcrm.alertDialog(this.$t("new_srv_handing.invoicetimeempty", "发票时间必填！"));
                                return;
                            }
                        }
                        if (!this.showOrder) {
                            if ((this.orderDataDetail["order_id"] != "" && this.maintenances.length > 0) && (this.valorderItemList == null || this.valorderItemList.length <= 0)) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoCheckOrder", "请至少勾选一条订单明细"));
                                return;
                            }
                            if (this.new_type == 3) {
                                if (this.orderDataDetail["iscontainszengpin"] || (this.orderDataDetail["sales_type"] == 4 && this.isNoOrderSearch)) {
                                    rtcrm.alertDialog(this.stringFormat(this.$t("CreateWorkorder_Tran_Containzhengche", "该串号订单下包含赠品，请使用订单号: {0}建单"), [this.orderDataDetail["order_id"]]));
                                    return;
                                }
                                var batchGoodsId = this.valorderItemList && this.valorderItemList.length > 0 ? this.valorderItemList[0].batch_goods_id : "";
                                if (this.orderDataDetail["iscontainsdalibao"] || (!rtcrm.isNull(batchGoodsId) && batchGoodsId != "0"
                                    && this.isNoOrderSearch)) {
                                    rtcrm.alertDialog(this.stringFormat(this.$t("CreateWorkorder_Tran_Containdalibao", "该串号订单下包含大礼包，请使用订单号: {0}建单"), [this.orderDataDetail["order_id"]]));
                                    return;
                                }
                            }
                            //3 - 退货 可以多个商品，5 - 安装和 2 - 换货后端校验
                            if ((this.new_type != 3 && this.new_type != 5 && this.new_type != 2)&& this.valorderItemList.length > 1) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateSelectonedata", "该服务类型下只允许勾选一件商品"));
                                return;
                            }
                            if (this.new_type == 3) {
                                //是否分期
                                if (this.orderDataDetail["new_isfenqi"] && this.valorderItemList.length != this.stations.length) {
                                    rtcrm.alertDialog(this.$t("CreateWorkorder_CreateReturnAllOrder", "该订单退货必须将订单中的所有商品一起寄回，建单失败"));
                                    return;
                                }
                                //订单含赠品时，需要控制勾选赠品，一并回退
                                //赠品单
                                if (this.orderDataDetail["sales_type"] == 4) {
                                    if (this.valorderItemList.Count != this.stations.Count) {
                                        rtcrm.alertDialog(this.$t("CreateWorkorder_CreateGiftlistAllreturn", "该订单为赠品单，明细需全部退回，建单失败"));
                                        return;
                                    }
                                }
                                //大礼包id
                                //将勾选的商品明细根据大礼包id分组
                                /*var distinct_valorderItemList = this.valorderItemList.map(item => item.batch_goods_id);//只读取batch_goods_id
                                var unique_valorderItemList = [...new Set(distinct_valorderItemList)];//数组去重
                                for (let i = 0; i < unique_valorderItemList.length; i++) {
                                    //代码
                                    /// Modify Depiction：过滤商品明细退换货状态不等于丢单的商品明细
                                    var allOrderItemListcount = this.stations.filter(x => x.batch_goods_id == unique_valorderItemList[i] && x.batch_goods_id != "0" && x.stat != 30).length;
                                    var orderItemListcount = this.valorderItemList.filter(x => x.batch_goods_id == unique_valorderItemList[i] && x.batch_goods_id != "0").length;
                                    if (allOrderItemListcount.Count != orderItemListcount.Count) {
                                        rtcrm.alertDialog(this.$t("CreateWorkorder_CreateReturnallbatchgoodsid", "请将大礼包id一样的一起退回，建单失败"));
                                        return;
                                    }
                                }*/
                            }
                            // 维保结果/校验三包有效期
                            if (this.maintenances == null || this.maintenances.length < 0) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_NoPacketsoftime", "查询三包时间返回为空，建单失败"));
                                return;
                            }
                        }
                        //是否退货类型,Add p-liuzanmxiang 2023/10/16 米家订单不允许在ISP退货
                        if (this.new_type == 3 && this.orderDataDetail["order_from"] != null && this.orderDataDetail["order_from"] == 9){
                            rtcrm.alertDialog(this.$t("CreateWorkorder_MijiaOrderRefundVerification", "小米之家订单需要用户前往米家进行退款"));
                            return;
                        }
                    }
                    if (this.isIncident) {
                        //寄修或上门 国家 省市 详细地址必填
                        if (this.new_servicemode == 2 || this.new_servicemode == 3) {
                            if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                                rtcrm.alertDialog((this.$t("warranty.CountryRegionRequired", "国家/区域必填！")));
                                return;
                            }
                            if (!this.blurform.new_province_id || this.blurform.new_province_id.id == undefined) {
                                rtcrm.alertDialog((this.$t("warranty.ProvinceRequired", "请在详细地址中选择省份！")));
                                return;
                            }
                            if (!this.blurform.new_city_id || this.blurform.new_city_id.id == undefined) {
                                rtcrm.alertDialog((this.$t("warranty.CityRequired", "请在详细地址中选择城市！")));
                                return;
                            }
                            // 根据页面类型验证不同的地址字段
                            if (this.showBlurOrder) {
                                // 串号工单模糊创建页面：验证客户地址
                                if (!this.blurform.customeraddress || !this.blurform.customeraddress.trim()) {
                                    rtcrm.alertDialog((this.$t("warranty.AddressRequired", "客户地址必填！")));
                                    return;
                                }
                            } else {
                                // 其他页面：验证具体地址
                                if (!this.address || !this.address.trim()) {
                                    rtcrm.alertDialog((this.$t("warranty.AddressRequired", "具体地址必填！")));
                                    return;
                                }
                            }
                        }
                        else if (this.new_servicemode == 1) {
                            //到店国家必填
                            if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                                rtcrm.alertDialog((this.$t("warranty.CountryRegionRequired", "国家/区域必填！")));
                                return;
                            }
                        }

                        /*
                        if (this.isCountyRequired && (!this.blurform.new_county || this.blurform.new_county.id == undefined)) {
                            rtcrm.alertDialog((this.$t("warranty.CountyRequired", "区县必填！")));
                            return;
                        }*/
                        if (this.activeName != "third") {
                            if (!this.texterro) {
                                rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty2", "故障描述必填！"));
                                return;
                            }
                            if (!this.email) {
                                rtcrm.alertDialog(this.$t("blurform4959.emailmsg", "客户邮箱必填！"));
                                return;
                            }
                            var emailRule = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                            if (!emailRule.test(this.email)) {
                                rtcrm.alertDialog($t("incident.ErrorEmailFormat", "邮箱格式错误!"));
                                return;
                            }
                            if (!this.customertype) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNocustomertype", "客户类型必填！"));
                                return;
                            }
                            if (!this.new_station || !this.new_station.id) {
                                rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoXMSStation", "派单网点必填！"));
                                return;
                            }
                        }

                        //回填案例服务方式
                        if (this.caseid && this.new_servicemode) {
                            Xrm.WebApi.updateRecord(`incident`, this.caseid, {
                                new_servicemode: parseInt(this.blurform.new_servicemode, 10)
                            });
                        }

                    }

                     if (this.new_station && this.new_station["new_xms"] && this.new_servicemode == 2) {
                        rtcrm.layerLoading();
                        this.XMSCreateWorkorder();
                        //rtcrm.closeLayerLoading();
                        return;
                    }

                    if (this.isDeliveryInstallation && !this.showOrder) {
                        if (this.valorderItemList == null || this.valorderItemList.length <= 0) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoCheckOrder", "请至少勾选一条订单明细"));
                            return;
                        }
                        this.isCreateBtnDisable = true;
                        rtcrm.layerLoading();
                        //送装一体创建前校验方法
                        this.CheckDeliveryInstallation().then(resp => {
                            var checkResp = JSON.parse(resp.output)
                            if (checkResp.code == 200) {
                                this.DeliveryInstallationCreateWorkorder();
                            }
                            else {
                                rtcrm.alertDialog(checkResp.msg);
                            }
                        }).catch(e => {
                            //打开创建按钮
                            this.isCreateBtnDisable = false;
                            rtcrm.closeLayerLoading();
                            rtcrm.alertDialog(e.message);
                        }).finally(() => {
                            rtcrm.closeLayerLoading();
                        });
                        return;
                    }
                    if (this.timeoutId) {
                        clearTimeout(this.timeoutId);
                    }
                    this.isCreateBtnDisable = true;
                    rtcrm.layerLoading();
                    this.timeoutId = setTimeout(async () => {
                        if (this.showOrder) {
                            //rtcrm.layerLoading();
                            //无串号建单

                            var goodsidCreateWorkorderModel = {
                                new_servicemode: this.new_servicemode,
                                new_type: this.new_type,
                                new_invoice_num: this.new_invoice_num,
                                new_invoice_time: this.new_invoice_time,
                                goodsid: this.suppliername,
                                new_returntype: this.new_returntype,//退货类型
                                new_isprototype: this.new_isprototype,//是否样机
                                new_send_questionnaire: this.new_send_questionnaire,//同意售后回访
                                customer_name: this.customer_name,
                                new_phone: this.new_phone,
                                new_phone2: this.new_phone2,
                                email: this.email,
                                zip_code: this.zip_code,
                                address: this.address,
                                textarea: this.textarea,
                                texterro: this.texterro,
                                new_srv_station: this.new_station?.new_code ?? "",
                                serviceSelectInvoice: this.serviceSelectInvoice,
                                new_station_id: this.new_station?.id ?? "",
                                customer_type: this.customertype,
                                new_country_id: this.blurform.new_country_id.id,
                                new_province_id: this.blurform.new_province_id.id,
                                new_city_id: this.blurform.new_city_id.id,
                                new_county_id: this.blurform.new_county.id,
                                new_street_id: this.blurform.new_street.id,
                                isIncident: this.isIncident
                            };

                            rtcrm.invokeHiddenApiAsync("new_service", "CreateWorkorder/GoodsidCreateWorkorder", {
                                goodsidCreateWorkorderModel: goodsidCreateWorkorderModel, caseid: this.caseid,
                                isDeliveryInstallation: this.isDeliveryInstallation
                            }).then(res => {
                                console.log(res);
                                rtcrm.closeLayerLoading();
                                if (res) {
                                    return res;
                                }
                            }).then(res => {
                                setTimeout(() => {
                                    this.orderReturn(res);
                                }, 500);
                            }).catch(e => {
                                rtcrm.closeLayerLoading();
                                rtcrm.alertDialog(e.message);
                            }).finally(() => {
                                rtcrm.closeLayerLoading();
                            });
                        } else {
                            if (this.showBlurOrder) {
                                this.blurCreate();
                            }
                            else {
                                //rtcrm.closeLayerLoading();
                                var suppliername = "";
                                var ifinvoice = false;
                                if (this.serviceSelectInvoice == 1) {
                                    ifinvoice = true;
                                }
                                var isreadnum = false;
                                if (this.new_isreadnum == 1) {
                                    isreadnum = true;
                                }
                                //查无订单
                                var sn = '';
                                var checkstation = 0;
                                if (this.stations != null) {
                                    checkstation = this.stations.filter(item => item.checked);
                                }
                                if (checkstation == 0) {
                                    sn = suppliername;
                                }
                                //创建Model赋值
                                //await this.SetCreateModelByConfirmBtn(ifinvoice,isreadnum);
                                //当时订单查询的时候，不自动勾选数据校验处理
                                if (this.showNumber && this.orderDataDetail["order_id"] != "" && this.afterSelectType) {
                                    //创建数据预处理
                                    await this.PreCreateDataByHandleSelect();
                                    //await this.getOrderInfoBySelectType();
                                }
                                let setModelTimer = setInterval(async() => {
                                    //检查是否所有数据检验完毕
                                    if (this.ifCheckQueryDone) {
                                        result = true;
                                        console.log('checkIfPreCheckDone');
                                        clearInterval(setModelTimer);
                                        setModelTimer = null;
                                        if (this.selectClickModel["checkOrderError" + this.selectCount] != null
                                            && this.selectClickModel["checkOrderError" + this.selectCount] != "") {
                                            rtcrm.closeLayerLoading();
                                            rtcrm.alertDialog(this.selectClickModel["checkOrderError" + this.selectCount]);
                                            this.isCreateBtnDisable = false;
                                            clearInterval(setModelTimer);
                                            setModelTimer = null;
                                            return;
                                        }
                                        this.selectClickModel["checkOrderError" + this.selectCount] = "";
                                        //进行赋值操作
                                        this.createOrderModel["serviceOrder"]["isparallelgoods"] = this.afterSelectTypeCheckOrder["isparallelgoods"];
                                        this.createOrderModel["serviceOrder"]["salechannel"] = this.afterSelectTypeCheckOrder["salechannel"];
                                        this.createOrderModel["serviceOrder"]["salechanneltype"] = this.afterSelectTypeCheckOrder["salechanneltype"];
                                        this.createOrderModel["serviceOrder"]["orderformid"] = this.afterSelectTypeCheckOrder["orderformid"];
                                        this.createOrderModel["returnImeiDataList"] = this.afterSelectTypeCheckOrder["returnImeiDataList"];
                                        //发票时间不为空，再请求一次获取最新的逻辑
                                        if(this.new_invoice_time != null && this.new_invoice_time != '')
                                        {
                                            //重新将发票时间赋值重新计算
                                            this.queryWorkOrder["orderDataDetail"]["invoiceTime"] = this.new_invoice_time;
                                            var newImeiDataList = null;
                                            //是否订单号查询
                                            if (this.showNumber && this.orderDataDetail["order_id"] != "") {
                                                //勾选明细需要查询Imei信息
                                                this.queryWorkOrder["isvalorder"] = "true"; //是否勾选商品
                                                newImeiDataList = await this.getImeiMaintenancesByInvoiceTime(this.queryWorkOrder, this.orderDataDetail, this.valorderItemList, this.getimeitime);
                                            }
                                            else {
                                                this.queryWorkOrder["isvalorder"] = "false"; //是否勾选商品
                                                newImeiDataList = await this.getImeiMaintenancesByInvoiceTime(this.queryWorkOrder, this.orderDataDetail,[],null);
                                            }
                                            var maintenancesRlt = JSON.parse(newImeiDataList.output);
                                            if (maintenancesRlt.code == 200) {
                                                result = maintenancesRlt.data;
                                                this.maintenances = result.returnImeiDatalist;//IMEI维保信息
                                                this.IMEIServiceReturnData = result.IMEIServiceReturnData;//imei服务接口
                                                this.ActiveReturnData = result.ActiveReturnData;//imei激活服务接口
                                                var checkOrder = await this.checkOrderByCreate();
                                                if (checkOrder.code == 200) {
                                                    if (this.afterSelectType) {
                                                        var checkOrderRlt = checkOrder.data;
                                                        this.createOrderModel["returnImeiDataList"] = checkOrderRlt["returnImeiDataList"];
                                                    }
                                                }
                                            }
                                        }
                                        this.createOrderModel["searchGoodsfileList"] = this.afterSelectTypeCheckOrder["searchGoodsfileList"];
                                        //B客户控制不赋值反馈人相关信息
                                        if (this.afterSelectTypeCheckOrder["salechanneltype"] != 2) {
                                            if (this.caseid != null && this.createOrderModel["incdentModel"] != null) {
                                                //反馈人电话
                                                if (this.createOrderModel["incdentModel"]["new_phone"]) {
                                                    this.createOrderModel["serviceOrder"]["feedbacktel"] = this.createOrderModel["incdentModel"]["new_phone"];
                                                }
                                                //邮箱
                                                if (this.createOrderModel["incdentModel"]["emailaddress"]) {
                                                    this.createOrderModel["serviceOrder"]["email"] = this.createOrderModel["incdentModel"]["emailaddress"];
                                                }
                                                //反馈人
                                                if (this.createOrderModel["incdentModel"]["customeridName"]) {
                                                    this.createOrderModel["serviceOrder"]["contactName"] = this.createOrderModel["incdentModel"]["customeridName"];
                                                }
                                            }
                                            else {
                                                //反馈人电话
                                                if (this.orderDataDetail["tel_c"]) {
                                                    this.createOrderModel["serviceOrder"]["feedbacktel"] = this.orderDataDetail["tel_c"];
                                                }
                                                //邮箱
                                                if (this.orderDataDetail["email_c"]) {
                                                    this.createOrderModel["serviceOrder"]["email"] = this.orderDataDetail["email_c"];
                                                }
                                                //反馈人
                                                if (this.orderDataDetail["consignee_c"]) {
                                                    this.createOrderModel["serviceOrder"]["contactName"] = this.orderDataDetail["consignee_c"];
                                                }
                                            }
                                        }
                                        else {
                                            if (this.caseid != null && this.createOrderModel["incdentModel"] != null) {
                                                //反馈人电话
                                                if (this.createOrderModel["incdentModel"]["new_phone"]) {
                                                    this.createOrderModel["serviceOrder"]["feedbacktel"] = this.createOrderModel["incdentModel"]["new_phone"];
                                                }
                                                //邮箱
                                                if (this.createOrderModel["incdentModel"]["emailaddress"]) {
                                                    this.createOrderModel["serviceOrder"]["email"] = this.createOrderModel["incdentModel"]["emailaddress"];
                                                }
                                                //反馈人
                                                if (this.createOrderModel["incdentModel"]["customeridName"] != null) {
                                                    this.createOrderModel["serviceOrder"]["contactName"] = this.createOrderModel["incdentModel"]["customeridName"];
                                                }
                                            }
                                        }
                                        this.createOrderModel["serviceOrder"]["salechanneltype"] = this.afterSelectTypeCheckOrder["salechanneltype"];
                                        this.createOrderModel["serviceOrder"]["ifinvoice"] = ifinvoice;
                                        this.createOrderModel["serviceOrder"]["isreadnum"] = isreadnum;
                                        this.createOrderModel["serviceOrder"]["invoiceTime"] = this.new_invoice_time;
                                        this.createOrderModel["serviceOrder"]["invoice_num"] = this.new_invoice_num;
                                        this.createOrderModel["serviceOrder"]["new_type"] = this.new_type;
                                        this.createOrderModel["serviceOrder"]["new_servicemode"] = this.new_servicemode;
                                        this.createOrderModel["serviceOrder"]["new_returntype"] = this.new_returntype;
                                        this.createOrderModel["serviceOrder"]["new_isprototype"] = this.new_isprototype;//是否样机
                                        this.createOrderModel["serviceOrder"]["caseid"] = this.caseid;
                                        this.createOrderModel["serviceOrder"]["new_send_questionnaire"] = this.new_send_questionnaire//同意售后回访
                                        var parameters = {};
                                        //无订单的串号创建
                                        var noOrderSerialNumberCreate = false;
                                        if ((this.orderDataDetail["order_id"] == "" || this.orderDataDetail["order_id"] == null) &&
                                            this.maintenances.length > 0 && (this.maintenances[0]["new_sn"] != null && this.maintenances[0]["new_sn"] != "")) {
                                            noOrderSerialNumberCreate = true;
                                            parameters = {
                                                new_sn: sn,//SN
                                                new_type: this.new_type,//服务类型
                                                new_servicemode: this.new_servicemode,//服务方式
                                                new_returntype: this.new_returntype,//退货类型
                                                new_ifinvoice: ifinvoice,//是否有发票
                                                new_invoice_num: this.new_invoice_num,//发票号码
                                                invoiceTime: this.new_invoice_time,//发票时间
                                                new_isprototype: this.new_isprototype,//是否样机
                                                new_activeservicerecordid: this.new_activeservicerecordid,//已选主动服务id
                                                orderDataDetail: this.orderDataDetail,//订单详情
                                                orderItemList: this.valorderItemList,//勾选的商品明细
                                                allOrderItemList: this.stations,//全部商品明细
                                                searchDate: this.searchDate,//区域国家省市县显示
                                                new_isreadnum: isreadnum,//是否支持读取串号
                                                returnImeiDatalist: this.maintenances,//imei维保返回
                                                IMEIServiceReturnData: (this.IMEIServiceReturnData == null || this.IMEIServiceReturnData.length < 1) ? null : this.IMEIServiceReturnData,//imei服务接口
                                                ActiveReturnData: (this.ActiveReturnData == null || this.ActiveReturnData.length < 1) ? null : this.ActiveReturnData,//imei激活服务接口
                                                new_send_questionnaire: this.new_send_questionnaire,//同意售后回访
                                            };
                                        }
                                        console.log(this.caseid);
                                        //退货----订单详情中未勾选并且是赠品的进行提醒
                                        var flagType = false;
                                        if (this.new_type == 3) {
                                            for (var item of this.stations) {
                                                if (!item.checked && item.new_iszengpin) {
                                                    flagType = true;
                                                    rtcrm.closeLayerLoading();
                                                    rtcrm.confirmDialog(this.$t("CreateWorkorder_iszengpin", "请确认是否已经勾选赠品？"), () => {
                                                        if (noOrderSerialNumberCreate) {
                                                            this.createSerialNumber(parameters);
                                                        }
                                                        else {
                                                            this.IsChechServiceType().then(resp=>{
                                                                var checkResp = JSON.parse(resp.output)
                                                                if(checkResp.code == 200)
                                                                {
                                                                    this.newCreateSerialNumber();
                                                                }
                                                                else{
                                                                    rtcrm.alertDialog(checkResp.msg);
                                                                }
                                                            });
                                                        }
                                                    });
                                                    break;
                                                } else {
                                                    flagType = false;
                                                }
                                            }
                                            if (!flagType) {
                                                if (noOrderSerialNumberCreate) {
                                                    this.createSerialNumber(parameters);
                                                }
                                                else {
                                                    this.newCreateSerialNumber();
                                                }
                                            }
                                        } else {
                                            if (noOrderSerialNumberCreate) {
                                                this.createSerialNumber(parameters);
                                            }
                                            else {
                                                this.newCreateSerialNumber();
                                            }
                                        }

                                    }
                                    else {
                                        console.log('ifPreCheckDone checking...');
                                    }
                                }, 500);
                                rtcrm.closeLayerLoading();
                                // 3秒后停止执行
                                setTimeout(() => {
                                    if (setModelTimer != null) {
                                        this.isCreateBtnDisable = false;
                                        rtcrm.alertDialog("Time Out,Please try Again!");
                                        //保存数据异常埋点
                                        // var aiLogDataModel = new top.AILogging.aiLogDataModelV2(this.requestId, "ServiceOrder", "ServiceOrder", "ConfirmRibbon",
                                        //     0, 'ConfirmRibbon_TimeOut', 0, new Date().getUTCDate());
                                        // this.writeLog(new Date(), null, aiLogDataModel, 2);
                                    }
                                    rtcrm.closeLayerLoading();
                                    clearInterval(setModelTimer);
                                    setModelTimer = null;
                                }, 40000);
                            }
                        }
                    }, 500)
                },
                //保存校验
                async IsChechServiceType() {
                    var para = {
                        inputPara: JSON.stringify({
                                workOrderSearchRetun:this.queryWorkOrder["workOrderSearchRetun"],
                                orderItemList:this.valorderItemList,
                                allOrderItemList:this.orderItemList,
                                getImeiTimeList:(this.getimeitime == null || this.getimeitime.length < 1) ? [] : [this.getimeitime],
                                returnImeiDataList:this.maintenances,
                                srvworkerModel:this.queryWorkOrder["srvworkerModel"],
                                caseid:this.caseid,
                                uniquecode:this.maintenances.length > 0 ? this.maintenances[0]["new_sn"] : "",
                                showNumber:this.showNumber,
                                new_returntype:this.new_returntype,
                                new_type:this.new_type,
                                new_servicemode:this.new_servicemode,
                            afterSelectType:this.afterSelectType,
                            isDeliveryInstallation: this.iDeliveryInstallation,
                            IMEIServiceReturnData: (this.IMEIServiceReturnData == null || this.IMEIServiceReturnData.length < 1) ? [] : [this.IMEIServiceReturnData],//imei服务接口
                            ActiveReturnData: (this.ActiveReturnData == null || this.ActiveReturnData.length < 1) ? [] : [this.ActiveReturnData]//imei激活服务接口
                        }),
                        funcType: "ChechServiceType",
                        tempPara1:this.requestId
                    };
                    var resp = XM.ActionAsync('new_ServiceOrderQueryAction', para);
                    return resp;
                },
                //更新服务单
                async UpdateOrderByCreate() {
                    var para = {
                        inputPara: JSON.stringify(this.createOrderModel),
                        funcType: "UpdateOrderByCreate",
                        tempPara1:this.requestId
                    };
                    var resp = XM.ActionAsync('new_ServiceOrderQueryAction', para);
                    return resp;
                },
                //更新特批单
                async UpdateSpecialapply() {
                    var para = {
                        inputPara: JSON.stringify(this.createOrderModel),
                        funcType: "UpdateSpecialapply",
                        tempPara1:this.requestId
                    };
                    var resp = XM.ActionAsync('new_ServiceOrderQueryAction', para);
                    return resp;
                },
                //送装一体查询
                CheckDeliveryInstallation() {
                    var para = {
                        inputPara: JSON.stringify({
                            isDeliveryInstallation: this.isDeliveryInstallation,
                            //勾选明细
                            orderItemList: this.valorderItemList,
                            allOrderItemList: this.orderItemList,
                            orderDataDetail: this.orderDataDetail
                        }),
                        funcType: "CheckDeliveryInstallation",
                        tempPara1:this.requestId
                    };
                    var resp = XM.ActionAsync('new_ServiceOrderQueryAction', para);
                    return resp;
                },
                //送装一体建单
                DeliveryInstallationCreateWorkorder() {

                    var DeliveryInstallationCreateModel = {
                        new_servicemode: this.new_servicemode,
                        new_type: this.new_type,
                        new_invoice_num: this.new_invoice_num,
                        new_invoice_time: this.new_invoice_time,
                        new_isprototype: this.new_isprototype,//是否样机
                        new_send_questionnaire: this.new_send_questionnaire,//同意售后回访
                        order_id: this.orderDataDetail.order_id,
                        customer_name: this.customer_name,
                        new_phone: this.new_phone,
                        new_phone2: this.new_phone2,
                        email: this.email,
                        zip_code: this.zip_code,
                        address: this.address,
                        textarea: this.textarea,
                        texterro: this.texterro,
                        new_srv_station: this.new_station["new_code"],
                        new_station_id: this.new_station.id,
                        customer_type: this.customertype,
                        new_country_id: this.blurform.new_country_id.id,
                        new_province_id: this.blurform.new_province_id.id,
                        new_city_id: this.blurform.new_city_id.id,
                        new_county_id: this.blurform.new_county.id,
                        isIncident: this.isIncident
                    }
                    var para = {
                        inputPara: JSON.stringify({
                            isDeliveryInstallation: this.isDeliveryInstallation,
                            DeliveryInstallationCreateWorkorderModel: DeliveryInstallationCreateModel,
                            //勾选明细
                            orderItemList: this.valorderItemList,
                            caseid: this.caseid,
                            orderDataDetail: this.orderDataDetail
                        }),
                        funcType: "DeliveryInstallationCreateWorkorder",
                        tempPara1:this.requestId
                    };
                    XM.ActionAsync('new_ServiceOrderQueryAction', para).then(resp => {
                        var checkResp = JSON.parse(resp.output);
                        if (checkResp.code == 200) {
                            setTimeout(() => {
                                this.getWorkOrderInfo(checkResp.data).then(s => {
                                    this.orderReturn(checkResp.data);
                                });
                            }, 5000);
                        }
                        else {
                            rtcrm.alertDialog(checkResp.msg);
                        }
                    }).catch(e => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                        rtcrm.alertDialog(e);
                    }).finally(() => {
                        rtcrm.closeLayerLoading();
                    });
                },

                //XMS建单
                XMSCreateWorkorder() {
                    if ((this.valorderItemList == null || this.valorderItemList.length <= 0) && this.activeName == "first" && this.isDeliveryInstallation && !this.showOrder) {
                        rtcrm.alertDialog(this.$t("CreateWorkorder_CreateNoCheckOrder", "请至少勾选一条订单明细"));
                        rtcrm.closeLayerLoading();
                        return;
                    }
                    var orderid = "";
                    var goodsId = "";
                    if (this.activeName == "first") {
                        orderid = this.orderDataDetail.order_id;
                    } else {
                        goodsId = this.suppliername;
                    }

                    var XMSCreateModel = {
                        new_servicemode: this.new_servicemode,
                        new_type: this.new_type,
                        new_invoice_num: this.new_invoice_num,
                        new_invoice_time: this.new_invoice_time,
                        new_expvisitdate: this.new_expvisitdate,
                        new_isprototype: this.new_isprototype,//是否样机
                        new_send_questionnaire: this.new_send_questionnaire,//同意售后回访
                        isIncident: this.isIncident,
                        customer_name: this.customer_name,
                        new_phone: this.new_phone,
                        new_phone2: this.new_phone2,
                        email: this.email,
                        zip_code: this.zip_code,
                        address: this.address,
                        textarea: this.textarea,
                        texterro: this.texterro,
                        new_srv_station: this.new_station["new_code"],
                        serviceSelectInvoice:this.serviceSelectInvoice,
                        new_station_id: this.new_station.id,
                        new_returntype: this.new_returntype,
                        customer_type: this.customertype,
                        new_country_id: this.blurform.new_country_id.id,
                        new_province_id: this.blurform.new_province_id.id,
                        new_city_id: this.blurform.new_city_id.id,
                        new_county_id: this.blurform.new_county.id,
                        new_street_id: this.blurform.new_street.id,
                        goodsid: goodsId,
                        order_id: orderid
                    }
                    // 处理时间
                    if (XMSCreateModel.new_invoice_time) {
                        XMSCreateModel.new_invoice_time = this.localDateToUTC(XMSCreateModel.new_invoice_time);
                    }
                    var para = {
                        inputPara: JSON.stringify({
                            isDeliveryInstallation: this.isDeliveryInstallation,
                            XMSCreateModel: XMSCreateModel,
                            //勾选明细
                            orderItemList: this.valorderItemList,
                            caseid: this.caseid,
                            allOrderItemList: this.orderItemList,
                            orderDataDetail: this.orderDataDetail
                        }),
                        funcType: "XMSCreateWorkorder",
                        tempPara1: this.requestId
                    };
                    return XM.ActionAsync('new_ServiceOrderQueryAction', para).then(resp => {
                            var checkResp = JSON.parse(resp.output);
                            if (checkResp.code == 200) {
                                return new Promise((resolve) => {
                                    setTimeout(() => {
                                        this.getWorkOrderInfo(checkResp.data).then(s => {
                                            if (this.caseid && this.new_servicemode) {
                                                Xrm.WebApi.updateRecord(`incident`, this.caseid, {
                                                    new_servicemode: parseInt(this.new_servicemode + '', 10)
                                                });
                                            }
                                            this.orderReturn(checkResp.data);
                                            resolve();
                                        })
                                            .catch(e => {
                                                rtcrm.alertDialog(e.message);
                                                resolve(); // 或 reject(e) 传递错误到外层
                                            });
                                    }, 5000);
                                });
                           
                            }
                            else {
                                rtcrm.alertDialog(checkResp.msg);
                                return Promise.resolve(); 
                            }
                        }).catch(e => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.alertDialog(e.message);
                        }).finally(() => {
                            rtcrm.closeLayerLoading();
                        });
                },
                //格式化本地时间
                formatLocalTime(date) {
                    const pad = (n) => n.toString().padStart(2, '0');
                    return `${date.getFullYear()}-${pad(date.getMonth()+1)}-${pad(date.getDate())} ` +
                        `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
                },
                
                newCreateSerialNumber() {
                    if(this.isIncident){
                        var XMSCreateModel = {
                            new_servicemode: this.new_servicemode,
                            new_type: this.new_type,
                            new_invoice_num: this.new_invoice_num,
                            new_invoice_time: this.new_invoice_time,
                            new_isprototype: this.new_isprototype,//是否样机
                            new_send_questionnaire: this.new_send_questionnaire,//同意售后回访
                            customer_name: this.customer_name,
                            new_phone: this.new_phone,
                            new_phone2: this.new_phone2,
                            email: this.email,
                            zip_code: this.zip_code,
                            address: this.address,
                            textarea: this.textarea,
                            texterro: this.texterro,
                            new_srv_station: this.new_station["new_code"],
                            serviceSelectInvoice:this.serviceSelectInvoice,
                            new_station_id: this.new_station.id,
                            new_returntype: this.new_returntype,
                            customer_type: this.customertype,
                            new_country_id: this.blurform.new_country_id.id,
                            new_province_id: this.blurform.new_province_id.id,
                            new_city_id: this.blurform.new_city_id.id,
                            new_county_id: this.blurform.new_county.id,
                            new_street_id: this.blurform.new_street.id
                        }
                        this.createOrderModel["xmsCreateModel"] = XMSCreateModel;
                    }
                    this.createOrderModel["isIncident"] = this.isIncident;
                    var para = {
                        inputPara: JSON.stringify(this.createOrderModel),
                        funcType: "CreateOrderByCreate",
                        tempPara1:this.requestId
                    };
                    //串号建单
                    rtcrm.layerLoading();
                    XM.ActionAsync('new_ServiceOrderQueryAction', para).then(res=>{
                        console.log(res);
                        //rtcrm.closeLayerLoading();
                        if (res) {
                            return res;
                        }
                    }).then(res => {
                        setTimeout(async () => {
                            var createResp = JSON.parse(res.output)
                            if (createResp.code == 200) {
                                var createResultId = createResp.data;
                                this.createOrderModel["serviceOrder"]["new_srv_workorderid"] = createResultId;
                                var ifAllSuccess = true;
                                //this.UpdateOrderByCreate();//更新服务单
                                //this.UpdateSpecialapply();//更新特批单
                                // this.checkWaybillOpen(createResultId);
                                console.log('创建开始时间'+new Date());
                                var primises = [];
                                //primises.push(this.UpdateOrderByCreate());//添加更新服务单
                                //primises.push(this.UpdateSpecialapply());//添加更新特批单
                                primises.push(this.getWorkOrderInfo(createResultId));//添加服务单据
                                Promise.all(primises).then(res => {
                                    res.forEach((data, index) => {
                                        var respData = null;
                                        if(data["output"])
                                        {
                                            respData = JSON.parse(data["output"]);
                                        }
                                        else{
                                            respData = data;
                                        }
                                        if (respData != null && respData["code"] && respData["code"] != 200) {
                                            ifAllSuccess = false
                                            rtcrm.alertDialog(respData.msg);
                                            return;
                                        }
                                        if (ifAllSuccess && index == (primises.length - 1)) {
                                            if (this.caseid && this.createOrderModel["serviceOrder"]["new_servicemode"]) {
                                                Xrm.WebApi.updateRecord(`incident`, this.caseid, {
                                                    new_servicemode: parseInt(this.createOrderModel["serviceOrder"]["new_servicemode"] + '', 10)
                                                });
                                            }
                                            this.orderReturn(createResultId);
                                            console.log('Create Order Success')
                                            console.log('创建结束时间' + new Date());
                                        }
                                    });
                                    rtcrm.closeLayerLoading();
                                }, msg => {
                                    console.log('Create Order Error:', msg)
                                })
                            } else {
                                rtcrm.closeLayerLoading();
                                rtcrm.alertDialog(createResp.msg);
                            }
                        }, 100);
                    }).catch(e => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                        rtcrm.alertDialog(e);
                    }).finally(() => {
                        rtcrm.closeLayerLoading();
                    });
                },
                createSerialNumber(parameters) {
                    //串号建单
                    rtcrm.layerLoading();
                    rtcrm.invokeHiddenApiAsync("new_service", "CreateWorkorder/CreateWorkorder", { WorkorderModel: parameters, caseid: this.caseid }).then(res => {
                        console.log(res);
                        rtcrm.closeLayerLoading();
                        if (res) {
                            return res;
                        }
                    }).then(res => {
                        if (this.caseid) {
                            Xrm.WebApi.updateRecord(`incident`, this.caseid, {
                                new_servicemode: parseInt(parameters.new_servicemode, 10)
                            });
                        }
                        setTimeout(() => {
                            this.getWorkOrderInfo(res).then(s => {
                                this.orderReturn(res);
                            });
                        }, 500);
                    }).catch(e => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                        rtcrm.alertDialog(e.message);
                    }).finally(() => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                    });
                },
                orderReturn: function (res) {
                    //传入id进行上传附件
                    //var uploadFile = document.getElementById("upload");
                    //if (uploadFile.files.length > 0 && this.pleaseuploadfile) {
                    //    this.uploadInvoice(res);
                    //}
                    if (this.fileList.length > 0) {
                        this.uploadEnclosure(res);
                    }
                    this.checkWaybillOpen(res);
                    //如果是门店叫号的页面，需要回写工单到门店叫号序列
                    if (parent.StoreCallNumberWriteBackWorkOrder) {
                        parent.StoreCallNumberWriteBackWorkOrder(res)
                    }
                },
                //历史工单跳转按钮
                jumpRibbon: function (new_srv_workorderid) {
                    try {
                        if (new_srv_workorderid != null) {
                            var windowOptions = { openInNewWindow: true };
                            Xrm.Utility.openEntityForm("new_srv_workorder", new_srv_workorderid, null, windowOptions);
                        }
                    } catch (e) {
                        rtcrm.alertDialog(e.message);
                    }
                },
                checkWaybillOpen:async function (id) {
                    //console.log(result);
                    //无串号创建查询
                    if(this.showOrder)
                    {
                        await this.getWorkOrderInfo(id);
                    }
                    var result = this.getWorkOrder;
                    if (result.length > 0) {
                        let new_id = result[0] ? result[0]['c.new_id'] : '';
                        let new_servicemode = result[0] ? result[0]['new_servicemode'] : '';
                        let new_dealstatus = result[0] ? result[0]['new_dealstatus'] : '';
                        let new_country_id = result[0]?._new_country_id_value;
                        //印尼、寄修、待收货，自动弹出创建运单页面
                        if (new_id == '254391' && new_servicemode == 3 && new_dealstatus == 2 && new_country_id) {
                            this.waybill(id);
                        } else {
                            this.finallyEvent(id);
                        }
                    }
                    else {
                        rtcrm.alertDialog('查询工单信息失败');
                    }
                },
                //获取服务单据信息
                getWorkOrderInfo: async function (id) {
                    var fetchXml = null;
                    if (this.caseid == null) {
                        fetchXml = `<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                          <entity name='new_srv_workorder'>
                                                            <attribute name='new_servicemode' />
                                                            <attribute name='new_dealstatus' />
                                                            <attribute name='new_country_id' />
                                                            <link-entity name='new_srv_station' to='new_station_id' from='new_srv_stationid' alias='b' link-type='inner'>
                                                              <link-entity name='new_country' to='new_country_id' from='new_countryid' alias='c' link-type='inner'>
                                                                <attribute name='new_id' />
                                                              </link-entity>
                                                            </link-entity>
                                                            <filter>
                                                              <condition attribute='new_srv_workorderid' operator='eq' value='` + id + `' />
                                                            </filter>
                                                          </entity>
                                                        </fetch>`;
                    } else {
                        fetchXml = `<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                          <entity name='new_srv_workorder'>
                                                            <attribute name='new_servicemode' />
                                                            <attribute name='new_dealstatus' />
                                                            <attribute name='new_country_id' />
                                                            <filter>
                                                              <condition attribute='new_srv_workorderid' operator='eq' value='` + id + `' />
                                                            </filter>
                                                          </entity>
                                                        </fetch>`;
                    }
                    var result = await rtcrm.fetchAsync('new_srv_workorders', fetchXml, true);
                    this.getWorkOrder = result;
                    return result;
                },
                waybill: function (id) {
                    if (rtcrm.isNullOrWhiteSpace(id)) {
                        this.finallyEvent(id);
                        return;
                    }

                    id = id.replace('{', '').replace('}', '');
                    var pageInput = {
                        pageType: "webresource",
                        webresourceName: "new_/Service/WayBill.html",
                        data: encodeURIComponent("id=" + id),
                    }
                    var navigationOptions = {
                        title: $t("waybill.information", "运单信息"),
                        target: 2,
                        width: 800,
                        height: 850,
                        position: 1
                    };
                    var that = this;
                    window.top.Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                        function success(res) {
                            rtcrm.refresh();
                            that.finallyEvent(id);
                        },
                        function error(e) {
                            errorCallback(e);
                            that.finallyEvent(id);
                        });
                },
                finallyEvent: function (id) {
                    rtcrm.closeLayerLoading();
                    var windowOptions = { openInNewWindow: false };
                    var returnValue = {
                        new_srv_workorderid: id,
                    }
                    parent.window.returnValue = returnValue;
                    if (!this.caseid) {
                        Xrm.Utility.openEntityForm("new_srv_workorder", id, null, windowOptions);
                    }
                    setTimeout(function () { window.close(); }, 500);
                },
                //模糊建单选项赋值
                getBlurPickList: function () {
                    var userid = Xrm.Utility.getGlobalContext().userSettings.userId.replace('{', '').replace('}', '');
                    var queryWorkers = "new_srv_workers?$select=new_srv_workerid&$expand=new_srv_station_id($select=new_srctype,new_way)&$filter=_new_systemuser_id_value eq " + userid;
                    var reqobjWorkers = rtcrm.retrieve(queryWorkers, true);
                    if (reqobjWorkers && reqobjWorkers.value.length > 0) {
                        //服务站服务方式
                        if (!this.caseid) {
                            var new_way = reqobjWorkers.value[0].new_srv_station_id.new_way;
                            var new_wayLabel = reqobjWorkers.value[0].new_srv_station_id["<EMAIL>"];
                            this.blurServicemodeOption = [];
                            var arrayWayLabel = new_wayLabel.split(';');
                            var arrayWay = new_way.split(',');
                            for (var i = 0; i < arrayWay.length; i++) {
                                this.blurServicemodeOption.push({
                                    id: arrayWay[i].toString(),
                                    name: arrayWayLabel[i].toString()
                                });
                            }
                        }
                        //新增判断，如果服务方式只有一个可选项，则默认赋值
                        if (this.blurServicemodeOption != null && this.blurServicemodeOption.length == 1) {
                            this.blurform.new_servicemode = this.blurServicemodeOption[0].id;
                        }
                        if (!this.caseid) {
                            //服务站服务类型
                            var new_srctype = reqobjWorkers.value[0].new_srv_station_id.new_srctype;
                            var new_srctypeLabel = reqobjWorkers.value[0].new_srv_station_id["<EMAIL>"];
                            this.blurTypeOption = [];
                            var arraySrctypeLabel = new_srctypeLabel.split(';');
                            var arraySrctype = new_srctype.split(',');
                            for (var i = 0; i < arraySrctype.length; i++) {
                                //仅限维修检测
                            if (arraySrctype[i].toString() == "1" || arraySrctype[i].toString() =="4") {
                                    this.blurTypeOption.push({
                                        id: arraySrctype[i].toString(),
                                        name: arraySrctypeLabel[i].toString()
                                    });
                                }
                            }
                        }
                        //新增判断，如果服务类型只有一个可选项，则默认赋值
                        if (this.blurTypeOption != null && this.blurTypeOption.length == 1) {
                            this.blurform.new_type = this.blurTypeOption[0].id;
                        }
                    }
                },
                //模糊建单校验
                blurCheck: function () {
                    if (this.showBlurOrder) {
                        if (!this.blurform.new_goodsfiles_id || this.blurform.new_goodsfiles_id.id == undefined) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty1", "商品名称必填！"));
                            return false;
                        }
                        if (!this.blurform.new_faultdescription) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty2", "故障描述必填！"));
                            return false;
                        }
                        if (!this.blurform.customername) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty3", "客户姓名必填！"));
                            return false;
                        }
                        if (!this.blurform.new_email) {
                            rtcrm.alertDialog(this.$t("blurform4959.emailmsg", "客户邮箱必填！"));
                            return false;
                        }
                        var emailRule = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if (!emailRule.test(this.blurform.new_email)) {
                            rtcrm.alertDialog($t("incident.ErrorEmailFormat", "邮箱格式错误!"));
                            return false;
                        }
                        if (!this.blurform.new_country_id || this.blurform.new_country_id.id == undefined) {
                            rtcrm.alertDialog(this.$t("warranty.CountryRegionRequired", "国家/区域必填！"));
                            return false;
                        }
                        if (!this.blurform.new_servicemode) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_SelectServicemode", "请选择服务方式！"));
                            return false;
                        }
                        if (!this.blurform.new_type) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.selecttype", "请选择服务类型！"));
                            return false;
                        }
                        if (this.blurform.new_type == 3 && !this.blurform.new_returntype) {
                            rtcrm.alertDialog(this.$t("CreateWorkorder_selectreturntype", "请选择退货类型!"));
                            return;
                        }
                        if (!this.blurform.new_send_questionnaire) {
                            rtcrm.alertDialog(this.$t("new_srv_handing.questionnaireeempty", "同意售后回访必填！"));
                            return false;
                        }
                        if (this.blurform.new_ifinvoice == 1) {
                            if (!this.blurform.new_invoice_time) {
                                rtcrm.alertDialog(this.$t("new_srv_handing.invoicetimeempty", "发票时间必填！"));
                                return false;
                            }
                        }
                    }
                    return true;
                },
                //客户Change赋值
                accountChange: function () {
                    if (this.blurform.new_customerid?.id != null) {
                        var queryAccount = "accounts(" +
                            this.blurform.new_customerid?.id + ")?$select=new_address,_new_city_value,_new_country_value,new_email,name,new_account_tel,_new_county_value,_new_province_id_value"
                        var reqobjAccount = rtcrm.retrieve(queryAccount, true);
                        this.blurform.customername = reqobjAccount["name"];
                        this.blurform.new_email = reqobjAccount["new_email"];
                        this.blurform.customerphone = reqobjAccount["new_account_tel"];
                        this.blurform.customeraddress = reqobjAccount["new_address"];
                        if (reqobjAccount["_new_country_value"] != null) {
                            this.blurform.new_country_id = {
                                id: reqobjAccount["_new_country_value"],
                                name: reqobjAccount["<EMAIL>"]
                            };
                        }
                        if (reqobjAccount["new_province_id"] != null) {
                            this.blurform.new_province_id = {
                                id: reqobjAccount["_new_province_id_value"],
                                name: reqobjAccount["<EMAIL>"]
                            };
                        }
                        if (reqobjAccount["_new_city_value"] != null) {
                            this.blurform.new_city_id = {
                                id: reqobjAccount["_new_city_value"],
                                name: reqobjAccount["<EMAIL>"]
                            };
                        }
                        if (reqobjAccount["_new_county_value"] != null) {
                            this.blurform.new_county = {
                                id: reqobjAccount["_new_county_value"],
                                name: reqobjAccount["<EMAIL>"]
                            };
                        }
                        this.initCascaderCounty();
                    }
                },
                  initCascaderCounty(){ 
                 this.cascaderhandleVisibleChange(true);
                      if (this.blurform.new_street?.id) {
                          this.countySelect = this.blurform.new_street.id.replace("{", "").replace("}", "").toLocaleLowerCase();
                      }
                      else if (this.blurform.new_county?.id) {
                          this.countySelect = this.blurform.new_county.id.replace("{", "").replace("}", "").toLocaleLowerCase() + this.suffixtext;
                      }
                      else if (this.blurform.new_city_id?.id) {
                          this.countySelect = this.blurform.new_city_id.id.replace("{", "").replace("}", "").toLocaleLowerCase() + this.suffixtext + this.suffixtext;
                      }
                      else if (this.blurform.new_province_id?.id) {
                          this.countySelect = this.blurform.new_province_id.id.replace("{", "").replace("}", "").toLocaleLowerCase() + this.suffixtext + this.suffixtext + this.suffixtext;
                      }
                  
         },
                //串号模糊建单
                blurCreate: function () {
                    this.blurform.caseid = this.caseid;
                    //串号模糊建单
                    XM.ActionAsync('new_ServiceOrderQueryAction', {
                        inputPara: JSON.stringify(this.blurform),
                        funcType: "BlurWorkOrder",
                        tempPara1: this.generateUUID()
                    }).then((res) => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                        if (res == null || res == undefined) {
                            XM.openAlertDialog(" err:null");
                            return;
                        }
                        var resultObj = JSON.parse(res["output"]);
                        if (resultObj.code == 200) {
                            if (this.caseid) {
                                Xrm.WebApi.updateRecord(`incident`, this.caseid, {
                                    new_servicemode: parseInt(this.blurform.new_servicemode, 10)
                                });
                            }
                            setTimeout(() => {
                                this.finallyEvent(resultObj.data);
                            }, 500);
                        } else {
                            XM.openAlertDialog(resultObj.msg);;
                        }
                    }).catch((err) => {
                        //打开创建按钮
                        this.isCreateBtnDisable = false;
                        rtcrm.closeLayerLoading();
                        XM.openAlertDialog(err);
                    });
                },
                //本地时间转UTC时间 localDateStr : yyyy-MM-dd
                localDateToUTC: function (localDateStr) {
                    // 1. 分割本地日期
                    const [year, month, day] = localDateStr.split('-');

                    // 2. 创建本地时间的Date对象（午夜00:00:00）
                    const localDate = new Date(
                        parseInt(year),
                        parseInt(month) - 1, // 月份0-11
                        parseInt(day),
                        0,  // 小时
                        0,  // 分钟
                        0   // 秒
                    );

                    // 3. 提取UTC时间组件
                    const utcYear = localDate.getUTCFullYear();
                    const utcMonth = String(localDate.getUTCMonth() + 1).padStart(2, '0'); // 补零
                    const utcDay = String(localDate.getUTCDate()).padStart(2, '0');
                    const utcHours = String(localDate.getUTCHours()).padStart(2, '0');
                    const utcMinutes = String(localDate.getUTCMinutes()).padStart(2, '0');
                    const utcSeconds = String(localDate.getUTCSeconds()).padStart(2, '0');

                    // 4. 拼接为UTC时间字符串
                    return `${utcYear}-${utcMonth}-${utcDay} ${utcHours}:${utcMinutes}:${utcSeconds}`;
                },
                // 新增：通过guid查new_countries表获取new_code
                getCountryCodeById: function (countryId) {
                    if (!countryId) {
                        return "";
                    }
                    var queryString = "/new_countries?$select=new_code&$filter=new_countryid eq '" + countryId + "'";
                    var result = rtcrm.retrieveMultiple(queryString, false);
                    if (result && result.length > 0) {
                        return result[0].new_code;
                    } else {
                        return "";
                    }
                },
                getRequiredCountry: function () {
                    try {
                        var queryString = "/new_systemparameters?$select=new_name,new_value&$filter=new_name eq 'CountyRequiredCountry'";
                        var systemParams = rtcrm.retrieveMultiple(queryString, false);
                        if (systemParams && systemParams.length > 0) {
                            this.systemParams.Sys_Area = systemParams[0].new_value;
                        }
                        // 获取配置的国家编码（逗号分隔的二简码）
                        var requiredCountryCodes = (this.systemParams.Sys_Area || "").split(",").map(function (item) { return item.trim(); });
                        var countryCode = this.getCountryCodeById(this.blurform.new_country_id && this.blurform.new_country_id.id);
                        this.isCountyRequired = requiredCountryCodes.indexOf(countryCode) !== -1;
                    } catch (e) {
                        console.error(e);
                    }
                },
                // 国家信息变动时的处理函数
                onCountryChange: function() {
                    // 重新获取国家编码并更新区县必填标志
                     var requiredCountryCodes = (this.systemParams.Sys_Area || "").split(",").map(function (item) { return item.trim(); });
                     var countryCode = this.getCountryCodeById(this.blurform.new_country_id && this.blurform.new_country_id.id);
                     this.isCountyRequired = requiredCountryCodes.indexOf(countryCode) !== -1;
                },
               


            }
        })
    </script>
</body>
</html>
