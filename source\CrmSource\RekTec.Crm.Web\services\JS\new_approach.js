﻿//#region 文件描述
/******************************************************************
** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved.
** 创建人   : charlestian
** 创建时间 : 2021-8-13 16:32:21
** 说明     : 处理方法
******************************************************************/
//#endregion

//选择处理方法类别，自动带出服务类型

function new_approach_sort_id_onchange() {
    if (!rtcrm.isNullOrWhiteSpace(rtcrm.getLookupId("new_approach_sort_id"))) {
        var new_approach_sort_id = rtcrm.getLookupId("new_approach_sort_id").replace("{", "").replace("}", "");
        var new_approach_sort = rtcrm.getFieldValue(new_approach_sort_id, "new_approach_sorts", "new_servicetype", true);
        if (new_approach_sort == null) return;
        rtcrm("#new_servicetype").val(new_approach_sort["new_servicetype"]);
    }
}
async function getapproachidBycategory3(resultorder) {
    var category3_id = "";
    var new_type = "";
    if (resultorder != null && resultorder != "") {
        category3_id = resultorder.split(";")[0];
        if (resultorder.split(";").length > 1) {
            new_type = resultorder.split(";")[1];
        }
    }
    var fexchapproachid = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                                                <entity name = 'new_approach_category' >\
                                                <attribute name='new_approach_categoryid' />\
                                                <attribute name='new_name' />\
                                                <attribute name='createdon' />\
                                               <attribute name='new_approach_id' />\
                                                <order attribute='new_name' descending='false' />\
                                                <filter type='and'>\
                                                  <filter type='and'>\
                                                    <condition attribute='statecode' operator='eq' value='0' />\
                                                    <condition attribute='new_ifusematerial' operator='eq' value='0' />\
                                                    <condition attribute='new_servicetype' operator='eq'    value='"+ new_type + "' />\
                                                    <filter type='or'>\
                                                      <condition attribute='new_category3_id' operator='eq'  uitype='new_category3'  value='"+ category3_id + "' />\
                                                     <condition attribute = 'new_category3_id' operator = 'null' />\
                                                    </filter ></filter ></filter ></entity ></fetch >";
    var result = await rtcrm.fetchAsync("new_approach_categories", fexchapproachid, true);
    return result;
}
function new_approachChange() {
    try {
        if (rtcrm.getLookupID('new_approach_id') != null) {
            var new_approach_id = rtcrm.getLookupID('new_approach_id').replace('{', '').replace('}', '');
            var approachFetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                                                                       <entity name = 'new_approach' >\
                                                                    <attribute name='new_approachid' />\
                                                                    <attribute name='new_name' />\
                                                                    <attribute name='new_code' />\
                                                                    <attribute name='new_approach_sort_id' />\
                                                                    <order attribute='new_name' descending='false' />\
                                                                    <filter type='and'>\
                                                                      <condition attribute='statecode' operator='eq' value='0' />\
                                                                      <condition attribute='new_approachid' operator='eq' value='"+ new_approach_id + "' />\
                                                                    </filter>\
                                                                  </entity >\
                                    </fetch >";
            var approachs = rtcrm.fetch("new_approachs", approachFetchXml, true);
            if (approachs == null || approachs.length <= 0) {
                return;
            }
            //处理方法编码
            if (approachs[0]["new_code"] != null) {
                rtcrm('#new_name').val(approachs[0]["new_code"])
            }
            //处理方法new_approach_sort_id
            var new_approachsort_id = approachs[0]["_new_approach_sort_id_value"];
            var new_approachsort_idname = approachs[0]["<EMAIL>"]
            if (new_approachsort_id != null) {
                rtcrm("#new_approach_sort_id").val(rtcrm.buildLookup(new_approachsort_id, new_approachsort_idname, "new_approach_sort"));
            }
            var fetchxml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                            <entity name = 'new_approach_category' >\
                                <attribute name='new_approach_categoryid' />\
                                <attribute name='new_name' />\
                                <attribute name='createdon' />\
                                <attribute name='new_repairlevel' />\
                                <order attribute='createdon' descending='true' />\
                                <filter type='and'>\
                                 <condition attribute='new_approach_id' operator='eq' value='"+ new_approach_id + "' />\
                                 <condition attribute='statecode' operator='eq' value='0' />\
                                </filter>\
                              </entity >\
                            </fetch >";
            console.log(fetchxml);
            var categorys = rtcrm.fetch("new_approach_categories", fetchxml, true);
            if (categorys != null && categorys.length > 0) {
                var new_repairlevel = categorys[0]["new_repairlevel"];
                if (new_repairlevel != null) {
                    rtcrm('#new_repairlevel').val(new_repairlevel)
                }
            }
        }

    } catch (e) {
        rtcrm.alertDialog(e.message);
    }
}

async function form_load() {
    if (rtcrm.getLookupID("new_srv_msdynworkorder_id") != null) {
        await setdefauntapproach(); 
    }
}

async function Getnew_category3_id() {
    var resultinfo = "";
    var new_category3_id = "";
    var new_type = "";
    if (rtcrm.getLookupID("new_srv_msdynworkorder_id") != null) {
        var new_srv_msdynworkorder_id = rtcrm.getLookupID("new_srv_msdynworkorder_id").replace('{', '').replace('}', '');
        // 获取三级品类 new_category3_id

        var fetchStrworkorder = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
       <entity name = 'msdyn_workorder' >\
        <attribute name='new_category3_id' />\
         <attribute name='msdyn_name' />\
         <attribute name='new_type' />\
        <order attribute='msdyn_name' descending='false' />\
        <filter type='and'>\
            <condition attribute='msdyn_workorderid' operator='eq' value='"+ new_srv_msdynworkorder_id + "'/>\
         </filter></entity></fetch>";
        var result = await rtcrm.fetchAsync("msdyn_workorders", fetchStrworkorder, true);
        if (result != null) {
            new_category3_id = result[0]["_new_category3_id_value"];
            new_type = result[0]["new_type"];
            resultinfo = new_category3_id + ";" + new_type;
        }
    }
    return resultinfo;
}
//从三级品类读取数据，逻辑与PC端保持一致
async function GetApprochFilter(workorderId,workOrderNewType,newWarranty,new_category3_id) {      
                    var fetchXml = `<condition attribute='statecode' operator='eq' value='0' />`; 
                    var CheckIsGoodXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                                                <entity name = 'new_srv_workorder_approach' >\
                                                    <attribute name='new_srv_workorder_approachid' />\
                                                    <attribute name='new_approach_id' />\
                                                    <attribute name='new_ifgoods' />\
                                                    <order attribute='new_name' descending='false' />\
                                                    <filter type='and'>\
                                                        <condition attribute='statecode' operator='eq' value='0' />\
                                                        <condition attribute='new_ifgoods' operator='eq' value='true' />\
                                                        <condition attribute='new_srv_workorder_id' operator='eq'  value='"+ workorderId + "' />\
                                                    </filter>\
                                                    <link-entity name='new_approach' from='new_approachid' to='new_approach_id' link-type='inner' alias='ac'>\
                                                        <attribute name = 'new_code' />\
                                                            <attribute name='new_ismaterials' />\
                                                    </link-entity >\
                                                </entity>\
                                            </fetch >";
                                             
                     var checkisgoodappraoch = await rtcrm.fetchAsync("new_srv_workorder_approachs", CheckIsGoodXml, true); 
                    if (checkisgoodappraoch != null && checkisgoodappraoch.length > 0) {
                        fetchXml += "<condition attribute='new_ismaterials' operator='eq' value='1' />";
                        var hasexistedapproachList = checkisgoodappraoch.map(x => x._new_approach_id_value);
                        fetchXml += "<condition attribute='new_approachid' operator='in' >";
                        for (var j = 0; j < hasexistedapproachList.length; j++) {
                            fetchXml += "<value>" + hasexistedapproachList[j] + "</value>";
                        }

                        var approach_categoryXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                                                        <entity name = 'new_approach_category' >\
                                                            <attribute name='new_approach_categoryid' />\
                                                            <attribute name='new_approach_id' />\
                                                            <attribute name='new_ifusematerial' />\
                                                            <order attribute='new_name' descending='false' />\
                                                            <filter type='and'>\
                                                                <condition attribute='statecode' operator='eq' value='0' />\
                                                                <condition attribute='new_ifusematerial' operator='eq' value='true' />\
                                                                <condition attribute='new_servicetype' operator='eq'  value='"+ workOrderNewType + "' />\
                                                            </filter>\
                                                            <link-entity name='new_materialcategory2' from='new_materialcategory2id' to='new_mcategory2_id' link-type='inner' alias='mc'>\
                                                                <attribute name = 'new_code' />\
                                                                    <filter type='and'>\
                                                                        <condition attribute='new_code' operator='eq' value='480' />\
                                                                    </filter>\
                                                            </link-entity >\
                                                        </entity>\
                                                    </fetch >";
                         var goodappraoch = await rtcrm.fetchAsync("new_approach_categories", approach_categoryXml, true);  
                        if (goodappraoch != null && goodappraoch.length > 0) {
                            var goodappraochList = goodappraoch.map(x => x._new_approach_id_value);
                            for (var k = 0; k < goodappraochList.length; k++) {
                                fetchXml += "<value>" + goodappraochList[k] + "</value>";
                            }
                        }
                        fetchXml += "</condition>";
                        return fetchXml;
                    }
                    else {
                        fetchXml += "<condition attribute='new_ismaterials' operator='eq' value='0' />";
                        if (newWarranty != null && newWarranty === 1) {
                            console.log('工单保内状态：', newWarranty);
                            /*fetchXml = `<condition attribute='statecode' operator='eq' value='0' /> <condition attribute='new_iswarrantysupport' operator='eq' value='0' /><condition attribute='new_ismaterials' operator='eq' value='0' /> `;*/
                            fetchXml = `<filter type='or'>
                        <filter type='and'>
                        <condition attribute='statecode' operator='eq' value='0' />
                        <condition attribute='new_ismaterials' operator='eq' value='0' />
                        <condition attribute='new_ishonaisupport' operator='eq' value='1' />
                        </filter>
                        <filter type='and'>
                            <condition attribute='statecode' operator='eq' value='0' />
                            <condition attribute='new_ismaterials' operator='eq' value='0' />
                            <condition attribute='new_iswarrantysupport' operator='eq' value='0' />
                            <condition attribute='new_ishonaisupport' operator='eq' value='0' />
                        </filter>
                    </filter>`;
                        }
                        //新增是否支仅保内支持逻辑--2024-05-07
                        if (newWarranty != null && newWarranty === 2) {
                            console.log('工单保内状态：', newWarranty);
                            /*fetchXml = `<condition attribute='statecode' operator='eq' value='0' /> <condition attribute='new_ishonaisupport' operator='eq' value='0' /><condition attribute='new_ismaterials' operator='eq' value='0' /> `;*/
                            fetchXml = `<filter type='or'>
                          <filter type='and'>
                         <condition attribute='statecode' operator='eq' value='0' />
                         <condition attribute='new_ismaterials' operator='eq' value='0' />
                         <condition attribute='new_iswarrantysupport' operator='eq' value='1' />
                         </filter>
                         <filter type='and'>
                             <condition attribute='statecode' operator='eq' value='0' />
                             <condition attribute='new_ismaterials' operator='eq' value='0' />
                             <condition attribute='new_iswarrantysupport' operator='eq' value='0' />
                             <condition attribute='new_ishonaisupport' operator='eq' value='0' />
                         </filter>
                     </filter>`;
                        }
                        if (workOrderNewType != null) {
                            fetchXml += `<condition attribute='new_servicetype' operator='eq' value='` + workOrderNewType + `' />`;
                        }                         
                        //三级品类过滤处理方法
                        if (new_category3_id) { 
                            var approachfetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>" +
                                "<entity name='new_approach_category' >" +
                                "<attribute name='new_approach_categoryid' />" +
                                "<attribute name='createdon' />" +
                                "<attribute name='new_approach_id' />" +
                                "<order attribute='new_approach_id' descending='false' />" +
                                "<filter type='and'>" +
                                "<filter type='or'>" +
                                "<condition attribute='new_category3_id' operator='eq' value='" + new_category3_id + "' />" +
                                "<condition attribute='new_category3_id' operator='null' />" +
                                "</filter>" +
                                "<condition attribute='new_ifusematerial' operator='eq' value='0' />" +
                                "<condition attribute='new_approach_id' operator='not-null' />" +
                                "<condition attribute='new_servicetype' operator='eq' value='" + workOrderNewType + "' />" +
                                "</filter>" +
                                "</entity>" +
                                "</fetch>";  
                                var approachResult = await rtcrm.fetchAsync("new_approach_categories", approachfetchXml, true); 
                                 
                              var approachList = approachResult.map(x => x._new_approach_id_value);
                                if (approachList) {
                                    if (approachList != null && approachList.length > 0) {
                                        fetchXml += "<condition attribute='new_approachid' operator='in' >"
                                        for (var i = 0; i < approachList.length; i++) {
                                            fetchXml += "<value>" + approachList[i] + "</value>"
                                        }
                                        fetchXml += "</condition>";
                                    }
                                }
                               return fetchXml; 
                        }
                        else {
                           return fetchXml;
                        }
                    }
                    
                 
}
//设置处理方法的过滤条件,逻辑和PC端保持一致
async function setdefauntapproach() {  
     var entityId = rtcrm.getLookupID("new_srv_msdynworkorder_id").replace('{', '').replace('}', '');
     var recordid =rtcrm.getEntityId().replace('{', '').replace('}', '').toLowerCase()
    var results = await Xrm.WebApi.retrieveMultipleRecords("msdyn_workorder", "?$select=new_imei,_new_srv_workorder_id_value&$expand=new_srv_workorder_id($select=_new_category3_id_value,new_warranty,new_dealstatus,new_type,new_origin)&$filter=msdyn_workorderid eq " + entityId);
    var result = results.entities[0];
    var msdyn_workorderid = result["msdyn_workorderid"]; // Guid 
    var new_srv_workorder_id = result["_new_srv_workorder_id_value"]; // Lookup 
    var new_srv_workorder_id_new_category3_id = result["new_srv_workorder_id"]["_new_category3_id_value"]; // Lookup 
    var new_srv_workorder_id_new_warranty = result["new_srv_workorder_id"]["new_warranty"]; // Choice 
    var workOrderNewDelStatus = result["new_srv_workorder_id"]["new_dealstatus"]; // Choice 
    var new_srv_workorder_id_new_type = result["new_srv_workorder_id"]["new_type"]; // Choice 
    var workOrderNewOrigin = result["new_srv_workorder_id"]["new_origin"]; // Choice 
    if ((workOrderNewOrigin != null && workOrderNewOrigin === 60) || (workOrderNewDelStatus != null && (workOrderNewDelStatus < 4 || workOrderNewDelStatus > 7))) { 
        rtcrm.disableAll();
        return;
    }

    var api = "WorkOrder/GetWorkorderApproachReplace";
    var param = {
        workorderId: new_srv_workorder_id
    }; 
        //调用接口获取备选处理方法
       var RepairDataList = await  rtcrm.invokeHiddenApiAsync("new_service", api, param);
        
             let itemfetchXml="";
           if (RepairDataList.length > 0  ) {
                               itemfetchXml = "<condition attribute='new_approachid' operator='in' >";
                            for (let i = 0; i < RepairDataList.length; i++) {
                                if (RepairDataList[i]["Id"] == recordid) {
                                    for (let ii = 0; ii < RepairDataList[i].ApproachIds.length; ii++) {
                                        itemfetchXml += "<value>" + RepairDataList[i].ApproachIds[ii] + "</value>";
                                    }
                                }
                            }
                            itemfetchXml += "</condition>";
                            }
                            else {
                                itemfetchXml = await GetApprochFilter(new_srv_workorder_id,new_srv_workorder_id_new_type,new_srv_workorder_id_new_warranty,new_srv_workorder_id_new_category3_id);
                            } 
   var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>" +
            "<entity name='new_approach' >" +
            "<attribute name='new_approachid' />" +
            "<attribute name='new_name' />" +
            "<attribute name='new_code' />" +
            "<order attribute='new_name' descending='false' />" +
            "<filter type='and'>" 
        fetchXml +=itemfetchXml;
        fetchXml += "</filter>";
        fetchXml += "</entity>";
        fetchXml += "</fetch >"; 

        var layoutXml = "<grid name='resultset' " + "object='1' " + "jump='name' " + "select='1' " + "icon='1' " + "preview='1'>" +
            "<row name='result' " + "id='new_approachid'>" +
            "<cell name='new_name' " + "width='120' />" +
            "<cell name='new_code' " + "width='120' />" +
            "</row>" +
            "</grid>";
        try {
            rtcrm.customizeLookupView("new_approach_id", "new_approach", fetchXml, layoutXml);
        } catch (e) {
            alert(e.message);
        } 
}

//判断是否存在处理方法
function ishaveapproachall() {
    var result = false;
    if (rtcrm.getLookupID('new_srv_msdynworkorder_id') != null) {
        var entityId = rtcrm.getLookupID("new_srv_msdynworkorder_id").replace('{', '').replace('}', '');
        var fetchxml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                       <entity name = 'new_srv_workorder_approach' >\
                        <attribute name='new_srv_workorder_approachid' />\
                        <attribute name='new_name' />\
                        <attribute name='createdon' />\
                        <order attribute='new_name' descending='false' />\
                        <filter type='and'>\
                          <condition attribute='new_srv_msdynworkorder_id' operator='eq'  uitype='msdyn_workorder'  value='"+ entityId + "'/>\
                        </filter>\
                      </entity >\
                    </fetch >";
        var resultapproachall = rtcrm.fetch("new_srv_workorder_approachs", fetchxml, true);
        
        if (resultapproachall != null && resultapproachall.length > 0) {
            result = true;
        }

        return result;
    }
}
//判断存在是否与其他处理方法并存为否
function isapproachcoexist() {
    var result = false;
    if (rtcrm.getLookupID("new_srv_msdynworkorder_id") != null) {
        var entityId = rtcrm.getLookupID("new_srv_msdynworkorder_id").replace('{', '').replace('}', '');
        var fetchxml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                                 <entity name = 'new_srv_workorder_approach' >\
                                <attribute name='new_srv_workorder_approachid' />\
                                <attribute name='new_name' />\
                                <attribute name='createdon' />\
                                <order attribute='new_name' descending='false' />\
                                <filter type='and'>\
                                <condition attribute='new_srv_msdynworkorder_id' operator='eq'  uitype='msdyn_workorder' value='"+ entityId + "'/>\
                                </filter>\
                                <link-entity name='new_approach' from='new_approachid' to='new_approach_id' link-type='inner' alias='aa'>\
                                  <filter type='and'>\
                                    <condition attribute='statecode' operator='eq' value='0' />\
                                    <condition attribute='new_isapproachcoexist' operator='eq' value='0' />\
                                  </filter>\
                                </link-entity>\
                              </entity >\
                            </fetch >";
        var resultapproachs = rtcrm.fetch("new_srv_workorder_approachs", fetchxml, true);
        
        if (resultapproachs != null && resultapproachs.length > 0) {
            result = true;
        }
        return result;
        //else {
        //    if (!rtcrm.isNullOrWhiteSpace(rtcrm.getLookupID("new_approach_id"))) {
        //        var selectnew_approach_id = rtcrm.getLookupID("new_approach_id").replace('{', '').replace('}', '');
        //    }
       // }
    }
}


function approachsave(context) {
    var isexist = isapproachcoexist();
    if (isexist) {
        rtcrm.alertDialog("保存失败【" + rtcrm("#new_approach_id").val()[0].name + "】该处理方法不支持与其他处理方法并存");
        context.getEventArgs().preventDefault();
        return ;
    }
    else {
        if (!rtcrm.isNullOrWhiteSpace(rtcrm.getLookupID("new_approach_id"))) {
            var selectnew_approach_id = rtcrm.getLookupID("new_approach_id").replace('{', '').replace('}', '');
            var resultworkorders = rtcrm.getFieldValue(selectnew_approach_id, "new_approachs", " new_isapproachcoexist", true);
            if (rtcrm.isNullOrWhiteSpace(resultworkorders) && rtcrm.isNullOrWhiteSpace(resultworkorders.new_isapproachcoexist)) {
                var new_isapproachcoexist = resultworkorders.new_isapproachcoexist
                var ifhasall = ishaveapproachall();
                if (!new_isapproachcoexist && ifhasall) {
                    rtcrm.alertDialog("保存失败【" + rtcrm("#new_approach_id").val()[0].name+"】该处理方法不支持与其他处理方法并存");
                    context.getEventArgs().preventDefault();
                    return ;
                }
            }
         }
    }
}