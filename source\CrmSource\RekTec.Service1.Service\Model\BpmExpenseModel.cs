﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RekTec.Api.Biz.Model;

namespace RekTec.Service1.Service.Model
{
    public class BpmExpenseModel
    {
        #region 常规字段
        /// <summary>
        /// 单号
        /// </summary>
        public string new_name { get; set; }

        /// <summary>
        /// 服务商
        /// </summary>
        public string new_srv_station_id { get; set; }

        /// <summary>
        /// 结算年度
        /// </summary>
        public string new_year { get; set; }
        /// <summary>
        /// 结算月份
        /// </summary>
        public string new_month { get; set; }
        /// <summary>
        /// 总费用合计
        /// </summary>
        public string new_totalcost { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string new_transactioncurrency_id { get; set; }
        #endregion

        #region 发票信息
        /// <summary>
        /// 发票编号
        /// </summary>
        public string new_invoiceno { get; set; }

      //  public List<ExpenseFileModel> fileList = new List<ExpenseFileModel>();
        public List<string> fileList = new List<string>();
        #endregion

        #region 服务单信息
        /// <summary>
        /// 维修劳务费
        /// </summary>
        public string new_repairmoney { get; set; }

        /// <summary>
        /// 箱子使用费
        /// </summary>
        public string new_casemoney { get; set; }
        /// <summary>
        /// 寄修补贴
        /// </summary>
        public string new_mailrepairmoney { get; set; }
        /// <summary>
        /// 路程补贴费
        /// </summary>
        public string new_distancemoney { get; set; }
        /// <summary>
        /// 备件费用
        /// </summary>
        public string new_partcost { get; set; }
        /// <summary>
        /// 上门物流费
        /// </summary>
        public string new_comedoormoney { get; set; }
        /// <summary>
        /// 录单费
        /// </summary>
        public string new_recordingmoney { get; set; }
        /// <summary>
        /// 其他费用
        /// </summary>
        public string new_othermoney { get; set; }
        /// <summary>
        /// 结算劳务费
        /// </summary>
        public string new_servicemoney { get; set; }
        /// <summary>
        /// 保底费
        /// </summary>
        public string new_minimummoney { get; set; }

        #region 服务单明细

        public  List<WorkOrderdetailModel> orderList = new List<WorkOrderdetailModel>();
        #endregion

        #endregion

        #region 特殊费用
        /// <summary>
        /// 特殊费用
        /// </summary>
        public string new_specialmoney { get; set; }

        public  List<SpecialExpenseModel> specialExpenseList = new List<SpecialExpenseModel>();
        #endregion

        #region 激活结费
        /// <summary>
        /// 激活结费
        /// </summary>
        public string new_activationmoney { get; set; }

        public List<ExpenseActiveModel> expenseActivesList = new List<ExpenseActiveModel>();
        #endregion

        #region 受理单
        /// <summary>
        /// 结算收集点补贴
        /// </summary>
        public string new_collectionmoney { get; set; }

        public List<IncidentModel> incidentList = new List<IncidentModel>();
        #endregion

        #region 运行商HF
        /// <summary>
        /// 结算运行商hf
        /// </summary>
        public string new_operatehfmoney { get; set; }

        public List<ExpenseHFModel> expenseHFList = new List<ExpenseHFModel>();
        #endregion

        #region  小数调差
        public List<ExpenseAdjustmentModel> expenseAdjustmentList = new List<ExpenseAdjustmentModel>();
        #endregion

        /// <summary>
        /// BPM单据id
        /// </summary>
        public string businessKey { get; set; }
    }
    public class BpmPartbody
    {
        /// <summary>
        /// 表单数据
        /// </summary>
        public BpmPartsExpenseModel formData { get; set; }
        /// <summary>
        /// 业务系统可以指定，若不填则由BPM系统生成
        /// </summary>
        public string businessKey { get; set; }
        public string modelCode { get; set; }
        public string processInstanceName { get; set; }
        public string startUserId { get; set; }
        /// <summary>
        /// 提交的扩展变量
        /// </summary>
        public Variables variables { get; set; } = new Variables();
    }
    public class BpmRollbackbody 
    {
        /// <summary>
        /// 表单数据
        /// </summary>
        public object formData { get; set; }
        /// <summary>
        /// 意见
        /// </summary>
        public string comment { get; set; }
        /// <summary>
        ///  操作用户
        /// </summary>
        public string @operator { get; set; }
        /// <summary>
        /// 任务id
        /// </summary>
        public string taskId { get; set; }
        /// <summary>
        /// 提交的扩展变量
        /// </summary>
        public Variables variables { get; set; } = new Variables();
    }
    public class BpmServiceOrderbody
    {
        /// <summary>
        /// 表单数据
        /// </summary>
        public BpmServiceOrderExpenseModel formData { get; set; }
        /// <summary>
        /// 业务系统可以指定，若不填则由BPM系统生成
        /// </summary>
        public string businessKey { get; set; }
        /// <summary>
        /// 模型编码
        /// </summary>
        public string modelCode { get; set; }
        /// <summary>
        /// 流程实例名称
        /// </summary>
        public string processInstanceName { get; set; }
        public string startUserId { get; set; }
        /// <summary>
        /// 提交的扩展变量
        /// </summary>
        public Variables variables { get; set; } = new Variables();
    }
    /// <summary>
    /// 仓储费-提交BPM-Body
    /// </summary>
    public class BpmWarehousingbody {
        /// <summary>
        /// 表单数据
        /// </summary>
        public BpmWarehousingExpenseModel formData  { get; set; }
        /// <summary>
        /// 业务系统可以指定，若不填则由BPM系统生成
        /// </summary>
        public string businessKey { get; set; }
        /// <summary>
        /// 模型编码
        /// </summary>
        public string modelCode { get; set; }
        /// <summary>
        /// 流程实例名称
        /// </summary>
        public string processInstanceName  { get; set; }
        /// <summary>
        /// 发起人Id
        /// </summary>
        public string startUserId  { get; set; }
        /// <summary>
        /// 提交的扩展变量
        /// </summary>
        public Variables variables { get; set; } = new Variables();
    }
    /// <summary>
    /// 物流费-提交BPM-Body
    /// </summary>
    public class BpmLogisticsbody {
        /// <summary>
        /// 表单数据
        /// </summary>
        public BpmLogisticsExpenseModel formData  { get; set; }
        /// <summary>
        /// 业务系统可以指定，若不填则由BPM系统生成
        /// </summary>
        public string businessKey { get; set; }
        /// <summary>
        /// 模型编码
        /// </summary>
        public string modelCode { get; set; }
        /// <summary>
        /// 流程实例名称
        /// </summary>
        public string processInstanceName  { get; set; }
        /// <summary>
        /// 发起人Id
        /// </summary>
        public string startUserId  { get; set; }
        /// <summary>
        /// 提交的扩展变量
        /// </summary>
        public Variables variables { get; set; } = new Variables();
    }

    //备件费用信息 model
    public class BpmPartsExpenseModel 
    {
        #region 基础信息
        /// <summary>
        /// 付款单号
        /// </summary>
        public string input_5aea43494178 { get; set; }
        /// <summary>
        /// 主题
        /// </summary>
        public string input_fb91992140e9 { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string input_137bfef89a20 { get; set; }
        /// <summary>
        /// 申请人部门
        /// </summary>
        public string input_f071d5147411 { get; set; }
        /// <summary>
        /// 支付公司（含编码）
        /// </summary>
        public string input_5e33b62e046b { get; set; }
        /// <summary>
        /// 承担公司（含编码）
        /// </summary>
        public string input_fa2973ef5f79 { get; set; }
        /// <summary>
        /// 付款金额（含币种）
        /// </summary>
        public string input_e74a0b9bcf56 { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        public string input_b3a49ceddd6e { get; set; }
        /// <summary>
        /// 提交审批日期
        /// </summary>
        public string input_df86a76189eb { get; set; }
        /// <summary>
        /// 最迟付款日期
        /// </summary>
        public string input_7014975e3ac6 { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string input_689672f471d2 { get; set; }
        /// <summary>
        /// SAP供应商编码
        /// </summary>
        public string input_ba0e1e612c48 { get; set; }
        /// <summary>
        /// 国际业务性质
        /// </summary>
        public string input_5194f429fe93 { get; set; }
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_598d2d8d10f9 { get; set; }
        /// <summary>
        /// 当前审批人
        /// </summary>
        public string input_7a084d7d9512 { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        public string input_dcc032592055 { get; set; }
        /// <summary>
        /// 签约主体
        /// </summary>
        public string input_5a3ee052b7e6 { get; set; }
        /// <summary>
        /// 区域
        /// </summary>
        public string input_592d42d196f8 { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        //public decimal input_c0d0f31a538b { get; set; }
        public decimal inputFloat_07d2f59f5d7e { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string input_f9bc0d7a4554 { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string input_073fb66e2bbc { get; set; }
        /// <summary>
        /// 是否reverse charge
        /// </summary>
        public string input_fcf4a85bb57d { get; set; }
        #endregion
        #region 付款单信息
        /// <summary>
        /// 国家
        /// </summary>
        public string input_ac2890dca977 { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string textarea_003372613a8d { get; set; }
        /// <summary>
        /// 结算年份
        /// </summary>
        public string input_3ed54e02a87e { get; set; }
        /// <summary>
        /// 结算月份
        /// </summary>
        public string input_3ef6442527a7 { get; set; }
        /// <summary>
        /// 预提费用（不含税）（含币种）
        /// </summary>
        public string input_24f259c23c02 { get; set; }
        /// <summary>
        /// 结算费用（不含税）（含币种）
        /// </summary>
        public string input_2eb132132cc0 { get; set; }
        /// <summary>
        /// 增值税率
        /// </summary>
        public string input_ebc61c4ee991 { get; set; }
        /// <summary>
        /// 增值税额（含币种）
        /// </summary>
        public string input_1db56adf999d { get; set; }
        /// <summary>
        /// 代扣税率
        /// </summary>
        public string input_2269950f67a7 { get; set; }
        /// <summary>
        /// 代扣税额（含币种）
        /// </summary>
        public string input_95f97c096609 { get; set; }
        /// <summary>
        /// 付款金额（含币种）
        /// </summary>
        public string input_27271fb86b9d { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        public string textarea_614427d1e7a1 { get; set; }
        /// <summary>
        /// 开户名称
        /// </summary>
        public string textarea_1a1dabebb02b { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string input_63b9cb7aa1bd { get; set; }
        /// <summary>
        /// SWIFT CODE
        /// </summary>
        public string input_5aa28b5252d1 { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string input_a0baf697605f { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string input_610877ce81d1 { get; set; }
        //开票日期
        public string input_37e5e5a42e0d { get; set; }
        /// <summary>
        /// 税号
        /// </summary>
        public string input_0cc00a6008b9 { get; set; }
        /// <summary>
        /// 交易附言
        /// </summary>
        public string textarea_fd7a92ba8b5e { get; set; }
        /// <summary>
        /// 国家代码
        /// </summary>
        public string input_e837cf0ceca5 { get; set; }
        #endregion
        #region 备件运营
        /// <summary>
        /// 工单数量
        /// </summary>
        public string input_fd30466bc07a { get; set; }
        /// <summary>
        /// 备件费
        /// </summary>
        public string input_e374d4c0a7f4 { get; set; }
        /// <summary>
        /// 备件服务费
        /// </summary>
        public string input_a6b388db4c48 { get; set; }
        /// <summary>
        /// 单量保底费
        /// </summary>
        public string input_328e34536d2f { get; set; }
        /// <summary>
        /// 换机local buy 费
        /// </summary>
        public string input_a2fcfcd4007d { get; set; }
        /// <summary>
        /// 换机markup 费用
        /// </summary>
        public string input_16d20f109f70 { get; set; }
        /// <summary>
        /// 物流费
        /// </summary>
        public string input_9ce6241a80d3 { get; set; }
        /// <summary>
        /// 仓储费
        /// </summary>
        public string input_8217963f70d4 { get; set; }
        /// <summary>
        /// 墨西哥固定服务费
        /// </summary>
        public string input_3bbea73e4fad { get; set; }
        /// <summary>
        /// 客户退款
        /// </summary>
        public string input_e9d51de00487 { get; set; }
        /// <summary>
        /// 资本利息费
        /// </summary>
        public string input_d4bd815889f9 { get; set; }
        /// <summary>
        /// 其他特殊费用
        /// </summary>
        public string input_49ab64a41661 { get; set; }
        /// <summary>
        /// 生态链品类回购费用
        /// </summary>
        public string input_97a764351491 { get; set; }
        /// <summary>
        /// 箱子使用费
        /// </summary>
        public string input_f547cd82ffd5 { get; set; }
        /// <summary>
        /// 手机KPI
        /// </summary>
        public string input_23d13d6201fb { get; set; }
        /// <summary>
        /// 电视KPI
        /// </summary>
        public string input_de99c3fe2cda { get; set; }
        /// <summary>
        /// 滑板车KPI
        /// </summary>
        public string input_7841b3299f84 { get; set; }
        /// <summary>
        /// 笔记本KPI
        /// </summary>
        public string input_5632b5088d84 { get; set; }
        /// <summary>
        /// 扫拖KPI
        /// </summary>
        public string input_df5afbd50c47 { get; set; }
        /// <summary>
        /// 生态链KPI
        /// </summary>
        public string input_ab7e4bb0b648 { get; set; }
        #endregion
        /// <summary>
        /// 结算单明细
        /// </summary>
        public List<LinkModelPart> formTable_ff7240cecfc1 { get; set; }
        /// <summary>
        /// 特殊费用明细
        /// </summary>
        public List<SpecialExpenseFeeModel> formTable_1f7d83792fd4 { get; set; }
        /// <summary>
        /// 发票附件明细
        /// </summary>
        public List<PartInvoiceModel> formTable_253fa9a62466 { get; set; }
        public string businessKey { get; set; }
    }
    /// <summary>
    /// 劳务费用信息 model
    /// </summary>
    public class BpmServiceOrderExpenseModel
    {
        #region 基础信息
        /// <summary>
        /// 付款单号
        /// </summary>
        public string input_f67d9d7a0540 { get; set; }
        /// <summary>
        /// 主题
        /// </summary>
        public string input_d54e10a93aab { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string input_b5f0d5367833 { get; set; }
        /// <summary>
        /// 申请人部门
        /// </summary>
        public string input_0c4630eca2ac { get; set; }
        /// <summary>
        /// 支付公司（含编码）
        /// </summary>
        public string input_8889972d0f41 { get; set; }
        /// <summary>
        /// 承担公司（含编码）
        /// </summary>
        public string input_c7189fb1e285 { get; set; }
        /// <summary>
        /// 付款金额（含币种）
        /// </summary>
        public string input_1ded2e239add { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        public string input_ca9d4f4017ee { get; set; }
        /// <summary>
        /// 提交审批日期
        /// </summary>
        public string input_1c8ea85e1dd6 { get; set; }
        /// <summary>
        /// 最迟付款日期
        /// </summary>
        public string input_9f343327c1ec { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string input_ed848e5fc57e { get; set; }
        /// <summary>
        /// SAP供应商编码
        /// </summary>
        public string input_bd92cb77da2a { get; set; }
        /// <summary>
        /// 国际业务性质
        /// </summary>
        public string input_ac05ac6af3ab { get; set; }
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_d7116be54dc2 { get; set; }
        /// <summary>
        /// 当前审批人
        /// </summary>
        public string input_005cd3928978 { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        public string input_81aaadd479a1 { get; set; }
        /// <summary>
        /// 签约主体
        /// </summary>
        public string input_0084c1478948 { get; set; }
        /// <summary>
        /// 区域
        /// </summary>
        public string input_61e7dce5f012 { get; set; }
        /// <summary>
        /// 付款金额（隐藏）
        /// </summary>
        public decimal inputFloat_a7c224e83898 { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string input_90cae210d2cb { get; set; }
        #endregion
        #region 付款单信息
        /// <summary>
        /// 国家
        /// </summary>
        public string input_170864bdf47b { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string textarea_675899ad0736 { get; set; }
        /// <summary>
        /// 网点类型
        /// </summary>
        public string input_7017fca92b77 { get; set; }
        /// <summary>
        /// 结算年份
        /// </summary>
        public string input_d85f6917671f { get; set; }
        /// <summary>
        /// 结算月份
        /// </summary>
        public string input_7539e944f720 { get; set; }
        /// <summary>
        /// 预提费用（不含税）（含币种）
        /// </summary>
        public string input_db8f5acb13d5 { get; set; }
        /// <summary>
        /// 结算费用（不含税）（含币种）
        /// </summary>
        public string input_11bf2f0b3f56 { get; set; }
        /// <summary>
        /// 增值税率
        /// </summary>
        public string input_cecfa127c101 { get; set; }
        /// <summary>
        /// 增值税额（含币种）
        /// </summary>
        public string input_d9760991e9a5 { get; set; }
        /// <summary>
        /// 代扣税率
        /// </summary>
        public string input_64a5dc3633cd { get; set; }
        /// <summary>
        /// 代扣税额（含币种）
        /// </summary>
        public string input_ed222d04c639 { get; set; }
        /// <summary>
        /// 付款金额（含币种）
        /// </summary>
        public string input_f47fb7ae965f { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        public string textarea_f856868fc19a { get; set; }
        /// <summary>
        /// 开户名称
        /// </summary>
        public string textarea_78e50be63edf { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string input_857fd8dadd4c { get; set; }
        /// <summary>
        /// SWIFT CODE
        /// </summary>
        public string input_ce6f9039e337 { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string input_8cd3e7a4a205 { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string input_63654c5ba2e9 { get; set; }
        //开票日期
        public string input_d4c209f0cc7c { get; set; }
        /// <summary>
        /// 税号
        /// </summary>
        public string input_bc28343e97b5 { get; set; }
        /// <summary>
        /// 交易附言
        /// </summary>
        public string textarea_ba6262d23e23 { get; set; }
        /// <summary>
        /// 国家代码
        /// </summary>
        public string input_db9579324bbd { get; set; }
        #endregion
        #region 售后网点劳务费
        /// <summary>
        /// 工单数量
        /// </summary>
        public string input_42ad78b83bac { get; set; }
        /// <summary>
        /// 维修劳务费
        /// </summary>
        public string input_68a26176926f { get; set; }
        /// <summary>
        /// 寄修物流费
        /// </summary>
        public string input_6e9987d5f9df { get; set; }
        /// <summary>
        /// 上门物流费
        /// </summary>
        public string input_c1f68deb221c { get; set; }
        /// <summary>
        /// 固定运营补贴
        /// </summary>
        public string input_82fe6b4aacbf { get; set; }
        /// <summary>
        /// 保底费
        /// </summary>

        public string input_db80d42613db { get; set; }
        /// <summary>
        /// B2X管理费
        /// </summary>
        public string input_689f0089bbe4 { get; set; }
        /// <summary>
        /// 路程补贴费
        /// </summary>
        public string input_b1f51f45e642 { get; set; }
        /// <summary>
        /// 录单费
        /// </summary>
        public string input_0796797027b7 { get; set; }
        /// <summary>
        /// 电话回访费
        /// </summary>
        public string input_54b608959ff7 { get; set; }
        /// <summary>
        /// 转派工单检测费
        /// </summary>
        public string input_b700b828ba99 { get; set; }
        /// <summary>
        /// 箱子费
        /// </summary>
        public string input_70faeca6c20c { get; set; }
        /// <summary>
        /// 收集点补贴
        /// </summary>
        public string input_88c4a231269c { get; set; }
        /// <summary>
        /// 备件费
        /// </summary>
        public string input_361415eb7d7f { get; set; }
        /// <summary>
        /// 远程维修费
        /// </summary>
        public string input_90fbe9787be3 { get; set; }
        /// <summary>
        /// 激活费
        /// </summary>
        public string input_2aaf7289df4a { get; set; }
        /// <summary>
        /// 运营商HF
        /// </summary>
        public string input_a11a11b1a537 { get; set; }
        /// <summary>
        /// 加减项费
        /// </summary>
        public string input_0efa62e16b42 { get; set; }
        /// <summary>
        /// 手机KPI
        /// </summary>
        public string input_630ddad9d60a { get; set; }
        /// <summary>
        /// 电视KPI
        /// </summary>
        public string input_b103f7f3acd0 { get; set; }
        /// <summary>
        /// 滑板车KPI
        /// </summary>
        public string input_bc4295e32b5e { get; set; }
        /// <summary>
        /// 笔记本KPI
        /// </summary>
        public string input_316dca7ec090 { get; set; }
        /// <summary>
        /// 扫拖KPI
        /// </summary>
        public string input_202dc03cca65 { get; set; }
        /// <summary>
        /// 生态链KPI
        /// </summary>
        public string input_71f3904679f7 { get; set; }
        /// <summary>
        /// 对外呼叫费
        /// </summary>
        public decimal inputFloat_895293cee755 { get; set; }
        /// <summary>
        /// 空箱费
        /// </summary>
        public decimal inputFloat_5a6b77fc820d { get; set; }
        /// <summary>
        /// 翻新费
        /// </summary>
        public decimal inputFloat_b2533db65cf7 { get; set; }
        /// <summary>
        /// 电视保护费
        /// </summary>
        public decimal inputFloat_fcc1164954a2 { get; set; }
        /// <summary>
        /// 清关费
        /// </summary>
        public decimal inputFloat_551ef79823e6 { get; set; }
        /// <summary>
        /// 物流费
        /// </summary>
        public decimal inputFloat_e1098363fc84 { get; set; }
        /// <summary>
        /// 空箱物流费
        /// </summary>
        public decimal inputFloat_f0d415eae926 { get; set; }
        /// <summary>
        /// 备件费
        /// </summary>

        public string input_fdb9d1ca36d6 { get; set; }
        /// <summary>
        /// 备件markup
        /// </summary>
        public string input_a80a6d15d844 { get; set; }
        /// <summary>
        /// Local buy费
        /// </summary>
        public string input_0ef6a2edcb8f { get; set; }
        /// <summary>
        /// Local buy markup费
        /// </summary>
        public string input_a9aa64208f64 { get; set; }
        /// <summary>
        /// 销售额（ Sales Fee）
        /// </summary>
        public string input_b52a7d792947 { get; set; }
        /// <summary>
        /// 安装劳务费
        /// </summary>
        public string input_06ce473b4fdf { get; set; }
        /// <summary>
        /// 是否reverse charge 
        /// </summary>
        public string input_dc3dfbfabb90 { get; set; }
        /// <summary>
        /// 售后网点杂费-DOA回购
        /// </summary>
        public string input_37b00ad6a594 { get; set; }
        /// <summary>
        /// 网点半自营费
        /// </summary>
        public string input_79b201e34695 { get; set; }
        /// <summary>
        /// 售后网点杂费-SWAP结费
        /// </summary>
        public string input_ca6f0d180dde { get; set; }
        /// <summary>
        /// 售后网点杂费-Refund退款
        /// </summary>
        public string input_735f450e4266 { get; set; }
        /// <summary>
        /// 售后网点杂费-保外免费换屏
        /// </summary>
        public string input_5a548f6d9edb { get; set; }
        /// <summary>
        /// 拆机费
        /// </summary>
        public string input_5b0b9a27543f { get; set; }
        /// <summary>
        /// 售后网点杂费-清关费
        /// </summary>
        public string input_323504a1961a { get; set; }
        /// <summary>
        /// 售后网点杂费-其他
        /// </summary>
        public string input_b9c37f0db011 { get; set; }
        /// <summary>
        /// 清关费（转派）
        /// </summary>
        public string input_53a4ef227bcc { get; set; }
        /// <summary>
        /// 安装固定月度费
        /// </summary>
        public string input_5bdda7e76512 { get; set; }
        /// <summary>
        /// 大家电KPI
        /// </summary>
        public string input_405d988b3bd2 { get; set; }

        #endregion
        /// <summary>
        /// 结算单明细
        /// </summary>
        public List<LinkModelServiceOrder> formTable_8de0f4ddf57e { get; set; }
        /// <summary>
        /// 加减项费用明细（不含税）
        /// </summary>
        public List<SpecialExpenseServiceOrderModel> formTable_c1f8126b7b3c { get; set; }
        /// <summary>
        /// 发票附件明细
        /// </summary>
        public List<ServiceOrderInvoiceModel> formTable_57dc94ce8e6b { get; set; }
        public string businessKey { get; set; }
    }

    /// <summary>
    /// 仓储费用信息 model
    /// </summary>
    public class BpmWarehousingExpenseModel {
        #region 基础信息
        /// <summary>
        /// 付款单号
        /// </summary>
        public string input_c654397de1bd { get; set; }
        /// <summary>
        /// 主题
        /// </summary>
        public string textarea_0f2fe10ef189 { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string input_d2f86f5b61e8 { get; set; }
        /// <summary>
        /// 申请人部门
        /// </summary>
        public string input_8d021dd15b3e { get; set; }
        /// <summary>
        /// 支付公司
        /// </summary>
        public string input_140cbe98bb25 { get; set; }
        /// <summary>
        /// 承担公司
        /// </summary>
        public string input_461b6e6f3e81 { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public string input_ef18e1381e79 { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        public string input_7dc2e8b6b631 { get; set; }
        /// <summary>
        /// 提交审批日期
        /// </summary>
        public string datepicker_9fff85484b28 { get; set; }
        /// <summary>
        /// 最迟付款日期
        /// </summary>
        public string datepicker_ef3d09efbf26 { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string input_c7988e20cf5c { get; set; }
        /// <summary>
        /// SAP供应商编码
        /// </summary>
        public string input_90ec538e0d78 { get; set; }
        /// <summary>
        /// 国际业务性质
        /// </summary>
        public string input_3af233b12d65 { get; set; }
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_ff0685db1a82 { get; set; }
        #endregion

        #region 付款单信息
        /// <summary>
        /// 币种
        /// </summary>
        public string input_e37b695ab117 { get; set; }
        /// <summary>
        /// 付款金额（不含币种）
        /// </summary>
        public decimal inputFloat_23b844fbd14f { get; set; }
        /// <summary>
        /// 服务商编码
        /// </summary>
        public string input_4ba5fe38f028 { get; set; }
        /// <summary>
        /// 主体
        /// </summary>
        public string input_b0b1f972fc27 { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string input_5e9aead82791 { get; set; }
        /// <summary>
        /// 预提费用（不含税）
        /// </summary>
        public string input_fa68279df0e1 { get; set; }
        /// <summary>
        /// 结算年份
        /// </summary>
        public string input_dcf8af1d26a8 { get; set; }
        /// <summary>
        /// 结算费用（不含税）
        /// </summary>
        public string input_aa3a84d769d4 { get; set; }
        /// <summary>
        /// 结算月份
        /// </summary>
        public string input_4601412f4b00 { get; set; }
        /// <summary>
        /// 增值税率
        /// </summary>
        public string textarea_83927f68a2de { get; set; }
        /// <summary>
        /// 代扣税率
        /// </summary>
        public string input_d2002a8f8e47 { get; set; }
        /// <summary>
        /// 增值税额
        /// </summary>
        public string input_6dcb277f1c7e { get; set; }
        /// <summary>
        /// 代扣税额
        /// </summary>
        public string input_7be059d76aae { get; set; }
        /// <summary>
        /// 开户名称
        /// </summary>
        public string textarea_de3f3a1e1e90 { get; set; }
        /// <summary>
        /// 单行文本框
        /// </summary>
        public string input_10a4c8af59b6 { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        public string textarea_33dc8ae1f5a0 { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string input_871fd48b8265 { get; set; }
        /// <summary>
        /// SWIFT CODE
        /// </summary>
        public string input_df6e33330d67 { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string input_e577da65d75c { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string input_23b073f11399 { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string datepicker_618f0d62ae68 { get; set; }
        /// <summary>
        /// 交易附言
        /// </summary>
        public string textarea_007c752a50c8 { get; set; }
        /// <summary>
        /// 是否Reverse charge
        /// </summary>
        public string input_08d8839ada31 { get; set; }
        /// <summary>
        /// 发票附件
        /// </summary>
        public List<WarehousingInvoiceModel> formTable_cbe62c59e19d { get; set; }
        #endregion

        #region 结算单明细(不含税)
        /// <summary>
        /// 租金固定费
        /// </summary>
        public string input_dbd0d369e657 { get; set; }
        /// <summary>
        /// 租金货架费
        /// </summary>
        public string input_e6516f308c22 { get; set; }
        /// <summary>
        /// 租金扩仓费
        /// </summary>
        public string input_54eeb0136dd1 { get; set; }
        /// <summary>
        /// 人力固定费
        /// </summary>
        public string input_d33158c0e51d { get; set; }
        /// <summary>
        /// 人力变动费
        /// </summary>
        public string input_3477864acf47 { get; set; }
        /// <summary>
        /// 运营固定费
        /// </summary>
        public string input_726c18dad277 { get; set; }
        /// <summary>
        /// 电视操作费
        /// </summary>
        public string input_b67a47b3403b { get; set; }
        /// <summary>
        /// 出入库操作费
        /// </summary>
        public string input_910160df04f6 { get; set; }
        /// <summary>
        /// 大小件操作费
        /// </summary>
        public string input_0498d97a97d5 { get; set; }
        /// <summary>
        /// 入库上架费
        /// </summary>
        public string input_db4e30cccf5e { get; set; }
        /// <summary>
        /// 打包费
        /// </summary>
        public string input_9b96918b4d21 { get; set; }
        /// <summary>
        /// 质检费
        /// </summary>
        public string input_fdae8bfdb4ed { get; set; }
        /// <summary>
        /// 物流费
        /// </summary>
        public string input_90c3b0c9cc12 { get; set; }
        /// <summary>
        /// 其他费用
        /// </summary>
        public string input_8e10bde197cc { get; set; }
        /// <summary>
        /// 高维工厂劳务费
        /// </summary>
        public string input_c85727963c7e { get; set; }
        /// <summary>
        /// 加减项费用明细（不含税）
        /// </summary>
        public List<SpecialExpenseWarehousingModel> formTable_3f8eb4d509ec { get; set; }
        /// <summary>
        /// 结算单链接
        /// </summary>
        public string link_2fdc8e25aa3b { get; set; }
        #endregion
    
        public string businessKey { get; set; }
    }

    /// <summary>
    /// 物流费用信息 model
    /// </summary>
    public class BpmLogisticsExpenseModel {
        #region 基础信息
        /// <summary>
        /// 付款单号
        /// </summary>
        public string input_c654397de1bd { get; set; }
        /// <summary>
        /// 主题
        /// </summary>
        public string textarea_0f2fe10ef189 { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string input_d2f86f5b61e8 { get; set; }
        /// <summary>
        /// 申请人部门
        /// </summary>
        public string input_8d021dd15b3e { get; set; }
        /// <summary>
        /// 支付公司
        /// </summary>
        public string input_140cbe98bb25 { get; set; }
        /// <summary>
        /// 承担公司
        /// </summary>
        public string input_461b6e6f3e81 { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public string input_ef18e1381e79 { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        public string input_7dc2e8b6b631 { get; set; }
        /// <summary>
        /// 提交审批日期
        /// </summary>
        public string datepicker_9fff85484b28 { get; set; }
        /// <summary>
        /// 最迟付款日期
        /// </summary>
        public string datepicker_ef3d09efbf26 { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string input_c7988e20cf5c { get; set; }
        /// <summary>
        /// SAP供应商编码
        /// </summary>
        public string input_90ec538e0d78 { get; set; }
        /// <summary>
        /// 国际业务性质
        /// </summary>
        public string input_3af233b12d65 { get; set; }
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_ff0685db1a82 { get; set; }
        #endregion

        #region 付款单信息
        /// <summary>
        /// 国家
        /// </summary>
        public string input_5e9aead82791 { get; set; }
        /// <summary>
        /// 服务商名称
        /// </summary>
        public string textarea_cde46e6a3d2f { get; set; }
        /// <summary>
        /// 国家（二字码）
        /// </summary>
        public string input_492bc60166c2 { get; set; }
        /// <summary>
        /// 结算年份
        /// </summary>
        public string input_dcf8af1d26a8 { get; set; }
        /// <summary>
        /// 结算月份
        /// </summary>
        public string input_4601412f4b00 { get; set; }
        /// <summary>
        /// 预提费用（不含税）
        /// </summary>
        public string input_fa68279df0e1 { get; set; }
        /// <summary>
        /// 结算费用（不含税）
        /// </summary>
        public string input_aa3a84d769d4 { get; set; }
        /// <summary>
        /// 增值税率
        /// </summary>
        public string textarea_83927f68a2de { get; set; }
        /// <summary>
        /// 增值税额
        /// </summary>
        public string input_6dcb277f1c7e { get; set; }
        /// <summary>
        /// 代扣税率
        /// </summary>
        public string input_d2002a8f8e47 { get; set; }
        /// <summary>
        /// 代扣税额
        /// </summary>
        public string input_7be059d76aae { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        public string textarea_33dc8ae1f5a0 { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string input_871fd48b8265 { get; set; }
        /// <summary>
        /// SWIFT CODE
        /// </summary>
        public string input_df6e33330d67 { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string input_e577da65d75c { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string input_23b073f11399 { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string datepicker_618f0d62ae68 { get; set; }
        /// <summary>
        /// 付款金额（不含币种）
        /// </summary>
        public decimal inputFloat_23b844fbd14f { get; set; }
        /// <summary>
        /// 开户名称
        /// </summary>
        public string textarea_de3f3a1e1e90 { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string input_e37b695ab117 { get; set; }
        /// <summary>
        /// 主体
        /// </summary>
        public string input_b0b1f972fc27 { get; set; }
        /// <summary>
        /// 服务商编码
        /// </summary>
        public string input_67c4bbf988fc { get; set; }
        /// <summary>
        /// 交易附言
        /// </summary>
        public string textarea_007c752a50c8 { get; set; } 
        /// <summary>
        /// 是否Reverse charge
        /// </summary>
        public string input_1e03103db5d5 { get; set; } 
        /// <summary>
        /// 发票附件
        /// </summary>
        public List<LogisticsInvoiceModel> formTable_cbe62c59e19d { get; set; }
        #endregion

        #region 结算单明细(不含税)
        /// <summary>
        /// 加减项费用（不含税）
        /// </summary>
        public List<SpecialExpenseLogisticsModel> formTable_3f8eb4d509ec { get; set; }
        /// <summary>
        /// 结算单链接
        /// </summary>
        public string link_2fdc8e25aa3b { get; set; }
        #endregion
    
        public string businessKey { get; set; }
    }

    public class LinkModelPart 
    {
        /// <summary>
        /// 结算单链接
        /// </summary>
        public string link_1710316920832 { get; set; }
    }
    public class LinkModelServiceOrder
    {
        /// <summary>
        /// 结算单链接
        /// </summary>
        public string link_1710314891456 { get; set; }
    }
    /// <summary>
    /// 特殊费用明细--备件
    /// </summary>
    public class SpecialExpenseFeeModel
    {
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_1710317073481 { get; set; }
        /// <summary>
        /// 特殊费用金额
        /// </summary>
        public string input_1710317075808 { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string textarea_1717039394892 { get; set; }
    }
    /// <summary>
    /// 加减项费用明细（不含税）
    /// </summary>
    public class SpecialExpenseServiceOrderModel
    {
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_1710297576448 { get; set; }
        /// <summary>
        /// 特殊费用金额
        /// </summary>
        public string input_1710297766608 { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string textarea_1717039316084 { get; set; }
    }
    /// <summary>
    /// 加减项费用明细(不含税)(仓储费)
    /// </summary>
    public class SpecialExpenseWarehousingModel {
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_1725518608243 { get; set; }
        /// <summary>
        /// 特殊费用金额
        /// </summary>
        public string input_1725518613371 { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string input_1725518611344 { get; set; }
    }
    /// <summary>
    /// 物流费费用明细(不含税)(物流费)
    /// </summary>
    public class SpecialExpenseLogisticsModel {
        /// <summary>
        /// 费用类型
        /// </summary>
        public string input_1725518608243 { get; set; }
        /// <summary>
        /// 申请理由
        /// </summary>
        public string input_1725518611344 { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string input_1725518613371 { get; set; }
    }

    public class PartInvoiceModel 
    {
        /// <summary>
        /// 附件路径
        /// </summary>
        public string upload_1710316197800 { get; set; }
    }
    public class ServiceOrderInvoiceModel
    {
        /// <summary>
        /// 附件路径
        /// </summary>
        public string upload_1710314821176 { get; set; }
    }
    public class WarehousingInvoiceModel
    {
        /// <summary>
        /// 附件路径
        /// </summary>
        public string upload_1725518373540 { get; set;}
    }
    public class LogisticsInvoiceModel
    {
        /// <summary>
        /// 附件路径
        /// </summary>
        public string upload_1725518373540 { get; set;}
    }
    /// <summary>
    /// 服务单明细
    /// </summary>
    public class WorkOrderdetailModel
    {
        /// <summary>
        /// 服务单号
        /// </summary>
        public string new_name { get; set; }
        /// <summary>
        /// 服务网点
        /// </summary>
        public string new_station_id { get; set; }
        /// <summary>
        /// 工单类型
        /// </summary>
        public string new_type { get; set; }
        /// <summary>
        /// 维修劳务费
        /// </summary>
        public string new_repairfee { get; set; }

        /// <summary>
        /// 维修劳务费(kpi)
        /// </summary>
        public string new_repairfeekpi { get; set; }

        /// <summary>
        /// 商品sku 
        /// </summary>
        public string new_goodsfiles_id { get; set; }
        /// <summary>
        /// 箱子使用费
        /// </summary>
        public string new_boxfee { get; set; }
        /// <summary>
        /// 寄修补贴
        /// </summary>
        public string new_repairsubsidy { get; set; }

        /// <summary>
        /// 路程补贴
        /// </summary>
        public string new_distancesubsidy { get; set; }
        /// <summary>
        /// 上门物流费
        /// </summary>
        public string new_logisticsfee { get; set; }
        /// <summary>
        /// 录单费
        /// </summary>
        public string new_recordingfee { get; set; }
        /// <summary>
        /// 其他费用
        /// </summary>
        public string new_othercost { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }
    }

    /// <summary>
    /// 附件
    /// </summary>
    public class ExpenseFileModel
    {
        /// <summary>
        /// 附件地址
        /// </summary>
        public string url { get; set; }
    }

    /// <summary>
    /// 特殊费用明细
    /// </summary>
    public class SpecialExpenseModel
    {
        /// <summary>
        /// 特殊费用单号
        /// </summary>
        public string new_name { get; set; }
        /// <summary>
        /// 服务单
        /// </summary>
        public string new_workorder_id { get; set; }
        /// <summary>
        /// 服务商
        /// </summary>
        public string new_stationservice_id { get; set; }
        /// <summary>
        /// 服务网点
        /// </summary>
        public string new_station_id { get; set; }
        /// <summary>
        /// 费用类型
        /// </summary>
        public string new_feetype { get; set; }
        /// <summary>
        /// 特殊费用金额
        /// </summary>
        public string new_specialamount { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public string new_submittime { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }

        // 特殊费用审批记录
        public List<ApprovallogModel> approvalList = new List<ApprovallogModel>();
    }

    /// <summary>
    /// 结算激活费
    /// </summary>
    public class ExpenseActiveModel
    {
        /// <summary>
        /// 国家
        /// </summary>
       public string new_country_id { get; set; }
        /// <summary>
        /// 一级品类
        /// </summary>
       public string new_category1_id { get; set; }
        /// <summary>
        /// 二级品类
        /// </summary>
        public string new_category2_id { get; set; }
        /// <summary>
        /// 三级品类
        /// </summary>
        public string new_category3_id { get; set; }
        /// <summary>
        /// 激活数量
        /// </summary>
        public string new_number { get; set; }
        /// <summary>
        /// po均价
        /// </summary>
        public string new_poprice { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public string new_totalmoney { get; set; }
        /// <summary>
        /// 售后费用率
        /// </summary>
        public string new_costrate { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        public string new_settlementmoney { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }
    }

    /// <summary>
    /// 服务受理单
    /// </summary>
    public class IncidentModel
    {
        /// <summary>
        /// 服务受理单号
        /// </summary>
        public string new_name { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        public string new_settlementmoney { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }
    }

    /// <summary>
    /// 运行商HF
    /// </summary>
    public class ExpenseHFModel
    {
        /// <summary>
        /// 服务单
        /// </summary>
        public string new_workorder_id { get; set; }
        /// <summary>
        /// 服务商
        /// </summary>
        public string new_station_id { get; set; }
        /// <summary>
        /// 一级品类
        /// </summary>
        public string new_category1_id { get; set; }
        /// <summary>
        /// 二级品类
        /// </summary>
        public string new_category2_id { get; set; }
        /// <summary>
        /// 三级品类
        /// </summary>
        public string new_category3_id { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        public string new_settlementmoney{ get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }
    }

    /// <summary>
    /// 小数调差
    /// </summary>
    public class ExpenseAdjustmentModel
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string new_name { get; set; }
        /// <summary>
        /// 总费用
        /// </summary>
        public string new_totalmoney { get; set; }
        /// <summary>
        /// 调差金额
        /// </summary>
        public string new_adjustment_money { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        public string new_settlementmoney { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createdon { get; set; }
       
    }

    public class ApprovallogModel
    {
        /// <summary>
        /// 审批节点名称
        /// </summary>
        public string new_name { get; set; }
        /// <summary>
        /// 关联实体名
        /// </summary>
        public string new_entityname { get; set; }
        /// <summary>
        /// 关联单据ID
        /// </summary>
        public string new_entityid { get; set; }
        /// <summary>
        /// 审批人
        /// </summary>
        public string new_approvaluser { get; set; }
        /// <summary>
        /// 审批通过时间
        /// </summary>
        public string new_approvaltime { get; set; }
        /// <summary>
        /// 审批状态 
        /// </summary>
        public string new_approvalstatus { get; set; }
        /// <summary>
        /// 审批意见
        /// </summary>
        public string new_memo { get; set; }

    }
   
}
