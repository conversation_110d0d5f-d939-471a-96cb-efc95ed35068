﻿//#region 文件描述
/******************************************************************
** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved.
** 创建人   :charlestian
** 创建时间 :2021-11-30 10:00:33
** 说明     :服务网点KPI
******************************************************************/
//#endregion

function form_load() {
    var new_kpistatecode = rtcrm("new_kpistatecode").val();
    if (new_kpistatecode != 0) {
        rtcrm.disableAll(true);
    }
    this.new_type_onchange();
}

//确认按钮显示隐藏
function btnSettConfirmEnabled() {
    var new_kpistatecode = rtcrm("#new_kpistatecode").val();
    if (new_kpistatecode == 0)
        return true;
    return false;
}
//确认
function btnSettConfirm() {

    rtcrm.confirmDialog($t("ServiceSettlement.beenconfirmed", "是否已确认KPI？"), function () {
        try {
            rtcrm.layerLoading();
            var new_station_actualkpi = {};
            new_station_actualkpi["new_kpistatecode"] = 1;
            rtcrm.update("new_station_actualkpis", rtcrm.getEntityId().replace("{", "").replace("}", ""), new_station_actualkpi);
            rtcrm.closeLayerLoading();
            rtcrm.alertDialog($t("ServiceSettlement.successful", "确认成功！"), function () {
                rtcrm.refresh();
            });
        } catch (e) {
            rtcrm.alertDialog(e.message);
        }
    }, function () {
        rtcrm.refresh();
    });

}
//选择所属网点后，带出该网点对应的服务商、所属区域、国家
function new_station_id_onchange() {
    debugger;

    var new_station_id = rtcrm.getLookupId("new_station_id");
    if (new_station_id == null) return;
    new_station_id = new_station_id.replace("{", "").replace("}", "");
    var new_srv_station = rtcrm.getFieldValue(new_station_id, "new_srv_stations", "*", true);
    if (new_srv_station == null) return;

    var new_srv_station_id = new_srv_station["_new_srv_station_id_value"];
    var new_srv_station_idname = new_srv_station["<EMAIL>"];

    if (!rtcrm.isNull(new_srv_station_id) && !rtcrm.isNull(new_srv_station_idname)) {
        rtcrm("#new_serviceprovider_id").val([{ id: new_srv_station_id, name: new_srv_station_idname, entityType: "new_srv_station" }])
    }

    var new_country_id = new_srv_station["_new_country_id_value"];
    var new_country_idname = new_srv_station["<EMAIL>"];

    if (!rtcrm.isNull(new_country_id)) {

        if (!rtcrm.isNull(new_country_idname)) {
            rtcrm("#new_country_id").val([{ id: new_country_id, name: new_country_idname, entityType: "new_country" }]);
        }

        var new_country = rtcrm.getFieldValue(new_country_id, "new_countries", "*", true);
        if (new_country == null) return;
        var new_region_id = new_country["_new_region_id_value"];
        var new_region_idname = new_country["<EMAIL>"];

        if (!rtcrm.isNull(new_region_id) && !rtcrm.isNull(new_region_idname)) {
            rtcrm("#new_region_id").val([{ id: new_region_id, name: new_region_idname, entityType: "new_region" }]);
        }


    }


}
//国家KPI明细导入显隐规则
function ImportCountryLineEnabled() {
    //只有XM总部结算专员才能显示按钮
    if (rtcrm.isCurrentUserHasRoles("XM总部结算专员")) {
        return true;
    } else {
        return false;
    }
}
//国家KPI明细导入
function ImportCountryLine() {
    try {
        rtcrm.showImportWindow("new_station_countrykpiline")

    } catch (e) {
        Xrm.Utility.alertDialog(e);
    }
}
function new_type_onchange() {
    var new_type = Xrm.Page.getAttribute("new_type").getValue();//类型
    var isshow = rtcrm.isCurrentUserHasRoles("XM总部结算专员");
    if (new_type == 1 || new_type == 5) {
        //工单-隐藏 一二三级品类、服务网点KPI细分选项卡
        //Xrm.Page.getControl("new_category1_id").setVisible(true);
        //Xrm.Page.getControl("new_category2_id").setVisible(true);
        //Xrm.Page.getControl("new_category3_id").setVisible(true);
        Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
        Xrm.Page.ui.tabs.get("tab_4").setVisible(false);

        Xrm.Page.getControl("new_station_id").setVisible(true);
        Xrm.Page.getControl("new_type").setVisible(true);
        Xrm.Page.getControl("new_serviceprovider_id").setVisible(true);
        Xrm.Page.getControl("new_year").setVisible(true);
        Xrm.Page.getControl("new_month").setVisible(true);
        Xrm.Page.getControl("new_ratio").setVisible(true);
        Xrm.Page.getControl("new_stietype").setVisible(true);
        //Xrm.Page.getControl("new_kpistatecode").setVisible(true);

        //类型为工单，安装时，KPI类型字段显示且必填,否则隐藏且非必填
        Xrm.Page.getControl("new_kpitype")?.setVisible(true);
        Xrm.Page.getAttribute('new_kpitype')?.setRequiredLevel('required');

    } else if (new_type == 2) {
        //备件-隐藏 一二三级品类、所属服务商、所属网点、系数、网点类型，仓储kpi明细
        //安装采用备件同样的显隐规则
        Xrm.Page.ui.tabs.get("tab_4").setVisible(false);
        Xrm.Page.getControl("new_category1_id").setVisible(false);
        Xrm.Page.getControl("new_category2_id").setVisible(false);
        Xrm.Page.getControl("new_category3_id").setVisible(false);
        Xrm.Page.getControl("new_station_id").setVisible(false);
        Xrm.Page.getControl("new_ratio").setVisible(false);
        Xrm.Page.getControl("new_serviceprovider_id").setVisible(false);
        Xrm.Page.getControl("new_stietype").setVisible(false);

        Xrm.Page.getControl("new_type").setVisible(true);
        Xrm.Page.getControl("new_year").setVisible(true);
        Xrm.Page.getControl("new_month").setVisible(true);
        //Xrm.Page.getControl("new_kpistatecode").setVisible(true);
        //类型为工单，安装时，KPI类型字段显示且必填,否则隐藏且非必填
        Xrm.Page.getControl("new_kpitype")?.setVisible(false);
        Xrm.Page.getAttribute('new_kpitype')?.setRequiredLevel('none');
        if (isshow) {
            Xrm.Page.ui.tabs.get("tab_3").setVisible(true);
        } else {
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
        }
    } else if (new_type == 4) {
        //仓储-隐藏 一二三级品类、所属服务商、所属网点、系数、网点类型,备件kpi明细
        Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
        Xrm.Page.getControl("new_category1_id").setVisible(false);
        Xrm.Page.getControl("new_category2_id").setVisible(false);
        Xrm.Page.getControl("new_category3_id").setVisible(false);
        Xrm.Page.getControl("new_station_id").setVisible(false);
        Xrm.Page.getControl("new_ratio").setVisible(false);
        Xrm.Page.getControl("new_serviceprovider_id").setVisible(false);
        Xrm.Page.getControl("new_stietype").setVisible(false);

        Xrm.Page.getControl("new_type").setVisible(true);
        Xrm.Page.getControl("new_year").setVisible(true);
        Xrm.Page.getControl("new_month").setVisible(true);
        //Xrm.Page.getControl("new_kpistatecode").setVisible(true);
        //类型为工单，安装时，KPI类型字段显示且必填,否则隐藏且非必填
        Xrm.Page.getControl("new_kpitype")?.setVisible(false);
        Xrm.Page.getAttribute('new_kpitype')?.setRequiredLevel('none');
        if (isshow) {
            Xrm.Page.ui.tabs.get("tab_4").setVisible(true);
        } else {
            Xrm.Page.ui.tabs.get("tab_4").setVisible(false);
        }
    }else {
        //否则全部显示
        Xrm.Page.getControl("new_category1_id").setVisible(false);
        Xrm.Page.getControl("new_category2_id").setVisible(false);
        Xrm.Page.getControl("new_category3_id").setVisible(false);
        Xrm.Page.getControl("new_station_id").setVisible(false);
        Xrm.Page.getControl("new_type").setVisible(true);
        Xrm.Page.getControl("new_serviceprovider_id").setVisible(true);
        Xrm.Page.getControl("new_year").setVisible(true);
        Xrm.Page.getControl("new_month").setVisible(true);
        Xrm.Page.getControl("new_ratio").setVisible(true);
        Xrm.Page.getControl("new_stietype").setVisible(false);
        Xrm.Page.getControl("new_kpistatecode").setVisible(false);
        Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
        Xrm.Page.ui.tabs.get("tab_4").setVisible(false);
        Xrm.Page.getControl("new_kpitype")?.setVisible(false);
        Xrm.Page.getAttribute('new_kpitype')?.setRequiredLevel('none');
    }

    //选择类型时，自动根据类型值对KPI类型字段进行筛选过滤
    Xrm.Page.getControl("new_kpitype_id").addPreSearch(function () {
        filterKpitype_id();
    });
}
function filterKpitype_id() {
    var new_type = Xrm.Page.getAttribute("new_type").getValue();//类型
    //选择类型时，自动根据类型值对KPI类型字段进行筛选过滤
    if (new_type) {
        var fetchXml = `
            <filter type='and'>
                <condition attribute='new_businesstype' operator='eq' value='${new_type}' />
            </filter>
        `;
        Xrm.Page.getControl("new_kpitype_id").addCustomFilter(fetchXml);
    }
}