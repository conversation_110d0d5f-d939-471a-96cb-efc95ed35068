﻿using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using RekTec.ServiceSettlement.Function.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace RekTec.ServiceSettlement.Function.Common
{
    public class HighdimensionalfactoryCommand
    {
        private ServiceClient OrganizationService;
        public ILogger logger;
        public ILogger log;
        public ActivateSettlementApiConfig apiconfig = null;
        //当前时间
        private static DateTime dateTime = DateTime.Now.AddMonths(-1);
        //开始时间
        private static DateTime beginDate = new DateTime(dateTime.Year, dateTime.Month, 1);
        //截止时间
        private static DateTime endDate = beginDate.AddMonths(1).AddMilliseconds(-1);
        OrganizationRequestCollection orColl = new OrganizationRequestCollection();
        //结算标准集合
        EntityCollection expensestandard = new EntityCollection();
        public HighdimensionalfactoryCommand(ILogger plog)
        {
            OrganizationService = (ServiceClient)CommonHelper.CreateConnection();
            logger = plog;
            expensestandard = GetExpenseStandard();
        }
        /// <summary>
        /// 查询修整单明细
        /// </summary>
        /// <returns></returns>
        public EntityCollection GetTrimmOrder(ILogger plog) 
        {
            QueryExpression qe = new QueryExpression("new_trimming_orderdetail");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_settlementtime", ConditionOperator.OnOrAfter, beginDate);
            qe.Criteria.AddCondition("new_settlementtime", ConditionOperator.OnOrBefore, endDate);
            qe.Criteria.AddCondition(new ConditionExpression("new_issettlement", ConditionOperator.In, new List<int>() { 1, 2 }));
            return OrganizationService.RetrieveMultiple(qe);
        }
        /// <summary>
        /// 工单分组后费用计算数据集合（B2X）   
        /// </summary>
        /// <param name="plog"></param>
        /// <param name="orderList"></param>
        /// <returns></returns>
        public List<List<Entity>> TraimmOrderGroupoCollection(ILogger plog, EntityCollection orderList)
        {
            List<List<Entity>> trimmorderCollectionhigh = new List<List<Entity>>();
            try
            {
                log = plog;
                log.LogInformation($"开始执行分组费用计算：{DateTime.Now}");
                var expenseClaimList = new List<Entity>();
                int i = 0;
                //根据ISP服务商ID分组处理
                foreach (var orderByStationList in orderList.Entities.GroupBy(m => m.GetAttributeValue<EntityReference>("new_srv_station_id").Id))
                {
                    //ISP服务商ID
                    var stationid = orderByStationList.Key;
                    log.LogInformation("stationid " + i + "：" + stationid);
                    //分组后的修整单集合
                    var order_stationList = orderByStationList.ToList();
                    log.LogInformation($"By 服务商高维工厂结算单生成：服务商ID：{stationid}");
                    trimmorderCollectionhigh.Add(order_stationList);
                }
                log.LogInformation($"执行分组费用计算结束：{DateTime.Now}");
                return trimmorderCollectionhigh;
            }
            catch (Exception ex)
            {
                log.LogInformation($"异常：{ex.Message}");
                log.LogInformation($"异常对象：{ex.Source}");
                log.LogInformation($"调用堆栈：{ex.StackTrace.Trim()}");
                log.LogInformation("【TraimmOrderGroupoCollection】错误" + ex.Message);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// B2X结算单生成
        /// </summary>
        /// <param name="traimmOrderCollection"></param>
        /// <param name="plog"></param>
        /// <returns></returns>
        public async Task CreateSAPLogHighdimensionalfactory(List<Entity> traimmOrderCollection, ILogger plog)
        {
            log = plog;
            log.LogInformation($"开始创建高维工厂结算单");
            try
            {
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                foreach (var orderByStationList in traimmOrderCollection.GroupBy(m => m.GetAttributeValue<EntityReference>("new_srv_station_id")))
                {
                    //服务商
                    var ispstation = orderByStationList.Key;
                    if (ispstation != null)
                    {
                        log.LogInformation("new_srv_stationid ：" + ispstation.Id);
                        //查询高维工厂服务商
                        var station = OrganizationService.Retrieve("new_srv_station", ispstation.Id, new ColumnSet(true));
                        //分组后的服务单集合
                        var order_stationList = orderByStationList.ToList();
                        log.LogInformation($"预提结算单生成，服务商ID：{station.Id}");
                        //生成结算单
                        CreatExpenseClaimHighdimensionalfactory(order_stationList, station);
                    }
                }
                stopwatch.Stop();
                log.LogInformation("创建高维工厂结算单结束：" + stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                log.LogInformation($"异常：{ex.Message}");
                log.LogInformation($"异常对象：{ex.Source}");
                log.LogInformation($"调用堆栈：{ex.StackTrace.Trim()}");
                log.LogInformation("【CreateSAPLogHighdimensionalfactory】错误" + ex.Message);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// B2X服务商创建结算预提单
        /// </summary>
        /// <param name="orderList">分组后的工单集合</param>
        /// <returns></returns>
        public void CreatExpenseClaimHighdimensionalfactory(List<Entity> orderList, Entity srvstation)
        {
            try
            {
                var workordercurrency = orderList.GroupBy(a=>a.GetAttributeValue<EntityReference>("new_transactioncurrency_service_id"));
                Guid transactioncurrencyid = Guid.Empty;
                if (workordercurrency.Count() == 1) 
                {
                    //有多个币种或者币种为空，则不生成高维工厂结算单
                    transactioncurrencyid = workordercurrency.FirstOrDefault().Key != null ? workordercurrency.FirstOrDefault().Key.Id : Guid.Empty;
                    if (transactioncurrencyid != Guid.Empty) 
                    {
                        //获取预提费（高维工厂）
                        var standard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 41
                         && a.ToDefault<Guid>("new_stationservice_id") == srvstation.Id
                         && !a.Contains("new_station_id") && !a.Contains("new_stietype")
                         && a.ToDefault<Guid>("new_transactioncurrency_id") == transactioncurrencyid).FirstOrDefault();
                        decimal withholdingfeeHighdimensionalfactory = 0m;
                        if (standard != null)
                            withholdingfeeHighdimensionalfactory = standard.GetAttributeValue<decimal>("new_amount");
                        OrganizationRequestCollection orColl = new OrganizationRequestCollection();
                        Entity station = srvstation;
                        var stationid = station.Id;
                        Entity expenseclaim = new Entity("new_srv_expense_claim");
                        expenseclaim.Id = Guid.NewGuid();
                        expenseclaim["new_businesstype"] = new OptionSetValue(10);//结算单类型 = 高维工厂
                        expenseclaim["new_srv_station_id"] = new EntityReference("new_srv_station", station.Id);
                        //国家/区域
                        expenseclaim["new_country_id"] = station.GetAttributeValue<EntityReference>("new_country_id");
                        //sap编码
                        expenseclaim["new_sapcode"] = station.GetAttributeValue<string>("new_supplier");
                        //结算年份
                        expenseclaim["new_year"] = new OptionSetValue(dateTime.Year);
                        //结算月份
                        expenseclaim["new_month"] = new OptionSetValue(dateTime.Month);
                        //结算单版本
                        expenseclaim["new_edition"] = new OptionSetValue(1);
                        //税率
                        expenseclaim["new_taxrate"] = station.GetAttributeValue<decimal>("new_taxrate") / 100;
                        //结算单状态
                        expenseclaim["new_formstatus"] = new OptionSetValue(1);
                        //成本中心
                        expenseclaim["new_costcenter"] = station.GetAttributeValue<string>("new_costcenter");
                        //利润中心
                        expenseclaim["new_profitcenter"] = station.GetAttributeValue<string>("new_profitcenter");
                        //付款方式
                        if (station.Contains("new_payway") && station.GetAttributeValue<OptionSetValue>("new_payway").Value != 3)
                        {
                            //服务网点付款方式 = 无（3）时，不赋值到结算单，结算单上无对应选项
                            expenseclaim["new_payway"] = station.GetAttributeValue<OptionSetValue>("new_payway");
                        }
                        //签约主体
                        expenseclaim["new_contractingbody"] = station.GetAttributeValue<string>("new_contractingbody");
                        //结算币种
                        expenseclaim["new_transactioncurrency_id"] = new EntityReference("transactioncurrency", transactioncurrencyid);
                        decimal sumAmount = orderList.Sum(a => a.GetAttributeValue<decimal>("new_high_dimensionalfee"));
                        sumAmount += withholdingfeeHighdimensionalfactory;
                        //费用总合计
                        expenseclaim["new_totalcost"] = sumAmount;
                        //结算劳务费
                        expenseclaim["new_servicemoney"] = sumAmount;
                        //结算费用=总费用*（1+税率）
                        expenseclaim["new_approvalamount"] = sumAmount * (1 + station.GetAttributeValue<decimal>("new_taxrate") / 100);
                        //预提费用
                        expenseclaim["new_withholdingmoney"] = sumAmount;
                        //预提费（高维工厂）
                        expenseclaim["new_withholdingfee"] = withholdingfeeHighdimensionalfactory;
                        //高维工厂劳务费
                        expenseclaim["new_high_dimensionalfee"] = orderList.Sum(a => a.GetAttributeValue<decimal>("new_high_dimensionalfee"));
                        //工单数量
                        //付款银行
                        QueryExpression query = new QueryExpression("new_paymentbank");
                        query.NoLock = true;
                        query.ColumnSet = new ColumnSet("new_bankcountry", "new_unionpay", "new_bankname", "new_bankaccountnumber", "new_swiftcode", "new_bank");
                        query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        query.Criteria.AddCondition("new_srv_station_id", ConditionOperator.Equal, station.Id);
                        //if (new_transactioncurrencyid != null)
                        //    query.Criteria.AddCondition("new_transactioncurrency_id", ConditionOperator.Equal, new_transactioncurrencyid.Id);
                        EntityCollection payBank = OrganizationService.RetrieveMultiple(query);
                        if (payBank?.Entities?.Count > 0)
                        {
                            var bank = payBank.Entities.FirstOrDefault();
                            //开户行国家
                            var new_bankcountry = bank.GetAttributeValue<string>("new_bankcountry");
                            //银联号
                            var new_unionpay = bank.GetAttributeValue<string>("new_unionpay");
                            //银行开户名
                            var new_bankname = bank.GetAttributeValue<string>("new_bankname");
                            //银行账号
                            var new_bankaccountnumber = bank.GetAttributeValue<string>("new_bankaccountnumber");
                            //SWIF CODE
                            var new_swiftcode = bank.GetAttributeValue<string>("new_swiftcode");
                            expenseclaim["new_bankcountry"] = new_bankcountry;
                            expenseclaim["new_unionnumber"] = new_unionpay;
                            expenseclaim["new_accountbankname"] = new_bankname;
                            expenseclaim["new_bankaccount"] = new_bankaccountnumber;
                            expenseclaim["new_swiftcode"] = new_swiftcode;
                            expenseclaim["new_bank"] = bank.ToDefault<string>("new_bank");
                        }
                        Guid guid = Guid.Empty;
                        guid = GetExpenseIdHighdimensionalfactory(stationid, dateTime.Year, dateTime.Month);
                        if (guid != Guid.Empty)
                        {
                            expenseclaim.Id = guid;
                            AddExecuteTransaction(expenseclaim, 2, ref orColl);
                        }
                        else
                        {
                            AddExecuteTransaction(expenseclaim, 1, ref orColl);
                            AddExecuteTransaction(new Entity(expenseclaim.LogicalName, expenseclaim.Id) { ["ownerid"] = station["ownerid"] }, 2, ref orColl);
                        }
                        //激活结算明细关联结算单
                        orColl.AddRange(AssociatedSettlementHighdimensionalfactory(orderList, expenseclaim.Id, withholdingfeeHighdimensionalfactory));
                        CommonHelper.BatchExecuteBatches(logger, OrganizationService, orColl).Wait();
                        orColl.Clear();
                        SettlementHelp settlementHelp = new SettlementHelp(logger);
                        //创建sap记录发sap  预提
                        SapCommand_new2 sapCommand = new SapCommand_new2(logger, OrganizationService);
                        var expense_claim = OrganizationService.Retrieve(expenseclaim.LogicalName, expenseclaim.Id, new ColumnSet(true));//需要重新Retrieve一遍，不然格式化值属性 FormattedValues 集合为空
                        settlementHelp.CreateSAPLogCostCenter(station, expenseclaim, 1, "预提", logger);
                        //创建sap记录发sap  反冲
                        settlementHelp.CreateSAPLogCostCenter(station, expenseclaim, 5, "反冲", logger);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("创建结算单方法 CreatExpenseClaimHighdimensionalfactory 异常：" + ex.Message);
            }
        }
        /// <summary>
        /// 查询结算标准
        /// </summary>
        /// <returns></returns>
        public EntityCollection GetExpenseStandard()
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_srv_expense_standard");
            qe.ColumnSet = new ColumnSet("new_country_id", "new_workordernumber", "new_station_id", "new_amount", "new_transactioncurrency_id", "new_feetype",
                "new_category1_id", "new_category2_id", "new_category3_id", "new_costweight", "new_optionalcharges", "new_ratio", "new_return_channel", "new_direction",
                "new_workinghours", "new_stationservice_id", "new_stietype", "new_feelabel", "new_issemiselfemployed", "new_materialcategory2_id", "new_category3id");
            qe.NoLock = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition(new ConditionExpression("new_feetype", ConditionOperator.In, new int[] {41}));
            cols = CommonHelper.QueryExpressionPage(OrganizationService, qe);
            return cols;
        }
        /// <summary>
        /// 高维工厂类型结算单是否存在
        /// </summary>
        /// <param name="new_srv_station_id"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="stieType"></param>
        /// <returns></returns>
        public Guid GetExpenseIdHighdimensionalfactory(Guid new_srv_station_id, int year, int month)
        {
            QueryExpression qe = new QueryExpression("new_srv_expense_claim");
            qe.NoLock = true;
            qe.Criteria.AddCondition("new_srv_station_id", ConditionOperator.Equal, new_srv_station_id);
            qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, year);
            qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, month);
            qe.Criteria.AddCondition("new_businesstype", ConditionOperator.Equal, 10);//结算单类型 = 高维工厂
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var ec = OrganizationService.RetrieveMultiple(qe);
            if (ec.Entities.Count > 0)
            {
                return ec.Entities.First().Id;
            }
            return Guid.Empty;
        }
        /// <summary>
        /// 事务处理
        /// </summary>
        /// <param name="orColl">事务</param>
        /// <param name="entity">待处理数据</param>
        /// <param name="type">类型</param>
        public void AddExecuteTransaction(Entity entity, int type, ref OrganizationRequestCollection orColl)
        {
            switch (type)
            {
                case 1:
                    orColl.Add(new CreateRequest() { Target = entity });
                    break;
                case 2:
                    orColl.Add(new UpdateRequest() { Target = entity });
                    break;
            }
        }
        /// <summary>
        /// 修整单明细关联高维工厂结算单
        /// </summary>
        /// <param name="orderList"></param>
        /// <param name="expenseclaimid"></param>
        /// <returns></returns>
        public OrganizationRequestCollection AssociatedSettlementHighdimensionalfactory(List<Entity> orderList, Guid expenseclaimid, decimal new_amount)
        {
            decimal aveamount = Math.Round(new_amount / orderList.Count, 2);
            decimal sumamount = 0m;
            OrganizationRequestCollection orColl = new OrganizationRequestCollection();
            foreach (var trimmorderdetail in orderList)
            {
                //预提费分摊
                int index = orderList.IndexOf(trimmorderdetail);
                var updatetrimmorderdetail = new Entity(trimmorderdetail.LogicalName, trimmorderdetail.Id);
                if (index == orderList.Count - 1)
                {
                    decimal lastamount = new_amount - sumamount;
                    updatetrimmorderdetail["new_withholdingfeehigh"] = lastamount;
                }
                else 
                {
                    updatetrimmorderdetail["new_withholdingfeehigh"] = aveamount;
                    sumamount += aveamount;
                }
                updatetrimmorderdetail["new_withholdingmoney"] = trimmorderdetail.GetAttributeValue<decimal>("new_high_dimensionalfee") 
                    + updatetrimmorderdetail.GetAttributeValue<decimal>("new_withholdingfeehigh");
                updatetrimmorderdetail["new_settlementmoney"] = trimmorderdetail.GetAttributeValue<decimal>("new_high_dimensionalfee")
                    + updatetrimmorderdetail.GetAttributeValue<decimal>("new_withholdingfeehigh");
                updatetrimmorderdetail["new_expenseclaim_id"] = new EntityReference("new_srv_expense_claim", expenseclaimid);
                updatetrimmorderdetail["new_issettlement"] = new OptionSetValue(2);//结算状态 = 结算中
                orColl.Add(new UpdateRequest() { Target = updatetrimmorderdetail });
            }
            return orColl;
        }
    }
}
