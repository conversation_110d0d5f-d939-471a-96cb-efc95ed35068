﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SettlementAction.Command;
using SettlementAction.Common;
using SettlementAction.Model;

namespace SettlementAction
{
    public class TrimorderdetailRematch : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            #region 使用组织服务公共代码段
            ITracingService tracingservice = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService organizationService = serviceFactory.CreateOrganizationService(context.UserId);
            IOrganizationService serviceAdmin = serviceFactory.CreateOrganizationService(null);
            #endregion
            //string output = "";//action 返回参数
            ResultOutput output = new ResultOutput();
            string trimmingorderdetailids = context.InputParameters["trimmingorderdetailids"]?.ToString();
            List<string> trimmingorderdetailidlist = JsonConvert.DeserializeObject<List<string>>(trimmingorderdetailids);
            try
            {
                QueryExpression qe = new QueryExpression("new_trimming_orderdetail");
                qe.ColumnSet = new ColumnSet("new_category3_id", "new_model1_id", "new_model2_id", "new_model3_id", "new_repair_method_id", "new_srv_station_id","new_returndate", "new_product_id",
                    "new_category3_code", "new_model1_code", "new_model2_code", "new_model3_code", "new_repair_method_code", "new_institution_id", "new_productcode", "new_issettlement", "new_receivetime");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition(new ConditionExpression("new_trimming_orderdetailid", ConditionOperator.In, trimmingorderdetailidlist));
                var trimorderdetailcols = serviceAdmin.RetrieveMultiple(qe);
                //查询是否有结算中和已结算的修整单，结算中和已结算的不可重新匹配
                var invalidtrimorder = trimorderdetailcols.Entities.Where(a => new int[] { 2, 3 }.Contains(a.ToDefault<int>("new_issettlement"))).ToList();
                if (invalidtrimorder.Count > 0) 
                {
                    output.code = "300";
                    output.msg = "结算中和已结算的修整单不可重新匹配！";
                    context.OutputParameters["outputresult"] = JsonConvert.SerializeObject(output);
                    return;
                }
                //查询三级品类lookup字段
                var categorys3codes = trimorderdetailcols.Entities.Where(a => a.Contains("new_category3_code")).Select(a => a.GetAttributeValue<string>("new_category3_code")).ToList();
                var categorys3cols = new EntityCollection();
                if (categorys3codes.Count > 0) 
                {
                    QueryExpression qe1 = new QueryExpression("new_category3");
                    qe1.ColumnSet = new ColumnSet("new_code");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_code", ConditionOperator.In, categorys3codes));
                    categorys3cols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询一级机型lookup字段
                var model1codes = trimorderdetailcols.Entities.Where(a => a.Contains("new_model1_code")).Select(a => a.GetAttributeValue<string>("new_model1_code")).ToList();
                var model1cols = new EntityCollection();
                if (model1codes.Count > 0)
                {
                    QueryExpression qe1 = new QueryExpression("new_model1");
                    qe1.ColumnSet = new ColumnSet("new_code", "new_spm_id");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_spm_id", ConditionOperator.In, model1codes));
                    model1cols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询二级机型lookup字段
                var model2codes = trimorderdetailcols.Entities.Where(a => a.Contains("new_model2_code")).Select(a => a.GetAttributeValue<string>("new_model2_code")).ToList();
                var model2cols = new EntityCollection();
                if (model2codes.Count > 0)
                {
                    QueryExpression qe1 = new QueryExpression("new_model2");
                    qe1.ColumnSet = new ColumnSet("new_code", "new_spm_id");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_spm_id", ConditionOperator.In, model2codes));
                    model2cols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询三级机型lookup字段
                var model3codes = trimorderdetailcols.Entities.Where(a => a.Contains("new_model3_code")).Select(a => a.GetAttributeValue<string>("new_model3_code")).ToList();
                var model3cols = new EntityCollection();
                if (model3codes.Count > 0)
                {
                    QueryExpression qe1 = new QueryExpression("new_model3");
                    qe1.ColumnSet = new ColumnSet("new_code", "new_spm_id");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_spm_id", ConditionOperator.In, model3codes));
                    model3cols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询维修方法lookup字段
                var repairwaycodes = trimorderdetailcols.Entities.Where(a => a.Contains("new_repair_method_code")).Select(a => a.GetAttributeValue<string>("new_repair_method_code")).ToList();
                var repairwaycols = new EntityCollection();
                if (repairwaycodes.Count > 0) 
                {
                    QueryExpression qe1 = new QueryExpression("new_repair_method");
                    qe1.ColumnSet = new ColumnSet("new_code");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_code", ConditionOperator.In, repairwaycodes));
                    repairwaycols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询ISP服务商lookup字段
                var xmscodes = trimorderdetailcols.Entities.Where(a => a.Contains("new_institution_id")).Select(a => a.GetAttributeValue<string>("new_institution_id")).ToList();
                var stationcols = new EntityCollection();
                if (xmscodes.Count > 0)
                {
                    QueryExpression qe1 = new QueryExpression("new_srv_station");
                    qe1.ColumnSet = new ColumnSet("new_xmscode", "new_code");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("new_xmscode", ConditionOperator.In, xmscodes));
                    stationcols = serviceAdmin.RetrieveMultiple(qe1);
                }
                //查询物料lookup字段
                var productcodes = trimorderdetailcols.Entities.Where(a => a.Contains("new_productcode")).Select(a => a.GetAttributeValue<string>("new_productcode")).ToList();
                var productcols = new EntityCollection();
                var goods_filescols = new EntityCollection();
                if (productcodes.Count > 0)
                {
                    QueryExpression qe1 = new QueryExpression("product");
                    qe1.ColumnSet = new ColumnSet("productnumber", "new_materialcategory2_id");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition(new ConditionExpression("productnumber", ConditionOperator.In, productcodes));
                    productcols = serviceAdmin.RetrieveMultiple(qe1);
                    //查询商品档案lookup字段
                    QueryExpression qe2 = new QueryExpression("new_goodsfiles");
                    qe2.ColumnSet = new ColumnSet("new_projectcode", "new_commoditycode", "new_sku");
                    qe2.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe2.Criteria.AddCondition(new ConditionExpression("new_commoditycode", ConditionOperator.In, productcodes));
                    goods_filescols = serviceAdmin.RetrieveMultiple(qe2);
                }
                foreach (var repairorder in trimorderdetailcols.Entities)
                {
                    Entity trimorderorder = new Entity(repairorder.LogicalName, repairorder.Id);
                    if (repairorder.Contains("new_category3_code")) 
                    {
                        var category3 = categorys3cols.Entities.Where(b => b.GetAttributeValue<string>("new_code") == repairorder.GetAttributeValue<string>("new_category3_code")).FirstOrDefault();
                        if (category3 != null)
                        {
                            trimorderorder["new_category3_id"] = category3.ToEntityReference();
                            repairorder["new_category3_id"] = category3.ToEntityReference();
                        }
                        else 
                        {
                            trimorderorder["new_category3_id"] = null;
                            repairorder["new_category3_id"] = null;
                        }
                    }
                    if (repairorder.Contains("new_model1_code")) 
                    {
                        var model1 = model1cols.Entities.Where(b => b.GetAttributeValue<string>("new_spm_id") == repairorder.GetAttributeValue<string>("new_model1_code")).FirstOrDefault();
                        if (model1 != null) 
                        {
                            trimorderorder["new_model1_id"] = model1.ToEntityReference();
                            repairorder["new_model1_id"] = model1.ToEntityReference();
                        }
                        else 
                        {
                            trimorderorder["new_model1_id"] = null;
                            repairorder["new_model1_id"] = null;
                        }
                    }
                    if (repairorder.Contains("new_model2_code"))
                    {
                        var model2 = model2cols.Entities.Where(b => b.GetAttributeValue<string>("new_spm_id") == repairorder.GetAttributeValue<string>("new_model2_code")).FirstOrDefault();
                        if (model2 != null) 
                        {
                            trimorderorder["new_model2_id"] = model2.ToEntityReference();
                            repairorder["new_model2_id"] = model2.ToEntityReference();
                        }
                        else
                        {
                            trimorderorder["new_model2_id"] = null;
                            repairorder["new_model2_id"] = null;
                        }
                    }
                    if (repairorder.Contains("new_model3_code"))
                    {
                        var model3 = model3cols.Entities.Where(b => b.GetAttributeValue<string>("new_spm_id") == repairorder.GetAttributeValue<string>("new_model3_code")).FirstOrDefault();
                        if (model3 != null) 
                        {
                            trimorderorder["new_model3_id"] = model3.ToEntityReference();
                            repairorder["new_model3_id"] = model3.ToEntityReference();
                        }
                        else
                        {
                            trimorderorder["new_model3_id"] = null;
                            repairorder["new_model3_id"] = null;
                        }
                    }
                    if (repairorder.Contains("new_repair_method_code"))
                    {
                        var repairway = repairwaycols.Entities.Where(b => b.GetAttributeValue<string>("new_code") == repairorder.GetAttributeValue<string>("new_repair_method_code")).FirstOrDefault();
                        if (repairway != null)
                        {
                            trimorderorder["new_repair_method_id"] = repairway.ToEntityReference();
                            repairorder["new_repair_method_id"] = repairway.ToEntityReference();
                        }
                        else 
                        {
                            trimorderorder["new_repair_method_id"] = null;
                            repairorder["new_repair_method_id"] = null;
                            trimorderorder["new_settlereason"] = "未查询到ISP内对应的维修方法信息";
                        }
                    }
                    if (repairorder.Contains("new_institution_id"))
                    {
                        var ispstation = stationcols.Entities.Where(b => b.GetAttributeValue<string>("new_xmscode") == repairorder.GetAttributeValue<string>("new_institution_id")).FirstOrDefault();
                        if (ispstation != null)
                        {
                            trimorderorder["new_srv_station_id"] = ispstation.ToEntityReference();
                            repairorder["new_srv_station_id"] = ispstation.ToEntityReference();
                            trimorderorder["new_servicestation_code"] = ispstation.GetAttributeValue<string>("new_code");
                        }
                        else 
                        {
                            trimorderorder["new_srv_station_id"] = null;
                            repairorder["new_srv_station_id"] = null;
                            trimorderorder["new_servicestation_code"] = null;
                            trimorderorder["new_settlereason"] = "未查询到ISP内对应的服务商机构信息";
                        }
                    }
                    var materialcategory2_id = Guid.Empty;
                    if (repairorder.Contains("new_productcode"))
                    {
                        var product = productcols.Entities.Where(b => b.GetAttributeValue<string>("productnumber") == repairorder.GetAttributeValue<string>("new_productcode")).FirstOrDefault();
                        if (product != null)
                        {
                            trimorderorder["new_product_id"] = product.ToEntityReference();
                            repairorder["new_product_id"] = product.ToEntityReference();
                            if (product.Contains("new_materialcategory2_id"))
                                materialcategory2_id = product.GetAttributeValue<EntityReference>("new_materialcategory2_id").Id;
                        }
                        else 
                        {
                            trimorderorder["new_product_id"] = null;
                            repairorder["new_product_id"] = null;
                        }
                        var goodsfiles = goods_filescols.Entities.Where(b => b.GetAttributeValue<string>("new_commoditycode") == repairorder.GetAttributeValue<string>("new_productcode")).FirstOrDefault();
                        if (goodsfiles != null)
                        {
                            trimorderorder["new_goodsfiles_id"] = goodsfiles.ToEntityReference();
                            trimorderorder["new_itemcode"] = goodsfiles.GetAttributeValue<string>("new_sku");
                        }
                        else 
                        {
                            trimorderorder["new_goodsfiles_id"] = null;
                            trimorderorder["new_itemcode"] = repairorder.GetAttributeValue<string>("new_productcode");
                        }
                    }
                    string settlereason = trimorderorder.GetAttributeValue<string>("new_settlereason");
                    if (string.IsNullOrWhiteSpace(settlereason))
                    {
                        //查询结算标准，匹配高维工厂劳务费
                        if (repairorder.Contains("new_srv_station_id") && repairorder.GetAttributeValue<EntityReference>("new_srv_station_id") != null)
                        {
                            var expensestandard = Getexpensestandard(repairorder.GetAttributeValue<EntityReference>("new_srv_station_id").Id.ToString(), serviceAdmin);
                            //计算高维工厂维修劳务费
                            var standard = CalcutationHighdimensionalFee(expensestandard, repairorder, materialcategory2_id);
                            if (standard != null)
                            {
                                trimorderorder["new_high_dimensionalfee"] = standard.GetAttributeValue<decimal>("new_amount");
                                trimorderorder["new_issettlement"] = new OptionSetValue(1);//结算状态 = 可结算
                                trimorderorder["new_settlereason"] = null;
                                if (standard.Contains("new_transactioncurrency_id"))
                                    trimorderorder["new_transactioncurrency_service_id"] = standard["new_transactioncurrency_id"];
                                if (repairorder.Contains("new_receivetime"))
                                    trimorderorder["new_settlementtime"] = repairorder["new_receivetime"];//可结算日期 = 签收时间
                            }
                            else
                            {
                                trimorderorder["new_settlementtime"] = null;
                                trimorderorder["new_high_dimensionalfee"] = null;
                                trimorderorder["new_transactioncurrency_service_id"] = null;
                                trimorderorder["new_settlereason"] = "未查询到有效的结算标准";
                                trimorderorder["new_issettlement"] = new OptionSetValue(4);//结算状态 = 不可结算
                            }
                        }
                    }
                    else
                    {
                        trimorderorder["new_settlementtime"] = null;
                        trimorderorder["new_high_dimensionalfee"] = null;
                        trimorderorder["new_transactioncurrency_service_id"] = null;
                        trimorderorder["new_issettlement"] = new OptionSetValue(4);//结算状态 = 不可结算
                    }
                    serviceAdmin.Update(trimorderorder);
                }
                output.code = "200";
            }
            catch (Exception ex)
            {
                output.code = "300";
                output.msg = ex.Message;
                throw new InvalidPluginExecutionException(ex.Message);
            }
            finally
            {
                context.OutputParameters["outputresult"] = JsonConvert.SerializeObject(output);
            }
        }
        /// <summary>
        /// 根据服务商查询结算标准
        /// </summary>
        /// <param name="stationid"></param>
        /// <param name="organizationService"></param>
        /// <returns></returns>
        public EntityCollection Getexpensestandard(string stationid, IOrganizationService organizationService)
        {
            QueryExpression qe = new QueryExpression("new_srv_expense_standard");
            qe.ColumnSet = new ColumnSet("new_stationservice_id", "new_transactioncurrency_id", "new_category3_id", "new_model1id", "new_model2id", "new_model3id", "new_repairmethodid", "new_feetype", "new_materialcategory2_id", "new_amount");
            qe.Criteria.AddCondition("new_stationservice_id", ConditionOperator.Equal, stationid);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_feetype", ConditionOperator.Equal, 158);//费用类型 = 高维工厂劳务费
            return organizationService.RetrieveMultiple(qe);
        }
        /// <summary>
        /// 计算高维工厂劳务费
        /// </summary>
        /// <param name="expensestandardlist"></param>
        /// <param name="workorder"></param>
        /// <returns></returns>
        public static Entity CalcutationHighdimensionalFee(EntityCollection expensestandardlist, Entity repairdetial, Guid materialcategory2_id)
        {
            Entity matchedstandard = null;
            //服务商+三级品类+一级机型+二级机型+三级机型+物料子类别+维修方法
            matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
            && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
            && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
            && a.Contains("new_model2id") && a.GetAttributeValue<string>("new_model2id").Contains(repairdetial.ToDefault<Guid>("new_model2_id").ToString())
            && a.Contains("new_model3id") && a.GetAttributeValue<string>("new_model3id").Contains(repairdetial.ToDefault<Guid>("new_model3_id").ToString())
            && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
            && a.Contains("new_repairmethodid") && a.GetAttributeValue<string>("new_repairmethodid").Contains(repairdetial.ToDefault<Guid>("new_repair_method_id").ToString())).FirstOrDefault();
            //服务商+三级品类+一级机型+二级机型+物料子类别+维修方法
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
           && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
           && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
           && a.Contains("new_model2id") && a.GetAttributeValue<string>("new_model2id").Contains(repairdetial.ToDefault<Guid>("new_model2_id").ToString())
           && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
           && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
           && a.Contains("new_repairmethodid") && a.GetAttributeValue<string>("new_repairmethodid").Contains(repairdetial.ToDefault<Guid>("new_repair_method_id").ToString())).FirstOrDefault();
            //服务商+三级品类+一级机型+物料子类别+维修方法
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
           && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
           && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
           && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
           && (!a.Contains("new_model2id") || a.GetAttributeValue<string>("new_model2id") == "[]")
           && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
           && a.Contains("new_repairmethodid") && a.GetAttributeValue<string>("new_repairmethodid").Contains(repairdetial.ToDefault<Guid>("new_repair_method_id").ToString())).FirstOrDefault();
            //服务商+三级品类+一级机型+二级机型+三级机型+物料子类别
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
            && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
            && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
            && a.Contains("new_model2id") && a.GetAttributeValue<string>("new_model2id").Contains(repairdetial.ToDefault<Guid>("new_model2_id").ToString())
            && a.Contains("new_model3id") && a.GetAttributeValue<string>("new_model3id").Contains(repairdetial.ToDefault<Guid>("new_model3_id").ToString())
            && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
            && (!a.Contains("new_repairmethodid") || a.GetAttributeValue<string>("new_repairmethodid") == "[]")).FirstOrDefault();
            //服务商+三级品类+一级机型+二级机型+物料子类别
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
            && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
            && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
            && a.Contains("new_model2id") && a.GetAttributeValue<string>("new_model2id").Contains(repairdetial.ToDefault<Guid>("new_model2_id").ToString())
            && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
            && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
            && (!a.Contains("new_repairmethodid") || a.GetAttributeValue<string>("new_repairmethodid") == "[]")).FirstOrDefault();
            //服务商+三级品类+一级机型+物料子类别
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
            && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
            && a.Contains("new_model1id") && a.GetAttributeValue<string>("new_model1id").Contains(repairdetial.ToDefault<Guid>("new_model1_id").ToString())
            && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
            && (!a.Contains("new_model2id") || a.GetAttributeValue<string>("new_model2id") == "[]")
            && (!a.Contains("new_repairmethodid") || a.GetAttributeValue<string>("new_repairmethodid") == "[]")
            && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id).FirstOrDefault();
            //服务商+三级品类+物料子类别+维修方法
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
           && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
           && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
           && (!a.Contains("new_model2id") || a.GetAttributeValue<string>("new_model2id") == "[]")
           && (!a.Contains("new_model1id") || a.GetAttributeValue<string>("new_model1id") == "[]")
           && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id
           && a.Contains("new_repairmethodid") && a.GetAttributeValue<string>("new_repairmethodid").Contains(repairdetial.ToDefault<Guid>("new_repair_method_id").ToString())).FirstOrDefault();
            //服务商+三级品类+物料子类别
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
           && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
           && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
           && (!a.Contains("new_model2id") || a.GetAttributeValue<string>("new_model2id") == "[]")
           && (!a.Contains("new_model1id") || a.GetAttributeValue<string>("new_model1id") == "[]")
           && (!a.Contains("new_repairmethodid") || a.GetAttributeValue<string>("new_repairmethodid") == "[]")
           && a.Contains("new_materialcategory2_id") && a.ToDefault<Guid>("new_materialcategory2_id") == materialcategory2_id).FirstOrDefault();
            //服务商+三级品类
            if (matchedstandard == null)
                matchedstandard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == repairdetial.ToDefault<Guid>("new_srv_station_id")
           && a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id") == repairdetial.ToDefault<Guid>("new_category3_id")
           && (!a.Contains("new_model3id") || a.GetAttributeValue<string>("new_model3id") == "[]")
           && (!a.Contains("new_model2id") || a.GetAttributeValue<string>("new_model2id") == "[]")
           && (!a.Contains("new_model1id") || a.GetAttributeValue<string>("new_model1id") == "[]")
           && (!a.Contains("new_repairmethodid") || a.GetAttributeValue<string>("new_repairmethodid") == "[]")
           && !a.Contains("new_materialcategory2_id")).FirstOrDefault();
            return matchedstandard;
        }
    }
}
