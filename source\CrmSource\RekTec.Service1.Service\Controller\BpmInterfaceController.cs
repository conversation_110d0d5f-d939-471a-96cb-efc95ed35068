﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using RekTec.Crm.HiddenApi;
using RekTec.Service1.Service.Command;

namespace RekTec.Service1.Service.Controller
{
    public class BpmInterfaceController : HiddenApiController
    {
        /// <summary>
        /// 危机事件提交Bpm
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        public virtual string BpmCriticalPay(string entityId)
        {
            return this.Command<BpmCriticalPayCommand>().SubmitBpm("new_critical", entityId, 1, null, "new_approvestatus");
        }
        /// <summary>
        /// 特殊费用申请提交bpm
        /// </summary>
        /// <param name="id">实体id</param>
        /// <returns></returns>
        public virtual string BpmSpecialSubmit(string id)
        {
            return this.Command<BpmSpecialCommand>().SubmitBpm("new_srv_specialexpense", id, 1, null, "new_approvestatus");
        }

        /// <summary>
        /// 特殊费用申请撤回bpm
        /// </summary>
        /// <param name="id">实体id</param>
        /// <returns></returns>
        public virtual string BpmSpecialRecall(string id)
        {
            return this.Command<BpmSpecialCommand>().SubmitBpm("new_srv_specialexpense", id, 2, null, "new_approvestatus");
        }

        /// <summary>
        /// 费用结算 BPM审批 
        /// 抛转成功后更新结算单状态
        /// </summary>
        /// <param name="id">费用结算id</param>
        /// <param name="formType">抛转类型 3：发票审核；6：付款申请</param>
        /// <param name="businesstype">业务类型 1：劳务费；2：备件费 5：仓储费</param>
        /// <returns></returns>
        public virtual string BpmExpenseSubmit(string id, string formType, int businesstype, string businesskey, bool isrollback)
        {
            try
            {
                var res = "";
                if (isrollback) 
                {
                    //退回到发起节点，调用submit接口重新提交，先调current接口查询当前节点
                    res = this.Command<BpmExpenseCommand>().SubmitBPMrollback("new_srv_expense_claim", id, businesstype, businesskey);
                }
                else 
                {
                    // 付款申请先分派给当前结算专员，后续审核通过需要抛给sap
                    if (formType == "6")
                    {
                        RekTec.Crm.BizCommon.CommonHelper.Assign(OrganizationService, "new_srv_expense_claim", new Guid(id), this.UserId);
                    }
                    Log.InfoMsg("费用结算-审核抛BPM开始..." + DateTime.Now.ToString());

                    if (businesstype == 3)
                    {
                        //结算单类型 = 迈创
                        res = this.Command<BpmExpenseCommand>().SubmitPartsBPM("new_srv_expense_claim", id);
                    }
                    else if (businesstype == 1 || businesstype == 2 || businesstype == 4 || businesstype == 6 || businesstype == 8 || businesstype == 9)
                    {
                        //结算单类型 = 非B2X，B2X，运营商，激活，收集点,大家电安装
                        res = this.Command<BpmExpenseCommand>().SubmitServiceOrderBPM("new_srv_expense_claim", id);
                    }
                    else if (businesstype == 5 || businesstype == 10) { // 仓储费、高维工厂
                        res = this.Command<BpmExpenseCommand>().SubmitWarehousingBPM("new_srv_expense_claim", id);
                    }
                    else if (businesstype == 7) { // 物流费
                        res = this.Command<BpmExpenseCommand>().SubmitLogisticsBPM("new_srv_expense_claim", id);
                    }
                    Log.InfoMsg("费用结算-审核抛BPM结束..." + DateTime.Now.ToString());
                    // 发票审核抛转后修改结算单状态
                    //this.Command<BpmExpenseCommand>().UpdateStatus(id, formType);
                    Log.InfoMsg("费用结算修改状态结束");

                }
                return res;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }

        }
    }
}
