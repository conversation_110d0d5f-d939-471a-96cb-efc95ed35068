﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace RekTec.ServiceSettlement.Function.Model
{
    /// <summary>
    /// 维修方法实体类
    /// </summary>
    public class RepairMethodModel
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public int id { get; set; } 
        /// <summary>
        /// 维修方法代码
        /// </summary>
        public string config_code { get; set; }
        /// <summary>
        /// 维修方法名称
        /// </summary>
        public string config_name { get; set; }
        /// <summary>
        /// 是否有效
        /// </summary>
        public string data_status { get; set; }
        /// <summary>
        /// 操作类型
        /// </summary>
        public string operate_way { get; set; }
    }
    public class RepairMethodRequest 
    {
        public List<RepairMethodModel> repairWayList { get; set; }      
    }
    /// <summary>
    /// 修整单明细实体类
    /// </summary>
    public class RepairOrder
    {
        [JsonProperty("macno")]
        public string macno { get; set; }  // 物品编号

        [JsonProperty("macName")]
        public string macName { get; set; }  // 修整品名称

        [JsonProperty("imei")]
        public string imei { get; set; }  // 新串号信息

        [JsonProperty("repairId")]
        public string repairId { get; set; }  // 修整单号

        [JsonProperty("orgId")]
        public string orgId { get; set; }  // XMS机构ID

        [JsonProperty("orgName")]
        public string orgName { get; set; }  // XMS机构名称

        [JsonProperty("sapSupplier")]
        public string sapSupplier { get; set; }  // SAP供应商编码

        [JsonProperty("prodId")]
        public string prodId { get; set; }  // 关联单据号

        [JsonProperty("status")]
        public string status { get; set; }  // 修整单状态

        [JsonProperty("transferId")]
        public string transferId { get; set; }  // 调拨单号

        [JsonProperty("transferStatus")]
        public string transferStatus { get; set; }  // 调拨状态

        [JsonProperty("isFastRepair")]
        public string isFastRepair { get; set; }  // 是否快速修整

        [JsonProperty("isGoodsRepair")]
        public string isGoodsRepair { get; set; }  // 是否商品修整

        [JsonProperty("isSpecialRepair")]
        public string isSpecialRepair { get; set; }  // 是否特殊修整

        [JsonProperty("brandClassName")]
        public string brandClassName { get; set; }  // 三级品类

        [JsonProperty("brandClassId")]
        public long brandClassId { get; set; }  // 三级品类id

        [JsonProperty("firClassName")]
        public string firClassName { get; set; }  // 一级机型

        [JsonProperty("firClassId")]
        public long firClassId { get; set; }  // 一级机型id

        [JsonProperty("secClassName")]
        public string secClassName { get; set; }  // 二级机型

        [JsonProperty("secClassId")]
        public long secClassId { get; set; }  // 二级机型id

        [JsonProperty("thrClassName")]
        public string thrClassName { get; set; }  // 三级机型

        [JsonProperty("thrClassId")]
        public long thrClassId { get; set; }  // 三级机型id

        [JsonProperty("repairWay")]
        public long repairWay { get; set; }  // 维修方法

        [JsonProperty("repairWayName")]
        public string repairWayName { get; set; }  // 维修方法名称

        [JsonProperty("repairResult")]
        public string repairResult { get; set; }  // 维修结果

        [JsonProperty("returnTime")]
        [JsonConverter(typeof(DateTimeConverter))]
        public DateTime returnTime { get; set; }  // 返还时间
    }
    public class DateTimeConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType) => objectType == typeof(DateTime);

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            => DateTime.ParseExact((string)reader.Value, "yyyy-MM-dd HH:mm:ss", null);

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            => writer.WriteValue(((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss"));
    }
    public class TrimmingdetailSync 
    {
        /// <summary>
        /// 物品编号
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 串号信息 新串号
        /// </summary>
        public string imei { get; set; }
        /// <summary>
        /// 修整单号 
        /// </summary>
        public string repairId { get; set; }
        /// <summary>
        /// 同步结果
        /// </summary>
        public string syncResult { get; set; }
        /// <summary>
        /// 同步失败原因
        /// </summary>
        public string syncFailReason { get; set; }
    }
    public class Header
    {
        /// <summary>
        /// appid
        /// </summary>
        public string appid { get; set; }
        /// <summary>
        /// sign
        /// </summary>
        public string sign { get; set; }
        /// <summary>
        /// project
        /// </summary>
        public string project { get; set; }
        /// <summary>
        /// id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// index
        /// </summary>
        public List<string> index { get; set; }
    }

    public class RepairOrderRequest
    {
        /// <summary>
        /// 
        /// </summary>
        public Header header { get; set; }
        /// <summary>
        /// ["{\"macno\":\"950C3K0S0001\",\"macName\":\"套机-Redmi8A-全球美规-2GB+32GB-蓝色\",\"imei\":\"861731482458053\",\"repairId\":\"XZ2504293900003\",\"orgId\":\"XZHK1002\",\"orgName\":\"深圳市畅思得电子设备维护有限公司\",\"sapSupplier\":\"SUP001\",\"prodId\":\"XZPO2504293900003\",\"transferId\":\"TR250429003\",\"transferStatus\":\"COMPLETED\",\"isFastRepair\":\"N\",\"isGoodsRepair\":\"N\",\"isSpecialRepair\":\"N\",\"status\":\"received_end\",\"brandClassName\":\"手机\",\"firClassName\":\"Redmi\",\"secClassName\":\"Redmi8A\",\"thrClassName\":\"全球美规\",\"repairWayName\":\"主板更换\",\"repairResult\":\"已修复\"}"]
        /// </summary>
        public string body { get; set; }
    }
    public class CountryAllocationratio 
    {
        /// <summary>
        /// 国家id
        /// </summary>
        public string countryid { get; set; }
        /// <summary>
        /// 工单数量
        /// </summary>
        public int workordercount { get; set; }
        /// <summary>
        /// 工单量占比
        /// </summary>
        public decimal ratio { get; set; }  
    }
    /// <summary>
    /// 高维工厂劳务费按照国家分摊
    /// </summary>
    public class Highdimensionalsapmodel
    {
        /// <summary>
        /// SKU3
        /// </summary>
        public string new_sku { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string new_costcenter { get; set; }
        /// <summary>
        /// 内部订单号
        /// </summary>
        public string new_internalorder { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoney { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 工单数量
        /// </summary>
        public int new_workordercount { get; set; }
    }

}
