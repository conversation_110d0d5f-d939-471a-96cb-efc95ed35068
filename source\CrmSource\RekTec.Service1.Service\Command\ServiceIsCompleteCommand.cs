﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Discovery;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Crm.BizCommon;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.HiddenApi;
using RekTec.Crm.OrganizationService.Common.Helper;
using RekTec.Service1.Service.Bll;
using RekTec.Service1.Service.Helper;
using RekTec.Service1.Service.Model;
using XiaoMi.Crm.BaseCommon.ApplicationInsights;
using XiaoMi.Crm.BaseCommon.Model;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using Newtonsoft.Json;
using RekTec.Service1.Service.Command;
using RekTec.Crm.Plugin.Helper;
using RekTec.Service1.Service.Constants;
using RekTec.Api.Model.Oc;
namespace RekTec.Service1.Service.Command
{
    /// <summary>
    /// 服务单服务完成按钮
    /// </summary>
    public class ServiceIsCompleteCommand : HiddenCommand
    {
        public void LogaHao(Action func, string desc)
        {

            Stopwatch sw = new Stopwatch();
            sw.Start();
            func.Invoke();
            sw.Stop();
            Log.InfoMsg($"{desc},耗时:{sw.ElapsedMilliseconds}");
        }

        /// <summary>
        /// 服务完成 从前端转到后端
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="installRights">安装转退换 可用安装权益</param>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public void ServiceComplete(string orderId ,List<usableInstallRightsModel> installRights=null)
        {
            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch st = new Stopwatch();
            try
            {
                Log.InfoMsg("工单服务完成开始:" + orderId);
                #region 获取数据
                Stopwatch sw = new Stopwatch();
                sw.Start();
                QueryExpression workOrderQuery = new QueryExpression("new_srv_workorder")
                {
                    ColumnSet = new ColumnSet("new_intdelivery_type", "new_latitude", "new_longitude",
                    "new_country_id", "new_province_id", "new_city_id", "new_county_id", "new_address",
                    "new_srv_workorderid", "new_servicemode", "new_exchange_way", "new_type", "new_servicestation_id",
                    "new_station_id", "new_mmiwhitelist", "new_warechangeway", "new_shipordernumber",
                    "new_goodsfiles_id", "new_category3_id", "new_ordercode", "new_name", "new_manualfee",
                    "new_model3_id", "new_model2_id", "new_model1_id", "new_category2_id", "new_category1_id",
                    "new_dealstatus", "new_recoilstate", "new_contact", "new_sumreceivableamount", "new_pay_manualfee", "new_pay_materialcost",
                    "new_pay_materialcost", "new_owcurrency", "new_sumtrueamount", "new_warranty", "new_warrantystatus", "createdon",
                    "new_b2x_orderid", "new_servicesubmode", "new_origin", "new_isforceinstall", "new_reduce_labor_cost", "new_actualpay_manualfee", "new_manualfee", "new_tms_pictures"),
                    Criteria = new FilterExpression
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_srv_workorderid", ConditionOperator.Equal, orderId),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    },
                    LinkEntities =
                    {
                        new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("new_code","new_stietype","new_operationmodecode"),
                            EntityAlias = "station",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            },
                            LinkEntities =
                            {
                                new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter)
                                {
                                    Columns = new ColumnSet("new_code"),
                                    EntityAlias = "new_country_id2",
                                    LinkCriteria = new FilterExpression
                                    {
                                        Conditions =
                                        {
                                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                        }
                                    }
                                }
                            }
                        },
                        new LinkEntity("new_srv_workorder", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("new_code"),
                            EntityAlias = "country",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            }
                        },
                        new LinkEntity("new_srv_workorder", "transactioncurrency", "new_owcurrency", "transactioncurrencyid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("isocurrencycode"),
                            EntityAlias = "owcurrency",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            }
                        }
                    }
                };
                Entity workOrder = OrganizationService.RetrieveMultiple(workOrderQuery).Entities.FirstOrDefault();     
                if (workOrder?.Id == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                int new_type = workOrder.Contains("new_type") ? workOrder.GetAttributeValue<OptionSetValue>("new_type").Value : 0;  //服务类型
                int new_refund_way = workOrder.Contains("new_refund_way") ? workOrder.GetAttributeValue<OptionSetValue>("new_refund_way").Value : 0; //退货方式
                int new_exchange_way = workOrder.Contains("new_exchange_way") ? workOrder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0; //换货方式
                int new_servicemode = workOrder.Contains("new_servicemode") ? workOrder.GetAttributeValue<OptionSetValue>("new_servicemode").Value : 0; //服务方式
                int warechangeWay = workOrder.Contains("new_warechangeway") ? workOrder.GetAttributeValue<OptionSetValue>("new_warechangeway").Value : 0;
                sw.Stop();
                Log.InfoMsg($"获取数据,耗时:{sw.ElapsedMilliseconds}");
                #endregion 获取数据

                #region 校验状态
                sw.Restart();
                int new_dealstatus = workOrder.Contains("new_dealstatus") ? workOrder.GetAttributeValue<OptionSetValue>("new_dealstatus").Value : 0;
                if (new_dealstatus != 6)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CannotComplete", "当前工单状态不为处理中，无法进行服务完成操作"));
                }

                //前端转后端，退货场景校验仓库类型必填
                StockTypeRequiredCheck(workOrder.Id, new_type);


                //added by p-huyan9 2023/06/12 服务完成判断服务工单配件更换明细物料类别
                JudgeMaterialCategoryAllowComplete(workOrder.Id);
                //end add by  p-huyan9 2023/06/12 服务完成判断服务工单配件更换明细物料类别

                int new_warranty = workOrder.Contains("new_warranty") ? workOrder.GetAttributeValue<OptionSetValue>("new_warranty").Value : 0;
                int new_warrantystatus = workOrder.Contains("new_warrantystatus") ? workOrder.GetAttributeValue<OptionSetValue>("new_warrantystatus").Value : 0;
                if (new_warranty == 2 && new_warrantystatus != 2)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NeedUser", "当前工单需要【确认联系用户】，确认已与用户联系报价后才能够完工"));
                }

                //校验更换件明细
                bool needCheck = CheckPartlineSnOrImei(workOrder);
                if (needCheck)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.SnOrImei.Required", "工程师处理的配件更换的sn或者新sn需要维护"));
                }
                //校验附件必填
                bool attachmentCheck = CheckAttachmentRequired(workOrder);
                if (attachmentCheck)
                {
                    throw new InvalidPluginExecutionException(GetResource("CheckAttachmentRequired.Pleaseuploadtheattachmentfirst", "请先上传附件"));
                }

                //modified by BaronKang 2022-07-25 优化修改判断方法为：如果有不为已到料的数据 则不允许完工

                //判断配件明细的领料状态
                var partlineList = JudgeAllowComplete(workOrder.Id, new_type);

                sw.Stop();
                Log.InfoMsg($"校验状态,耗时:{sw.ElapsedMilliseconds}");
                #endregion 校验状态

                #region 自动关单场景字段校验(工单上传、工单对接、B2X、多服务商来源除外)
                var originlist = new int[] { 50, 60, 100, 120 };
                int origin = workOrder.Contains("new_origin") ? workOrder.GetAttributeValue<OptionSetValue>("new_origin").Value : 0;//工单来源
                if (!originlist.Contains(origin) && !IsB2XWorkOrder(workOrder.Id))
                {
                    //是否可受理
                    bool handlable = Command<WorkOrderCommand>().IsExistenceApproach(orderId);
                    var returnType = new int[] { 2, 3 };
                    var dlscompany = workOrder.Contains("new_deliverylogisticscompany") ? workOrder.GetAttributeValue<string>("new_deliverylogisticscompany") : string.Empty;
                    bool ismeet = workOrder.Contains("new_ismeet") ? workOrder.GetAttributeValue<bool>("new_ismeet") : false;
                    //服务类型为退货，退货方式为【小米网退款、线上平台退款】 || 服务类型为换货，换货方式=大仓换货 || 服务类型为维修且大仓以换代修发货方式=大仓发货
                    if ((new_type == 3 && returnType.Contains(new_refund_way)) || (new_type == 2 && new_exchange_way == 2) || (new_type == 1 && warechangeWay == 2))
                    {
                        //非写号工单
                        if (workOrder.Contains("new_iswriteno") && !workOrder.GetAttributeValue<bool>("new_iswriteno"))
                        {
                            //换货：大仓换货，可受理； 维修：以换代修(sg调用大仓换货，cn调用createorder)
                            if ((new_type == 2 && new_exchange_way == 2 && handlable) || (new_type == 1 && warechangeWay == 2))
                            {
                                if ((!string.IsNullOrWhiteSpace(dlscompany) && dlscompany.Equals("CN")) || ismeet)
                                {
                                    var query = new QueryExpression("new_srv_workorder");
                                    query.ColumnSet = new ColumnSet("new_contact", "new_feedbacktel", "new_email", "new_country_id", "new_province_id", "new_city_id", "new_county_id", "new_address", "new_miliao");
                                    query.Criteria.AddCondition("new_srv_workorderid", ConditionOperator.Equal, orderId);
                                    query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                    //关联国家查找国家id
                                    LinkEntity link = new LinkEntity("new_srv_workorder", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter);
                                    link.Columns = new ColumnSet("new_id", "new_code1");
                                    link.EntityAlias = "ctry";
                                    link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                    query.LinkEntities.Add(link);
                                    //关联省份，查询省份id
                                    LinkEntity linkpr = new LinkEntity("new_srv_workorder", "new_province", "new_province_id", "new_provinceid", JoinOperator.LeftOuter);
                                    linkpr.Columns = new ColumnSet("new_id");
                                    linkpr.EntityAlias = "provic";
                                    linkpr.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                    query.LinkEntities.Add(linkpr);
                                    //关联城市，查询城市id
                                    LinkEntity linkcity = new LinkEntity("new_srv_workorder", "new_city", "new_city_id", "new_cityid", JoinOperator.LeftOuter);
                                    linkcity.Columns = new ColumnSet("new_id");
                                    linkcity.EntityAlias = "city";
                                    linkcity.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                    query.LinkEntities.Add(linkcity);
                                    //关联区县，获取区县id
                                    LinkEntity linkcounty = new LinkEntity("new_srv_workorder", "new_county", "new_county_id", "new_countyid", JoinOperator.LeftOuter);
                                    linkcounty.Columns = new ColumnSet("new_id", "new_postcode");
                                    linkcounty.EntityAlias = "county";
                                    linkcounty.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                    query.LinkEntities.Add(linkcounty);
                                    query.TopCount = 1;
                                    var orderList = OrganizationService.RetrieveMultiple(query);
                                    if (orderList != null && orderList.Entities.Count > 0)
                                    {
                                        var order = orderList.Entities[0];
                                        //反馈人
                                        if (!order.Contains("new_contact"))
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Checkfeedbackname", "反馈人为空，请维护反馈人"));
                                        //反馈人电话
                                        if (!order.Contains("new_feedbacktel"))
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Checkfeedbacktel", "反馈人电话为空，请维护反馈人电话"));
                                        //地址
                                        if (!order.Contains("new_address"))
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Checkaddress", "客户信息-具体地址为空，请完善信息"));
                                        if (!order.Contains("ctry.new_id"))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Country_empty", "请维护工单国家"));
                                        }
                                        if (!order.Contains("provic.new_id"))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Province_empty", "请维护工单省份"));
                                        }
                                        if (!order.Contains("city.new_id"))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.City_empty", "请维护工单城市"));
                                        }
                                        if (!order.Contains("county.new_id"))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.County_empty", "请维护工单区县"));
                                        }
                                        if (!order.Contains("county.new_postcode"))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CountyPostCode_empty", "请维护工单区县的邮编"));
                                        }
                                    }
                                }
                                else
                                {
                                    if (!ismeet)
                                    {
                                        bool result = CheckCountryIsShipping(orderId);
                                        if (!result)
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CountryNotSupportOkura_empty", "工单所属国家未配置大仓发货渠道"));
                                        }
                                        if (string.IsNullOrEmpty(dlscompany))
                                        {
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.origin_empty", "订单来源类型为空"));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                #region 更新工单

                Entity updateOrder = new Entity("new_srv_workorder", new Guid(orderId));

                //记录初检，复检时间
                if (new_type == 1 || new_type == 4 || new_type == 6)
                {
                    updateOrder = UpdateOrderCheckTime(workOrder.Id);
                }
                Log.InfoMsg($"记录初检，复检时间,耗时:{sw.ElapsedMilliseconds}");

                //服务完工防呆校验
                int way = ServiceIsComplete(workOrder, partlineList);
                if (way > 0)
                {
                    //更新工单发货类型
                    updateOrder["new_warechangeway"] = new OptionSetValue(way);
                    warechangeWay = way;
                }

                //取消报案  add by elvawang 2021/12/7
                LogaHao(() =>
                {
                    //保外取消报案
                    if (new_warranty == 2)
                    {
                        Command<InsuranceConfirmCommand>().ServiceCancelInsurance(workOrder);
                    }
                }, "取消报案");


                
                #region 转派场景，修改状态为 待返还受理点/转派网点
                int new_servicesubmode = workOrder.Contains("new_servicesubmode") ? workOrder.GetAttributeValue<OptionSetValue>("new_servicesubmode").Value : 0;
                int new_origin = workOrder.Contains("new_origin") ? workOrder.GetAttributeValue<OptionSetValue>("new_origin").Value : 0;
                if (new_origin == 20)
                {
                    //【来源】=20受理单转录,【状态】改为13完工待返还受理点
                    updateOrder["new_repairprovider_id"] = workOrder.GetAttributeValue<EntityReference>("new_servicestation_id");//维修服务商赋值为当前服务商
                    updateOrder["new_repairstation_id"] = workOrder.GetAttributeValue<EntityReference>("new_station_id"); // 维修服务网点
                    updateOrder["new_dealstatus"] = new OptionSetValue(13);
                    updateOrder["new_completiontime"] = DateTime.UtcNow;
                    OrganizationService.Update(updateOrder);
                    return;
                }
                else if (new_servicesubmode == 5)
                {
                    //【来源】!=20受理单转录，【服务子方式】=5代寄送，【状态】更新为11完工待返还网点
                    updateOrder["new_repairprovider_id"] = workOrder.GetAttributeValue<EntityReference>("new_servicestation_id");//维修服务商赋值为当前服务商
                    updateOrder["new_repairstation_id"] = workOrder.GetAttributeValue<EntityReference>("new_station_id"); // 维修服务网点
                    updateOrder["new_dealstatus"] = new OptionSetValue(11);
                    var new_b2x_orderid = workOrder.Contains("new_b2x_orderid") ? workOrder.GetAttributeValue<string>("new_b2x_orderid") : "";
                    if(new_origin != 60 && string.IsNullOrWhiteSpace(new_b2x_orderid))
                        updateOrder["new_completiontime"] = DateTime.UtcNow;
                    OrganizationService.Update(updateOrder);
                    return;
                }

                #endregion 转派场景

                #region 更新工单，推送数据

                //两个方法各场景都调用，提到前面
                LogaHao(() =>
                {
                    UpdateWorkOrderStatus(workOrder, updateOrder, partlineList);
                }, "更新工单");
                //只有上门工单同步工单至手机端
                if (new_servicemode == 2)
                {
                    LogaHao(() =>
                    {
                        Command<Msdyn_workorderCommand>().WorkOrderToApp(new Guid(orderId));
                    }, "推送数据");
                }
                //自动关单改由Flow执行：AutoCloseCaseAfterServiceComplete

                //var returnType = new int[] { 2, 3 };
                ////服务类型为退货，退货方式为【小米网退款、线上平台退款】/服务类型为换货，换货方式为大仓换货  或者 维修且大仓发货
                //if ((new_type == 3 && returnType.Contains(new_refund_way)) || (new_type == 2 && new_exchange_way == 2) || (new_type == 1 && warechangeWay == 2))
                //{
                //    LogaHao(() =>
                //    {
                //        // 维修大仓发货直接关单
                //        if (new_type == 1 && warechangeWay == 2)
                //        {
                //            //Command<WorkorderCommand_close>().CloseWorkOrder(new Guid(orderId));
                //            OrganizationService.InvokeHiddenApi("new_service", "WorkOrder/CloseCase", new Dictionary<string, object>() { { "workOrderId", new Guid(orderId) } });
                //        }
                //        else
                //        {
                //            bool handlable = Command<WorkOrderCommand>().IsExistenceApproach(orderId);
                //            if (handlable)
                //            {
                //                //Command<WorkorderCommand_close>().CloseWorkOrder(new Guid(orderId));
                //                OrganizationService.InvokeHiddenApi("new_service", "WorkOrder/CloseCase", new Dictionary<string, object>() { { "workOrderId", new Guid(orderId) } });
                //            }
                //        }
                //    }, "服务完成直接关单");
                //}
                #endregion 更新工单，推送数据

                #endregion

                #region 发送通知短信
                LogaHao(() =>
                {
                    SendCompleteMessage(workOrder);
                }, "发送通知短信");
                #endregion
                // 虚拟服务妥投处理
                if (installRights != null && installRights.Count > 0)
                {
                    InstallRightsHanding(workOrder, new_type == (int)WorkOrderType.Refund ? InstallRightsCommand.Refund : InstallRightsCommand.Swap, installRights);
                }
                st.Stop();
                Log.InfoMsg($"工单服务已完成，总耗时{st.ElapsedMilliseconds}");
            }
            catch (Exception e)
            {
                st.Stop();
                int Duration = (int)st.ElapsedMilliseconds;
                Log.InfoMsg($"[ServiceComplete]方法报错: {e.Message}。持续时间: {Duration} ms");
                Log.LogException(e);
                throw new InvalidPluginExecutionException(e.Message);
            }
        }
        /// <summary>
        /// 校验当前工单是否需要上传附件
        /// </summary>
        /// <param name="workOrder"></param>
        /// <returns></returns>
        private bool CheckAttachmentRequired(Entity workOrder)
        {
            if (workOrder.Contains("new_origin") && workOrder.Contains("new_type"))
            {
                //来源
                var new_origin = workOrder.GetAttributeValue<OptionSetValue>("new_origin").Value;
                //服务类型
                var new_type = workOrder.GetAttributeValue<OptionSetValue>("new_type").Value;
                //手工录入（10）受理单转录 （20）案例转录 （30）自助服务(40)无串号建单（90）
                int[] originList = new int[] { 10, 20, 30, 40, 90 };
                //退货（3） 换货（2）
                int[] typeList = new int[] { 3, 2 };
                if (originList.Contains(new_origin) && typeList.Contains(new_type))
                {
                    var attachment_country_code_required = Crm.Common.Helper.CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "attachment_country_code_required", false);
                    if (string.IsNullOrWhiteSpace(attachment_country_code_required))
                    {
                        return false;
                    }
                    var codeList = attachment_country_code_required.Split(',');
                    var new_code = workOrder.GetAliasAttributeValue<string>("new_country_id2.new_code");
                    //工单国家是否属于配置国家
                    if (!codeList.Contains(new_code))
                    {
                        return false;
                    }
                    QueryExpression que = new QueryExpression("new_picturepath");
                    que.ColumnSet = new ColumnSet("new_picturepathid", "new_formstatus", "new_name", "new_relationentityname");

                    que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    que.Criteria.AddCondition("new_formstatus", ConditionOperator.Equal, 1);
                    que.Criteria.AddCondition("new_relationentityname", ConditionOperator.Equal, "new_srv_workorder");
                    que.Criteria.AddCondition("new_relationid", ConditionOperator.Equal, workOrder.Id.ToString().ToUpper());
                    var list = OrganizationServiceAdmin.RetrieveMultiple(que);
                    if (list == null || list.Entities == null || list.Entities.Count <= 0)
                    {
                        return true;
                    }

                }
            }
            return false;
        }
        /// <summary>
        /// 处理安装权益，根据发货单状态 推妥投 和 使用安装权益
        /// </summary>
        /// <param name="workOrder">工单实体</param>
        /// <param name="new_type"></param>
        /// <param name="installRights"></param>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        private void InstallRightsHanding(Entity workOrder,int new_type, List<usableInstallRightsModel> installRights)
        {
            try
            {

                //不需要推送的发货单状态列表
                var excludedStatuses = new[] { WaybillStatus.losted, WaybillStatus.losted_input_waiting, WaybillStatus.losted_input_already, WaybillStatus.losted_payment_already, WaybillStatus.rejected, WaybillStatus.reject_return_waiting, WaybillStatus.reject_return_already, WaybillStatus.part_reject_return_waiting, WaybillStatus.reject_return_on_road,  WaybillStatus.lost_sup_return_input_waiting, WaybillStatus.lost_sup_return_input_already, WaybillStatus.lost_reject_input_waition, WaybillStatus.lost_reject_input_alaready, WaybillStatus.lost_reject_input_on_road, WaybillStatus.closed, WaybillStatus.closed_cancel_refund, WaybillStatus.closed_undeliver_reject };
                var deliveryStatus = new[] { WaybillStatus.deliver_already, WaybillStatus.deliver_already_home_send, WaybillStatus.deliver_already_take_their };

                var installRight = installRights.FirstOrDefault(e =>
                {
                    string status = e.new_deliverystatus;
                    return !excludedStatuses.Contains((WaybillStatus)(int.Parse(status)));
                });
               
                if (installRight != null)
                {
                    #region 推送OC虚拟发货单妥投，重新生成可用安装权益
                    string status = installRight.new_deliverystatus;
                    if(!deliveryStatus.Contains((WaybillStatus)(int.Parse(status))))
                    {
                        string new_id = Command<ServiceIsCompleteCommand>().GetCountryCode(workOrder);//受理地国家数字编码
                        Command<WorkorderCommand_close>().VirtualShippingOrderPushOCDelivery(installRight.new_delivery, new_id);//虚拟发货单推送OC妥投
                        Command<InstallRightsCommand>().GetUsableInstallRights(workOrder.Id.ToString());//重新生成可用安装权益
                    }
                    #endregion

                    #region 生成安装权益使用记录
                  
                    string new_ordercode = installRight.new_ordercode;
                    string new_delivery = installRight.new_delivery;

                    QueryExpression queryUsedinstallrights = new QueryExpression("new_srv_usedinstallrights");
                    queryUsedinstallrights.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    queryUsedinstallrights.Criteria.AddCondition("new_ordercode", ConditionOperator.Equal, new_ordercode);
                    queryUsedinstallrights.Criteria.AddCondition("new_delivery", ConditionOperator.Equal, new_delivery);

                    LinkEntity linkWorkOrder = queryUsedinstallrights.AddLink("new_srv_workorder", "new_srv_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                    linkWorkOrder.Columns.AddColumn("new_dealstatus");
                    linkWorkOrder.LinkCriteria.AddCondition("new_dealstatus", ConditionOperator.Equal, ((int)WorkOrderDealStatus.CLOSED));
                    EntityCollection resultsUsedEC = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryUsedinstallrights);
                    if (resultsUsedEC?.Entities?.Count > 0)
                    {
                        //安装权益已使用,正常完工
                        return;
                    }
                    else
                    {
                        //生成安装权益使用记录
                        usableInstallRightsModel rightsModel = new usableInstallRightsModel();
                        rightsModel.new_delivery = new_delivery;
                        rightsModel.new_ordercode = new_ordercode;
                        rightsModel.new_goodscode = installRight.new_goodscode;
                        rightsModel.new_goodsfiles_id = installRight.new_goodsfiles_id;
                        Command<InstallRightsCommand>().CreateUsedInstallRights(workOrder.Id.ToString(), rightsModel, null, new_type);
                    }
                    #endregion

                }

            }
            catch (Exception ex)
            {
                Log.InfoMsg($"[InstallRightsHanding] 出错：{ex.ToString()}");
                throw new InvalidPluginExecutionException(ex.Message);

            }
       
        }

        /// <summary>
        /// 手机端点击服务完成
        /// </summary>
        /// <param name="orderId">msdyn_workorderId 手机端工单id</param>
        public void ServiceCompleteMobile(string orderId, string new_latitude, string new_longitude, string Client,List<usableInstallRightsModel> installrights = null)
        {
            try
            {
                Entity msdynOrder = OrganizationService.RetrieveWithBypassPlugin("msdyn_workorder", new Guid(orderId), new ColumnSet("new_srv_workorder_id", "new_dealstatus"));
                if (!msdynOrder.Contains("new_srv_workorder_id"))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                Guid workorderId = msdynOrder.GetAttributeValue<EntityReference>("new_srv_workorder_id").Id;

                var new_dealstatus = msdynOrder.Contains("new_dealstatus") ? msdynOrder.GetAttributeValue<OptionSetValue>("new_dealstatus").Value : 0;
                if (new_dealstatus != 6)
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CannotComplete", "当前工单状态不为处理中，无法进行服务完成操作"));
           
                //首先同步app的数据到PC自定义服务单
                Command<Msdyn_workorderCommand>().WorkOrderFromApp(new Guid(orderId));

                //查询工单数据
                QueryExpression workOrderQuery = new QueryExpression("new_srv_workorder")
                {
                    ColumnSet = new ColumnSet("new_type", "new_servicemode", "new_servicestation_id", "new_station_id",
                    "new_userprofilerights_id", "new_country_id", "new_shipordernumber", "new_goodsfiles_id", "new_category3_id",
                    "new_warranty", "new_mmiwhitelist", "new_refund_way", "new_exchange_way", "new_warechangeway",
                    "new_dealstatus", "new_warrantystatus", "new_servicesubmode", "new_origin", "new_completiontime",
                    "new_model3_id", "new_model2_id", "new_model1_id", "new_category2_id", "new_category1_id",
                    "new_iswriteno", "new_deliverylogisticscompany", "new_ismeet", "new_intdelivery_type", "new_ordercode", "new_sumreceivableamount",
                    "new_name", "new_recoilstate", "new_contact", "new_pay_manualfee", "new_pay_materialcost",
                    "new_owcurrency", "new_sumtrueamount", "new_b2x_orderid", "new_isforceinstall", "ownerid", "new_tms_pictures"),
                    Criteria = new FilterExpression
                    {
                        Conditions =
                            {
                                new ConditionExpression("new_srv_workorderid", ConditionOperator.Equal, workorderId),
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                            }
                    },
                    LinkEntities =
                    {
                        new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("new_code","new_stietype","new_operationmodecode"),
                            EntityAlias = "station",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            }
                        },
                        new LinkEntity("new_srv_workorder", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("new_code"),
                            EntityAlias = "country",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            }
                        },
                        new LinkEntity("new_srv_workorder", "transactioncurrency", "new_owcurrency", "transactioncurrencyid", JoinOperator.LeftOuter)
                        {
                            Columns = new ColumnSet("isocurrencycode"),
                            EntityAlias = "owcurrency",
                            LinkCriteria = new FilterExpression
                            {
                                Conditions =
                                {
                                    new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                }
                            }
                        }
                    }
                };
                Entity workOrder = OrganizationService.RetrieveMultiple(workOrderQuery).Entities.FirstOrDefault();
                int new_type = workOrder.Contains("new_type") ? workOrder.GetAttributeValue<OptionSetValue>("new_type").Value : 0;//服务类型
                int new_exchange_way = workOrder.Contains("new_exchange_way") ? workOrder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0; //换货方式
               

                Entity updateOrder = new Entity("new_srv_workorder", workorderId);

                //前端转后端，退货场景校验仓库类型必填
                StockTypeRequiredCheck(workOrder.Id, new_type);

                //added by p-huyan9 2023/06/12 服务完成判断服务工单配件更换明细物料类别
                JudgeMaterialCategoryAllowComplete(workOrder.Id);
                //end add by  p-huyan9 2023/06/12 服务完成判断服务工单配件更换明细物料类别
                bool needCheck = CheckPartlineSnOrImei(workOrder);
                if (needCheck)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.SnOrImei.Required", "工程师处理的配件更换的sn或者新sn需要维护"));
                }

                //维修、换货 判断配件明细的领料状态
                var partlineList = JudgeAllowComplete(workorderId, new_type);

                //更新工单初检复检时间
                if (new_type == 1 || new_type == 4 || new_type == 6)
                {
                    updateOrder = UpdateOrderCheckTime(workorderId);
                }
                // 虚拟服务妥投处理 
                if (installrights != null && installrights.Count > 0)
                {
                    InstallRightsHanding(workOrder, new_type == (int)WorkOrderType.Refund ? InstallRightsCommand.Refund : InstallRightsCommand.Swap, installrights);
                }
                //服务完工防呆校验
                int way = ServiceIsComplete(workOrder, partlineList); 

                if (way > 0)
                {
                    //更新工单发货类型
                    updateOrder["new_warechangeway"] = new OptionSetValue(way);
                }

                //更新工单
                UpdateWorkOrderStatus(workOrder, updateOrder, partlineList);

                //发送通知短信
                SendCompleteMessage(workOrder);

                //生成终端日志
                GenerationLog(new_latitude, new_longitude, Client, workOrder);

                //同步信息到移动端
                Command<Msdyn_workorderCommand>().WorkOrderToApp(workorderId);

            }
            catch (Exception ex)
            {
                Log.InfoMsg($"[ServiceCompleteMobile]方法报错,{ex.Message}");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 校验受理商品存在多条得时候，也需要相同得故障件
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="partlineList"></param>
        private void CheckAcceptProductReplacement(Entity workOrder, EntityCollection partlineList)
        {
            try
            {
                if (!workOrder.TryGetAttributeValue<OptionSetValue>("new_type", out OptionSetValue serviceType))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.ServiceNull", "服务类型为空！"));
                }
                #region 查询受理物品的信息
                QueryExpression productline = new QueryExpression("new_srv_productline");
                productline.ColumnSet = new ColumnSet("new_productid", "new_sn", "new_workorder_id");
                productline.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                productline.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, workOrder.Id);
                var productlinedata = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(productline);
                #endregion
                //两个受理物品时校验
                if (productlinedata?.Entities?.Count == 2)
                { //  两个受理物品时，换货基于goodsid各自关联的更换件明细要=1，否则抛出异常
                    if (serviceType.Value == ((int)WorkOrderType.Wwap)) 
                    {
                        if (partlineList.Entities.Count == 2)
                        {
                            // 受理物品中的SN
                            var goodsSNs = productlinedata.Entities
                                .Where(entity => entity.Contains("new_sn"))
                                .Select(entity => entity.GetAttributeValue<string>("new_sn"))
                                .ToArray();
                            // 更换件中的SN
                            var productSNs = partlineList.Entities
                                .Where(e => e.Contains("new_oldsn"))
                                .Select(e => e.GetAttributeValue<string>("new_oldsn"))
                                .ToArray();
                            if (goodsSNs.Length != productSNs.Length)
                            {
                                throw new InvalidPluginExecutionException(GetResource("CheckAcceptProductReplacement", "请维护【goodsid】的配件更换明细!"));
                            }
                            var goodsSet = new HashSet<string>(goodsSNs);
                            var productSet = new HashSet<string>(productSNs);
                            if (!goodsSet.SetEquals(productSet))
                            {
                                throw new InvalidPluginExecutionException(GetResource("CheckAcceptProductReplacement", "请维护【goodsid】的配件更换明细!"));
                            }
                        }
                        else
                        {
                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CloseOrder.Return.UpdateUserProfile", "受理物品数量与更换件数量不一致"));
                        }

                    }
  
                }
 
            }
            catch (Exception ex)
            {
                Log.InfoMsg($"[CheckAcceptProductReplacement]方法报错,{ex.Message}");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException($"{ex.Message}");
            }
        }


        /// <summary>
        /// 前端转后端，退货 校验仓库类型
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="new_type"></param>
        private void StockTypeRequiredCheck(Guid workOrderId,int new_type)
        {
            try
            {
                if(new_type!=((int)WorkOrderType.Refund))
                {
                    return;
                }

                QueryExpression queryReturnLine = new QueryExpression("new_srv_changereturnline")
                {
                    ColumnSet = new ColumnSet("new_stocktype"),
                    Criteria = new FilterExpression
                    {
                        FilterOperator = LogicalOperator.And,
                        Conditions =
                        {   
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0),
                            new ConditionExpression("new_workorder_id",ConditionOperator.Equal,workOrderId),
                            new ConditionExpression("new_stocktype", ConditionOperator.Null) 
                        }
                    }
                };
                var returnLines =OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryReturnLine);
                if (returnLines?.Entities?.Count > 0)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CheckWarehouse", "请维护每一个退货明细的仓库类型"));
                }

            }
            catch(Exception ex)
            {
                Log.InfoMsg($"[StockTypeRequiredCheck]方法报错,{ex.Message}");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException($"{ex.Message}");

            }

        }

        private void GenerationLog(string new_latitude, string new_longitude, string Client, Entity orderPC)
        {
            var new_compliance = false;
            if (!string.IsNullOrEmpty(new_latitude) &&
                !string.IsNullOrEmpty(new_longitude)
                && (new_latitude != "0" || new_longitude != "0"))
            {
                Log.InfoMsg("GenerationLog_Mobile:new_latitude" + new_latitude + ";new_longitude:" + new_longitude);
                //规定范围内到达千米
                var distanceLimit = Crm.Common.Helper.CrmHelper.GetSystemParameterValue(OrganizationService, "ArriveattheRestrictionDistance", true);

                if (!string.IsNullOrEmpty(distanceLimit))
                {
                    //获取用户经纬度信息 
                    if (orderPC.Contains("new_latitude") && orderPC.Contains("new_longitude") && (orderPC.GetAttributeValue<decimal>("new_latitude") != 0 || orderPC.GetAttributeValue<decimal>("new_longitude") != 0))
                    {
                        var latitude = orderPC.GetAttributeValue<decimal>("new_latitude");
                        var longitude = orderPC.GetAttributeValue<decimal>("new_longitude");
                        var distance = CommonHelper.GetDistance(Convert.ToDouble(new_latitude), Convert.ToDouble(new_longitude), Convert.ToDouble(latitude), Convert.ToDouble(longitude));
                        new_compliance = (distance / 1000.0000) <= Convert.ToDouble(distanceLimit);
                    }
                    else
                    {
                        #region 计算客户地址经纬度
                        var country = orderPC.Contains("new_country_id") ? orderPC.GetAttributeValue<EntityReference>("new_country_id")?.Name : "";//国家
                        var province = orderPC.Contains("new_province_id") ? orderPC.GetAttributeValue<EntityReference>("new_province_id")?.Name : "";//省份
                        var city = orderPC.Contains("new_city_id") ? orderPC.GetAttributeValue<EntityReference>("new_city_id")?.Name : "";//城市
                        var county = orderPC.Contains("new_county_id") ? orderPC.GetAttributeValue<EntityReference>("new_county_id")?.Name : "";//区县
                        var new_address = orderPC.Contains("new_address") ? orderPC.GetAttributeValue<string>("new_address") : "";//具体地址
                        var account_address = $"{country} {province} {city} {county} {new_address}";
                        if (!string.IsNullOrEmpty(account_address))
                        {
                            string GoogleUrl = Crm.Common.Helper.CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "GoogleMapURL", true);
                            if (!string.IsNullOrEmpty(GoogleUrl))
                            {
                                string url = string.Format(GoogleUrl, account_address);
                                using (HttpClient client = new HttpClient())
                                {
                                    using (HttpResponseMessage response = client.GetAsync(url).Result)
                                    {
                                        Log.InfoMsg($"GenerationLog:url:{url}");
                                        if (response.IsSuccessStatusCode)
                                        {
                                            string content = response.Content.ReadAsStringAsync().Result;
                                            Log.InfoMsg($"GenerationLog:content:{content}");
                                            JObject json = JObject.Parse(content);
                                            if (json["results"] != null && json["results"].HasValues)
                                            {
                                                var location = json["results"][0]["geometry"]["location"];
                                                double lat = (double)location["lat"];
                                                double lng = (double)location["lng"];
                                                //计算差——公里
                                                if (lat.ToString() != "0" || lng.ToString() != "0")
                                                {
                                                    var distance = CommonHelper.GetDistance(Convert.ToDouble(new_latitude), Convert.ToDouble(new_longitude), lat, lng);
                                                    new_compliance = (distance / 1000.0000) <= Convert.ToDouble(distanceLimit);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            Log.InfoMsg($"GenerationLogError:url:{url}");
                                        }
                                    }
                                }
                            }
                        }
                        #endregion
                    }
                }
            }

            Entity record = new Entity("new_operatortermiallog");
            record["new_operatortime"] = DateTime.UtcNow; // Date Time
            record["new_businessphase"] = new OptionSetValue(4); // Choice
            record["new_srv_workorder_id"] = new EntityReference("new_srv_workorder", orderPC.Id); // Lookup
            record["new_compliance"] = new_compliance; // Boolean
            if (Client == "PC")
                record["new_operatortermial"] = new OptionSetValue(1); // Choice
            else
                record["new_operatortermial"] = new OptionSetValue(2); // Choice
            Guid id = OrganizationService.Create(record);


        }
        /// <summary>
        /// 判断配件明细是否允许完工                                                 
        /// </summary>
        /// <param name="orderId"></param>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public EntityCollection JudgeAllowComplete(Guid orderId, int new_type)
        {
            try
            {
                if (new_type != 1 && new_type != 2 && new_type != 5)
                    return null;
                QueryExpression qe_part = new QueryExpression("new_srv_partline");
                qe_part.ColumnSet = new ColumnSet("new_srv_partlineid", "new_status", "new_islackmaterial", "new_oldsn");
                //qe_part.Criteria.AddCondition("new_status", ConditionOperator.NotEqual, 4);
                qe_part.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe_part.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                var partdata = OrganizationService.RetrieveMultipleWithBypassPlugin(qe_part);
                if (partdata != null && partdata.Entities != null && partdata.Entities.Count > 0)
                {
                    if (partdata.Entities.Any(x => x.GetAttributeValue<OptionSetValue>("new_status").Value != 4 && x.GetAttributeValue<OptionSetValue>("new_islackmaterial")?.Value != 1))
                    {
                        throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.MaterialStatus", "配件更换明细中尚有未领料、待发料、缺料的物料，无法完工"));
                    }
                }
                return partdata;
            }
            catch (Exception ex)
            {
                Log.InfoMsg($"[JudgeAllowComplete]方法报错,{ex.Message}");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 记录初检复检时间
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public Entity UpdateOrderCheckTime(Guid orderId)
        {
            try
            {
                Entity updateOrder = new Entity("new_srv_workorder", orderId);

                QueryExpression qe_imeicheck = new QueryExpression("new_srv_workorder_imeicheck");
                qe_imeicheck.ColumnSet = new ColumnSet("createdon");
                qe_imeicheck.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe_imeicheck.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                qe_imeicheck.AddOrder("createdon", OrderType.Ascending);
                var imeicheckdata = OrganizationService.RetrieveMultipleWithBypassPlugin(qe_imeicheck);

                if (imeicheckdata != null && imeicheckdata.Entities != null && imeicheckdata.Entities.Count > 0)
                {
                    int count = imeicheckdata.Entities.Count;
                    if (count == 1)
                    {
                        updateOrder["new_check_time"] = imeicheckdata.Entities[0].GetAttributeValue<DateTime>("createdon");
                    }
                    else
                    {
                        updateOrder["new_check_time"] = imeicheckdata.Entities[0].GetAttributeValue<DateTime>("createdon");
                        updateOrder["new_check_time2"] = imeicheckdata.Entities[count - 1].GetAttributeValue<DateTime>("createdon");
                    }
                }
                return updateOrder;
            }
            catch (Exception ex)
            {
                Log.InfoMsg($"[UpdateOrderCheckTime]方法报错,{ex.Message}");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 服务完成，当工单为换货或者维修换主板和套机时，校验配件更换明细中新sn和imei是否必填
        /// 创建人：esterwang
        /// 创建时间:2021-11-20
        /// </summary>
        /// <param name="orderId">工单</param>
        /// <returns></returns>
        public bool CheckPartlineSnOrImei(Entity order)
        {
            try
            {
                bool result = false;

                if (order == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                // 获取工单类型
                if (!order.TryGetAttributeValue<OptionSetValue>("new_type", out OptionSetValue new_type))
                {
                    return result;
                }
                //获取换货方式
                int newExchangeWay = order.Contains("new_exchange_way") ? order.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;

                if (!(new_type.Value == ((int)WorkOrderType.Wwap) && newExchangeWay == ((int)ExchangeWay.WarehouseExchange)))
                {
                    #region 判断盘点状态--服务完成 add by Hyacinthhuang 2021-12-8
                    RekTec.Crm.BizCommon.InventorytaskCommon cmd = new Crm.BizCommon.InventorytaskCommon();
                    cmd.CheckOrderInventoryStatus(OrganizationService, order.Id.ToString());
                    #endregion
                }

                var part = new QueryExpression("new_srv_partline");
                part.ColumnSet = new ColumnSet("new_isserial", "new_isuser", "new_sn", "new_imei", "new_product_id", "new_materialcategory2_id", "new_oldimei", "new_oldsn", "new_productnew_id", "new_exchangeway", "new_islackmaterial");
                part.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, order.Id);
                part.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var partList = OrganizationService.RetrieveMultipleWithBypassPlugin(part);
                if (partList == null || partList.Entities == null || partList.Entities.Count <= 0)
                {
                    return result;
                }

                foreach (var item in partList.Entities)
                {  // 跳过B2X缺料的记录
                    if (item.Contains("new_islackmaterial") && item.GetAttributeValue<OptionSetValue>("new_islackmaterial").Value == 1)
                        continue;
                    // 获取旧物料和新物料的串号管理状态
                    bool oldSnIsSerial = IsSerialManaged(item, "new_product_id");
                    bool newSnIsSerial = IsSerialManaged(item, "new_productnew_id");

                    // 大仓发货校验逻辑 (服务单大仓换货 ，以换代修大仓发货)
                    if (newExchangeWay == ((int)ExchangeWay.WarehouseExchange)||(item.Contains("new_exchangeway")&&item.GetAttributeValue<OptionSetValue>("new_exchangeway").Value==((int)ExchangeWay.WarehouseExchange)))
                    {
                        if (oldSnIsSerial && newSnIsSerial)
                        {
                            if (string.IsNullOrEmpty(item.GetAttributeValue<string>("new_oldsn")) &&
                                string.IsNullOrEmpty(item.GetAttributeValue<string>("new_oldimei")))
                            {
                                return true;
                            }
                        }
                        continue;
                    }
                    //旧物料是串号未填旧sn  Modified By BaronKang SN、IMEI都为空才报错
                    if (oldSnIsSerial && string.IsNullOrEmpty(item.GetAttributeValue<string>("new_oldsn")) && string.IsNullOrEmpty(item.GetAttributeValue<string>("new_oldimei")))
                    {
                        return true;
                    }
                    //新物料未填新dn  Modified By BaronKang SN、IMEI都为空才报错
                    if (newSnIsSerial && string.IsNullOrEmpty(item.GetAttributeValue<string>("new_sn")) && string.IsNullOrEmpty(item.GetAttributeValue<string>("new_imei")))
                    {
                        return true;
                    }
                    // 换货场景 新增校验  Modified By tianmeng   3.13.2.1 https://xiaomi.f.mioffice.cn/docx/doxk4gvIvGP6NdKbu2Bze3Zppcg
                    if (new_type.Value == ((int)WorkOrderType.Wwap))
                    {
                        //旧件 SN不存在于受理物品的SN中抛出异常
                        if (oldSnIsSerial && item.TryGetAttributeValue<string>("new_oldsn", out string oldSn))
                        {
                            if (!JudgeSNinAcceptProduct(oldSn, order.Id))//受理物品中不含此sn
                            {
                                return true;
                            }
                        }
                    }
                    //换货和安装  若填写的新件SN在IMEI服务中不存在，则抛出异常  大仓换货不校验
                    if ((newExchangeWay != ((int)ExchangeWay.WarehouseExchange))&&(new_type.Value == ((int)WorkOrderType.Wwap) || new_type.Value == ((int)WorkOrderType.Installation)))
                    {
                        if (newSnIsSerial && item.TryGetAttributeValue<string>("new_sn", out string newSn))
                        {
                            var imeiResult = Command<IMEIInterfacebll>().ImeiSearchSn("sn", new string[] { newSn });
                            if (imeiResult == null || imeiResult.Count <= 0)
                            {
                                return true;
                            }
                        }
                    }
                }        
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[CheckPartlineSnOrImei]方法出错" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        private bool IsSerialManaged(Entity item, string attributeName)
        {
            try
            {
                if (item.Contains(attributeName))
                {
                    var product = OrganizationService.RetrieveWithBypassPlugin(
                        "product",
                        item.GetAttributeValue<EntityReference>(attributeName).Id,
                        new ColumnSet("new_isserial")
                    );
                    return product != null && product.GetAttributeValue<bool>("new_isserial");
                }
                return false;
            }
            catch(Exception ex)
            {
                Log.InfoMsg("[IsSerialManaged]方法出错" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }

        }


        /// <summary>
        /// 判断当前SN是否在受理物品的SN中
        /// </summary>
        /// <param name="sn"></param>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public bool JudgeSNinAcceptProduct(string sn,Guid workOrderId)
        {
            try
            {
                // 获取受理产品的SN
                var acceptProducts = new_srv_productlinebll.GetProductLineByWorkorderId(
                    OrganizationServiceAdmin,
                    workOrderId.ToString(),
                    new[] { "new_sn" }
                );
                if (acceptProducts?.Entities == null || acceptProducts.Entities.Count == 0)
                {
                    return false;
                }
                // 提取 SN 列表
                var sns = acceptProducts.Entities
                    .Where(entity => entity.TryGetAttributeValue("new_sn", out string entitySn))
                    .Select(entity => entity.GetAttributeValue<string>("new_sn"))
                    .ToList();
                if(sns.Count!=acceptProducts.Entities.Count)
                {
                    return false;
                }
                return sns.Contains(sn);
            }
            catch(Exception ex)
            {
                Log.InfoMsg("[JudgeSNinAcceptProduct]方法出错" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 服务完成防呆校验
        /// add by elvawang
        /// </summary>
        /// <param name="id"></param>
        public int ServiceIsComplete(Entity workorder,EntityCollection partlineList)
        {
            try
            {
                if (workorder == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                Guid workorderId = workorder.Id;
                if (workorder.Contains("new_type"))
                {
                    //除了服务类型为安装，工单里必须有故障与处理方法，如果没有给予系统提示（存在未填写的故障或处理方法，无法完工）
                    QueryExpression approachQE = new QueryExpression("new_srv_workorder_approach");//服务单处理方法
                    approachQE.ColumnSet = new ColumnSet("new_srv_workorder_approachid");
                    approachQE.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorderId);
                    approachQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    approachQE.TopCount = 1;
                    var approachEC = OrganizationService.RetrieveMultipleWithBypassPlugin(approachQE);
                    QueryExpression troubleQE = new QueryExpression("new_srv_trouble");//故障
                    troubleQE.ColumnSet = new ColumnSet("new_srv_troubleid");
                    troubleQE.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorderId);
                    troubleQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    troubleQE.TopCount = 1;
                    var troubleEC = OrganizationService.RetrieveMultipleWithBypassPlugin(troubleQE);
                    if (approachEC == null || approachEC.Entities == null || approachEC.Entities.Count <= 0 || troubleEC == null || troubleEC.Entities == null || troubleEC.Entities.Count <= 0)
                    {
                        throw new InvalidPluginExecutionException(GetResource("ServiceComplete.existprocess", "存在未填写的故障或处理方法，无法完工"));
                    }
                }

                Guid stationId = workorder.GetAttributeValue<EntityReference>("new_station_id").Id;//服务网点
                Entity station = OrganizationService.RetrieveWithBypassPlugin("new_srv_station", stationId, new ColumnSet("new_skipstockcheck", "new_sparepartssupplytype", "new_country_id"));
                bool skipStockCheck = station.GetAttributeValue<bool>("new_skipstockcheck");//是否跳过库存校验
                //added by Junhui
                int sparepartssupplytype = station.Contains("new_sparepartssupplytype") ? station.GetAttributeValue<OptionSetValue>("new_sparepartssupplytype").Value : -1;//备件供应模式
                int new_exchange_way = workorder.Contains("new_exchange_way") ? workorder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0; //换货方式
                int warechangeWay = workorder.Contains("new_warechangeway") ? workorder.GetAttributeValue<OptionSetValue>("new_warechangeway").Value : 0;//以换代修方式
                int workorderNewType = workorder.Contains("new_type") ? workorder.GetAttributeValue<OptionSetValue>("new_type").Value : 0;//服务方式
                if (sparepartssupplytype == 4 && ((workorderNewType == 2 && new_exchange_way == 2) || (workorderNewType == 1 && warechangeWay == 2)))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.maitroxconsignsupplytype", "The current work order is a Maitrox consign work order, which does not support big warehouse delivery and fails to complete."));
                }
                //add by tianmeng 服务完成时检验送装一体是否妥投
                if (workorder.TryGetAttributeValue<OptionSetValue>("new_intdelivery_type", out OptionSetValue intdeliveryType))
                {
                    if (intdeliveryType.Value == ((int)InstallationType.AfterSales))//送装一体校验是否妥投
                    {
                        int serviceType = workorder.Contains("new_type") ? workorder.GetAttributeValue<OptionSetValue>("new_type").Value : 0;
                        int refund = 3;//退货
                        string new_tms_pictures = workorder.Contains("new_tms_pictures") ? workorder.GetAttributeValue<string>("new_tms_pictures") : "";
                        List<string> deliveryList = new List<string>();
                        if (!string.IsNullOrWhiteSpace(new_tms_pictures))
                            deliveryList = new_tms_pictures.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
                        //服务类型!=退货 || （服务类型=退货 && 非大礼包退货），进入校验
                        if (serviceType != refund || (serviceType == refund && deliveryList.Count == 0))
                        {
                            Asserts.IsTrue(station == null, GetResource("workorder.invalidstation", "服务站无效"));
                            Command<TranTypeCommand>().CheckIntdelivery(workorder, station, -1);
                        }
                    }
                }

                // add by Hyacinth 2021-10-22 完工检验MMI检测记录
                LogaHao(() =>
                {
                    CompleteWorkOrderCheck(workorderId);

                }, "完工检验MMI检测记录");

                // 校验服务单完工处理方法 add by Hyacinth 2021-10-30
                LogaHao(() =>
                {
                    CheckOrderApproch(workorderId);
                }, "校验服务单完工处理方法");

                //完工校验条码库存 add by elvawang 2021/12/10。  增加判断，是否跳过库存校验
                if (!skipStockCheck)
                {
                    LogaHao(() =>
                    {
                        CheckPartbarcodeinv(workorderId);
                    }, "完工校验条码库存");
                }
                // 校验安装服务处理方法 add by p-zhoulin 2024-11-11
                LogaHao(() =>
                {
                    CheckWorkOrderInstallrights(workorder);
                }, "校验安装服务处理方法");

                if (partlineList != null&& workorderNewType==((int)WorkOrderType.Wwap))
                {
                    LogaHao(() =>
                    {
                        CheckAcceptProductReplacement(workorder, partlineList);
                    }, "校验受理商品的SN是否领料");
                }
                #region 判断发货方式是否全部一致 add by Hyacinthhuang 2021-12-11
                int way = 0;
                LogaHao(() =>
                {

                    QueryExpression query_partLine = new QueryExpression("new_srv_partline");
                    query_partLine.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    query_partLine.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorderId);
                    query_partLine.Criteria.AddCondition("new_exchangeway", ConditionOperator.NotNull);
                    query_partLine.ColumnSet.AddColumns("new_exchangeway");
                    EntityCollection ec = OrganizationService.RetrieveMultipleWithBypassPlugin(query_partLine);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        bool Way_Local = ec.Entities.All(x => x.Contains("new_exchangeway") && x.GetAttributeValue<OptionSetValue>("new_exchangeway").Value == 1);
                        bool Way_Warehouse = ec.Entities.All(x => x.Contains("new_exchangeway") && x.GetAttributeValue<OptionSetValue>("new_exchangeway").Value == 2);
                        Log.InfoMsg($"全部本地发货：{Way_Local}__全部大仓发货：{Way_Warehouse}");
                        if (!Way_Local && !Way_Warehouse)
                        {
                            throw new InvalidPluginExecutionException(GetResource("new_srv_partline.PartConfirmFail", "【完工失败，当前更换明细中不支持同时存在大仓发货和本地发货的物料】"));
                        }

                        way = ec.Entities[0].Contains("new_exchangeway") ? ec.Entities[0].GetAttributeValue<OptionSetValue>("new_exchangeway").Value : 0;
                    }

                }, "判断发货方式是否全部一致");
                return way;
                #endregion


            }
            catch (Exception ex)
            {
                Log.InfoMsg("服务完成防呆校验异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #region  完工校验 add by Hyacinth 

        /// <summary>
        /// 完工检验MMI检测记录
        /// </summary>
        /// <param name="orderId">服务单id</param>
        public void CompleteWorkOrderCheck(Guid orderId)
        {

            try
            {
                Entity order = GetWorkOrderInfo(orderId);
                if (order == null)
                    return;

                #region 1.关联服务单MMI检测明细
                List<string> imeiNewList = new List<string>();
                // 服务单imei号
                string imei = order.GetAttributeValue<string>("new_imei");
                string userId = string.Empty; //工单负责人               
                if (order != null && order.Contains("ownerid"))
                {
                    userId = order.GetAttributeValue<EntityReference>("ownerid").Id.ToString();
                }
                //imeiNewList = CheckWorkorderMMIDetailList(orderId.ToString(), imei, userId);

                #region m-1890969
                List<string> lsImeiOrSn = new List<string>();
                if (!string.IsNullOrWhiteSpace(order.GetAttributeValue<string>("new_imei"))) { lsImeiOrSn.Add(order.GetAttributeValue<string>("new_imei")); }
                if (!string.IsNullOrWhiteSpace(order.GetAttributeValue<string>("new_sn"))) { lsImeiOrSn.Add(order.GetAttributeValue<string>("new_sn")); }
                if (!string.IsNullOrWhiteSpace(order.GetAttributeValue<string>("new_fsn"))) { lsImeiOrSn.Add(order.GetAttributeValue<string>("new_fsn")); }
                imeiNewList = CheckWorkorderMMIDetailList(orderId.ToString(), lsImeiOrSn, userId);
                #endregion
                #endregion

                #region 2.检测服务单是否【MMI白名单】,是则不检测
                if (order.Contains("new_mmiwhitelist") && order.GetAttributeValue<bool>("new_mmiwhitelist"))
                    return;
                // add Hyacinthhuang 2022-1-3 上线后要求保外工单不需要检测MMI
                int new_warranty = order.Contains("new_warranty") ? order.GetAttributeValue<OptionSetValue>("new_warranty").Value : 0;
                if (new_warranty == 2)
                    return;
                #endregion

                #region m-1890969-MMI增强
                string stCategoryCode = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationServiceAdmin, "new_category3_mmicheckcode");
                List<string> listCategoryCode = new List<string>();
                if (!string.IsNullOrEmpty(stCategoryCode))
                {
                    listCategoryCode = stCategoryCode.Split(',').ToList();
                }
                var entityCategory3 = OrganizationServiceAdmin.Retrieve("new_category3", order.GetAttributeValue<EntityReference>("new_category3_id").Id, new ColumnSet("new_code"));
                //在原有系统参数中，或者在规则配置表中，则进行CITMMI校验
                if (listCategoryCode.Contains(entityCategory3.GetAttributeValue<string>("new_code")) || IsExistMMICITRule(order))
                {
                    if (order.GetAliasAttributeValue<bool>("WorkorderCountry.new_notverifyresult"))
                        return;
                    EntityCollection ecApproach = null;
                    imeiNewList.AddRange(lsImeiOrSn);
                    IQCCheck(order, imeiNewList, ref ecApproach);//IQC校验
                    OQCCheck(order, imeiNewList, ecApproach);//OQC校验
                }
                #endregion

                #region 3.当工单里【三级品类】=手机(取三级品类编码进行校验判断),取处理方法存在【MMI检测】为是，校验最新一条MMI检测记录为已通过

                //                #region 检测是否三级品类是手机

                //                bool isMobile = false, needMMI = false;
                //                if (order.Contains("new_category3_id"))
                //                    isMobile = WorkOrderCommon.SwarchCategory3(OrganizationService, Cache, order.GetAttributeValue<EntityReference>("new_category3_id").Id);

                //                #region 查询处理方法,是否存在【MMI检测】
                //                string fetchXml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>
                //  <entity name='new_srv_workorder'>
                //    <attribute name='new_name' />
                //    <attribute name='createdon' />
                //    <attribute name='new_origin' />
                //    <attribute name='new_warranty' />
                //    <attribute name='new_imei' />
                //    <attribute name='new_srv_workorderid' />
                //    <order attribute='new_name' descending='false' />
                //    <filter type='and'>
                //      <condition attribute='new_srv_workorderid' operator='eq' value='{orderId}' />
                //      <condition attribute='statecode' operator='eq' value='0' />
                //    </filter>
                //    <link-entity name='new_srv_workorder_approach' from='new_srv_workorder_id' to='new_srv_workorderid' link-type='inner' alias='ac'>
                //      <filter type='and'>
                //        <condition attribute='statecode' operator='eq' value='0' />
                //      </filter>
                //      <link-entity name='new_approach' from='new_approachid' to='new_approach_id' link-type='inner' alias='ad'>
                //        <filter type='and'>
                //          <condition attribute='new_ismmi' operator='eq' value='1' />
                //          <condition attribute='statecode' operator='eq' value='0' />
                //        </filter>
                //      </link-entity>
                //    </link-entity>
                //  </entity>
                //</fetch>";
                //                int originCurrent = 0;
                //                int warrantyCurrent = 0;


                //                EntityCollection collection = OrganizationService.RetrieveMultiple(new FetchExpression(fetchXml));
                //                if (collection != null && collection.Entities.Count > 0)
                //                {
                //                    needMMI = true;
                //                    if (collection.Entities[0].Contains("new_origin"))
                //                    {
                //                        originCurrent = collection.Entities[0].GetAttributeValue<OptionSetValue>("new_origin").Value;
                //                    }
                //                    if (collection.Entities[0].Contains("new_imei"))
                //                    {
                //                        imei = collection.Entities[0].GetAttributeValue<string>("new_imei");
                //                    }
                //                    if (collection.Entities[0].Contains("new_warranty"))
                //                    {
                //                        warrantyCurrent = collection.Entities[0].GetAttributeValue<OptionSetValue>("new_warranty").Value;
                //                    }
                //                }
                //                #endregion

                //                #endregion

                //                if (!(isMobile && needMMI))
                //                    return;

                //                #region 查询最是否有MMI检测记录
                //                QueryExpression expression = new QueryExpression("new_srv_workorder_imeicheck");
                //                expression.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                //                expression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //                expression.ColumnSet.AddColumns("createdon", "new_srv_workorder_imeicheckid");
                //                EntityCollection ec = OrganizationServiceDisableMultLang.RetrieveMultiple(expression);
                //                if (ec == null || ec.Entities.Count <= 0)
                //                {
                //                    if (originCurrent == 100 || originCurrent == 120 || originCurrent == 60)
                //                    {

                //                        if (!string.IsNullOrEmpty(imei))
                //                        {
                //                            QueryExpression query = new QueryExpression("new_srv_workorder_imeicheck");
                //                            query.ColumnSet.AddColumns("new_srv_workorder_imeicheckid");
                //                            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //                            FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                //                            if (imeiNewList.Count > 0 && imeiNewList != null)
                //                            {
                //                                filter.AddCondition("new_imei1", ConditionOperator.In, imeiNewList.ToArray());
                //                                filter.AddCondition("new_imei2", ConditionOperator.In, imeiNewList.ToArray());
                //                            }
                //                            else
                //                            {
                //                                filter.AddCondition("new_imei1", ConditionOperator.Equal, imei);
                //                                filter.AddCondition("new_imei2", ConditionOperator.Equal, imei);
                //                            }
                //                            query.Criteria.AddFilter(filter);

                //                            LinkEntity linkOrder = new LinkEntity("new_srv_workorder_imeicheck", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                //                            linkOrder.Columns = new ColumnSet("new_origin", "new_warranty", "new_imei");
                //                            linkOrder.EntityAlias = "srvWorkorder";
                //                            linkOrder.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //                            query.LinkEntities.Add(linkOrder);
                //                            query.AddOrder("createdon", OrderType.Descending);
                //                            EntityCollection imeicheckEc = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
                //                            if (imeicheckEc == null || imeicheckEc.Entities.Count <= 0)
                //                            {
                //                                throw new InvalidPluginExecutionException(GetResource("new_srv_workorder_imeicheck.HasNoMMIResult", "完工失败！请先进行MMI检测"));
                //                            }
                //                            else
                //                            {
                //                                var imeicheckEntity = imeicheckEc.Entities[0];
                //                                if (imeicheckEntity.Contains("srvWorkorder.new_origin") && imeicheckEntity.Contains("srvWorkorder.new_warranty") && imeicheckEntity.Contains("srvWorkorder.new_imei") &&
                //                                   imeicheckEntity.GetAliasAttributeValue<OptionSetValue>("srvWorkorder.new_origin").Value == originCurrent &&
                //                                   imeicheckEntity.GetAliasAttributeValue<OptionSetValue>("srvWorkorder.new_warranty").Value != warrantyCurrent &&
                //                                  imeicheckEntity.GetAliasAttributeValue<string>("srvWorkorder.new_imei") == imei)
                //                                {
                //                                    //操作
                //                                }
                //                                else
                //                                {
                //                                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder_imeicheck.HasNoMMIResult", "完工失败！请先进行MMI检测"));
                //                                }

                //                            }
                //                        }
                //                    }
                //                    else
                //                    {
                //                        throw new InvalidPluginExecutionException(GetResource("new_srv_workorder_imeicheck.HasNoMMIResult", "完工失败！请先进行MMI检测"));
                //                    }
                //                }
                //                #endregion

                #endregion
            }
            catch (Exception ex)
            {
                Log.ErrorMsg("【完工检验MMI检测记录报错】:" + ex.Message);
                Log.LogException(ex);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 完工前MMI检测明细关联服务单
        /// 场景：先建配件更换明细，再推送检测结果
        /// </summary>
        /// <param name="orderId">服务单id</param>
        /// <param name="imei">服务单imei</param>
        public void CheckWorkorderMMIDetails(string orderId, string imei)
        {
            if (string.IsNullOrWhiteSpace(orderId) || string.IsNullOrWhiteSpace(imei))
                return;

            try
            {
                #region 查询imei号集合,配件更换明细和服务单imei 去重
                List<string> Str = GetOrderPartLineImeis(orderId);
                List<string> Imeis = new List<string>();
                if (Str != null && Str.Count > 0)
                {
                    Imeis.AddRange(Str);
                }
                Imeis.Add(imei);
                Imeis = Imeis.Distinct().ToList();
                #endregion

                #region 查询工单负责人
                string userId = string.Empty;
                Entity order = OrganizationServiceDisableMultLang.Retrieve("new_srv_workorder", new Guid(orderId), new ColumnSet("ownerid"));
                if (order != null && order.Contains("ownerid"))
                {
                    userId = order.GetAttributeValue<EntityReference>("ownerid").Id.ToString();
                }
                #endregion

                #region 查询满足条件的MMI检测明细
                QueryExpression query = new QueryExpression("new_srv_workorder_imeicheck");
                query.ColumnSet.AddColumns("new_srv_workorder_imeicheckid");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_workorder_id", ConditionOperator.Null);
                FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                filter.AddCondition("new_imei1", ConditionOperator.In, Imeis.ToArray());
                filter.AddCondition("new_imei2", ConditionOperator.In, Imeis.ToArray());
                query.Criteria.AddFilter(filter);

                EntityCollection ec = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                    return;
                #endregion

                #region 依次回写MMI检测明细中服务单字段
                foreach (Entity item in ec.Entities)
                {
                    Entity imeiCheck = new Entity("new_srv_workorder_imeicheck");
                    imeiCheck.Id = item.Id;
                    imeiCheck["new_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(orderId));
                    OrganizationServiceAdmin.Update(imeiCheck);
                    if (!string.IsNullOrWhiteSpace(userId))
                    {
                        RekTec.Crm.BizCommon.CommonHelper.Assign(OrganizationServiceAdmin, "new_srv_workorder_imeicheck", item.Id, new Guid(userId));
                    }

                }
                #endregion
            }
            catch (Exception ex)
            {
                Log.ErrorMsg("【服务单完工MMI检测明细关联服务单】：" + ex.Message);
                Log.LogException(ex);
                throw new Exception(ex.Message);
            }


        }

        /// <summary>
        /// 完工前MMI检测明细关联服务单
        /// 场景：获取更换件明细中的新imei mmi匹配更换件明细
        /// </summary>
        /// <param name="orderId">服务单id</param>
        /// <param name="imei">服务单imei</param>
        public List<string> CheckWorkorderMMIDetailList(string orderId, string imei, string userId)
        {
            List<string> imeiNewList = new List<string>(); //新
            List<string> list = new List<string>();
            if (string.IsNullOrWhiteSpace(orderId) || string.IsNullOrWhiteSpace(imei))
            {
                return imeiNewList;
            }

            try
            {
                #region 查询imei号集合,配件更换明细和服务单imei 去重
                QueryExpression query = new QueryExpression("new_srv_partline");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                query.ColumnSet.AddColumns("new_oldimei", "new_imei");
                EntityCollection ec = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    foreach (Entity item in ec.Entities)
                    {
                        if (item.Contains("new_imei"))
                        {
                            list.Add(item.GetAttributeValue<string>("new_imei"));
                            imeiNewList.Add(item.GetAttributeValue<string>("new_imei"));
                        }

                        if (item.Contains("new_oldimei"))
                        {
                            list.Add(item.GetAttributeValue<string>("new_oldimei"));
                        }
                    }
                }
                list.Add(imei);
                list = list.Distinct().ToList();
                #endregion

                #region 查询满足条件的MMI检测明细  并回写负责人
                QueryExpression queryImei = new QueryExpression("new_srv_workorder_imeicheck");
                queryImei.ColumnSet.AddColumns("new_srv_workorder_imeicheckid");
                queryImei.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryImei.Criteria.AddCondition("new_workorder_id", ConditionOperator.Null);
                FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                filter.AddCondition("new_imei1", ConditionOperator.In, list.ToArray());
                filter.AddCondition("new_imei2", ConditionOperator.In, list.ToArray());
                queryImei.Criteria.AddFilter(filter);

                EntityCollection ecImei = OrganizationServiceDisableMultLang.RetrieveMultiple(queryImei);
                if (ecImei != null && ecImei.Entities.Count > 0)
                {
                    foreach (Entity item in ecImei.Entities)
                    {
                        Entity imeiCheck = new Entity("new_srv_workorder_imeicheck");
                        imeiCheck.Id = item.Id;
                        imeiCheck["new_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(orderId));
                        OrganizationServiceAdmin.Update(imeiCheck);
                        if (!string.IsNullOrWhiteSpace(userId))
                        {
                            RekTec.Crm.BizCommon.CommonHelper.Assign(OrganizationServiceAdmin, "new_srv_workorder_imeicheck", item.Id, new Guid(userId));
                        }

                    }
                }
                #endregion
                return imeiNewList;
            }
            catch (Exception ex)
            {
                Log.ErrorMsg("【服务单完工MMI检测明细关联服务单】：" + ex.Message);
                Log.LogException(ex);
                throw new Exception(ex.Message);
            }


        }

        /// <summary>
        /// mg-1890969-重载CheckWorkorderMMIDetailList方法，增加SN/FSN的逻辑
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="lsIMeiSN"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<string> CheckWorkorderMMIDetailList(string orderId, List<string> lsIMeiSN, string userId)
        {
            List<string> imeiNewList = new List<string>(); //新
            List<string> list = new List<string>();
            if (string.IsNullOrWhiteSpace(orderId) || lsIMeiSN.Count == 0)
            {
                return imeiNewList;
            }

            try
            {
                #region 查询imei号集合,配件更换明细和服务单imei 去重
                List<string> lsAttributeName = new List<string>() { "new_imei", "new_oldimei", "new_fsn", "new_oldfsn", "new_sn", "new_oldsn" };
                QueryExpression query = new QueryExpression("new_srv_partline");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                query.ColumnSet.AddColumns(lsAttributeName.ToArray());
                EntityCollection ec = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                    return imeiNewList;
                foreach (Entity item in ec.Entities)
                {
                    foreach (var labn in lsAttributeName)
                    {
                        if (!string.IsNullOrWhiteSpace(item.GetAttributeValue<string>(labn)))
                        {
                            list.Add(item.GetAttributeValue<string>(labn));
                            imeiNewList.Add(item.GetAttributeValue<string>(labn));
                        }
                    }
                }
                list.AddRange(lsIMeiSN);
                list = list.Distinct().ToList();
                #endregion

                #region 查询满足条件的MMI检测明细  并回写负责人
                QueryExpression queryImei = new QueryExpression("new_srv_workorder_imeicheck");
                queryImei.ColumnSet.AddColumns("new_srv_workorder_imeicheckid");
                queryImei.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryImei.Criteria.AddCondition("new_workorder_id", ConditionOperator.Null);
                FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                filter.AddCondition("new_imei1", ConditionOperator.In, list.ToArray());
                filter.AddCondition("new_imei2", ConditionOperator.In, list.ToArray());
                filter.AddCondition("new_fsn", ConditionOperator.In, list.ToArray());
                filter.AddCondition("new_sn", ConditionOperator.In, list.ToArray());
                queryImei.Criteria.AddFilter(filter);
                EntityCollection ecImei = OrganizationServiceDisableMultLang.RetrieveMultiple(queryImei);
                if (ecImei.Entities.Count > 0)
                {
                    foreach (Entity item in ecImei.Entities)
                    {
                        Entity imeiCheck = new Entity("new_srv_workorder_imeicheck");
                        imeiCheck.Id = item.Id;
                        imeiCheck["new_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(orderId));
                        OrganizationServiceAdmin.Update(imeiCheck);
                        if (!string.IsNullOrWhiteSpace(userId))
                        {
                            RekTec.Crm.BizCommon.CommonHelper.Assign(OrganizationServiceAdmin, "new_srv_workorder_imeicheck", item.Id, new Guid(userId));
                        }

                    }
                }
                #endregion
                return imeiNewList;
            }
            catch (Exception ex)
            {
                Log.ErrorMsg("【服务单完工MMI检测明细关联服务单】：" + ex.Message);
                Log.LogException(ex);
                throw new Exception(ex.Message);
            }


        }

        /// <summary>
        /// 查询服务单更换明细Imei
        /// </summary>
        /// <param name="orderId">服务单id</param>
        /// <returns></returns>
        public List<string> GetOrderPartLineImeis(string orderId)
        {
            List<string> list = new List<string>();
            if (string.IsNullOrWhiteSpace(orderId))
                return null;

            // 查询服务单配件更换明细
            QueryExpression query = new QueryExpression("new_srv_partline");
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
            query.ColumnSet.AddColumns("new_oldimei", "new_imei");
            EntityCollection ec = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
            if (ec == null || ec.Entities.Count <= 0)
                return null;

            foreach (Entity item in ec.Entities)
            {
                if (item.Contains("new_imei"))
                    list.Add(item.GetAttributeValue<string>("new_imei"));
                if (item.Contains("new_oldimei"))
                    list.Add(item.GetAttributeValue<string>("new_oldimei"));
            }

            return list;
        }

        /// <summary>
        /// 查询服务单处理方法是否存在rur
        /// </summary>
        /// <param name="orderId">服务单id</param>
        public void CheckOrderApproch(Guid orderId)
        {
            try
            {
                string fetchXml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>
  <entity name='new_srv_workorder'>
    <attribute name='new_name' />
    <attribute name='new_srv_workorderid' />
    <order attribute='createdon' descending='false' />
    <filter type='and'>
      <condition attribute='new_srv_workorderid' operator='eq' value='{orderId}' />
      <condition attribute='statecode' operator='eq' value='0' />
    </filter>
    <link-entity name='new_srv_workorder_approach' from='new_srv_workorder_id' to='new_srv_workorderid' link-type='inner' alias='ac'>
      <filter type='and'>
        <condition attribute='statecode' operator='eq' value='0' />
      </filter>
      <link-entity name='new_approach' from='new_approachid' to='new_approach_id' link-type='inner' alias='ad'>
        <filter type='and'>
          <condition attribute='new_isrur' operator='eq' value='1' />
          <condition attribute='statecode' operator='eq' value='0' />
        </filter>
      </link-entity>
    </link-entity>
  </entity>
</fetch>";

                EntityCollection ec = OrganizationService.RetrieveMultipleWithBypassPlugin(new FetchExpression(fetchXml));
                // 服务单处理方法存在rur工单，更新服务单rur状态
                if (ec != null && ec.Entities.Count > 0)
                {
                    Entity order = new Entity("new_srv_workorder", orderId);
                    order["new_isrur"] = true;
                    OrganizationService.Update(order);
                }

            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("【CheckOrderApproch】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }

        }

        private static int YES = 1;
        private static int Install = 5;
        /// <summary>
        /// 安装服务校验
        /// created on 2024-11-11 by p-zhoulin10
        /// https://xiaomi.f.mioffice.cn/docx/doxk4HqToDglXuzhce6DtSkiyUh
        /// </summary>
        /// <param name="workorderId"></param>
        public void CheckWorkOrderInstallrights(Entity workorder) {
            try {
                int new_type = workorder.GetAttributeValue<OptionSetValue>("new_type").Value;
                //非安装工单直接退出校验
                if (new_type != Install)
                    return;

                //受理地国家数字编码
                string new_id = GetCountryCode(workorder);

                //校验安装单中已购的安装服务是否已使用或已退购
                CheckInstallRightsIsUsedOrRefunded(workorder, new_id);

                //获取安装权益使用记录
                var InstallRights = GetUsedInstallrights(workorder.Id, false);
                string[] virtualGoodsFilesId = null;
                Entity virtualGoodsFiles = null;
                if (InstallRights != null && InstallRights.Entities.Count > 0)
                {
                    virtualGoodsFiles = InstallRights.Entities[0];
                    if (!virtualGoodsFiles.Contains("new_goodsfiles_id"))
                        throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.UsedInstallrightsGoodsFilesIsEmpty", "正常使用安装服务的使用记录中商品档案为空，数据异常"));
                    virtualGoodsFilesId = new string[] { virtualGoodsFiles.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id.ToString() };

                    //有使用安装服务，更新是否强安装=否
                    UpdateIsForceInstall(workorder.Id.ToString());
                }

                //强安装逻辑优化，未使用安装服务 && 是否强安装=是[1] 时生成使用记录
                var isForceInstall = workorder.Contains("new_isforceinstall") ? workorder.GetAttributeValue<OptionSetValue>("new_isforceinstall").Value : 0;
                if (virtualGoodsFiles == null && isForceInstall == 1) 
                {
                    var forceInstall = Command<InstallRightsCommand>().GetAcceptItemInfo(workorder.Id);
                    Command<InstallRightsCommand>().CreateUsedInstallRights(workorder.Id.ToString(), null, null, 0, forceInstall);
                }

                var countryId = workorder.GetAttributeValue<EntityReference>("new_country_id").Id.ToString();
                //校验虚拟服务匹配关系
                EntityCollection virtualServiceMatchRelation = Command<InstallRightsCommand>().CheckVirtualServiceMatchRelationTable(countryId, workorder, virtualGoodsFilesId);

                //未匹配到数据退出虚拟服务匹配关系表校验
                if (virtualServiceMatchRelation == null) {
                    //有使用服务时直接妥投
                    if (virtualGoodsFiles != null)
                    {
                        //虚拟商品发货单
                        /*string new_delivery = virtualGoodsFiles.GetAttributeValue<string>("new_delivery");
                        //调用OC-服务单妥投接口
                        Log.InfoMsg("不校验虚拟服务匹配关系表，调用OC-服务单妥投接口");
                        var res = Command<OcApiCommand>().ServiceOrderDelivery(new_delivery, new_id);
                        Log.InfoMsg($"不校验虚拟服务匹配关系表，OC-服务单妥投返回:{res}");
                        if (string.IsNullOrWhiteSpace(res))
                        {
                            throw new InvalidPluginExecutionException($"OC return:null");
                        }
                        var resobj = JsonConvert.DeserializeObject<dynamic>(res);
                        if (resobj?.header?.code != "200")
                        {
                            throw new InvalidPluginExecutionException($"OC return:{res}");
                        }

                        //更新是否使用安装服务=是
                        UpdateIsUseInstallRights(workorder.Id.ToString());*/
                    }

                    return;
                }

                //服务工单故障明细
                QueryExpression queryTrouble = new QueryExpression("new_srv_trouble");
                queryTrouble.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryTrouble.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorder.Id);
                queryTrouble.Criteria.AddCondition("new_srv_errorgroup_id", ConditionOperator.NotNull);
                queryTrouble.ColumnSet = new ColumnSet("new_srv_errorgroup_id");
                var troubleList = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryTrouble);
                if (troubleList == null || troubleList.Entities.Count == 0) {
                    //无故障
                    return;
                };
                //三级故障
                var errorgroupArr = troubleList.Entities.Select(x => x.GetAttributeValue<EntityReference>("new_srv_errorgroup_id").Id.ToString()).ToArray();

                //服务工单处理方法
                QueryExpression queryApproach = new QueryExpression("new_srv_workorder_approach");
                queryApproach.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryApproach.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorder.Id);
                queryApproach.Criteria.AddCondition("new_approach_id", ConditionOperator.NotNull);
                queryApproach.ColumnSet = new ColumnSet("new_approach_id");
                var approachList = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryApproach);
                if (approachList == null || approachList.Entities.Count == 0)
                {
                    //无处理方法
                    return;
                };
                //处理方法
                var approachArr = approachList.Entities.Select(x => x.GetAttributeValue<EntityReference>("new_approach_id").Id.ToString()).ToArray();

                //工单更换件明细无已到料数据
                QueryExpression queryPartline = new QueryExpression("new_srv_partline");
                queryPartline.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryPartline.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorder.Id);
                queryPartline.Criteria.AddCondition("new_status", ConditionOperator.Equal, 4);
                var tpartlineList = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryPartline);
                //已到料
                bool receivedMaterials = false;
                if (tpartlineList != null && tpartlineList.Entities.Count > 0) 
                    receivedMaterials = true;

                //虚拟服务匹配关系表记录ID集合
                string[] virtualServiceIds = virtualServiceMatchRelation.Entities.Select(x => x.Id.ToString()).Distinct().ToArray();
                //有使用安装服务
                if (virtualGoodsFiles != null)
                {
                    //虚拟商品发货单
                    string new_delivery = virtualGoodsFiles.GetAttributeValue<string>("new_delivery");
                    //查询new_srv_virtualservicematchrelation表
                    var queryExpressionList = GetVirtualservicematchrelation(virtualServiceIds, errorgroupArr, approachArr);
                    if (queryExpressionList != null && queryExpressionList.Entities.Count > 0)
                    {
                        int new_isallowedusematerial = queryExpressionList.Entities[0].GetAttributeValue<OptionSetValue>("new_isallowedusematerial").Value;
                        //是否允许用料=是 或者 是否允许用料=否 且 工单更换件明细无已到料数据
                        if (new_isallowedusematerial == YES || (new_isallowedusematerial != YES && !receivedMaterials))
                        {
                            //调用OC-服务单妥投接口
                            /*Log.InfoMsg("调用OC-服务单妥投接口");
                            var res = Command<OcApiCommand>().ServiceOrderDelivery(new_delivery, new_id);
                            Log.InfoMsg($"OC-服务单妥投返回:{res}");
                            if (string.IsNullOrWhiteSpace(res)) {
                                throw new InvalidPluginExecutionException($"OC return:null");
                            }
                            var resobj = JsonConvert.DeserializeObject<dynamic>(res);
                            if (resobj?.header?.code != "200")
                            {
                                throw new InvalidPluginExecutionException($"OC return:{res}");
                            }

                            //更新是否使用安装服务=是
                            UpdateIsUseInstallRights(workorder.Id.ToString());*/
                        }
                        else
                        {
                            CheckWorkOrderInstallrightsException();
                        }
                    }
                    else
                    {
                        CheckWorkOrderInstallrightsException();
                    }
                }
                //未使用安装服务
                else 
                { //2024-11-18新增逻辑
                    //查询new_srv_virtualservicematchrelation表
                    var queryExpressionList = GetVirtualservicematchrelation(virtualServiceIds, errorgroupArr, approachArr);
                    if (queryExpressionList != null && queryExpressionList.Entities.Count > 0)
                    {
                        int new_isallowedusematerial = queryExpressionList.Entities[0].GetAttributeValue<OptionSetValue>("new_isallowedusematerial").Value;
                        //是否允许用料=是 或者 是否允许用料=否 且 工单更换件明细无已到料数据
                        if (new_isallowedusematerial == YES || (new_isallowedusematerial != YES && !receivedMaterials))
                        {
                            //通过校验
                            return;
                        }
                        else
                        {
                            CheckWorkOrderInstallrightsException();
                        }
                    }
                    else
                    {
                        CheckWorkOrderInstallrightsException();
                    }
                }
            } catch (Exception ex) {
                Log.LogException(ex);
                Log.InfoMsg("【CheckWorkOrderInstallrights】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        private void CheckWorkOrderInstallrightsException() {
            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.CheckWorkOrderInstallrights", "使用安装服务的工单需要使用限定的故障、处理方法及是否允许用料，请检查以上信息选择是否有误。"));
        }

        /// <summary>
        /// 查询new_srv_virtualservicematchrelation表
        /// </summary>
        /// <param name="virtualServiceIds"></param>
        /// <param name="errorgroupArr"></param>
        /// <param name="approachArr"></param>
        /// <returns></returns>
        private EntityCollection GetVirtualservicematchrelation(string[] virtualServiceIds, string[] errorgroupArr,string[] approachArr) {
            //查询new_srv_virtualservicematchrelation表
            QueryExpression queryExpression = new QueryExpression("new_srv_virtualservicematchrelation");
            queryExpression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            //虚拟服务匹配关系记录表ID in virtualServiceIds
            queryExpression.Criteria.AddCondition("new_srv_virtualservicematchrelationid", ConditionOperator.In, virtualServiceIds);
            
            //可用故障 In 工单三级故障
            queryExpression.Criteria.AddCondition("new_srv_errorgroup_id", ConditionOperator.In, errorgroupArr);
            //可用的处理方法 In 工单使用的处理方法
            queryExpression.Criteria.AddCondition("new_approach_id", ConditionOperator.In, approachArr);
            //是否允许用料
            queryExpression.ColumnSet = new ColumnSet("new_isallowedusematerial");
            return  OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryExpression);
        }

        /// <summary>
        /// 根据服务单查询安装权益使用记录
        /// </summary>
        /// <param name="workorderId"></param>
        /// <param name="isRefund">是否退款</param>
        /// <returns></returns>
        public EntityCollection GetUsedInstallrights(Guid workorderId,bool isRefund = true)
        {
            //安装权益使用记录表 
            QueryExpression query = new QueryExpression("new_srv_usedinstallrights");
            query.TopCount = 1;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            //退款标识 1=退款
            if(isRefund)
                query.Criteria.AddCondition("new_refund", ConditionOperator.Equal, 1);
            else
                query.Criteria.AddCondition("new_refund", ConditionOperator.NotIn, 1, 2);
            //服务工单
            query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorderId);
            query.ColumnSet = new ColumnSet("new_goodsfiles_id", "new_delivery", "new_ordercode", "new_usedtime", "new_repairrightsrule_id", "new_occurrency", "createdon");
            return OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(query);
        }

        /// <summary>
        /// 校验安装单中已购的安装服务是否已使用或已退购
        /// </summary>
        /// <param name="workorder">服务单</param>
        /// <param name="new_id">受理地国家数字编码</param>
        /// <returns></returns>
        private void CheckInstallRightsIsUsedOrRefunded(Entity workorder, string new_id) 
        {
            //关联发货单不存在，直接退出校验
            if (!workorder.Contains("new_shipordernumber"))
                return;

            //查询使用记录
            var usedEc = GetUsedInstallrights(workorder.Id, false);

            //查询退购记录
            var refundEc = GetUsedInstallrights(workorder.Id);

            //有使用或退购记录，直接退出校验
            if (usedEc?.Entities?.Count > 0 || refundEc?.Entities?.Count > 0)
                return;

            Log.InfoMsg("OC查询(实体)商品发货单_start");
            var waybillList = Command<OcApiCommand>().SearchWaybillById(workorder.GetAttributeValue<string>("new_shipordernumber"), new_id);
            Log.InfoMsg("OC查询(实体)商品发货单_end" + JsonConvert.SerializeObject(waybillList));
            //未查询到OC实物发货单信息，直接退出校验
            if (waybillList == null || waybillList.Count <= 0)
                return;

            //实物发货单信息
            var waybillbodyItem = waybillList.Where(x => x.waybill_item != null).FirstOrDefault();
            //未查询到OC实物发货单扩展信息，直接退出校验
            if (waybillbodyItem == null || waybillbodyItem.waybill_extend == null || waybillbodyItem.waybill_extend.dataList.Count <= 0)
                return;

            //获取关联的虚拟发货单
            var wayextend = waybillbodyItem.waybill_extend.dataList.Where(x => x.key_name == "ship_install_related_waybill" && x.group_name == "xm_waybill").FirstOrDefault();
            //未查询到OC实物发货单关联的虚拟发货单，直接退出校验
            if (wayextend == null || string.IsNullOrWhiteSpace(wayextend.data))
                return;

            Log.InfoMsg("OC查询(虚拟)商品发货单_start");
            waybillList = Command<OcApiCommand>().SearchWaybillById(wayextend.data, new_id);
            Log.InfoMsg("OC查询(虚拟)商品发货单_end" + JsonConvert.SerializeObject(waybillList));
            //未查询到OC虚拟发货单信息，直接退出校验
            if (waybillList == null || waybillList.Count <= 0)
                return;

            //虚拟发货单信息
            waybillbodyItem = waybillList.Where(x => x.waybill_item != null).FirstOrDefault();
            int[] deliverStatus = new int[] { (int)Api.Model.Oc.WaybillStatus.deliver_already, (int)Api.Model.Oc.WaybillStatus.deliver_already_home_send, (int)Api.Model.Oc.WaybillStatus.deliver_already_take_their };
            //虚拟发货单没有商品或者状态=6000、6001、6002，直接退出校验
            if (waybillbodyItem == null || waybillbodyItem.waybill_item.dataList.Count <= 0 || deliverStatus.Contains(waybillbodyItem.waybill.status))
                return;

            //虚拟发货单商品ID
            string[] goodsfilesArr = waybillbodyItem.waybill_item.dataList.Select(x => x.goods_id).Distinct().ToArray();
            //虚拟商品
            var virtualgoods = Command<InstallRightsCommand>().GetGoods(goodsfilesArr)?.Entities.Where(x => x.Contains("category1.new_code") && x.GetAliasAttributeValue<string>("category1.new_code") == InstallRightsCommand.VirtualService).ToList();
            //虚拟发货单没有虚拟商品，直接退出校验
            if (virtualgoods == null || virtualgoods.Count <= 0)
                return;

            var virtualgoodsIds = virtualgoods.Select(x => x.Id.ToString()).ToArray();
            //根据订单号和虚拟商品查询已关单的虚拟商品退货工单，不存在则报错
            if (!GetVirtualGoodsReturnWorkOrder(virtualgoodsIds, waybillbodyItem.waybill.order_id))
                throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.InstallRights.NotUsedOrRefunded", "该设备有购买的安装服务，请操作核销或退款。"));
        }

        /// <summary>
        /// 根据订单号和虚拟商品查询已关单的虚拟商品退货工单
        /// </summary>
        /// <param name="virtualgoodsIds">虚拟商品</param>
        /// <param name="orderCode">订单号</param>
        /// <param name="serviceType">3-退货</param>
        /// <param name="dealStatus">9-关单</param>
        /// <returns></returns>
        public bool GetVirtualGoodsReturnWorkOrder(string[] virtualgoodsIds, string orderCode, int serviceType = 3, int dealStatus = 9)
        {
            QueryExpression query = new QueryExpression("new_srv_workorder");
            query.TopCount = 1;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_ordercode", ConditionOperator.Equal, orderCode);
            query.Criteria.AddCondition("new_type", ConditionOperator.Equal, serviceType);
            query.Criteria.AddCondition("new_dealstatus", ConditionOperator.Equal, dealStatus);
            query.ColumnSet = new ColumnSet("new_srv_workorderid");
            LinkEntity link = new LinkEntity("new_srv_workorder", "new_srv_productline", "new_srv_workorderid", "new_workorder_id", JoinOperator.Inner);
            link.EntityAlias = "productline";
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            link.LinkCriteria.AddCondition("new_goodsfiles_id", ConditionOperator.In, virtualgoodsIds);
            query.LinkEntities.Add(link);

            var ec = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(query);
            if (ec?.Entities?.Count > 0)
                return true;

            return false;
        }

        /// <summary>
        /// 更新是否使用安装服务=是
        /// </summary>
        /// <param name="workorder"></param>
        /// <returns></returns>
        public void UpdateIsUseInstallRights(string orderId)
        {
            Entity entity = new Entity("new_srv_workorder", new Guid(orderId));
            entity["new_isuseinstallrights"] = true;
            OrganizationService.Update(entity);
        }

        /// <summary>
        /// 有使用安装服务，更新是否强安装=否【2】
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public void UpdateIsForceInstall(string orderId)
        {
            Entity entity = new Entity("new_srv_workorder", new Guid(orderId));
            entity["new_isforceinstall"] = new OptionSetValue(2);
            OrganizationService.Update(entity);
        }

        /// <summary>
        /// 受理地国家数字编码
        /// </summary>
        /// <param name="workorder"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public string GetCountryCode(Entity workorder) {
            //受理地国家数字编码
            string new_id = string.Empty;
            var station_country_id = Guid.Empty;
            #region 国家
            if (workorder.Contains("new_country_id"))
            {
                station_country_id = workorder.GetAttributeValue<EntityReference>("new_country_id").Id;
            }
            else
            {
                var srvworkerEC = WorkOrderCommon.GetEntityStation(Cache, OrganizationServiceAdmin, UserId);//查询当前登录人的服务网点
                if (!srvworkerEC.Contains("station.new_country_id"))
                    throw new InvalidPluginExecutionException(GetResource("CreateWorkorder_WorkOrderSearch_PersonNoCountryid", "当前登录人所在网点国家为空"));
                station_country_id = srvworkerEC.GetAliasAttributeValue<EntityReference>("station.new_country_id").Id;
            }
            if (station_country_id != Guid.Empty)
            {
                Stopwatch sw1 = new Stopwatch();
                sw1.Start();
                var country = WorkOrderCommon.GetCountryEntity(Cache, OrganizationService, station_country_id);//国家
                if (country != null)
                {
                    new_id = country.GetAttributeValue<string>("new_id");

                    if (string.IsNullOrWhiteSpace(new_id))
                    {
                        throw new InvalidPluginExecutionException(GetResource("CreateWorkorder_WorkOrderSearch_NoCountryNewid", "请维护对应国家id"));
                    }
                }
                sw1.Stop();
                Log.InfoMsg($"根据国家id找国家编码 耗时：{sw1.ElapsedMilliseconds}");
            }
            else
            {
                throw new InvalidPluginExecutionException(GetResource("CreateWorkorder_WorkOrderSearch_NoCountryNewid", "请维护对应国家id"));
            }
            #endregion
            return new_id;
        }
        #endregion

        /// <summary>
        /// 服务完成时修改服务单
        /// BaronKang 2022-04-26
        /// </summary>
        /// <param name="workorder">工单数据</param>
        /// <param name="updateOrder">更新工单</param>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public void UpdateWorkOrderStatus(Entity workorder, Entity updateOrder, EntityCollection partlineList)
        {
            try
            {
                if (workorder == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }

                ////完工时没有配件，清空物料申请时间及发放时间
                //QueryExpression qe_part = new QueryExpression("new_srv_partline");
                //qe_part.ColumnSet = new ColumnSet("new_srv_partlineid");
                //qe_part.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workorder.Id);
                //qe_part.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec_part = OrganizationServiceDisableMultLang.RetrieveMultiple(qe_part);
                if (partlineList == null || partlineList.Entities == null || partlineList.Entities.Count <= 0)
                {
                    updateOrder["new_application_time"] = null;
                    updateOrder["new_issuance_time"] = null;
                }

                updateOrder["new_repairprovider_id"] = workorder.GetAttributeValue<EntityReference>("new_servicestation_id");//维修服务商赋值为当前服务商
                updateOrder["new_repairstation_id"] = workorder.GetAttributeValue<EntityReference>("new_station_id");//维修网点赋值为当前网点
                updateOrder["new_dealstatus"] = new OptionSetValue(8);//待关单
                updateOrder["new_pulloutstatus"] = new OptionSetValue(2);//收获状态：已收货
                var new_b2x_orderid = workorder.Contains("new_b2x_orderid") ? workorder.GetAttributeValue<string>("new_b2x_orderid") : "";
                int new_origin = workorder.Contains("new_origin") ? workorder.GetAttributeValue<OptionSetValue>("new_origin").Value : 0;
                if (!workorder.Contains("new_completiontime") && string.IsNullOrWhiteSpace(new_b2x_orderid) && new_origin != 60)
                    updateOrder["new_completiontime"] = DateTime.UtcNow;
                if (CheckMobilePosOwOrder(workorder))
                {
                    Guid owReceiptId = CreateWorkOwReceipt(workorder, partlineList);
                    updateOrder["new_ow_receipt_id"] = new EntityReference("new_ow_receipt", owReceiptId);
                }
                OrganizationService.Update(updateOrder);
            }
            catch (Exception e)
            {
                Log.InfoMsg("【UpdateWorkOrderStatus】方法报错：" + e.Message);
                Log.LogException(e);
                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        /// <summary>
        /// 服务完成发送通知短信
        /// add by BaronKang 2022-06-09
        /// </summary>
        /// <param name="orderId"></param>
        public void SendCompleteMessage(Entity workorder)
        {
            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                //Entity workorder = OrganizationServiceDisableMultLang.Retrieve("new_srv_workorder", orderId, new ColumnSet("new_station_id", "new_type", "new_servicemode", "new_exchange_way"));

                int type = workorder.GetAttributeValue<OptionSetValue>("new_type").Value;//工单类型
                string stationId = workorder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString();//工单网点
                int serviceMode = workorder.Contains("new_servicemode") ? workorder.GetAttributeValue<OptionSetValue>("new_servicemode").Value : 0;//服务方式
                int exchangeway = workorder.Contains("new_exchange_way") ? workorder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;//换货方式

                // 网点地区是否是香港
                bool isHK = Command<ApproachAndPartlineCommand>().CheckStationCountry(stationId, "3385");
                // 网点地区是否是台湾
                bool isTW = Command<ApproachAndPartlineCommand>().CheckStationCountry(stationId, "3386");

                if (!isHK && !isTW)
                {
                    sw.Stop();
                    log.PostTraceAsync(new AppInsightsLogModel()
                    {
                        RequestId = workorder.Id.ToString(),
                        ModuleName = "服务完成",
                        ClassInfo = "ServiceIsCompleteCommand",
                        Method = "SendCompleteMessage",
                        Duration = (int)sw.ElapsedMilliseconds,
                        Msg = "创建短信记录完成:网点国家非香港非台湾不发送短信",
                        Level = LogSeverityLevel.Informational,
                        Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecordId = workorder.Id.ToString(),
                        LoginUserId = Context.InitiatingUserId.ToString()
                    }, AppInsightsLogType.Trace);
                    return;
                }

                if (isHK && type != 1)
                {
                    sw.Stop();
                    log.PostTraceAsync(new AppInsightsLogModel()
                    {
                        RequestId = workorder.Id.ToString(),
                        ModuleName = "服务完成",
                        ClassInfo = "ServiceIsCompleteCommand",
                        Method = "SendCompleteMessage",
                        Duration = (int)sw.ElapsedMilliseconds,
                        Msg = "创建短信记录完成:香港非维修工单不发送短信",
                        Level = LogSeverityLevel.Informational,
                        Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecordId = workorder.Id.ToString(),
                        LoginUserId = Context.InitiatingUserId.ToString()
                    }, AppInsightsLogType.Trace);
                    Log.InfoMsg("香港非维修工单不发送短信");
                    return;
                }

                //台湾：到店；维修、换货；（不包含大仓换货）发送短信
                bool checkServiceMode = type == 1 || (type == 2 && exchangeway == 1);//服务方式是否符合
                if (isTW && serviceMode != 1 && !checkServiceMode)
                {
                    sw.Stop();
                    log.PostTraceAsync(new AppInsightsLogModel()
                    {
                        RequestId = workorder.Id.ToString(),
                        ModuleName = "服务完成",
                        ClassInfo = "ServiceIsCompleteCommand",
                        Method = "SendCompleteMessage",
                        Duration = (int)sw.ElapsedMilliseconds,
                        Msg = "创建短信记录完成:台湾非到店;维修、换货(本地换货);不发送短信",
                        Level = LogSeverityLevel.Informational,
                        Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecordId = workorder.Id.ToString(),
                        LoginUserId = Context.InitiatingUserId.ToString()
                    }, AppInsightsLogType.Trace);
                    Log.InfoMsg("台湾非到店；维修、换货(本地换货)； 不发送短信");
                    return;
                }


                this.Command<SMSApiCommand>().SendMessage(workorder.Id.ToString(), 4);
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workorder.Id.ToString(),
                    ModuleName = "服务完成",
                    ClassInfo = "ServiceIsCompleteCommand",
                    Method = "SendCompleteMessage",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = "创建短信记录完成",
                    Level = LogSeverityLevel.Informational,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workorder.Id.ToString(),
                    LoginUserId = Context.InitiatingUserId.ToString()
                }, AppInsightsLogType.Trace);
            }
            catch (Exception ex)
            {
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workorder.Id.ToString(),
                    ModuleName = "服务完成",
                    ClassInfo = "ServiceIsCompleteCommand",
                    Method = "SendCompleteMessage",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = $"创建短信记录异常:{ex}",
                    Level = LogSeverityLevel.Error,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workorder.Id.ToString(),
                    LoginUserId = Context.InitiatingUserId.ToString(),
                    Code = ex.GetType().ToString() == "Microsoft.Xrm.Sdk.InvalidPluginExecutionException" ? ExceptionCode.BusinessAlertException : ExceptionCode.SystemException
                }, AppInsightsLogType.Exception);
                Log.LogException(ex);
                Log.InfoMsg("创建短信记录错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 获取服务单信息及网点国家 OnightDai 2022-06-14
        /// </summary>
        /// <returns></returns>
        public Entity GetWorkOrderInfo(Guid workorderId)
        {
            try
            {
                //查询服务单
                QueryExpression queryExpression = new QueryExpression("new_srv_workorder");
                queryExpression.ColumnSet.AddColumns("new_category1_id", "new_category2_id", "new_goodsfiles_id", "new_sn", "new_fsn", "new_imei", "new_mmiwhitelist", "new_category3_id", "new_type", "new_warranty", "ownerid", "new_origin");
                queryExpression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryExpression.Criteria.AddCondition("new_srv_workorderid", ConditionOperator.Equal, workorderId);

                #region m-1890969-使用服务单上的国家地区
                LinkEntity WorkorderCountry = new LinkEntity("new_srv_workorder", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter);
                WorkorderCountry.EntityAlias = "WorkorderCountry";
                WorkorderCountry.Columns.AddColumns("new_notverifyresult", "new_iqcnodetection", "new_oqcnodetection");
                WorkorderCountry.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryExpression.LinkEntities.Add(WorkorderCountry);
                #endregion

                LinkEntity lq = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.LeftOuter);
                lq.EntityAlias = "station";
                lq.Columns.AddColumns("new_mmicitcheck");
                lq.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryExpression.LinkEntities.Add(lq);

                LinkEntity lqCountry = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter);
                lqCountry.EntityAlias = "country";
                lqCountry.Columns.AddColumns("new_notverifyresult", "new_iqcnodetection");
                lqCountry.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                lq.LinkEntities.Add(lqCountry);
                queryExpression.TopCount = 1;

                var entityWorkOrderList = OrganizationService.RetrieveMultipleWithBypassPlugin(queryExpression);
                if (entityWorkOrderList == null || entityWorkOrderList.Entities == null || entityWorkOrderList.Entities.Count == 0)
                {
                    return null;
                }
                return entityWorkOrderList.Entities.FirstOrDefault();
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[GetWorkOrderInfo]方法报错：" + ex.Message);
                throw new InvalidPluginExecutionException("[GetWorkOrderInfo]方法报错：" + ex.Message);
            }

        }

        #region 完工校验条码库存  add by elvawang 2021/12/10
        //完工时，检查更换件明细中填写了新imei sn在网点条码库存状态是否为可用，是的话更新为占用，否的话 提示【完工失败，当前snxxxxxxxxx在网点条码库中不为可用状态，请检查该条码配件是否可用】
        public void CheckPartbarcodeinv(Guid id)
        {
            try
            {
                QueryExpression QE = new QueryExpression("new_srv_partline");//配件更换明细
                QE.ColumnSet = new ColumnSet(new string[] { "new_imei", "new_sn", "new_productnew_id", "new_isserial", "new_isdamaged", "new_inrepairs", "new_status", "new_materialcategory1_id", "new_oldimei", "new_oldsn", "new_isuser", "new_exchangeway", "new_islackmaterial" });
                QE.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, id);
                //   QE.Criteria.AddCondition("new_isuser", ConditionOperator.Equal, true);//已使用
                QE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity link = new LinkEntity("new_srv_partline", "new_srv_workorder", "new_srv_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                link.EntityAlias = "order";
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                link.Columns.AddColumns("new_exchange_way", "new_category3_id", "new_station_id", "new_sn", "new_type");
                QE.LinkEntities.Add(link);
                var EC = OrganizationService.RetrieveMultipleWithBypassPlugin(QE);
                if (EC == null || EC.Entities == null || EC.Entities.Count <= 0)
                {
                    return;
                }

                // 所有的是保内
                bool AllInside = EC.Entities.All(x => x.Contains("new_inrepairs") && x.GetAttributeValue<OptionSetValue>("new_inrepairs").Value == 1);
                // 所有的是保外
                bool AllOutsise = EC.Entities.All(x => x.Contains("new_inrepairs") && x.GetAttributeValue<OptionSetValue>("new_inrepairs").Value == 2);

                if (!AllInside && !AllOutsise)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.NotAllExit", "配件更换明细不可同时存在保内、保外配件，请拆分建单"));
                }
                // 判断所有更换件是否有旧imei
                //bool HasOldImei = EC.Entities.All(x => !x.Contains("new_oldsn"));
                //if (HasOldImei)
                //{
                //    throw new InvalidPluginExecutionException(GetResource("", "旧IMEI为空，无法在IMEI服务中判断"));
                //}
                // 工单类型
                int orderType = EC.Entities[0].Contains("order.new_type") ? EC.Entities[0].GetAliasAttributeValue<OptionSetValue>("order.new_type").Value : 0;
                // 工单三级品类Id
                string Category3Id = EC.Entities[0].Contains("order.new_category3_id") ? EC.Entities[0].GetAliasAttributeValue<EntityReference>("order.new_category3_id").Id.ToString() : null;
                //  工单以换代修发货方式
                //  int wareChangeWay = EC.Entities[0].Contains("order.new_warechangeway") ? EC.Entities[0].GetAliasAttributeValue<OptionSetValue>("order.new_warechangeway").Value : 0;
                //工单换货方式 
                int exchange_way = EC.Entities[0].Contains("order.new_exchange_way") ? EC.Entities[0].GetAliasAttributeValue<OptionSetValue>("order.new_exchange_way").Value : 0;
                // 工单网点Id
                string stationId = EC.Entities[0].Contains("order.new_station_id") ? EC.Entities[0].GetAliasAttributeValue<EntityReference>("order.new_station_id").Id.ToString() : null;

                // 判断工单三级品类是否手机
                bool Isphone = WorkOrderCommon.SwarchCategory3(OrganizationServiceDisableMultLang, Cache, !string.IsNullOrWhiteSpace(Category3Id) ? new Guid(Category3Id) : Guid.Empty);
                //   this.Command<ApproachAndPartlineCommand>().JudegCategory3IsPhone(Category3Id);
                // 判断工单三级品类是否平板
                bool iSipad = WorkOrderCommon.SearchCategory3Pad(OrganizationServiceDisableMultLang, Cache, Category3Id);
                //this.Command<ApproachAndPartlineCommand>().JudegCategory3IsIpad(Category3Id);

                foreach (var item in EC.Entities)
                {
                    //POH
                    int new_islackmaterial = item.Contains("new_islackmaterial") ? item.GetAttributeValue<OptionSetValue>("new_islackmaterial").Value : 0;
                    if (new_islackmaterial == 1) continue;
                    // 到料状态
                    int status = item.Contains("new_status") ? item.GetAttributeValue<OptionSetValue>("new_status").Value : 0;
                    // 是否串号管理
                    bool isSerial = item.Contains("new_isserial") ? item.GetAttributeValue<bool>("new_isserial") : false;
                    // 是否使用
                    bool isUse = item.Contains("new_isuser") ? item.GetAttributeValue<bool>("new_isuser") : false;

                    // 已换代修发货方式取更换件明细
                    int wareChangeWay = item.Contains("new_exchangeway") ? item.GetAttributeValue<OptionSetValue>("new_exchangeway").Value : 0;

                    //add by Hyacinthhuang 2022-1-28 维修：本地发 || 换货：本地换 && 串号管理 && 已使用 && 新sn为空校验必填
                    if (isSerial && !item.Contains("new_sn") && isUse && ((orderType == 1 && wareChangeWay == 1) || (orderType == 2 && exchange_way == 1)))
                        throw new InvalidPluginExecutionException(GetResource("ServiceComplete.newsnisnull", "更换件新SN为空"));

                    // 更换件明细物料类别
                    string category1Id = item.Contains("new_materialcategory1_id") ? item.GetAttributeValue<EntityReference>("new_materialcategory1_id").Id.ToString() : null;
                    //已到料且是串号管理且已使用才需要校验
                    if (status != 4 || !isSerial || !isUse)
                        continue;

                    #region 对非套机或机头的物料进行子串号校验  Modified By BaronKang 2022-06-13 暂时去掉主子串号校验
                    //bool IsNeedCheck = this.Command<ApproachAndPartlineCommand>().JudgeMaterialcategory2IsTaojiOrDanji(Isphone, category1Id, iSipad);
                    //if (IsNeedCheck)
                    //{
                    //    Log.InfoMsg("服务完成进入串号校验");
                    //    Dictionary<string, object> parms = new Dictionary<string, object>();
                    //    List<string> oldSnList = new List<string>();
                    //    oldSnList.Add(item.GetAttributeValue<string>("new_oldsn"));
                    //    parms.Add("sns", oldSnList);
                    //    ApproachAndPartlineModel.QueryChildCondition queryChildCondition = new ApproachAndPartlineModel.QueryChildCondition();
                    //    queryChildCondition.has_extend = true;
                    //    queryChildCondition.has_parent = true;
                    //    queryChildCondition.q_type = "child_sn";
                    //    queryChildCondition.business = "xms.laptop";
                    //    parms.Add("condition", queryChildCondition);
                    //    Log.InfoMsg("串号校验传入参数" + JsonHelper.Serialize(parms));
                    //    var checkResult = Command<Command.CreateWorkorderCommand>().CheckSn(parms);
                    //    if (checkResult == null || checkResult.Count <= 0)
                    //    {
                    //        throw new InvalidPluginExecutionException(GetResource("", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                    //    }
                    //    if (string.IsNullOrEmpty(checkResult[0].sn))
                    //    {
                    //        throw new InvalidPluginExecutionException(GetResource("", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                    //    }
                    //    if (checkResult[0].sn != item.GetAliasAttributeValue<string>("order.new_sn"))
                    //    {
                    //        throw new InvalidPluginExecutionException(GetResource("", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                    //    }
                    //}
                    #endregion


                    // 换货：大仓换货、维修：大仓发货不需要校验网点配件库存 
                    if (wareChangeWay != 2 && exchange_way != 2)
                    {
                        //bool JudgeStock = this.Command<ApproachAndPartlineCommand>().JudgeNewImeiOrSnIsInBarcodeinv(stationId, item.GetAttributeValue<string>("new_imei"), item.GetAttributeValue<string>("new_sn"));
                        //if (!JudgeStock)
                        //{
                        //    string productName = item.Contains("new_productnew_id") ? item.GetAttributeValue<EntityReference>("new_productnew_id").Name : "";
                        //    string resultStr = "配件" + productName + " 串号" + item.GetAttributeValue<string>("new_sn") + "不在网点库存中，保存失败";
                        //    throw new InvalidPluginExecutionException(GetResource("", resultStr));
                        //}
                        string Imei = item.GetAttributeValue<string>("new_imei");
                        string Sn = item.GetAttributeValue<string>("new_sn");
                        if (string.IsNullOrWhiteSpace(Sn))
                            throw new InvalidPluginExecutionException(GetResource("ServiceComplete.newsnisnull", "更换件新SN为空"));

                        EntityReference productNew = item.Contains("new_productnew_id") ? item.GetAttributeValue<EntityReference>("new_productnew_id") : null;
                        QueryExpression stocksiteQE = new QueryExpression("new_srv_stocksite");
                        stocksiteQE.ColumnSet = new ColumnSet(new string[] { "new_srv_stocksiteid" });
                        stocksiteQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        stocksiteQE.Criteria.AddCondition("new_stocktype", ConditionOperator.Equal, 1);//良品库
                        LinkEntity partbarcodeinv = new LinkEntity("new_srv_stocksite", "new_partbarcodeinv", "new_srv_stocksiteid", "new_stocksite_id", JoinOperator.Inner);
                        partbarcodeinv.EntityAlias = "partbarcodeinv";
                        partbarcodeinv.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        if (productNew != null)
                        {
                            partbarcodeinv.LinkCriteria.AddCondition("new_product_id", ConditionOperator.Equal, productNew.Id);
                        }
                        partbarcodeinv.LinkCriteria.AddCondition("new_name", ConditionOperator.Equal, Sn);
                        //if (!string.IsNullOrWhiteSpace(Imei))
                        //{
                        //    partbarcodeinv.LinkCriteria.AddCondition("new_imei", ConditionOperator.Equal, Imei);
                        //}
                        partbarcodeinv.Columns.AddColumns(new string[] { "new_status", "new_partbarcodeinvid" });
                        stocksiteQE.LinkEntities.Add(partbarcodeinv);
                        var stocksiteEC = OrganizationServiceDisableMultLang.RetrieveMultiple(stocksiteQE);
                        if (stocksiteEC == null || stocksiteEC.Entities == null || stocksiteEC.Entities.Count <= 0)
                        {
                            throw new InvalidPluginExecutionException(GetResource("ServiceComplete.lacknewimeisn", "配件条码库存中缺少新imei、sn数据，无法完工"));
                        }
                        #region 创建事务
                        #endregion
                        foreach (var Stock in stocksiteEC.Entities)
                        {
                            if (!Stock.Contains("partbarcodeinv.new_status"))
                            {
                                throw new InvalidPluginExecutionException(GetResource("ServiceComplete.newimeisnstocknull", "新imei、sn对应的配件条码库存状态为空，无法完工"));
                            }
                            if (Stock.GetAliasAttributeValue<OptionSetValue>("partbarcodeinv.new_status").Value != 1)
                            {
                                throw new InvalidPluginExecutionException(GetResource("ServiceComplete.newimeisnenable", "新imei、sn在网点条码库中不为可用状态，请检查该条码配件是否可用，无法完工"));
                            }
                            Entity entity = new Entity("new_partbarcodeinv");
                            entity.Id = Stock.GetAliasAttributeValue<Guid>("partbarcodeinv.new_partbarcodeinvid");
                            entity["new_status"] = new OptionSetValue(2);
                            OrganizationService.Update(entity);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.InfoMsg("服务完成校验条码库存异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #endregion

        /// <summary>
        /// Module ID：无
        /// Author：p-huyan9
        /// Create Date：2023-06-12
        /// Depiction：服务完成判断服务工单配件更换明细物料类别
        /// </summary>
        /// <param name="orderId">服务工单id</param>
        public void JudgeMaterialCategoryAllowComplete(Guid orderId)
        {
            EntityCollection ec = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_srv_partline");
            qe.ColumnSet = new ColumnSet("new_name");
            //状态为可用
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            //服务工单id为当前服务工单
            qe.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
            //B2X缺料状态为空
            qe.Criteria.AddCondition("new_islackmaterial", ConditionOperator.Null);
            //是否为小米物料!=否
            qe.Criteria.AddCondition("new_ismipart", ConditionOperator.NotEqual, false);

            //关联物料
            LinkEntity linkProduct = new LinkEntity("new_srv_partline", "product", "new_productnew_id", "productid", JoinOperator.Inner);
            linkProduct.EntityAlias = "product";
            linkProduct.Columns = new ColumnSet("new_isgoods");
            //状态为可用
            linkProduct.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.LinkEntities.Add(linkProduct);

            //关联物料子类别
            LinkEntity linkMaterialCategory2 = new LinkEntity("product", "new_materialcategory2", "new_materialcategory2_id", "new_materialcategory2id", JoinOperator.LeftOuter);
            linkMaterialCategory2.EntityAlias = "materialcategory2";
            linkMaterialCategory2.Columns = new ColumnSet("new_code");
            //状态为可用
            linkMaterialCategory2.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.LinkEntities.Add(linkMaterialCategory2);
            qe.NoLock = true;

            #region 查询配件更换明细
            qe.PageInfo.PageNumber = 1;
            while (true)
            {
                EntityCollection list = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(qe);
                ec.Entities.AddRange(list.Entities);

                if (list.MoreRecords)
                {
                    qe.PageInfo.PageNumber++;
                    qe.PageInfo.PagingCookie = list.PagingCookie;
                }
                else
                {
                    break;
                }
            }
            #endregion

            if (ec != null && ec.Entities.Count > 0)
            {
                bool allIsAC = ApproachAndPartlineCommand.CheckAllAC(orderId.ToString(), OrganizationService);//是否满足 安装/换货 受理物品两个且都是空调
                int allowedGoodsCount = allIsAC ? 2 : 1;
                //获取服务单更换件明细总数
                var partLineAllCount = ec.Entities.Count;
                Log.InfoMsg("【JudgeMaterialCategoryAllowComplete.服务单更换件明细总数】：" + partLineAllCount);
                //获取服务单更换件明细是否商品为是的数据
                var partLineIsGoodsCount = ec.Entities.Count(x => x.Contains("product.new_isgoods") && x.GetAliasAttributeValue<bool>("product.new_isgoods") == true);
                Log.InfoMsg("【JudgeMaterialCategoryAllowComplete.服务单更换件明细是否商品为是总数】：" + partLineIsGoodsCount);
                //获取服务单更换件明细信息中物料子类别为套机的数据【物料子类别编码等于8】
                var partLineIIsNestingMachineCount = ec.Entities.Count(x => x.Contains("materialcategory2.new_code") && "8".Equals(x.GetAliasAttributeValue<string>("materialcategory2.new_code")));
                Log.InfoMsg("【JudgeMaterialCategoryAllowComplete.服务单更换件明细信息中物料子类别为套机总数】：" + partLineIIsNestingMachineCount);
                //获取服务单更换件明细信息中物料子类别为单机的数据【物料子类别编码等于7】
                var partLineIIsSingleMachineCount = ec.Entities.Count(x => x.Contains("materialcategory2.new_code") && "7".Equals(x.GetAliasAttributeValue<string>("materialcategory2.new_code")));
                Log.InfoMsg("【JudgeMaterialCategoryAllowComplete.服务单更换件明细信息中物料子类别为单机总数】：" + partLineIIsSingleMachineCount);
                //服务单更换件明细是否商品为是的数据不为空
                if (partLineIsGoodsCount > 0)
                {
                    //服务单更换件明细信息是否商品为是的数据数量不等于更换件明细总数
                    if (!partLineAllCount.Equals(partLineIsGoodsCount) || partLineIsGoodsCount > allowedGoodsCount)
                    {
                        throw new InvalidPluginExecutionException(GetResource("ServiceComplete.materialcategoryisgoods", "商品物料和非商品物料不能同时使用，服务完成失败"));
                    }
                }
                //服务单更换件明细信息中物料子类别为套机的数据不为空
                if (partLineIIsNestingMachineCount > 0)
                {
                    //服务单更换件明细信息中物料子类别为套机的数据数据量不等于更换件明细总数
                    if (!partLineAllCount.Equals(partLineIIsNestingMachineCount) || partLineIIsNestingMachineCount > allowedGoodsCount)
                    {
                        throw new InvalidPluginExecutionException(GetResource("ServiceComplete.materialcategoryisnestingmachine", "套机物料和非套机物料不能同时使用，服务完成失败"));
                    }
                }
                //服务单更换件明细信息中物料子类别为单机的数据不为空
                if (partLineIIsSingleMachineCount > 0)
                {
                    if (!partLineAllCount.Equals(partLineIIsSingleMachineCount) || partLineIIsSingleMachineCount > allowedGoodsCount)
                    {
                        throw new InvalidPluginExecutionException(GetResource("ServiceComplete.materialcategoryissinglemachine", "单机物料和非单机物料不能同时使用，服务完成失败"));
                    }
                }

                #region 查询处理方法
                var fetchXml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                                                           <entity name = 'new_srv_workorder_approach' >
                                                              <attribute name='new_srv_workorder_approachid' />
                                                              <attribute name='new_name' />
                                                              <order attribute='createdon' descending='false' />
                                                              <filter type='and'>
                                                                <condition attribute='statecode' operator='eq' value='0' />
                                                                <condition attribute='new_srv_workorder_id' operator='eq' value='{orderId}' />
                                                              </filter>
                                                              <link-entity name='new_approach' from='new_approachid' to='new_approach_id' link-type='inner' alias = 'ac'>
                                                                     <attribute name = 'new_ismaterials'/>
                                                              </link-entity>
                                                            </entity>
                                                        </fetch>";
                var nswaEnt = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(new FetchExpression(fetchXml));
                if (nswaEnt != null || nswaEnt.Entities.Count > 0)
                {
                    var ismaterials = nswaEnt.Entities.Where(a => a.GetValue<bool>("ac.new_ismaterials"));
                    if (ismaterials.Count() == 0)
                    {
                        throw new InvalidPluginExecutionException(GetResource("addmaterial.ApproachesMissing", "处理方法缺失，请联系************************维护。"));
                    }
                }
                #endregion
            }
        }

        /// <summary>
        /// 零售通保外工单需求：完工时创建售后工单保外收款记录信息
        /// </summary>
        /// <param name="workorder"></param>
        /// <param name="partlineList"></param>
        public Guid CreateWorkOwReceipt(Entity workorder, EntityCollection partlineList)
        {
            Entity owReceipt = new Entity("new_ow_receipt");
            if (workorder.Contains("new_name"))
                owReceipt["new_name"] = workorder.GetAttributeValue<string>("new_name");
            if (workorder.Contains("new_dealstatus"))
                owReceipt["new_workorder_dealstatus"] = new OptionSetValue((int)WorkOrderDealStatus.PENDING_CLOSE);
            if (workorder.Contains("new_recoilstate"))
                owReceipt["new_workorder_recoilstate"] = workorder.GetAttributeValue<OptionSetValue>("new_recoilstate");
            if (workorder.Contains("new_contact"))
                owReceipt["new_customer_name"] = workorder.GetAttributeValue<string>("new_contact");
            if (workorder.Contains("new_sumreceivableamount"))
                owReceipt["new_tax_total_amount"] = workorder.GetAttributeValue<decimal>("new_sumreceivableamount");
            if (workorder.Contains("new_pay_manualfee"))
                owReceipt["new_tax_manualfee"] = workorder.GetAttributeValue<decimal>("new_pay_manualfee");
            if (workorder.Contains("new_pay_materialcost"))
                owReceipt["new_tax_materials_amount"] = workorder.GetAttributeValue<decimal>("new_pay_materialcost");
            if (workorder.Contains("new_owcurrency"))
                owReceipt["new_receivable_currency"] = workorder.GetAttributeValue<EntityReference>("new_owcurrency");
            owReceipt["new_collection_status"] = new OptionSetValue((int)WorkOrderCollectionStatus.NotCollected);
            owReceipt["new_payment_method"] = new OptionSetValue((int)WorkOrderPaymentMethod.MobilePosCollection);
            owReceipt["new_sap_code"] = this.Command<DigitalStoreCommand>().QueryStoreSapCodeByOrgId(workorder.GetAliasAttributeValue<string>("station.new_code"));
            List<TaxCalculationLine> taxLines = this.Command<TaxCalculationCommand>().CalculationTaxRates(workorder, owReceipt["new_sap_code"].ToString(), partlineList);
            owReceipt["new_manual_tax_rate"] = Decimal.Parse(taxLines.Where(taxLine => taxLine.PART_NUMBER.Equals(WorkOrderConstants.MANUAL_FEE_SKU))
                .Select(taxLine => taxLine.TAX_RATE).FirstOrDefault());
            owReceipt["new_material_tax_rate"] = Decimal.Parse(taxLines.Where(taxLine => taxLine.PART_NUMBER.Equals(WorkOrderConstants.MATERIAL_FEE_SKU))
                .Select(taxLine => taxLine.TAX_RATE).FirstOrDefault());
            return OrganizationService.Create(owReceipt);
        }

        /// <summary>
        /// 校验是否为零售通保外维修服务单
        /// 零售通网点且保外需要付维修金额
        /// </summary>
        /// <returns></returns>
        public bool CheckMobilePosOwOrder(Entity workOrder)
        {
            if (workOrder.GetAliasAttributeValue<OptionSetValue>("station.new_stietype")?.Value == (int)StationSiteTypeEnum.SIS
                && workOrder.GetAliasAttributeValue<OptionSetValue>("station.new_operationmodecode")?.Value == (int)StationOperationModeEnum.MobilePosDirectSale
                && workOrder.GetAttributeValue<OptionSetValue>("new_warranty")?.Value == (int)WorkOrderWarrantyEnum.OutWarranty
                && workOrder.GetAttributeValue<decimal>("new_sumreceivableamount") >= 0m)
            {
                #region 手工费特批减免金额校验逻辑
                QueryExpression specialapplyQuery = new QueryExpression("new_srv_specialapply")
                {
                    ColumnSet = new ColumnSet("new_name", "new_deduction_amount", "new_approvalstatus"),
                    Criteria = new FilterExpression
                    {
                        Conditions =
                            {
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0),
                                new ConditionExpression("new_workorder_id", ConditionOperator.Equal, workOrder.Id),
                                new ConditionExpression("new_type", ConditionOperator.Equal,11),
                                new ConditionExpression("new_deduction_amount", ConditionOperator.GreaterThan, 0),
                                new ConditionExpression("new_approvalstatus", ConditionOperator.In, new int[] { 2, 3 })
                            }
                    }
                };
                //服务活动减免金额
                QueryExpression activityQuery = new QueryExpression("new_srv_orderactivity")
                {
                    ColumnSet = new ColumnSet("new_handdiscount"),
                    LinkEntities =
                            {
                                new LinkEntity("new_srv_orderactivity", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner)
                                {
                                    LinkCriteria = new FilterExpression
                                    {
                                        Conditions =
                                        {
                                            new ConditionExpression("new_srv_workorderid", ConditionOperator.Equal,  workOrder.Id),
                                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                                        }
                                    }
                                }
                            }
                };
                Entity orderActivityEntity = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(activityQuery)?.Entities?.FirstOrDefault();
                EntityCollection specialapplyList = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(specialapplyQuery);
                if ((specialapplyList != null && specialapplyList?.Entities?.Count > 0) || orderActivityEntity != null)
                {
                    if (specialapplyList.Entities.Count > 1)
                    {
                        Log.ErrorMsg(string.Format("工单已申请特批单数量不符，当前工单{0}，特批单数量为{1}", workOrder.GetAttributeValue<string>("new_name"), specialapplyList.Entities.Count));
                        throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                    }
                    else if (specialapplyList != null && specialapplyList?.Entities?.Count == 1)
                    {
                        Entity specialapplyEntity = specialapplyList.Entities[0];
                        if (specialapplyEntity.GetAttributeValue<OptionSetValue>("new_approvalstatus").Value == 2)
                        {
                            Log.ErrorMsg(string.Format("工单申请的特批单未审批通过，当前工单{0}，特批单为{1}", workOrder.GetAttributeValue<string>("new_name"), specialapplyEntity.GetAttributeValue<string>("new_name")));
                            throw new InvalidPluginExecutionException(GetResource("special_approval_not_approved", "该工单存在待提交的特批单，请先确认审批状态再完成服务"));
                        }
                        if (!specialapplyEntity.Contains("new_deduction_amount"))
                        {
                            Log.ErrorMsg(string.Format("工单申请的特批单减免金额不符，当前工单{0}，特批单为{1}", workOrder.GetAttributeValue<string>("new_name"), specialapplyEntity.GetAttributeValue<string>("new_name")));
                            throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                        }
                        if (specialapplyEntity.GetAttributeValue<decimal>("new_deduction_amount") != workOrder.GetAttributeValue<decimal>("new_reduce_labor_cost"))
                        {
                            Log.ErrorMsg(string.Format("特批单减免金额校验不通过，当前工单{0}，特批单减免金额为{1}，工单减免金额为{2}", workOrder.GetAttributeValue<string>("new_name"), specialapplyEntity.GetAttributeValue<decimal>("new_deduction_amount"), workOrder.GetAttributeValue<decimal>("new_reduce_labor_cost")));
                            throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                        }
                    }
                    if ((workOrder.Contains("new_pay_manualfee") && workOrder.GetAttributeValue<decimal>("new_pay_manualfee") < 0)
                          || (workOrder.Contains("new_actualpay_manualfee") && workOrder.GetAttributeValue<decimal>("new_actualpay_manualfee") < 0))
                    {
                        Log.ErrorMsg(string.Format("工单未正常计算手工费，当前工单{0}，已支付手工费{1}，实际支付手工费{2}", workOrder.GetAttributeValue<string>("new_name"), workOrder.GetAttributeValue<decimal>("new_pay_manualfee"), workOrder.GetAttributeValue<decimal>("new_actualpay_manualfee")));
                        throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                    }
                    if (!workOrder.Contains("new_manualfee") || !workOrder.Contains("new_pay_manualfee"))
                    {
                        Log.ErrorMsg(string.Format("工单未正常计算手工费，当前工单{0}", workOrder.GetAttributeValue<string>("new_name")));
                        throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                    }
                    else
                    {
                        decimal manualFee = workOrder.GetAttributeValue<decimal>("new_manualfee");
                        decimal payManualFee = workOrder.GetAttributeValue<decimal>("new_pay_manualfee");
                        decimal reduceLaborCost = workOrder.GetAttributeValue<decimal>("new_reduce_labor_cost");
                        decimal activityDisCount = 0m;
                        if (orderActivityEntity != null)
                        {
                            if (orderActivityEntity.Contains("new_handdiscount"))
                            {
                                activityDisCount = manualFee - orderActivityEntity.GetAttributeValue<decimal>("new_handdiscount") * manualFee / 100;
                                if (manualFee - payManualFee != activityDisCount)
                                {
                                    Log.ErrorMsg(string.Format("工单手工费计算不正确，当前工单{0}，手工费{1}，已支付手工费{2}，减免金额{3},活动减免金额{4}", workOrder.GetAttributeValue<string>("new_name"), manualFee, payManualFee, reduceLaborCost, activityDisCount));
                                    throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                                }

                            }
                        }
                        else if (manualFee - payManualFee != reduceLaborCost)
                        {
                            Log.ErrorMsg(string.Format("工单手工费计算不正确，当前工单{0}，手工费{1}，已支付手工费{2}，减免金额{3},活动减免金额{4}", workOrder.GetAttributeValue<string>("new_name"), manualFee, payManualFee, reduceLaborCost, activityDisCount));
                            throw new InvalidPluginExecutionException(GetResource("special_approval_reduction_amount_verification_failed", "特批单减免金额校验不通过，请联系系统支持"));
                        }
                    }
                }
                #endregion
                return true;
            }
            return false;
        }

        #region IQC/OQC校验 m-1890969 

        #region Try壳子
        /// <summary>
        /// Try壳子
        /// </summary>
        /// <param name="fun"></param>
        /// <exception cref="Exception"></exception>
        public static void ShellTry(Action fun)
        {
            try
            {
                fun.Invoke();
            }
            catch (Exception ex)
            {
                throw new Exception($"[{new StackFrame(1).GetMethod().Name}-Error]{ex.Message}");
            }
        }
        #endregion

        #region IQC校验
        /// <summary>
        /// IQC校验
        /// </summary>
        /// <param name="entityWorkOrder"></param>
        public void IQCCheck(Entity entityWorkOrder, List<string> listImei, ref EntityCollection ecApproach )
        {
            bool isIQCCheck = false;
            List<int> liOrigin = new List<int>() { 100, 120, 60 };//B2X(100)、服务商对接(120)、工单上传(60)
            int iOrigin = entityWorkOrder.GetAttributeValue<OptionSetValue>("new_origin").Value;
            if (entityWorkOrder.GetAttributeValue<AliasedValue>("WorkorderCountry.new_iqcnodetection")?.Value != null)
            {
                isIQCCheck = Convert.ToBoolean(entityWorkOrder.GetAttributeValue<AliasedValue>("WorkorderCountry.new_iqcnodetection").Value);
            }
            if (isIQCCheck) { return; }
            #region 获取故障品类关系
            QueryExpression qeTrouble = new QueryExpression("new_srv_trouble")
            {
                ColumnSet = new ColumnSet(null),
                Criteria = new FilterExpression()
                {
                    Conditions = {
                        new ConditionExpression("statecode",ConditionOperator.Equal,0),
                        new ConditionExpression("new_srv_workorder_id",ConditionOperator.Equal,entityWorkOrder.Id)
                    }
                }
            };
            //关联故障品类关系(new_errorcategory_relation)
            qeTrouble.LinkEntities.Add(new LinkEntity()
            {
                EntityAlias = "new_errorcategory_relation",
                JoinOperator = JoinOperator.Inner,
                LinkFromEntityName = "new_srv_trouble",
                LinkToEntityName = "new_errorcategory_relation",
                LinkFromAttributeName = "new_srv_errorgroup_id",
                LinkToAttributeName = "new_errorgroup_id",
                Columns = new ColumnSet("new_iqcnodetection")
            });
            EntityCollection ecTrouble = OrganizationServiceAdmin.RetrieveMultiple(qeTrouble);
            #endregion
            Entity entityTrouble = ecTrouble.Entities.Where(x => (x.GetAttributeValue<AliasedValue>("new_errorcategory_relation.new_iqcnodetection")?.Value is null
            || !Convert.ToBoolean(x.GetAttributeValue<AliasedValue>("new_errorcategory_relation.new_iqcnodetection").Value))).FirstOrDefault();
            if (ecTrouble.Entities.Count != 0 && entityTrouble is null)
            {
                return;//关联数据有值&不校验IQC都是true;
            }
            ecApproach = GetApproach(entityWorkOrder.Id);
            Entity entityApproach = ecApproach.Entities.Where(x => (x.GetAttributeValue<AliasedValue>("new_approach.new_ismmiiqc")?.Value != null
            && Convert.ToBoolean(x.GetAttributeValue<AliasedValue>("new_approach.new_ismmiiqc").Value))).FirstOrDefault();
            if (entityApproach != null)
            {
                EntityCollection ecWorkorderImeicheck = GetImeiCheck(entityWorkOrder.Id, 100000000);//优先按Guid查询
                if ((ecWorkorderImeicheck?.Entities?.Count is null || ecWorkorderImeicheck?.Entities?.Count == 0)
                    && liOrigin.Contains(iOrigin))
                {
                    ecWorkorderImeicheck = GetImeiCheck(listImei, 100000000);
                }
                if (ecWorkorderImeicheck?.Entities?.Count is null || ecWorkorderImeicheck.Entities.Count == 0)
                {
                    throw new Exception(GetResource("mmi1890969.iqccheck", "未进行IQC MMI/CIT检测."));
                }
                if (Convert.ToBoolean(entityWorkOrder.GetAttributeValue<AliasedValue>("station.new_mmicitcheck")?.Value)
                && ecWorkorderImeicheck.Entities[0].GetAttributeValue<OptionSetValue>("new_result")?.Value != 2)
                {
                    throw new Exception(GetResource("mmi1890969.iqccheck", "未进行IQC MMI/CIT检测."));
                }
            }
        }
        #endregion

        #region OQC校验
        /// <summary>
        /// OQC校验
        /// </summary>
        /// <param name="entityWorkOrder"></param>
        public void OQCCheck(Entity entityWorkOrder, List<string> listImei, EntityCollection ecApproach = null)
        {
            bool isOQCCheck = false;
            if (entityWorkOrder.GetAttributeValue<AliasedValue>("WorkorderCountry.new_oqcnodetection")?.Value != null)
            {
                isOQCCheck = Convert.ToBoolean(entityWorkOrder.GetAttributeValue<AliasedValue>("WorkorderCountry.new_oqcnodetection").Value);
            }
            if (isOQCCheck) { return; }
            if (!entityWorkOrder.Contains("new_type") || !entityWorkOrder.Contains("new_origin"))
            {
                return; 
            }
            int iType = entityWorkOrder.GetAttributeValue<OptionSetValue>("new_type").Value;
            int iOrigin = entityWorkOrder.GetAttributeValue<OptionSetValue>("new_origin").Value;
            List<int> liOrigin = new List<int>() { 100, 120, 60 };//B2X(100)、服务商对接(120)、工单上传(60)
            #region 暂时不区分类型
            //List<int> ints = new List<int>() { 2, 3, 4 };//换货/退货/检测
            //if (ints.Contains(iType))
            //{
            //    return;//换货/退货/检测无需OQC MMI检测；
            //}
            #endregion
            if (ecApproach is null)
            {
                ecApproach = GetApproach(entityWorkOrder.Id);
            }
            Entity entityApproach = ecApproach.Entities
            .Where(x => (x.GetAttributeValue<AliasedValue>("new_approach.new_ismmi")?.Value != null
            && Convert.ToBoolean(x.GetAttributeValue<AliasedValue>("new_approach.new_ismmi").Value))).FirstOrDefault();
            if (entityApproach != null)
            {
                EntityCollection ecWorkorderImeicheck = GetImeiCheck(entityWorkOrder.Id, 100000001);

                if ((ecWorkorderImeicheck?.Entities?.Count is null || ecWorkorderImeicheck?.Entities?.Count == 0)
                && (liOrigin.Contains(iOrigin) || IsB2XWorkOrder(entityWorkOrder.Id)))
                {
                    ecWorkorderImeicheck = GetImeiCheck(listImei, 100000001);
                }
                if (ecWorkorderImeicheck?.Entities?.Count is null
                    || ecWorkorderImeicheck.Entities.Count == 0)
                {
                    throw new Exception(GetResource("mmi1890969.oqccheck", "未进行OQC MMI/CIT检测."));
                }
                if (Convert.ToBoolean(entityWorkOrder.GetAttributeValue<AliasedValue>("station.new_mmicitcheck")?.Value)
                && ecWorkorderImeicheck.Entities[0].GetAttributeValue<OptionSetValue>("new_result")?.Value != 2)
                {
                    throw new Exception(GetResource("mmi1890969.oqccheck", "未进行OQC MMI/CIT检测."));
                }
            }
        }
        #endregion

        #region 获取检测结果By检测类型
        /// <summary>
        /// 获取检测结果By检测类型\Imei\SN\FSN
        /// </summary>
        /// <param name="listImei"></param>
        /// <returns></returns>
        public EntityCollection GetImeiCheck(List<string> listImei, int iQcdetectionType)
        {
            EntityCollection ecWorkorderImeicheck = new EntityCollection();
            ShellTry(() =>
            {
                //MMICIT检验结果校验
                QueryExpression qeWorkorderImeicheck = new QueryExpression("new_srv_workorder_imeicheck")
                {
                    ColumnSet = new ColumnSet("new_result"),
                    Criteria = new FilterExpression()
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode",ConditionOperator.Equal,0),
                            new ConditionExpression("new_qcdetectiontype",ConditionOperator.Equal,iQcdetectionType)
                        }
                    },
                    Orders = {
                    new OrderExpression("createdon", OrderType.Descending)
                    }
                };
                FilterExpression filterExpression = new FilterExpression()
                {
                    FilterOperator = LogicalOperator.Or,
                    Conditions = {
                         new ConditionExpression("new_imei1",ConditionOperator.In,listImei),
                         new ConditionExpression("new_imei2",ConditionOperator.In,listImei),
                         new ConditionExpression("new_fsn",ConditionOperator.In,listImei),
                         new ConditionExpression("new_sn",ConditionOperator.In,listImei)
                    }
                };
                qeWorkorderImeicheck.Criteria.AddFilter(filterExpression);
                ecWorkorderImeicheck = OrganizationServiceAdmin.RetrieveMultiple(qeWorkorderImeicheck);
            });
            return ecWorkorderImeicheck;
        }

        /// <summary>
        /// 获取检测结果By检测类型\服务单
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="iQcdetectionType"></param>
        /// <returns></returns>
        public EntityCollection GetImeiCheck(Guid workOrderId, int iQcdetectionType)
        {
            EntityCollection ecWorkorderImeicheck = new EntityCollection();
            ShellTry(() =>
            {
                //MMICIT检验结果校验
                QueryExpression qeWorkorderImeicheck = new QueryExpression("new_srv_workorder_imeicheck")
                {
                    ColumnSet = new ColumnSet("new_result"),
                    Criteria = new FilterExpression()
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode",ConditionOperator.Equal,0),
                            new ConditionExpression("new_qcdetectiontype",ConditionOperator.Equal,iQcdetectionType),
                            new ConditionExpression("new_workorder_id",ConditionOperator.Equal,workOrderId)
                        }
                    },
                    Orders = {
                        new OrderExpression("createdon", OrderType.Descending)
                    }
                };
                ecWorkorderImeicheck = OrganizationServiceAdmin.RetrieveMultiple(qeWorkorderImeicheck);
            });
            return ecWorkorderImeicheck;
        }
        #endregion

        #region 获取服务单处理方法
        /// <summary>
        /// 获取服务单处理方法
        /// </summary>
        /// <param name="guidWorkOrderId"></param>
        /// <returns></returns>
        public EntityCollection GetApproach(Guid guidWorkOrderId)
        {
            EntityCollection ecApproach = new EntityCollection();
            ShellTry(() =>
            {
                QueryExpression qeApproach = new QueryExpression("new_srv_workorder_approach")
                {
                    ColumnSet = new ColumnSet(null),
                    Criteria = new FilterExpression()
                    {
                        Conditions = {
                        new ConditionExpression("statecode",ConditionOperator.Equal,0),
                        new ConditionExpression("new_srv_workorder_id",ConditionOperator.Equal,guidWorkOrderId)
                    }
                    }
                };
                //关联处理方法
                qeApproach.LinkEntities.Add(new LinkEntity()
                {
                    EntityAlias = "new_approach",
                    JoinOperator = JoinOperator.Inner,
                    LinkFromEntityName = "new_srv_workorder_approach",
                    LinkToEntityName = "new_approach",
                    LinkFromAttributeName = "new_approach_id",
                    LinkToAttributeName = "new_approachid",
                    Columns = new ColumnSet("new_ismmiiqc", "new_ismmi")
                });
                ecApproach = OrganizationServiceAdmin.RetrieveMultiple(qeApproach);
            });
            return ecApproach;
        }
        #endregion

        #region 配置规则校验
        /// <summary>
        /// MMI/CIT检测规则(new_mmicitrule)校验
        /// </summary>
        /// <param name="entityWorkOrder">服务单数据</param>
        /// <returns></returns>
        public bool IsExistMMICITRule(Entity entityWorkOrder)
        {
            List<string> lsAttName = new List<string>();
            lsAttName.Add("new_category1_id");
            lsAttName.Add("new_category2_id");
            lsAttName.Add("new_category3_id");
            lsAttName.Add("new_goodsfiles_id");
            EntityReference erCategory1 = entityWorkOrder.GetAttributeValue<EntityReference>("new_category1_id");
            EntityReference erCategory2 = entityWorkOrder.GetAttributeValue<EntityReference>("new_category2_id");
            EntityReference erCategory3 = entityWorkOrder.GetAttributeValue<EntityReference>("new_category3_id");
            EntityReference erGoodsfiles = entityWorkOrder.GetAttributeValue<EntityReference>("new_goodsfiles_id");
            if (erCategory1 == null || erCategory2 == null || erCategory3 == null || erGoodsfiles == null)
            {
                return false;
            }
            #region 查询MMI/CIT检测规则(new_mmicitrule)
            //1.若规则表配置到一级品类，则所有关联一级品类的商品都需要走上述校验逻辑
            //2.若规则表配置了到二级品类，则所有关联二级品类的商品都需要走上述校验逻辑，若同时存在一个包含该二级品类的一级品类配置，则以一级品类配置为准；
            //3.若规则表配置了到三级品类，则所有关联三级品类的商品都需要走上述校验逻辑，若同时存在一个包含该三级品类的一二级品类配置，则以一二级品类配置为准；
            //4.若规则表配置了到具体商品，则对应商品需要走上述校验逻辑，若同时存在一个包含该商品的一二三级品类配置，则以一二三级品类配置为准；
            string fetxml = string.Format(@"<fetch version=""1.0"" mapping=""logical""   no-lock=""true"" top=""1"">
        <entity name=""new_mmicitrule"">
                <attribute name=""createdon""/>
                <filter type=""or"">
                                <filter type=""and"">
                                        <condition attribute=""new_category1_id"" operator=""eq"" value=""{0}""  uitype=""new_category1""/>
                                        <condition attribute=""new_category2_id"" operator=""null""/>
                                        <condition attribute=""new_category3_id"" operator=""null""/>
                                        <condition attribute=""new_goodsfiles_id"" operator=""null""/>
                <condition attribute=""statecode"" operator=""eq"" value=""0""/>
                                </filter>
                                <filter type=""and"">
                                        <condition attribute=""new_category1_id"" operator=""eq"" value=""{0}""   uitype=""new_category1""/>
                                        <condition attribute=""new_category2_id"" operator=""eq"" value=""{1}""  uitype=""new_category2""/>
                                        <condition attribute=""new_category3_id"" operator=""null""/>
                                        <condition attribute=""new_goodsfiles_id"" operator=""null""/>
                <condition attribute=""statecode"" operator=""eq"" value=""0""/>
                                </filter>
                                <filter type=""and"">
                                        <condition attribute=""new_category1_id"" operator=""eq"" value=""{0}""   uitype=""new_category1""/>
                                        <condition attribute=""new_category2_id"" operator=""eq"" value=""{1}""   uitype=""new_category2""/>
                                        <condition attribute=""new_category3_id"" operator=""eq"" value=""{2}""   uitype=""new_category3""/>
                                        <condition attribute=""new_goodsfiles_id"" operator=""null""/>
                <condition attribute=""statecode"" operator=""eq"" value=""0""/>
                                </filter>
                                <filter type=""and"">
                                        <condition attribute=""new_category1_id"" operator=""eq"" value=""{0}""   uitype=""new_category1""/>
                                        <condition attribute=""new_category2_id"" operator=""eq"" value=""{1}""   uitype=""new_category2""/>
                                        <condition attribute=""new_category3_id"" operator=""eq"" value=""{2}""   uitype=""new_category3""/>
                                        <condition attribute=""new_goodsfiles_id"" operator=""eq"" value=""{3}""   uitype=""new_goodsfiles""/>
                <condition attribute=""statecode"" operator=""eq"" value=""0""/>
                                </filter>
                        </filter>
        </entity>
</fetch>", erCategory1.Id, erCategory2.Id, erCategory3.Id, erGoodsfiles.Id);
            var new_mmicitrule = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetxml));
            return new_mmicitrule.Entities.Count > 0;
        }
        #endregion
        #endregion

        #endregion
        #region 校验国家是否满足大仓发货
        /// <summary>
        /// 校验工单国家是否配置大仓配置参数
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public bool CheckCountryIsShipping(string orderId)
        {
            bool result = false;
            if (string.IsNullOrWhiteSpace(orderId))
                return result;

            try
            {
                // 查询工单国家
                Entity order = WorkOrderCommon.GetEntityWorkOrderAllFileds(new Guid(orderId), OrganizationService);
                if (!order.Contains("new_country_id"))
                    return result;
                string countryId = order.GetAttributeValue<EntityReference>("new_country_id").Id.ToString();
                // 查询大仓发货销售渠道配置
                QueryExpression query = new QueryExpression("new_shippingsaleschannel");
                query.Criteria.AddCondition("new_country_id", ConditionOperator.Equal, countryId);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_shippingsaleschannelid");
                query.TopCount = 1;
                EntityCollection collection = OrganizationService.RetrieveMultiple(query);
                if (collection != null && collection.Entities.Count > 0)
                    result = true;

                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("【CheckCountryIsShipping】错误：" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion

        /// <summary>
        /// 判断是否服务商对接接口过来的服务单
        /// </summary>
        /// <param name="workorderId"></param>
        /// <returns>是对接服务单为true，否则为false</returns>
        public bool IsB2XWorkOrder(Guid workorderId) 
        {
            // 查询工单
            Entity workorderEn = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_srv_workorder", workorderId, new ColumnSet("new_b2x_orderid"));

            return workorderEn.Contains("new_b2x_orderid") && !string.IsNullOrWhiteSpace(workorderEn.GetAttributeValue<string>("new_b2x_orderid")) ? true : false;
        }
    }
}
