﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Caching; 
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using RekTec.Crm.BizCommon;
using RekTec.Crm.Common.Cache;
using RekTec.Crm.Common.Logger;
using RekTec.Crm.OrganizationService.Common.Helper;
using RekTec.Crm.Plugin.Common.EntitySerialize;
using RekTec.Service1.Service.Command;
namespace RekTec.Service1.Service.Bll
{
    public static class WorkOrderCommon
    {
        /// <summary>
        /// 根据主键id获取对应的new_code值
        /// </summary>
        /// <param name="OrganizationService">组织服务</param>
        /// <param name="entityName">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <returns></returns>
        public static string GetAddressId(IOrganizationService organizationService, string entityName, Guid entityId)
        {
            try
            {
                var ec = organizationService.Retrieve(entityName, entityId, new ColumnSet("new_id"));
                if (ec == null)
                    return string.Empty;
                if (!ec.Contains("new_id"))
                    return string.Empty;
                return ec.GetAttributeValue<string>("new_id");
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 根据主键Guid获取对应的国家3位编码
        /// </summary>
        /// <param name="OrganizationService">组织服务</param>
        /// <param name="entityName">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <returns></returns>
        public static string GetCountryCode(IOrganizationService organizationService, string entityName, Guid entityId)
        {
            var ec = organizationService.Retrieve(entityName, entityId, new ColumnSet("new_code1"));
            if (ec == null)
                return string.Empty;
            if (!ec.Contains("new_code1"))
                return string.Empty;
            return ec.GetAttributeValue<string>("new_code1");
        }

        /// <summary>
        /// 查询三级品类是否为手机
        /// </summary>
        /// <param name="new_category3_id"></param>
        /// <returns></returns>
        public static bool SwarchCategory3(IOrganizationService OrganizationService, CrmCache cache, Guid new_category3_id)
        {
            try
            {
                bool iscategory3 = false;
                if (new_category3_id == Guid.Empty)
                    return iscategory3;

                //var category3Entity = GetEntityCategory3(OrganizationService, cache);
                //if (category3Entity == null || category3Entity.Entities == null || category3Entity.Entities.Count <= 0)
                //    return false;
                //var category3 = category3Entity.Entities.Where(x => x.Id == new_category3_id).FirstOrDefault();
                //if (category3 != null)
                //{
                var new_code = GetEntityCategory3Code(OrganizationService, cache, new_category3_id);
                //获取三级品类为手机的系统参数
                var new_category3_iphonecode = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "new_category3_iphonecode");
                if (new_code == new_category3_iphonecode)
                {
                    iscategory3 = true;
                }
                //}
                return iscategory3;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询三级品类是否为手机异常" + ex.Message);
            }
        }
        /// <summary>
        /// 判断三级品类是否智能手表或者其他配置编码
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="category3Id"></param>
        /// <returns></returns>
        public static bool SearchCategory3WatchAndOtherCode(IOrganizationService organizationService, CrmCache cache, string category3Id)
        {
            bool result = false;
            if (string.IsNullOrWhiteSpace(category3Id))
                return result;
            try
            {
                var new_code = GetEntityCategory3Code(organizationService, cache, Guid.Parse(category3Id));
                //获取三级品类为手机的系统参数
                var watchAndOthercode = CommonHelper.GetSystemParameterValueFromMemoryCatch(organizationService, "new_category3_watchAndOthercode");
                if (watchAndOthercode.Split(';').Contains(new_code))
                {
                    result = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("【SearchCategory3WatchAndOtherCode】错误：" + ex.Message);
            }
        }
        /// <summary>
        /// 判断三级品类是否平板
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="category3Id"></param>
        /// <returns></returns>
        public static bool SearchCategory3Pad(IOrganizationService organizationService, CrmCache cache, string category3Id)
        {
            bool result = false;
            if (string.IsNullOrWhiteSpace(category3Id))
                return result;
            try
            {
                //var category3Entity = GetEntityCategory3(organizationService, cache,category3Id);
                //if (category3Entity == null || category3Entity.Entities == null || category3Entity.Entities.Count <= 0)
                //    return false;
                //var category3 = category3Entity.Entities.Where(x => x.Id == new Guid(category3Id)).FirstOrDefault();
                //if (category3 != null)
                //{
                var new_code = GetEntityCategory3Code(organizationService, cache, Guid.Parse(category3Id));
                //获取三级品类为手机的系统参数
                var new_category3_padcode = CommonHelper.GetSystemParameterValueFromMemoryCatch(organizationService, "new_category3_padcode");
                if (new_code == new_category3_padcode)
                {
                    result = true;
                }
                //}
                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("【SearchCategory3Pinban】错误：" + ex.Message);
            }
        }
        /// <summary>
        /// 判断三级品类是否智能手表
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="cache"></param>
        /// <param name="category3Id"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public static bool SearchCategory3Watch(IOrganizationService organizationService, CrmCache cache, Guid category3Id)
        {
            bool result = false;
            if (category3Id == Guid.Empty)
                return result;
            try
            {
                var new_code = GetEntityCategory3Code(organizationService, cache, category3Id);
                //获取三级品类为智能手表的系统参数
                var new_category3_watch = CommonHelper.GetSystemParameterValueFromMemoryCatch(organizationService, "new_category3_watchAndOthercode").Split(';');
                if (new_category3_watch.Contains(new_code))
                {
                    result = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("【SearchCategory3Pinban】错误：" + ex.Message);
            }
        }

        /// <summary>
        /// 查询物料类别是否为主板
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="new_productnew_id"></param>
        public static bool SearchProductZhuBan(IOrganizationService OrganizationService, Guid new_materialcategory1_id, CrmCache cache)
        {
            try
            {
                bool iszhuban = false;
                var materialcategory1Entity = GetEntityMaterialcategory1(OrganizationService, cache);
                if (materialcategory1Entity == null || materialcategory1Entity.Count <= 0)
                    return false;
                var materialcategory1 = materialcategory1Entity.Where(x => x.Id == new_materialcategory1_id).FirstOrDefault();
                if (materialcategory1 == null)
                    return false;
                var new_code = materialcategory1.GetAttributeValue<string>("new_code");
                //获取物料品类为成品的系统参数
                var new_materialcategory1_mainboard = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "new_materialcategory1_mainboard");
                if (new_code == new_materialcategory1_mainboard)
                    iszhuban = true;
                return iszhuban;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询物料类别是否为主板异常" + ex.Message);
            }
        }


        /// <summary>
        /// 查询物料类别是否为成品
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="new_materialcategory1_id"></param>
        /// <returns></returns>
        public static bool SearchProductChengPin(IOrganizationService OrganizationService, Guid new_materialcategory1_id, CrmCache cache)
        {
            try
            {
                bool ischengpin = false;
                var materialcategory1Entity = GetEntityMaterialcategory1(OrganizationService, cache);
                if (materialcategory1Entity == null || materialcategory1Entity.Count <= 0)
                    return false;
                var materialcategory1 = materialcategory1Entity.Where(x => x.Id == new_materialcategory1_id).FirstOrDefault();
                if (materialcategory1 == null)
                    return false;
                var new_code = materialcategory1.GetAttributeValue<string>("new_code");
                //获取物料品类为成品的系统参数
                var new_materialcategory1_finshedproduct = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "new_materialcategory1_finshedproduct");
                if (new_code == new_materialcategory1_finshedproduct)
                    ischengpin = true;
                return ischengpin;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询物料类别是否为主板异常" + ex.Message);
            }
        }

        /// <summary>
        /// 查询物料类别是否为商品
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="new_productnew_id"></param>
        /// <returns></returns>
        public static bool SearchProductIsShangPin(IOrganizationService OrganizationService, Guid new_productnew_id)
        {
            try
            {
                bool isgoods = false;
                var product = OrganizationService.RetrieveWithBypassPlugin("product", new_productnew_id, new ColumnSet("new_isgoods"));
                if (product == null)
                {
                    return isgoods;
                }
                if (product.Contains("new_isgoods") && product.GetAttributeValue<bool>("new_isgoods"))
                {
                    isgoods = true;
                }
                return isgoods;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询物料类别是否为主板异常" + ex.Message);
            }
        }

        /// <summary>
        /// 根据物料类别guid获取物料名称
        /// </summary>
        /// <param name="organizationService">服务</param>
        /// <param name="code">物料类别GUID</param>
        /// <returns>物料类别id</returns>
        public static string GetCategoryName(IOrganizationService organizationService, Guid id, CrmCache cache)
        {
            if (id == Guid.Empty)
                return "";
            try
            {
                string name = "";
                var materialcategory1Entity = GetEntityMaterialcategory1(organizationService, cache);
                if (materialcategory1Entity == null || materialcategory1Entity.Count <= 0)
                    return name;
                var materialcategory1 = materialcategory1Entity.Where(x => x.Id == id).FirstOrDefault();
                if (materialcategory1 == null)
                    return name;
                name = materialcategory1.GetAttributeValue<string>("new_name");
                return name;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #region 查询物料类别
        public static List<Entity> GetEntityMaterialcategory1(IOrganizationService OrganizationService, CrmCache cache)
        {
            try
            {
                List<Entity> ec = new List<Entity>();

                string value = cache.GetValue("GetEntityMaterialcategory1", moduleName + "_Materialcategory1");
                if (!string.IsNullOrWhiteSpace(value))
                {
                    ec = JsonConvertExt.DeSerialize<List<Entity>>(value);
                }
                else
                {
                    QueryExpression QE = new QueryExpression("new_materialcategory1");
                    QE.ColumnSet = new ColumnSet(new string[] { "new_code", "new_materialcategory1id", "new_name" });
                    QE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    EntityCollection collection = OrganizationService.RetrieveMultiple(QE);
                    cache.SetValue("GetEntityMaterialcategory1", JsonConvertExt.Serialize(collection?.Entities.ToList()), moduleName + "_Materialcategory1", (int)TimeSpan.FromHours(8).TotalSeconds);
                    return collection?.Entities?.ToList();
                }
                return ec;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询物料类别


        /// <summary>
        /// 根据物料子类别guid获取物料名称
        /// </summary>
        /// <param name="organizationService">服务</param>
        /// <param name="code">物料类别GUID</param>
        /// <returns>物料类别id</returns>
        public static string GetMaterialcategory2Name(IOrganizationService organizationService, CrmCache cache, Guid id)
        {
            if (id == Guid.Empty)
                return "";
            try
            {
                string name = "";
                var materialcategory2Entity = GetEntityMaterialcategory2(organizationService, cache);
                if (materialcategory2Entity == null || materialcategory2Entity.Entities == null || materialcategory2Entity.Entities.Count <= 0)
                    return name;
                var materialcategory2 = materialcategory2Entity.Entities.Where(x => x.Id == id).FirstOrDefault();
                if (materialcategory2 == null)
                    return name;
                name = materialcategory2.GetAttributeValue<string>("new_name");
                return name;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #region 查询物料子类别
        public static EntityCollection GetEntityMaterialcategory2(IOrganizationService OrganizationService, CrmCache cache)
        {
            EntityCollection EntityMaterialcategory2 = new EntityCollection();
            try
            {
                string value = cache.GetValue("GetEntityMaterialcategory2", moduleName + "_Materialcategory2");
                if (!string.IsNullOrWhiteSpace(value))
                {
                    EntityMaterialcategory2 = JsonConvertExt.DeSerialize<EntityCollection>(value);
                }
                else
                {
                    QueryExpression QE = new QueryExpression("new_materialcategory2");
                    QE.ColumnSet = new ColumnSet(new string[] { "new_code", "new_materialcategory2id", "new_name" });
                    QE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    EntityMaterialcategory2 = OrganizationService.RetrieveMultiple(QE);
                    cache.SetValue("GetEntityMaterialcategory2", JsonConvertExt.Serialize(EntityMaterialcategory2), moduleName + "_Materialcategory2", (int)TimeSpan.FromHours(8).TotalSeconds);
                }
                return EntityMaterialcategory2;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询物料子类别

        /// <summary>
        /// 判断一级品类是否为生态链
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="cache">cache</param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool Getcategory1idstl(IOrganizationService organizationService, CrmCache cache, Guid id)
        {
            if (id == Guid.Empty)
                return false;
            try
            {
                var category1Code = GetEntityCategory1Code(organizationService, cache, id.ToString());
                if (string.IsNullOrWhiteSpace(category1Code))
                    return false;
                //一级品类编码（生态链）
                var new_materialcategory1_finshedproduct = CommonHelper.GetSystemParameterValueFromMemoryCatch(organizationService, "firstcategory1_shengtailian");
                if (new_materialcategory1_finshedproduct == category1Code)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 判断一级品类是否为手机平板
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="cache">cache</param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool Getcategory1idsjpb(IOrganizationService organizationService, CrmCache cache, Guid id)
        {
            if (id == Guid.Empty)
                return false;
            try
            {
                var category1Code = GetEntityCategory1Code(organizationService, cache, id.ToString());
                if (string.IsNullOrWhiteSpace(category1Code))
                    return false;
                //一级品类编码（手机平板）
                var new_materialcategory1_finshedproduct = CommonHelper.GetSystemParameterValueFromMemoryCatch(organizationService, "firstcategory1_shoujipingban");
                if (new_materialcategory1_finshedproduct == category1Code)
                    return true;
                return false;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }



        /// <summary>
        /// 获取物料类别主板和成品的主键Id
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        public static string GetMaterialCategoryId(IOrganizationService OrganizationService, CrmCache cache, string categoryCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(categoryCode))
                    return string.Empty;

                string value = cache.GetValue("GetEntityMaterialcategory1", moduleName + "_Materialcategory1");
                if (!string.IsNullOrWhiteSpace(value))
                {
                    List<Entity> MaterialCategorys = JsonConvertExt.DeSerialize<List<Entity>>(value);
                    return MaterialCategorys.FirstOrDefault(i => i.GetAttributeValue<string>("new_code") == categoryCode).Id.ToString();
                }
                else
                {
                    QueryExpression QE = new QueryExpression("new_materialcategory1");
                    QE.ColumnSet = new ColumnSet(new string[] { "new_code", "new_materialcategory1id", "new_name" });
                    QE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    EntityCollection collection = OrganizationService.RetrieveMultiple(QE);
                    cache.SetValue("GetEntityMaterialcategory1", JsonConvertExt.Serialize(collection?.Entities.ToList()), moduleName + "_Materialcategory1", (int)TimeSpan.FromHours(8).TotalSeconds);
                    return collection?.Entities.Where(x => x.GetAttributeValue<string>("new_code") == categoryCode).FirstOrDefault()?.Id.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }


        #region 查询案例

        /// <summary>
        /// 案例集合
        /// </summary>
        private static List<Entity> Incidents = new List<Entity>();

        private static readonly object Incident_Locker = new object();
        /// <summary>
        /// 查询案例
        /// </summary>
        public static Entity GetEntityIncident(Guid caseid, IOrganizationService OrganizationService)
        {
            try
            {
                if (Incidents.Count == 0 || !Incidents.Any(i => i.Id == caseid))
                {
                    lock (Incident_Locker)
                    {
                        if (Incidents.Count == 0 || !Incidents.Any(i => i.Id == caseid))
                        {
                            //查询
                            Incidents.Add(OrganizationService.Retrieve("incident", caseid, new ColumnSet(true)));
                        }
                    }
                }
                return Incidents.FirstOrDefault(i => i.Id == caseid);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #endregion 查询案例

        #region 登录人网点合集
        /// <summary>
        /// 登录人网点合集
        /// </summary>
        private static List<Entity> Stations = new List<Entity>();

        private static readonly object Station_Locker = new object();
        /// <summary>
        /// 根据当前登录人查询服务商服务网络
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public static Entity GetEntityStation(CrmCache Cache, IOrganizationService OrganizationService, Guid UserId)
        {
            try
            {
                Entity entity = null;
                var json = Cache.GetValue(UserId.ToString(), moduleName + "_Worker_Station");
                if (!string.IsNullOrWhiteSpace(json))
                    entity = JsonConvertExt.DeSerialize<Entity>(json);
                if (entity == null)
                {
                    //查询
                    QueryExpression srvworkerQE = new QueryExpression("new_srv_worker");
                    srvworkerQE.ColumnSet = new ColumnSet("new_srv_station_id", "new_code", "new_name", "new_systemuser_id");
                    srvworkerQE.Criteria.AddCondition("new_systemuser_id", ConditionOperator.Equal, UserId);
                    srvworkerQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    LinkEntity stationentity = new LinkEntity("new_srv_worker", "new_srv_station", "new_srv_station_id", "new_srv_stationid", JoinOperator.Inner);//服务网站
                    stationentity.EntityAlias = "station";
                    stationentity.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    stationentity.Columns.AddColumns(new string[] { "new_srctype", "new_way", "new_srv_station_id", "new_region_id", "new_country_id", "new_province_id", "new_stietype", "new_cooperationstatus" });
                    srvworkerQE.LinkEntities.Add(stationentity);
                    var srvworkerEC = OrganizationService.RetrieveMultipleWithBypassPlugin(srvworkerQE);
                    if (srvworkerEC?.Entities?.Count > 0)
                        entity = srvworkerEC[0];
                }
                if (entity == null)
                {
                    throw new InvalidPluginExecutionException($"根据当前登录人{UserId}查询服务网络为空");
                }
                else
                {
                    var cooperationstatu = entity.GetAliasAttributeValue<OptionSetValue>("station.new_cooperationstatus").Value;
                    if (cooperationstatu != 1)
                    {
                        throw new InvalidPluginExecutionException("当前登录人:" + entity.GetAttributeValue<string>("new_name") + ",服务网络网点状态不为合作中");
                    }
                }
                return entity;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion 登录人网点合集

        #region 产品档案

        /// <summary>
        /// 产品档案合集
        /// </summary>
        private static List<Entity> Userprofiles = new List<Entity>();

        private static readonly object Userprofile_Locker = new object();
        /// <summary>
        /// 产品档案
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="new_sn"></param>
        /// <returns></returns>
        public static List<Entity> GetEntityUserprofile(IOrganizationService OrganizationService, string new_sn)
        {
            try
            {
                if (Userprofiles.Count == 0 || !Userprofiles.Any(i => i.GetAttributeValue<string>("new_name") == new_sn))
                {
                    lock (Userprofile_Locker)
                    {
                        if (Userprofiles.Count == 0 || !Userprofiles.Any(i => i.GetAttributeValue<string>("new_name") == new_sn))
                        {
                            //查询
                            List<Entity> userprofileEC = null;
                            if (string.IsNullOrWhiteSpace(new_sn))
                                return userprofileEC;

                            QueryExpression userprofileQE = new QueryExpression("new_srv_userprofile");//产品档案
                            userprofileQE.ColumnSet = new ColumnSet("new_packetsstarttime", "new_maintenanceendtime", "new_returnendtime", "new_issuesreturn_stoptime", "new_exchangeendtime", "new_casttime", "new_factorytime", "new_invoicetime", "new_activate_date", "new_policy_type", "new_imei", "new_repairrightsrule_id", "new_type", "new_salechanneltype", "new_fsn", "new_sapid", "new_sale_address", "new_activate", "new_name", "new_repairrights_id");
                            userprofileQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            userprofileQE.Criteria.AddCondition("new_name", ConditionOperator.Equal, new_sn);

                            LinkEntity userprofilerightsline = new LinkEntity("new_srv_userprofile", "new_srv_userprofile_rightsline", "new_srv_userprofileid", "new_userprofile_id", JoinOperator.LeftOuter);//部件三包
                            userprofilerightsline.EntityAlias = "userprofilerightsline";
                            userprofilerightsline.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            userprofilerightsline.Columns.AddColumns(new string[] { "new_srv_userprofile_rightslineid", "new_product_id", "new_begintime", "new_endtime", "new_name" });
                            userprofileQE.LinkEntities.Add(userprofilerightsline);
                            var EC = OrganizationService.RetrieveMultiple(userprofileQE);
                            if (EC == null || EC.Entities == null || EC.Entities.Count <= 0)
                                return userprofileEC;
                            foreach (var item in EC.Entities)
                                Userprofiles.Add(item);
                        }
                    }
                }
                return Userprofiles.Where(i => i.GetAttributeValue<string>("new_name") == new_sn).ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 产品档案

        #region 订单来源渠道关系表
        private static EntityCollection OrderfromChannel = new EntityCollection();
        public static EntityCollection GetEntityOrderfromChannel(IOrganizationService OrganizationService)
        {
            try
            {
                if (OrderfromChannel == null || OrderfromChannel.Entities == null || OrderfromChannel.Entities.Count <= 0)
                {
                    QueryExpression QE = new QueryExpression("new_orderfrom_channel_rel");//订单来源渠道关系表
                    QE.ColumnSet = new ColumnSet(new string[] { "new_salechanneltype", "new_salechannel", "new_orderfrom_channel_relid", "new_channel", "new_orderfrom" });//渠道属性、销售渠道
                    QE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    OrderfromChannel = OrganizationService.RetrieveMultiple(QE);
                }
                return OrderfromChannel;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #endregion 订单来源渠道关系表

        #region 查询客户
        private static List<Entity> Accounts = new List<Entity>();
        private static readonly object Account_Locker = new object();
        public static Entity GetEntityAccount(IOrganizationService OrganizationService, string tel_c, List<string> sapidlist, IPluginLogger log)
        {
            try
            {
                log.InfoMsg("静态查询前数据条数：" + Accounts.Count);
                if (Accounts.Count == 0 || !Accounts.Any(i => i.GetAttributeValue<string>("telephone1") == tel_c || sapidlist.Contains(i.GetAttributeValue<string>("new_sapid"))))
                {
                    lock (Account_Locker)
                    {
                        if (Accounts.Count == 0 || !Accounts.Any(i => i.GetAttributeValue<string>("telephone1") == tel_c || sapidlist.Contains(i.GetAttributeValue<string>("new_sapid"))))
                        {
                            Stopwatch sw = new Stopwatch();
                            sw.Start();
                            //查询
                            QueryExpression accountQE = new QueryExpression("account");
                            accountQE.ColumnSet = new ColumnSet(new string[] { "accountid", "name", "telephone1", "new_sapid" });
                            FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                            filter.AddCondition("telephone1", ConditionOperator.Equal, tel_c);//电话
                            if (sapidlist != null && sapidlist.Count > 0)
                                filter.AddCondition("new_sapid", ConditionOperator.In, sapidlist.ToArray());//sapid
                            accountQE.Criteria.AddFilter(filter);
                            var accountEC = OrganizationService.RetrieveMultipleWithBypassPlugin(accountQE);
                            sw.Stop();
                            log.InfoMsg($"静态查询客户 总耗时：{sw.ElapsedMilliseconds}");
                            if (accountEC?.Entities?.Count > 0)
                            {
                                log.InfoMsg("有数据");
                                foreach (var item in accountEC.Entities)
                                {
                                    item["telephone1"] = tel_c;
                                    Accounts.Add(item);
                                }
                                log.InfoMsg($"{"插入数据之后数据条数：" + Accounts.Count}");
                            }
                            else
                                return null;
                        }
                    }
                }
                return Accounts.FirstOrDefault(i => i.GetAttributeValue<string>("telephone1") == tel_c || sapidlist.Contains(i.GetAttributeValue<string>("new_sapid")));
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询客户

        #region 查询工单
        /// <summary>
        /// 工单集合
        /// </summary>
        private static List<Entity> WorkOrders = new List<Entity>();

        private static readonly object WorkOrder_Locker = new object();
        /// <summary>
        /// 查询工单
        /// </summary>
        /// <param name="new_model3id"></param>
        /// <param name="OrganizationService"></param>
        /// <returns></returns>
        public static Entity GetEntityWorkOrder(Guid workorderid, IOrganizationService OrganizationService)
        {
            try
            {
                if (WorkOrders.Count == 0 || !WorkOrders.Any(i => i.Id == workorderid))
                {
                    lock (WorkOrder_Locker)
                    {
                        if (WorkOrders.Count == 0 || !WorkOrders.Any(i => i.Id == workorderid))
                        {
                            //查询
                            WorkOrders.Add(OrganizationService.Retrieve("new_srv_workorder", workorderid, new ColumnSet("new_type", "new_refund_amount", "new_returntype", "new_station_id", "new_customerid", "new_productgroup_id", "new_productmodel_id", "new_product"
                                      , "new_userprofile_id", "new_warranty", "new_enddate", "new_buydate", "new_phone")));
                        }
                    }
                }
                return WorkOrders.FirstOrDefault(i => i.Id == workorderid);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #region 关单使用
        private static List<Entity> WorkOrderAllFields = new List<Entity>();

        private static readonly object WorkOrderAllFields_Locker = new object();
        /// <summary>
        /// 查询工单
        /// </summary>
        /// <param name="new_model3id"></param>
        /// <param name="OrganizationService"></param>
        /// <returns></returns>
        public static Entity GetEntityWorkOrderAllFileds(Guid workorderId, IOrganizationService OrganizationService)
        {
            try
            {
                if (WorkOrderAllFields.Count == 0 || !WorkOrderAllFields.Any(i => i.Id == workorderId))
                {
                    lock (WorkOrderAllFields_Locker)
                    {
                        if (WorkOrderAllFields.Count == 0 || !WorkOrderAllFields.Any(i => i.Id == workorderId))
                        {
                            //查询
                            WorkOrderAllFields.Add(OrganizationService.RetrieveWithBypassPlugin("new_srv_workorder", workorderId, new ColumnSet(true)));
                        }
                    }
                }
                return WorkOrderAllFields.FirstOrDefault(i => i.Id == workorderId);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"errMsg:{ex.Message}, stackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 工单关单时，更新[WorkOrderAllFields]中对应记录字段，最后统一更新，减少[Update]次数
        /// </summary>
        /// <param name="workorderId">工单id</param>
        /// <param name="updateParam">（字段名，值） </param>
        public static void SetEntityWorkOrderAllFileds(Guid workorderId, Dictionary<string, object> updateParam)
        {
            foreach (var item in updateParam)
            {
                WorkOrderAllFields.FirstOrDefault(i => i.Id == workorderId)[item.Key] = item.Value;
            }
        }
        /// <summary>
        /// 关单最终更新时获取所有关单中更新的字段，执行[Update]操作
        /// </summary>
        /// <param name="workorderId">工单id</param>
        /// <returns></returns>
        public static Entity GetUpdateWorkorder(Guid workorderId)
        {
            /** 字段更新位置：
             * CloseWorkOrder：大仓发货产品档案、是否可受理、DOA成品库
             * FinallyUpdateOrderStatus：赋值工单费用、修改状态、关单人
             * UpdateOrderSettlement：更新服务单结算状态
             * UpdateWorkOrderOrderChange：退款更新换货订单号、实退金额
             * UpdateWorkOrderOrderCode：换新订单号
             */
            Entity originOrder = WorkOrderAllFields.FirstOrDefault(i => i.Id == workorderId);
            Entity uptOrder = new Entity("new_srv_workorder", workorderId);
            //EntityReference
            uptOrder["new_stocksiteid"] = originOrder.GetAttributeValue<EntityReference>("new_stocksiteid"); //DOA仓库
            uptOrder["new_houseuserprofile_id"] = originOrder.GetAttributeValue<EntityReference>("new_houseuserprofile_id"); //大仓发货产品档案
            uptOrder["new_transactioncurrency_service_id"] = originOrder.GetAttributeValue<EntityReference>("new_transactioncurrency_service_id"); //结算币种
            uptOrder["new_closeorder_user_id"] = originOrder.GetAttributeValue<EntityReference>("new_closeorder_user_id"); //关单人

            //bool
            uptOrder["new_accept"] = originOrder.GetAttributeValue<bool>("new_accept"); //是否可受理

            //OptionsetValue
            uptOrder["new_dealstatus"] = originOrder.GetAttributeValue<OptionSetValue>("new_dealstatus"); //工单状态
            uptOrder["new_issettlement"] = originOrder.GetAttributeValue<OptionSetValue>("new_issettlement"); //可结算状态

            //DateTime
            uptOrder["new_endtime"] = originOrder.GetAttributeValue<DateTime>("new_endtime"); //关单时间
            //uptOrder["new_settlementtime"] = originOrder.GetAttributeValue<DateTime>("new_settlementtime"); //可结算时间
            uptOrder["new_closingtime"] = originOrder.GetAttributeValue<DateTime>("new_closingtime"); //关单时间

            //string
            uptOrder["new_settlereason"] = originOrder.GetAttributeValue<string>("new_settlereason"); // 不可结算原因
            uptOrder["new_change_order"] = originOrder.GetAttributeValue<string>("new_change_order"); // 换新订单号
            uptOrder["new_return_order"] = originOrder.GetAttributeValue<string>("new_return_order"); // 退款订单号
            uptOrder["new_materialdetails"] = originOrder.GetAttributeValue<string>("new_materialdetails"); // 物料编码
            uptOrder["new_billingtype"] = originOrder.GetAttributeValue<string>("new_billingtype"); //开票类型

            //decimal
            uptOrder["new_actualrefund_amount"] = originOrder.GetAttributeValue<decimal>("new_actualrefund_amount"); // 退款金额
            uptOrder["new_boxfee"] = originOrder.GetAttributeValue<decimal>("new_boxfee"); // 箱子费
            uptOrder["new_repairsubsidy"] = originOrder.GetAttributeValue<decimal>("new_repairsubsidy"); // 寄修补贴
            uptOrder["new_distancesubsidy"] = originOrder.GetAttributeValue<decimal>("new_distancesubsidy"); // 路程补贴
            uptOrder["new_logisticsfee"] = originOrder.GetAttributeValue<decimal>("new_logisticsfee"); // 上门物流费
            uptOrder["new_returnvisitfee"] = originOrder.GetAttributeValue<decimal>("new_returnvisitfee"); // 电话回访费
            uptOrder["new_reverselogisticsfee"] = originOrder.GetAttributeValue<decimal>("new_reverselogisticsfee"); // 逆向物流费
            uptOrder["new_repairfee"] = originOrder.GetAttributeValue<decimal>("new_repairfee"); // 维修劳务费
            uptOrder["new_partservicecost"] = originOrder.GetAttributeValue<decimal>("new_partservicecost"); // 备件服务费
            uptOrder["new_partstotalfee"] = originOrder.GetAttributeValue<decimal>("new_partstotalfee"); // 备件合计费用
            uptOrder["new_recordingfee"] = originOrder.GetAttributeValue<decimal>("new_recordingfee"); // 录单费
            uptOrder["new_remoterepairfee"] = originOrder.GetAttributeValue<decimal>("new_remoterepairfee"); // 远程维修费

            return uptOrder;
        }

        public static void RemoveWorkorder(Guid workorderId)
        {
            if (WorkOrderAllFields.Count != 0 && WorkOrderAllFields.Any(i => i.Id == workorderId))
            {
                lock (WorkOrderAllFields_Locker)
                {
                    WorkOrderAllFields.Remove(WorkOrderAllFields.FirstOrDefault(i => i.Id == workorderId));
                }
            }
        }
        #endregion 关单使用

        #endregion 查询工单

        #region 查询配件更换明细

        private static Dictionary<Guid, EntityCollection> PartlineList = new Dictionary<Guid, EntityCollection>();

        private static readonly object PartlineList_Locker = new object();
        //直接使用内存缓存替换掉静态变量
        private static readonly MemoryCache PartlineCache = MemoryCache.Default;
        /// <summary>
        /// 查询工单对应配件更换明细
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="OrganizationService"></param>
        /// <param name="isFilterMiPart"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public static EntityCollection GetPartlineCollection(Guid workOrderId, IOrganizationService OrganizationService)
        {
            try 
            {
                var cacheKey = $"WorkOrder_Partlines_{workOrderId}";
                // 尝试从缓存获取
                if (PartlineCache.Get(cacheKey) is EntityCollection cachedData)
                {
                    return cachedData;
                }
                // 获取新数据并添加到缓存 
                QueryExpression qe_part = new QueryExpression("new_srv_partline");
                qe_part.ColumnSet = new ColumnSet("new_srv_partlineid", "new_product_id", "new_productnew_id", "new_sn", "new_oldsn", "new_imei", "new_oldimei", "new_isuser", "new_inrepairs", "new_stocksiteid", "new_oldgoodsfiles_id", "new_goodsfiles_id", "new_qty", "new_islackmaterial");
                qe_part.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe_part.Criteria.AddCondition("new_ismipart", ConditionOperator.NotEqual, false);
                LinkEntity link_order = new LinkEntity("new_srv_partline", "new_srv_workorder", "new_srv_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                link_order.EntityAlias = "order";
                link_order.Columns.AddColumns("new_station_id", "new_goodsfiles_id");
                link_order.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                link_order.LinkCriteria.AddCondition("new_srv_workorderid", ConditionOperator.Equal, workOrderId);
                qe_part.LinkEntities.Add(link_order);
                var ec_part = OrganizationService.RetrieveMultipleWithBypassPlugin(qe_part);
                PartlineCache.AddOrGetExisting(cacheKey, ec_part, CachePolicy);
                return ec_part;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }            
        }
        // 缓存策略配置
        private static readonly CacheItemPolicy CachePolicy = new CacheItemPolicy
        {
            AbsoluteExpiration = DateTimeOffset.Now.AddSeconds(60) 
        };
        #endregion 查询配件更换明细

        #region 查询工单处理方法

        private static Dictionary<Guid, EntityCollection> ApproachList = new Dictionary<Guid, EntityCollection>();

        private static readonly object ApproachList_Locker = new object();

        public static EntityCollection GetApproachCollection(Guid workOrderId, IOrganizationService OrganizationService)
        {
            try
            {
                if (ApproachList.Count == 0 || !ApproachList.Any(i => i.Key == workOrderId))
                {
                    lock (ApproachList_Locker)
                    {
                        if (ApproachList.Count == 0 || !ApproachList.Any(i => i.Key == workOrderId))
                        {
                            //查询
                            QueryExpression qe_approach = new QueryExpression("new_srv_workorder_approach");
                            qe_approach.ColumnSet = new ColumnSet("new_approach_id", "new_repairlevel", "new_srv_workorder_id");
                            qe_approach.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            qe_approach.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workOrderId);
                            var ec_part = OrganizationService.RetrieveMultiple(qe_approach);
                            ApproachList.Add(workOrderId, ec_part);
                        }
                    }
                }
                return ApproachList.FirstOrDefault(i => i.Key == workOrderId).Value;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #endregion 查询处理方法

        #region 查询一级品类
        // private static EntityCollection EntityCategory1 = new EntityCollection();
        /// <summary>
        /// 获取一级品类
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="cache"></param>
        /// <param name="new_category1id"></param>
        /// <returns></returns>
        public static string GetEntityCategory1Code(IOrganizationService OrganizationService, CrmCache cache, string new_category1id)
        {
            try
            {
                string code = cache.GetValue(new_category1id, moduleName + "_Category1");
                if (!string.IsNullOrWhiteSpace(code))
                    return code;

                var ret = OrganizationService.RetrieveWithBypassPlugin("new_category1", new Guid(new_category1id), new ColumnSet("new_code"));
                code = ret != null && ret.Contains("new_code") ? ret["new_code"].ToString() : "";
                cache.SetValue(new_category1id, code, moduleName + "_Category1", (int)TimeSpan.FromHours(8).TotalSeconds);
                return code;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询一级品类

        #region 查询三级品类Code
        //  private static EntityCollection EntityCategory3 = new EntityCollection();
        public static string GetEntityCategory3Code(IOrganizationService OrganizationService, CrmCache cache, Guid new_category3id)
        {
            try
            {
                string code = cache.GetValue(new_category3id.ToString(), moduleName + "_Category3");
                if (!string.IsNullOrWhiteSpace(code))
                    return code;

                var ret = OrganizationService.RetrieveWithBypassPlugin("new_category3", new_category3id, new ColumnSet("new_code"));
                code = ret != null && ret.Contains("new_code") ? ret["new_code"].ToString() : "";
                cache.SetValue(new_category3id.ToString(), code, moduleName + "_Category3", (int)TimeSpan.FromHours(8).TotalSeconds);
                return code;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询三级品类

        #region 查询网点服务人
        //plugin:new_srv_productline_create_post使用

        /// <summary>
        /// 网点服务人集合
        /// </summary>
        private static List<Entity> StationOwners = new List<Entity>();

        private static readonly object StationOwner_Locker = new object();
        /// <summary>
        /// 查询网点服务人
        /// </summary>
        /// <param name="new_model3id"></param>
        /// <param name="OrganizationService"></param>
        /// <returns></returns>
        public static Entity GetEntityStationOwner(IOrganizationService OrganizationService, Guid new_station_id)
        {
            try
            {
                if (StationOwners.Count == 0 || !StationOwners.Any(i => i.Id == new_station_id))
                {
                    lock (StationOwner_Locker)
                    {
                        if (StationOwners.Count == 0 || !StationOwners.Any(i => i.Id == new_station_id))
                        {
                            //查询
                            StationOwners.Add(OrganizationService.Retrieve("new_srv_station", new_station_id, new ColumnSet("ownerid")));
                        }
                    }
                }
                return StationOwners.FirstOrDefault(i => i.Id == new_station_id);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询网点服务人

        #region 查询物料
        //private static EntityCollection EntityProduct = new EntityCollection();
        //public static EntityCollection GetEntityProduct(IOrganizationService OrganizationService, List<string> new_goods_id)
        //{
        //    try
        //    {

        //        var query = new QueryExpression("product");
        //        query.ColumnSet = new ColumnSet("productid", "new_materialcategory2_id", "productnumber");
        //        query.Criteria.AddCondition("new_goods_id", ConditionOperator.In, new_goods_id.ToArray());
        //        query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
        //        //是否是商品为是
        //        query.Criteria.AddCondition("new_isgoods", ConditionOperator.Equal, true);

        //        queryList = OrganizationService.RetrieveMultiple(query);
        //        return EntityProduct;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new InvalidPluginExecutionException(ex.Message);
        //    }
        //}
        #endregion 查询物料

        #region 查询分期订单配置表
        private static EntityCollection Installmentorder = new EntityCollection();
        public static EntityCollection GetEntityInstallmentorder(IOrganizationService OrganizationService)
        {
            try
            {
                if (Installmentorder == null || Installmentorder.Entities == null || Installmentorder.Entities.Count <= 0)
                {
                    QueryExpression installmentorderQE = new QueryExpression("new_installmentorder");
                    installmentorderQE.ColumnSet = new ColumnSet(new string[] { "new_installmentorderid", "new_payid" });
                    installmentorderQE.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var installmentorderEC = OrganizationService.RetrieveMultiple(installmentorderQE);
                    Installmentorder = OrganizationService.RetrieveMultiple(installmentorderQE);
                }
                return Installmentorder;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询分期订单配置表

        #region 查询三包权益政策

        private static List<RepairrightsruleModel> Repairrightsrule = new List<RepairrightsruleModel>();
        private static readonly object Repairrightsrule_Locker = new object();
        /// <summary>
        /// 查询三包权益政策
        /// </summary>
        /// <param name="OrganizationService"></param>
        /// <param name="country">受理国家id</param>
        /// <param name="salechannel">销售渠道</param>
        /// <param name="new_packetsstarttime">三包开始时间</param>
        /// <returns></returns>
        public static RepairrightsruleModel GetSanBaoEquityPolicy(IOrganizationService OrganizationService, string country, int salechannel)
        {
            try
            {
                Repairrightsrule = new List<RepairrightsruleModel>();
                if (Repairrightsrule == null || Repairrightsrule.Count == 0 || !Repairrightsrule.Any(i => i.countryid == country && i.salechannel == salechannel))
                {
                    lock (Repairrightsrule_Locker)
                    {
                        if (Repairrightsrule == null || Repairrightsrule.Count == 0 || !Repairrightsrule.Any(i => i.countryid == country && i.salechannel == salechannel))
                        {
                            //查询
                            RepairrightsruleModel repairrightsruleEC = new RepairrightsruleModel();
                            //三包政策
                            var que = new QueryExpression("new_repairrightsrule");
                            que.ColumnSet = new ColumnSet("new_category1_id", "new_model1_id", "new_repairrightsruleid", "new_category3_id", "new_model3_id", "new_category2_id", "new_model2_id", "new_newrepairrights_id", "new_toggledate", "new_region_id", "new_brand_id", "new_goodsfiles_id", "new_country_id", "new_approvestatus", "new_repairrights_id", "new_name", "new_equitybydate", "new_province_id");
                            que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            que.Criteria.AddCondition("new_approvestatus", ConditionOperator.Equal, 3);//已审核
                            if (!string.IsNullOrEmpty(country))
                                que.Criteria.AddCondition("new_country_id", ConditionOperator.Equal, country);
                            //三包权益
                            LinkEntity repairrightsentity = new LinkEntity("new_repairrightsrule", "new_repairrights", "new_repairrights_id", "new_repairrightsid", JoinOperator.Inner);
                            repairrightsentity.EntityAlias = "repairrights";
                            //repairrightsentity.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            repairrightsentity.LinkCriteria.AddCondition("new_salechannel", ConditionOperator.ContainValues, new string[] { salechannel.ToString() });
                            repairrightsentity.Columns.AddColumns(new string[] { "new_serviceway", "new_name", "new_repairrightsid", "new_salechannel" });

                            que.LinkEntities.Add(repairrightsentity);

                            var queList = OrganizationService.RetrieveMultipleWithBypassPlugin(que);

                            if (queList == null || queList.Entities == null || queList.Entities.Count <= 0)
                                return repairrightsruleEC;
                            List<Entity> entity = new List<Entity>();
                            repairrightsruleEC.countryid = country;
                            repairrightsruleEC.salechannel = salechannel;
                            foreach (var item in queList.Entities)
                                entity.Add(item);
                            repairrightsruleEC.repairrightsruleEntity = entity;
                            Repairrightsrule.Add(repairrightsruleEC);
                        }
                    }
                }
                return Repairrightsrule.Where(i => i.countryid == country && i.salechannel == salechannel).FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        public class RepairrightsruleModel
        {
            /// <summary>
            /// 国家
            /// </summary>
            public string countryid { get; set; }
            /// <summary>
            /// 销售渠道
            /// </summary>
            public int salechannel { get; set; }
            /// <summary>
            /// 数据
            /// </summary>
            public List<Entity> repairrightsruleEntity { get; set; }
        }

        #endregion 查询三包权益政策


        private const string moduleName = "WorkOrder";
        #region 查询国家
        /// <summary>
        /// 查询缓存国家
        /// </summary>
        /// <param name="Cache"></param>
        /// <param name="OrganizationService"></param>
        /// <param name="new_countryid"></param>
        /// <returns></returns>
        public static Entity GetCountryEntity(CrmCache Cache, IOrganizationService OrganizationService, Guid new_countryid)
        {
            try
            {
                Entity entity = null;
                var json = Cache.GetValue(new_countryid.ToString(), moduleName + "_Country");
                if (!string.IsNullOrWhiteSpace(json))
                    entity = JsonConvertExt.DeSerialize<Entity>(json);
                if (entity == null)
                    entity = OrganizationService.Retrieve("new_country", new_countryid, new ColumnSet(new string[] { "new_name", "new_code", "new_region_id", "new_timezone", "new_isparallelgoods", "new_englishname", "new_code1", "crcce_servicemanager", "new_languagecode", "new_id", "new_notverifyresult", "new_language_id", "new_ifsplit" }));
                return entity;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 通过国家new_id 缓存国家数据
        /// </summary>
        /// <param name="Cache"></param>
        /// <param name="OrganizationService"></param>
        /// <param name="new_id"></param>
        /// <returns></returns>
        public static Entity GetCountryEntityByNewId(CrmCache Cache, IOrganizationService OrganizationService, string new_id)
        {
            Entity country = new Entity();
            if (string.IsNullOrWhiteSpace(new_id))
                return country;
            try
            {
                var value = Cache.GetValue(new_id, moduleName + "_CountryByNewId");
                if (!string.IsNullOrWhiteSpace(value))
                {
                    country = JsonConvertExt.DeSerialize<Entity>(value);
                }
                else
                {
                    QueryExpression query = new QueryExpression("new_country");
                    query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    query.Criteria.AddCondition("new_id", ConditionOperator.Equal, new_id);
                    query.ColumnSet.AddColumns("new_name", "new_code", "new_region_id", "new_timezone", "new_isparallelgoods", "new_englishname", "new_code1", "crcce_servicemanager", "new_languagecode", "new_id", "new_notverifyresult", "new_language_id", "new_ifsplit");
                    EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                    if (ec == null || ec.Entities.Count == 0)
                        return country;

                    country = ec.Entities[0];
                    Cache.SetValue(new_id, JsonConvertExt.Serialize(country), moduleName + "_CountryByNewId", (int)TimeSpan.FromHours(8).TotalSeconds);
                    return country;

                }
                return country;
            }
            catch (Exception ex)
            {

                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询国家


        #region 查询商品档案
        /// <summary>
        /// 查询缓存商品档案
        /// </summary>
        /// <param name="Cache"></param>
        /// <param name="OrganizationService"></param>
        /// <param name="new_commoditycode"></param>
        /// <returns></returns>
        public static Entity GetGoodsFilesEntity(CrmCache Cache, IOrganizationService OrganizationService, string new_commoditycode, int new_language_code)
        {
            try
            {
                Entity entity = null;
                var json = Cache.GetValue(new_commoditycode, moduleName + "_GoodsFiles");
                if (!string.IsNullOrWhiteSpace(json))
                    entity = JsonConvertExt.DeSerialize<Entity>(json);
                if (entity == null)
                {
                    QueryExpression qe = new QueryExpression("new_goodsfiles");
                    qe.ColumnSet = new ColumnSet("new_projectcode", "new_commoditycode", "new_sku", "new_brand_id", "new_new_category1_id", "new_category2_id", "new_category3_id", "new_model3_id", "new_model2_id", "new_model1_id", "new_isserialnum", "new_commodityname", "new_acceptancemode", "new_replacefee", "new_airconditioningtype");
                    qe.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, new_commoditycode);
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = OrganizationService.RetrieveMultiple(qe);
                    if (ec == null || ec.Entities == null || ec.Entities.Count <= 0)
                        throw new InvalidPluginExecutionException(string.Format("系统内缺少编码为：{0}的商品档案，请联系总部进行补充！", new_commoditycode));
                    entity = ec[0];
                }
                if (entity != null)
                {
                    MiPublicMethodCommand c = new MiPublicMethodCommand();
                    string new_projectcode = c.GetMultLangCachRedis(Cache, "new_goodsfiles", "new_projectcode", entity.Id.ToString(), new_language_code);
                    if (!string.IsNullOrWhiteSpace(new_projectcode))
                        entity["new_projectcode"] = new_projectcode;
                }
                return entity;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion 查询商品档案


        #region 查询国家英语翻译 add by Hyacinthhuang 2022-10-11
        /// <summary>
        /// 获取国家/地区英文名
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="value">中文名</param>
        /// <param name="log"></param>
        /// <returns>国家英文名</returns>
        public static string GetCountryEnglishName(CrmCache Cache, IOrganizationService organizationService, string value, IPluginLogger log)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            if (string.IsNullOrWhiteSpace(value))
                return null;
            try
            {
                value = value.Replace(" ", "");
                string new_englishname = string.Empty;
                string countrynameEnList = Cache.GetValue("List", "WorkOrder_CountryNameEn");
                if (!string.IsNullOrWhiteSpace(countrynameEnList))
                {
                    Dictionary<string, string> countrynameEn = JsonConvert.DeserializeObject<Dictionary<string, string>>(countrynameEnList);
                    new_englishname = countrynameEn.ContainsKey(value) ? countrynameEn[value] : "";
                    if (!string.IsNullOrWhiteSpace(new_englishname))
                        return new_englishname;
                }

                QueryExpression query = new QueryExpression("new_country");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_namecn", ConditionOperator.Equal, value);
                query.ColumnSet.AddColumns("new_englishname");
                EntityCollection collection = organizationService.RetrieveMultipleWithBypassPlugin(query);
                if (collection == null || collection.Entities.Count == 0)
                    return null;
                new_englishname = collection[0].GetAttributeValue<string>("new_englishname");
                return new_englishname;
            }
            catch (Exception ex)
            {
                log.LogException(ex);
                throw new InvalidPluginExecutionException("【GetCountryEnglishName】 error：" + ex.Message);
            }
            finally
            {
                sw.Stop();
                log.InfoMsg("查询国家/地区英文名耗时；" + sw.ElapsedMilliseconds);
            }
        }
        #endregion
        /// <summary>
        /// 通过Attribute获取Entity
        /// </summary>
        /// <param name="log"></param>
        /// <param name="OrganizationService"></param>
        /// <param name="entityName"></param>
        /// <param name="filterAttribute"></param>
        /// <param name="filterAttributeValue"></param>
        /// <param name="selectAttributes"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public static Entity GetEntityByAttribute(IPluginLogger log,IOrganizationService OrganizationService,string entityName,string filterAttribute, string filterAttributeValue,string[] selectAttributes)
        {
            try
            {
                QueryExpression query = new QueryExpression(entityName);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition(filterAttribute, ConditionOperator.Equal, filterAttributeValue);
                query.ColumnSet.AddColumns(selectAttributes);
                EntityCollection ec = OrganizationService.RetrieveMultipleWithBypassPlugin(query);
                if (ec?.Entities?.Count > 0)
                    return ec.Entities.FirstOrDefault();
                return null;
            }catch(Exception ex)
            {
                log.InfoMsg($"通过{filterAttribute}:[{filterAttributeValue}]查询实体{entityName}出错：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
    }
}
