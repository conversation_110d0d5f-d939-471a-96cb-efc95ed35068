﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls.Expressions;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Api.Model.Oc;
using RekTec.Crm.FileStorage.Model;
using RekTec.Crm.HiddenApi;
using RekTec.Service1.Service.Command;
using RekTec.Service1.Service.Model;
using static RekTec.Service1.Service.Bll.new_srv_partlinebll;
using static RekTec.Service1.Service.Model.CrossServiceProvideQueryModel;

namespace RekTec.Service1.Service
{
    public class WorkOrderController : HiddenApiController
    {
        /// <summary>
        /// 服务取消
        /// </summary>
        /// <param name="workorderId">服务单id</param>
        /// <param name="cancelReasonId">取消原因</param>
        /// <returns></returns>
        public virtual bool CancelWorkorder(Guid workorderId, int cancelReasonId, string cancelReaonDesc)
        {
            //var id = PartnerUserId;
            return this.Command<WorkOrderCommand>().CancelWorkorder(workorderId, cancelReasonId, cancelReaonDesc);
        }
        /// <summary>
        /// 服务取消-客服
        /// </summary>
        /// <param name="workorderId">服务单id</param>
        /// <param name="cancelReasonId">取消原因</param>
        /// <returns></returns>
        public bool customerServiceCancelWorkOrder(Guid workorderId, int cancelReasonId, string cancelReaonDesc)
        {
            return this.Command<WorkOrderCommand>().customerServiceCancelWorkOrder(workorderId, cancelReasonId, cancelReaonDesc);
        }
        /// <summary>
        /// 服务取消审核
        /// </summary>
        /// <param name="workorderId">服务单id</param>
        /// <param name="cancelReasonId">取消原因</param>
        /// <returns></returns>
        public virtual void ConfirmExamine(Guid workorderId, int cancelReasonId)
        {
            //var id = PartnerUserId;
            this.Command<WorkOrderCommand>().ConfirmExamine(workorderId, cancelReasonId);
        }
        /// <summary>
        /// CRM服务单派工
        /// [BindEntity("new_srv_workorder")]
        /// [MapCrmAction("srv_workorder_Assign")]
        /// <crmaction>srv_workorder_Assign</crmaction>
        /// <flowclass>RekTec.ServiceOne.Service.Activities.new_srv_workorder_Assign </flowclass>
        /// </summary>
        /// <param name="WorkerBody"></param>
        /// <param name="workorderid"></param>
        public virtual void Assign(WorkerChangeBody WorkerBody, Guid workorderid)
        {
            this.Command<DispatchToStationOrUserCommand>().Assign(WorkerBody, workorderid);
        }

        /// <summary>
        /// Portal地图派工
        /// </summary>
        /// <param name="workerOrderId">服务单Id</param>
        /// <param name="workerid">服务人员Id</param>
        /// <param name="time">预约时间</param>
        //public virtual void PortalAssign(Guid? workerOrderId, Guid? workerid, DateTime time, int searchType, int timeStep)
        //{
        //    this.Command<DispatchToStationOrUserCommand>().PortalAssign(workerOrderId, workerid, time, searchType, timeStep);
        //}
        /// <summary>
        /// 服务单派工完成-发送派工消息
        /// [MapCrmAction("srv_workorder_Assign_SendMessage")]
        /// [BindEntity(new_srv_workorder.EntityLogicalName)]
        /// <crmaction>srv_workorder_Assign_SendMessage</crmaction>
        /// <flowclass>RekTec.ServiceOne.Service.Activities.new_srv_workorder_Assign_SendMessage</flowclass>
        /// </summary>
        /// <param name="workorderid"></param>
        /// <returns></returns>
        //public virtual string SendMessage(Guid workorderid)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().SendMessage(workorderid, null, null);
        //}

        /// <summary>
        /// 自动派单
        /// [MapCrmAction("srv_workorder_AutoDispatchOrder")]
        /// [BindEntity("new_srv_workorder")]
        /// <crmaction>srv_workorder_AutoDispatchOrder</crmaction>
        /// <flowclass>RekTec.ServiceOne.Service.Activities.new_srv_workorder_AutoDispatchOrder</flowclass>
        /// </summary>
        public virtual ResultData DispatchOrderAction(Guid WorkOrderId, bool isXMS = false, string inputPara = "")
        {

            return this.Command<DispatchToStationOrUserCommand>().DispatchOrderAction(WorkOrderId, isXMS, inputPara);
        }

        /// <summary>
        /// 根据派单规则获取有效的服务站信息
        /// </summary>
        /// <param name="WorkOrderId">服务单id</param>
        /// <param name="Query">查询服务站名称或服务站负责人</param>
        /// <param name="PageIndex">页面索引</param>
        /// <param name="PageSize">页面大小</param>
        /// <returns></returns>
        public virtual StationInfoByRules GetStationsByRules(Guid WorkOrderId, string StationName, string StationOwner, string wingwName, int PageIndex, int PageSize,int type,bool isXMS = false,string inputPara = "")
        {
            return this.Command<DispatchToStationOrUserCommand>().GetStationsByRules(WorkOrderId, StationName, StationOwner, wingwName, PageIndex, PageSize, type, isXMS, inputPara);
        }

        /// <summary>
        /// 服务单-派单（根据派单规则筛选出符合规则的服务站）
        /// </summary>
        /// <param name="WorkOrderId"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageIndex"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public virtual StationInfoByRules DispatchOrderByRules(Guid WorkOrderId, int pageSize, int pageIndex,int type, bool isXMS = false, string inputPara = "")
        {
            return this.Command<DispatchToStationOrUserCommand>().GetStationsByRules(WorkOrderId, string.Empty, string.Empty, string.Empty, pageSize, pageIndex,type, isXMS,inputPara);
        }


        /// <summary>
        /// 服务单-自动派工_NEW
        /// [MapCrmAction("srv_workorder_AutoDispatchWorder")]
        /// <crmaction>srv_workorder_AutoDispatchWorder</crmaction>
        /// <flowclass>RekTec.ServiceOne.Service.Activities.new_srv_workorder_AutoDispatchWorker </flowclass>
        /// </summary>
        /// <param name="WorkOrderId"></param>
        /// <returns></returns>
        public virtual DisPatchResult DispatchWorkAction(Guid WorkOrderId)
        {
            return this.Command<DispatchToStationOrUserCommand>().DispatchWorkAction(WorkOrderId, true);
        }

        /// <summary>
        /// 派工:获取根据派单规则及空闲规则筛选出来的服务人员
        /// </summary>
        /// <param name="paramter"></param>
        /// <returns></returns>
        public virtual AssignDateByPage GetWorkersByStationsRules(AssignParamter paramter)
        {
            return this.Command<DispatchToStationOrUserCommand>().GetWorkersByStationsRules(paramter);
        }


        /// <summary>
        /// 服务单审批通过后方法
        /// </summary>
        /// <param name="target"></param>
        //public virtual void WorkOrderApprovedAgree(EntityReference target)
        //{
        //    Guid workorderid = target.Id;
        //    this.Command<WorkOrderCommand>().WorkOrderApprovedAgree(workorderid);
        //}

        /// <summary>
        /// 服务单提交前验证
        /// </summary>
        /// <param name="target"></param>
        //public virtual void VaildateAction(EntityReference target)
        //{
        //    Guid workorderid = target.Id;
        //    Log.DebugMsg(workorderid.ToString());
        //    this.Command<WorkOrderCommand>().VaildateInv(workorderid);
        //    Log.DebugMsg("4444444");
        //    this.Command<WorkOrderCommand>().VailBarCode(workorderid);
        //}


        /// <summary>
        /// 派单给指定服务服站
        /// </summary>
        /// <param name="workorderId">服务单id</param>
        /// <param name="stationId">服务站id</param>
        public virtual void DispatchStation(Guid workorderId, Guid stationId)
        {
            this.Command<DispatchToStationOrUserCommand>().DispatchStation(workorderId, stationId);
        }


        /// <summary>
        /// 给Job使用，删除分派权限记录表
        /// </summary>
        public virtual void DelPrincipalObjectAccess()
        {
            this.Command<WorkOrderCommand>().DelPrincipalObjectAccess();
        }

        /// <summary>
        /// 为客户/联系人创建对应的服务单
        /// </summary>
        /// <param name="model"></param>
        //public virtual string CreateWorkorderForCustomer(WorkorderWithCustomer model)
        //{
        //    return this.Command<WorkOrderCommand>().CreateWorkorderForCustomer(model);
        //}



        /// <summary>
        /// 获取服务活动
        /// </summary>
        /// <param name="openId">微信od</param>
        /// <param name="registerStatus">注册类型（1客户or2联系人）</param>
        /// <param name="servicemode">服务方式（1上门or2寄送修）</param>
        /// <param name="queryValue">查询条件</param>
        /// <param name="pageIndex">分页</param>
        /// <param name="pageSize">分页</param>
        /// <returns></returns>
        //public virtual List<ProgressQuery_Model> GetProgress(string openId, int registerStatus, int servicemode, string queryValue, int pageIndex, int pageSize)
        //{
        //    return this.Command<WorkOrderCommand>().GetProgress(openId, registerStatus, servicemode, queryValue, pageIndex, pageSize);
        //}

        /// <summary>
        /// 停用服务单的费用相关明细
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        //public virtual bool InActiveLine(Guid workOrderId)
        //{
        //    return this.Command<WorkOrderCommand>().InActiveLine(workOrderId);
        //}

        /// <summary>
        /// 根据设备序列号获取设备相关信息
        /// 2019-07-16 amyge
        /// </summary>
        /// <param name="machineCode">机器条码</param>
        /// <returns>设备信息的model</returns>
        //public virtual MachineInfoByMachineCode GetMachineInfoByMachineCode(string machineCode)
        //{
        //    return this.Command<WorkOrderCommand>().GetMachineInfoByMachineCode(machineCode);
        //}

        /// <summary>
        /// 删除服务单产品明细
        /// </summary>
        /// <param name="id">明细id</param>
        public virtual void DeleteProductLine(Guid id)
        {
            this.Command<WorkOrderCommand>().DeleteProductLine(id);
        }


        /// <summary>
        /// 拒单
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="refusedReason"></param>
        //public virtual string RefusedWorkorder(Guid workOrderId, Guid workerId, string workerName, string refusedReason)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().RefusedWorkorder(workOrderId, workerId, workerName, refusedReason);
        //}
        /// <summary>
        /// 拒单
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="refusedReason"></param>
        //public virtual string RefusedWorkorderApp(Guid workOrderId, string refusedReason)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().RefusedWorkorder(workOrderId, new Guid(AppHelper.GetCrmUserId(OrganizationService, PartnerUserId)), AppHelper.GetOuterUserName(OrganizationService, PartnerUserId), refusedReason);
        //}

        /// <summary>
        /// 判断当前服务单是否满足派工(改派)或拒单的条件
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="type"></param>
        /// <param name="workerId"></param>
        //public virtual void CheckWorkOrder(Guid workOrderId, string type, Guid workerId)
        //{
        //    this.Command<DispatchToStationOrUserCommand>().CheckWorkOrder(workOrderId, type, workerId);
        //}

        /// <summary>
        /// 复制服务单
        /// </summary>
        /// <param name="oldWorkOrderId"></param>
        /// <returns></returns>
        public virtual Guid CopyWorkOrder(Guid oldWorkOrderId)
        {
            return this.Command<WorkOrderCommand>().CopyWorkOrder(oldWorkOrderId);
        }

        /// <summary>
        /// 获取预约排期信息
        /// 2018-11-26 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单主键id</param>
        /// <returns>预约排期信息</returns>
        //public virtual ScheduleModel GetScheduleInfo(string workOrderId)
        //{
        //    return this.Command<WorkOrderCommand>().GetScheduleInfo(workOrderId);
        //}

        /// <summary>
        /// 保存预约排期信息
        /// 2018-11-26 bettychen
        /// </summary>
        /// <param name="model">预约排期的信息</param>
        /// <returns></returns>
        //public virtual void SaveScheduleInfo(ScheduleModel model)
        //{
        //    Log.DebugMsg("1111111");
        //    this.Command<WorkOrderCommand>().SaveScheduleInfo(model);
        //    Log.DebugMsg("2222222");
        //}

        //public virtual ServiceOnePicklistModel GetWorkOrderState(Guid WorkOrderId)
        //{
        //    return this.Command<WorkOrderCommand>().GetWorkOrderState(WorkOrderId);
        //}

        /// <summary>
        /// 特批申请单
        /// </summary>
        /// <param name="id"></param>
        public virtual void SpecialOrderSubmit(string Id, int type, string memo)
        {
            this.Command<SpecialApplyCommand>().SpecialOrderSubmit(Id, type, memo);
        }
        /// <summary>
        /// 创建或修改服务故障明细
        /// </summary>
        /// <param name="list"></param>
        public virtual void CreateOrUpdateTrouble(TroubleModelList list)
        {
            this.Command<WorkOrderCommand>().CreateOrUpdateTrouble(list);
        }
        /// <summary>
        /// 创建或者修改退货信息
        /// </summary>
        /// <param name="list"></param>
        public virtual void CreateOrUpdateReturnGoodsByGoodsId(ReturnGoodsModelList list)
        {
            this.Command<WorkOrderCommand>().CreateOrUpdateReturnGoodsByGoodsId(list);
        }
        /// <summary>
        /// 根据服务单id获取服务故障明细
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual List<TroubleModel> GetToubleList(string workOrderId)
        {
            return this.Command<WorkOrderCommand>().GetToubleList(workOrderId);
        }
        /// <summary>
        /// 根据三级故障id查询对应的一二级故障id
        /// </summary>
        /// <param name="errorgroupId"></param>
        /// <returns></returns>
        public virtual TroubleModel GetToubleByErrorgroupId(string errorgroupId)
        {
            return this.Command<WorkOrderCommand>().GetToubleByErrorgroupId(errorgroupId);
        }
        /// <summary>
        /// 通过服务故障明细的id删除服务故障明细
        /// </summary>
        /// <param name="troubleId"></param>
        public virtual bool DeleteTroubsleById(string troubleId, string workOrderId)
        {
            return this.Command<WorkOrderCommand>().DeleteTroubsleById(troubleId, workOrderId);
        }
        /// <summary>
        /// 获取配件信息
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        /// <param name="errorgroupId">三级故障id</param>
        /// <param name="materialsName">物料名称</param>
        /// <param name="materialsCode">物料编码</param>
        /// <param name="shipmentType">发货状态 1：本地发货；2：大仓大货</param>
        /// <returns></returns>
        public virtual List<ProductErroupModel> GetErrorgroupList(string workOrderId, string errorgroupId, string materialsName, string materialsCode, int shipmentType, int productProperty,string goodsId)
        {
            return this.Command<Bll.new_srv_partlinebll>().GetErrorgroupList(workOrderId, errorgroupId, materialsName, materialsCode, shipmentType, productProperty, goodsId);
        }
        /// <summary>
        /// 获取替代料数据
        /// </summary>
        /// <param name="workOrderId">工单id</param>
        /// <param name="productId">当前物料</param>
        /// <param name="productName">上级物料</param>
        /// <param name="materialsName">替代料名称</param>
        /// <param name="materialsCode">替代料编码</param>
        /// <param name="errorgroupId">故障id</param>
        /// <returns></returns>
        public virtual List<ProductErroupModel> GetReplaceProduct(string workOrderId, string productId, string productName, string materialsName, string materialsCode, string errorgroupId)
        {
            return this.Command<Bll.new_srv_partlinebll>().GetReplaceProduct(workOrderId, productId, productName, materialsName, materialsCode, errorgroupId);
        }
        /// <summary>
        /// 判断取消工单是否达到上限
        /// </summary>
        /// <param name="workorderId"></param>
        /// <returns></returns>
        public virtual bool JudgeCancelWorkOrderIsUpperLimit(Guid workorderId)
        {
            return this.Command<WorkOrderCommand>().JudgeCancelWorkOrderIsUpperLimit(workorderId);
        }
        /// <summary>
        /// 修改服务单工单状态
        /// </summary>
        /// <param name="workorderId"></param>
        public virtual void UpdateCancelStatus(Guid workorderId)
        {
            this.Command<WorkOrderCommand>().UpdateCancelStatus(workorderId);
        }

        /// Modifier：p-huyan9
        /// Modification Date：2023-05-22
        /// Modify Depiction：增加传入参数操作终端，operatortermial(1：PC，2：APP)
        /// <summary>
        /// 创建配件更换明细
        /// </summary>
        /// <param name="model"></param>
        /// <param name="operatortermial">操作终端</param>
        public virtual string CreateUpdatePartLine(List<ProductErroupModel> lists,int operatortermial=-1)
        {
            return this.Command<Bll.new_srv_partlinebll>().CreateUpdatePartLine(lists, operatortermial);
        }

        /// <summary>
        /// 查询可参与的服务活动
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual List<ServiceActionModel> GetWorkOrderServiceAction(Guid workOrderId)
        {
            return this.Command<WorkOrderCommand>().GetWorkOrderServiceAction(workOrderId);
        }
        /// <summary>
        /// 创建服务单活动明细
        /// </summary>
        /// <param name="model"></param>
        public virtual void CreateServiceAction(ServiceActionModel model)
        {
            this.Command<WorkOrderCommand>().CreateServiceAction(model);
        }
        /// Modifier：p-huyan9
        /// Modification Date：2023-05-22
        /// Modify Depiction：增加传入参数操作终端，operatortermial(1：PC，2：APP)
        /// <summary>
        /// 关闭案例
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns>额外展示的提示信息</returns>
        public virtual void CloseCase(Guid workOrderId, int operatortermial = -1)
        {
            this.Command<WorkorderCommand_close>().CloseWorkOrder(workOrderId, operatortermial);
        }

        /// <summary>
        /// 判断是否匹配到维修劳务费结算标准
        /// 创建人：baronkang
        /// 时间：2022-02-21
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual bool MatchRepairFee(Guid workOrderId)
        {
            return this.Command<WorkorderCommand_close>().MatchRepairFee(workOrderId);
        }

        /// <summary>
        /// 判断检测费
        /// </summary>
        /// <param name="serviceProviderId"></param>
        /// <param name="stationId"></param>
        /// <param name="category1"></param>
        /// <param name="category2"></param>
        /// <param name="category3"></param>
        /// <returns></returns>
        public virtual bool MatchCheckFee(Guid workOrderId)
        {
            return this.Command<WorkorderCommand_close>().MatchCheckFee(workOrderId);
        }

        /// <summary>
        /// 服务完成-换货
        /// </summary>
        /// <param name="id">实体id</param>
        /// <returns></returns>
        public virtual OcApiResult ExchangeGoods(string id)
        {
            return this.Command<WorkOrderCommand>().ExchangeGoods(id);
        }

        /// <summary>
        /// 生成可用安装权益（服务完成前调用）
        /// </summary>
        /// <param name="workorderId"></param>
        public virtual List<usableInstallRightsModel> GetUsableInstallRights(string workorderId)
        { 
           return this.Command<InstallRightsCommand>().GetUsableInstallRights(workorderId);

        }



        /// <summary>
        /// 服务完成-退货
        /// </summary>
        /// <param name="id">实体id</param>
        /// <returns></returns>
        public virtual OcApiResult SalesReturn(string id)
        {
            return this.Command<WorkOrderCommand>().SalesReturn(id);
        }

        /// <summary>
        /// 根据退货id删除退货信息
        /// </summary>
        /// <param name="returnid"></param>
        public virtual void DeleteChangeReturnLine(string returnid)
        {
            this.Command<WorkOrderCommand>().DeleteChangeReturnLine(returnid);
        }
        /// <summary>
        /// 根据服务单id查询工单状态
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual int GetWorkOrderDealstatus(string workOrderId)
        {
            return this.Command<WorkOrderCommand>().GetWorkOrderDealstatus(workOrderId);
        }
        /// <summary>
        /// 根据服务单id查询工单服务类型
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual PrecssBackDataInfo GetOrderServiceType(string workOrderId)
        {
            return this.Command<WorkOrderCommand>().GetOrderServiceType(workOrderId);
        }
        /// <summary>
        /// 配件确认申请数量判断
        /// add by esterwang 2021-10-14 10:54
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual ResultModel ConfirmApplyQuantityJudge(string workOrderId)
        {
            return this.Command<WorkOrderCommand>().ConfirmApplyQuantityJudge(workOrderId);
        }
        /// <summary>
        /// 判断工单的处理方法是否存在与可受理的处理方法中
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        public virtual bool IsExistenceApproach(string workOrderId)
        {
            return this.Command<WorkOrderCommand>().IsExistenceApproach(workOrderId);
        }
        /// <summary>
        /// 返料确认修改库存
        /// 创建人：esterwang 2021-11-12
        /// </summary>
        /// <param name="partIdList"></param>
        public virtual void UpdateWareHouseChackByReturnProduct(List<string> partIdList)
        {
            this.Command<WorkOrderCommand>().UpdateWareHouseChackByReturnProduct(partIdList);
        }

        #region xmobile接口

        /// <summary>
        /// 获取服务活动列表
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        //public virtual List<ActivityInfo> GetActivities(Guid workOrderId)
        //{
        //    return this.Command<WorkOrderCommand>().GetActivities(workOrderId);
        //}

        /// <summary>
        /// 获取当前服务单负责的服务人员信息
        /// 2018-11-28 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        //public virtual WorkerModel GetWorkOrderOwnerInfo(Guid workOrderId)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().GetWorkOrderOwnerInfo(workOrderId);
        //}

        /// <summary>
        /// 获取拍照的实现方法-标签
        /// 2018-11-26 bettychen
        /// </summary>
        /// <param name="stageId">阶段（1：出发  2：开始服务 3：结束服务）</param>
        /// <returns></returns>
        public virtual List<LabelsModel> GetPhotoLabels(int stageId)
        {
            return this.Command<WorkOrderCommand>().GetPhotoLabels(stageId);
        }

        /// <summary>
        /// 获取出发的信息
        /// 2018-11-27 bettychen
        //public virtual ServiceprocesseModel GetDepartureInfo(Guid workOrderId, int statecode = 3)
        //{
        //    return this.Command<WorkOrderCommand>().GetActivityInfo(workOrderId, statecode);
        //}

        /// <summary>
        /// 获取到达/开始服务的信息
        /// 2018-11-29 bettychen
        //public virtual ServiceprocesseModel GetArriveInfo(Guid workOrderId, int statecode = 4)
        //{
        //    return this.Command<WorkOrderCommand>().GetActivityInfo(workOrderId, statecode);
        //}

        /// <summary>
        /// 服务出发
        /// 2018-111-27 bettychen
        /// </summary>
        /// <param name="appmodel">服务出发的信息</param>
        //public virtual void SaveDepartureInfo(ServiceprocesseModel appmodel)
        //{
        //    this.Command<WorkOrderCommand>().SaveDepartureInfo(appmodel);
        //}

        /// <summary>
        /// 到位/开始服务
        /// 2018-11-28 bettychen
        /// </summary>
        /// <param name="appmodel">服务到位的信息</param>
        //public virtual void SaveArriveInfo(ServiceprocesseModel appmodel)
        //{
        //    this.Command<WorkOrderCommand>().SaveArriveInfo(appmodel);
        //}

        /// <summary>
        /// 获取完工页面信息
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        //public virtual CompleteInfoForMutilProModel GetCompletionInfoForMutilPro(string workOrderId, bool isFromProductLine)
        //{
        //    return this.Command<WorkOrderCommand>().GetCompletionInfoForMutilPro(workOrderId, isFromProductLine);
        //}

        /// <summary>
        /// 获取多产品信息
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="productLineId"></param>
        /// <returns></returns>
        //public virtual ProductLineInfo GetDetailProductLineInfo(string workOrderId, string productLineId)
        //{
        //    return this.Command<WorkOrderCommand>().GetDetailProductLineInfo(workOrderId, productLineId);
        //}

        /// <summary>
        /// 创建或更新产品信息
        /// </summary>
        /// <param name="model"></param>
        //public virtual string CreateOrUpdateProductInfo(ProductLineInfo model)
        //{
        //    return this.Command<WorkOrderCommand>().CreateOrUpdateProductInfo(model);
        //}

        /// <summary>
        /// 批量保存故障信息明细   
        /// 2018-11-30 bettychen
        /// </summary>
        /// <param name="model">保存故障信息明细</param>
        /// <returns>true:保存成功 false:保存失败</returns>
        //public virtual bool SaveFaultAndWorkHours(BatchErrorInfoModel model)
        //{
        //    return this.Command<WorkOrderCommand>().SaveFaultAndWorkHours(model);
        //}

        /// <summary>
        /// 获取维修工时标准的(标准工时、工时费用)
        /// 2018-12-04 bettychen
        /// </summary>
        /// <param name="WorkOrderId">服务单Id</param>
        /// <param name="WorkHoursId">服务项目id</param>
        /// <returns>标准工时、工时费用</returns>
        //public virtual Dictionary<string, decimal> GetWorkHoursFee(string WorkOrderId, string WorkHoursId)
        //{
        //    return this.Command<WorkOrderCommand>().GetWorkHoursFee(WorkOrderId, WorkHoursId);
        //}

        /// <summary>
        /// 保存维修内容
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //public virtual bool SaveWorkHours(BatchRepairInfoModel model)
        //{
        //    return this.Command<WorkOrderCommand>().SaveWorkHours(model);
        //}
        /// <summary>
        /// 保存服务单费用信息
        /// 2018-12-06 bettychen
        /// </summary>
        /// <param name="model">费用信息</param>
        /// <returns>true:更新成功  false:更新失败</returns>
        //public virtual bool SaveCostInfo(CostInfoModel model)
        //{
        //    return this.Command<WorkOrderCommand>().SaveCostInfo(model);
        //}

        /// <summary>
        /// 提交服务完工信息
        /// 2018-12-06 bettychen
        /// </summary>
        /// <param name="model">完工信息</param>
        /// <returns>true:提交成功  false：提交失败</returns>
        //public virtual bool SubmitCompletionInfo(CompleteInfoForMutilProModel model)
        //{
        //    return this.Command<WorkOrderCommand>().SubmitCompletionInfo(model);
        //}

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public virtual string SubmitAuto(string id, string entity = "new_srv_workorder")
        {
            return this.Command<WorkOrderCommand>().SubmitAuto(id, entity);
        }

        /// <summary>
        /// 服务单完工后展示的二维码
        /// 2018-12-04 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        /// <returns>二维码信息</returns>
        public virtual QRcodeModel GetEvaluateQRcode(Guid workOrderId)
        {
            return this.Command<WorkOrderCommand>().GetEvaluateQRcode(workOrderId);
        }

        /// <summary>
        /// 获取待派工和已派工的服务单信息的列表
        /// 2018-11-22 bettychen
        /// </summary>
        /// <param name="queryType">0：待派工 1：已派工</param>
        /// <param name="queryValue">查询内容</param>
        /// <param name="pageIndex">页面索引</param>
        /// <param name="pageSize">页面大小</param>
        /// <returns>待派工和已派工的服务单信息的列表</returns>
        //public virtual AssignAllWorkorderModel GetAssignWorkOrderList(int queryType, string queryValue, int pageIndex = 1, int pageSize = 10)
        //{
        //    Log.DebugMsg("aaaaaaaaaaaa");
        //    return this.Command<DispatchToStationOrUserCommand>().GetAssignWorkOrderList(queryType, queryValue, pageIndex, pageSize);
        //}

        /// <summary>
        ///  获取当前工单的主要信息、负责人、可派工的服务人员信息
        ///  2018-11-28 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="type"></param>
        /// <param name="queryValue"></param>
        /// <returns>工单和相关服务人员信息</returns>
        //public virtual WorkOrderInfo GetOrderAndWorkerInfo(Guid workOrderId, int pageIndex, int pageSize, int type = 1, string queryValue = "")
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().GetOrderAndWorkerInfo(workOrderId, pageIndex, pageSize, type, queryValue);
        //}

        /// <summary>
        /// 派工/改派
        /// 2018-11-28 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        /// <param name="workerId">服务人员id</param>
        /// <param name="type">1:自己，2：别人</param>
        /// <returns>派工结果</returns>
        //public virtual string AssignToOther(Guid workOrderId, Guid workerId)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().ChangeWorker(workOrderId, workerId, 2);
        //}

        /// <summary>
        /// 派工/改派
        /// 2018-11-28 bettychen
        /// </summary>
        /// <param name="workOrderId">服务单id</param>
        /// <param name="workerId">服务人员id</param>
        /// <param name="type">1:自己，2：别人</param>
        /// <returns>派工结果</returns>
        //public virtual string AssignToMe(Guid workOrderId)
        //{
        //    return this.Command<DispatchToStationOrUserCommand>().ChangeWorker(workOrderId, new Guid(AppHelper.GetCrmUserId(OrganizationService, PartnerUserId)), 1);
        //}

        /// <summary>
        /// 撤回
        /// </summary>
        /// <param name="id"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public virtual string Recall(string id, string entity = "new_srv_workorder")
        {
            return this.Command<WorkFlowCommand>().Recall(id, entity);
        }

        /// <summary>
        /// 获取服务进度列表
        /// </summary>
        /// <param name="filterForProgress"></param>
        /// <returns></returns>
        //public virtual List<WorkOrderItem> GetWorkOrderForProgress(FilterForProgress filterForProgress)
        //{
        //    return this.Command<WorkOrderCommand>().GetWorkOrderForProgress(filterForProgress);
        //}

        /// <summary>
        /// 获取所有未完工的服务单信息及数量
        /// 2018-11-27 bettychen
        /// </summary>
        /// <returns>所有未完工的服务单信息及数量</returns>
        //public virtual AllWorkOrderForMapModel GetAllWorkOrdersForMap()
        //{
        //    return this.Command<WorkOrderCommand>().GetAllWorkOrdersForMap();
        //}

        /// <summary>
        /// 创建完工照片附件
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //public virtual List<ImageModel> CreateImages(ProductLineImages model)
        //{
        //    return this.Command<WorkOrderCommand>().CreateImages(model);
        //}


        /// <summary>
        /// 删除照片
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //public virtual void DeleteImage(FileModel model, string entityId)
        //{
        //    this.Command<WorkOrderCommand>().DeleteImage(model, entityId);
        //}


        /// <summary>
        /// 获取完工状态信息
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        //public virtual CompleteStateModel GetCompleteState(Guid workOrderId)
        //{
        //    return this.Command<WorkOrderCommand>().GetCompleteState(workOrderId);
        //}

        /// <summary>
        /// 通过三级品类获取对应一级三级故障
        /// 2022-06-23 Baronkang
        /// </summary>
        /// <param name="category3Id"></param>
        /// <returns></returns>
        public ErrorListFetchInfo GetErrorByCategory(string workorderId)
        {
            return this.Command<WorkOrderCommand>().GetErrorByCategory(workorderId);
        }

        /// <summary>
        /// 根据故障信息id获取故障信息和维修内容信息
        /// </summary>
        /// <param name="TroubleId"></param>
        /// <returns></returns>
        //public virtual FaultAndWorkHoursModel GetFaultAndWorkHoursByTroubleId(Guid TroubleId)
        //{
        //    return this.Command<WorkOrderCommand>().GetFaultAndWorkHoursByTroubleId(TroubleId);
        //}


        /// <summary>
        /// 根据故障信息id删除故障信息和维修工时
        /// 2018-12-03 bettychen
        /// 原方法名:DeleteFaultInformationData
        /// </summary>
        /// <param name="TroubleId">故障信息id</param>
        /// <param name="WorkOrderId">服务单id</param>
        /// <returns>tue:删除成功  false:删除失败</returns>
        //public virtual bool DeleteTroubleById(string TroubleId, string WorkOrderId)
        //{
        //    var requestCollection = new OrganizationRequestCollection();
        //    var executeTran = new ExecuteTransactionRequest();
        //    requestCollection.AddRange(this.Command<WorkOrderCommand>().DeleteTroubleById(TroubleId, WorkOrderId));
        //    executeTran.Requests = requestCollection;
        //    this.OrganizationServiceAdmin.Execute(executeTran);
        //    return true;
        //}

        /// <summary>
        /// 删除维修内容（服务项目）
        /// </summary>
        /// <param name="RepairId"></param>
        /// <returns></returns>
        //public virtual bool DeleteRepairLineById(Guid RepairId)
        //{
        //    var requestCollection = new OrganizationRequestCollection();
        //    var executeTran = new ExecuteTransactionRequest();
        //    requestCollection.AddRange(this.Command<WorkOrderCommand>().DeleteRepairById(RepairId));
        //    executeTran.Requests = requestCollection;
        //    OrganizationServiceAdmin.Execute(executeTran);
        //    return true;
        //}

        //public virtual WorkHoursModel GetWorkHoursByRepairId(Guid RepairId)
        //{
        //    return this.Command<WorkOrderCommand>().GetWorkHoursByRepairId(RepairId);
        //}

        /// <summary>
        /// 获取产品信息
        /// </summary>
        /// <param name="WorkOrderId"></param>
        /// <param name="isNeedWorkOrderStatusCode"></param>
        /// <returns></returns>
        //public virtual ProductInfoModel GetProductInfoByWorkOrderId(Guid WorkOrderId, bool isNeedWorkOrderStatusCode = true)
        //{
        //    return this.Command<WorkOrderCommand>().GetProductInfoByWorkOrderId(WorkOrderId);
        //}

        /// <summary>
        /// 保存产品信息
        /// </summary>
        /// <param name="model">产品信息和故障信息</param>
        //public virtual bool SaveProductInfo(ProductInfoModel model)
        //{
        //    return this.Command<WorkOrderCommand>().SaveProductInfo(model);
        //}
        /// <summary>
        /// 根据物料判断品类是否是成品或者主板
        /// </summary>
        /// <param name="MaterialcategoryIsId">物料id</param>
        /// <returns></returns>
        public virtual bool JudgeMaterialcategoryIsBoardOrAProduct(string MaterialcategoryIsId)
        {
            return this.Command<WorkOrderCommand>().JudgeMaterialcategoryIsBoardOrAProduct(MaterialcategoryIsId);
        }
        #endregion xmobile接口


        #region 多维换机 add by Hyacinth 2021-10-26
        /// <summary>
        /// 多维换机检测
        /// </summary>
        /// <param name="orderId">服务单id</param>
        /// <returns>检测数量</returns>
        public virtual int MultiChangeCheck(string orderId)
        {
            return this.Command<WorkOrderCommand>().MultiChangeCheck(orderId);
        }
        #endregion

        #region 故障保存EWP校验
        /// <summary>
        /// 创建故障EWP规则校验
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public EwpblockruleCheckModel EwpblockruleCheck(TroubleModelList list)
        {
            return this.Command<WorkOrderCommand>().EwpblockruleCheck(list);
        }
        #endregion

        #region 配件申请确认校验 add by Hyacinth 2021-10-29
        /// <summary>
        /// 配件申请确认校验
        /// </summary>
        /// <param name="partlineList"></param>
        public virtual void ConfirmSublit(string orderId, string partLine)
        {
            this.Command<ApproachAndPartlineCommand>().ConfirmSublit(orderId, partLine);
        }
        #endregion

        public virtual decimal SearchProductPriceByStationId(string stationId, int warreny, string productId)
        {
            return this.Command<WorkOrderCommand>().SearchProductPriceByStationId(stationId, warreny, productId);
        }

        //public virtual void ServiceCancelInsurance(string id)
        //{
        //    this.Command<InsuranceConfirmCommand>().ServiceCancelInsurance(id);
        //}
        /// <summary>
        /// 批量修改DOA回退方式
        /// 创建人：hobartgu
        /// 创建时间:2021-12-09
        /// </summary>
        /// <param name="workorderId">服务单id</param>
        /// <param name="cancelReasonId">DOA回退方式</param>
        /// <returns></returns>
        public virtual void UpdateDoareturnway(string workorderId, int doareturnway)
        {
            //var id = PartnerUserId;
            this.Command<WorkOrderCommand>().UpdateDoareturnway(workorderId, doareturnway);
        }

        public virtual decimal GetStockCountByStationIdAndProductIdNew(string stationId, string productId, int type, int changeType, string goodesCode, string salechannel, int shipmentType = 1)
        {
            //var id = PartnerUserId;
            return this.Command<WorkOrderCommand>().GetStockCountByStationIdAndProductIdNew(stationId, productId, type, changeType, goodesCode, salechannel, shipmentType);
        }
        /// <summary>
        /// 判断物料是否存在与ewp拦截规制中
        /// 创建人:esterwang
        /// 创建时间:2021-11-30 16:38:22
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <param name="prductId">物料id</param>
        /// <returns></returns>
        public virtual string JudgeProductIsEwpProduct(string orderId, string prductId)
        {
            return this.Command<WorkOrderCommand>().JudgeProductIsEwpProduct(orderId, prductId);
        }
        //public virtual void CanCelOrderUpdateStock(string orderId, string prductId,int detail)
        //{
        //     this.Command<WorkOrderCommand>().CanCelOrderUpdateStock(orderId, prductId, detail);
        //}

        /// <summary>
        /// 工单发货方式显示 
        /// add by Hyacinthhuang 2021-12-22
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <returns></returns>
        public bool CheckOrderStationCountry(string orderId)
        {
            return this.Command<WorkOrderCommand>().CheckOrderStationCountry(orderId);
        }

        /// <summary>
        /// 更新串号信息
        /// add by Hyacinthhuang 2022-2-15
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <param name="sn">sn</param>
        /// <param name="imei">ime</param>
        public void UpdateSnImeiInfo(string orderId, string sn, string imei)
        {
            this.Command<Bll.new_srv_productlinebll>().UpdateSnImeiInfo(orderId, sn, imei);
        }

        /// <summary>
        /// 测试spm推数据使用
        /// </summary>
        /// <param name="type"></param>
        /// <param name="id"></param>
        /// <param name="queueType">3：buy-sell；4：Xiaomi-Consign</param>
        public void TestSPM(int type, string id, int queueType, string num = "")
        {
            this.Command<WorkOrderCommand>().ThrowOrderToSPM(type, id, queueType, num);
        }

        /// <summary>
        /// 查询大仓库存
        /// </summary>
        /// <param name="goodsId">商品编码</param>
        /// <param name="sale_channel">销售渠道</param>
        /// <param name="mihomeId"></param>
        /// <returns></returns>
        public int GetPssStockCount(string goodsId, string stationId, string orderForm)
        {
            return Command<WorkOrderCommand>().GetPssStockCount(goodsId, stationId, orderForm);
        }

        /// <summary>
        /// 更换件明细获取替代料
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public List<PartlineModel> GetPartLineList(string orderId)
        {
            return this.Command<Bll.new_srv_partlinebll>().GetPartLineList(orderId);
        }

        /// <summary>
        /// 更新工单结算状态
        /// </summary>
        /// <param name="orderId"></param>
        public void UpdateOrderSettlement(Guid orderId)
        {
            this.Command<WorkorderCommand_close>().UpdateSettlementFromCache(orderId);
        }

        /// <summary>
        /// 跨服务商查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="imei"></param>
        /// <param name="sn"></param>
        /// <param name="orderid"></param>
        /// <param name="workorderid"></param>
        /// <param name="miorderid"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        public ReturnResult GetOtherOrderList(string countryid, int pageIndex, int pageSize, string imei = "", string sn = "", string orderid = "", string workorderid = "", string miorderid = "", string phone = "")
        {
            return Command<CrossServiceProvideQueryCommand>().GetOtherOrderList(countryid, pageIndex, pageSize, imei, sn, orderid, workorderid, miorderid, phone);
        }

        /// <summary>
        /// 测试迈创
        /// </summary>
        /// <param name="workorderId">工单id</param>
        /// <param name="type">操作类型 </param>
        /// <param name="partStatus">更换件明细物料状态</param>
        /// <param name="partlineId"></param>
        public void OrderToQueue(string workorderId, int type, int partStatus = 0, string partlineId = null)
        {
            this.Command<WorkorderToMCCommand>().ThrowWorkorderToQueue(workorderId, type, partStatus, partlineId);
        }

        /// <summary>
        /// 调用根据工单所属服务商抛转工单信息
        /// </summary>
        /// <param name="workorderId"></param>
        /// <param name="type"></param>
        /// <param name="partStatus"></param>
        /// <param name="partlineId"></param>
        public virtual void ThrowWorkOrderToQueue(string workorderId, int type, int partStatus = 0, string partlineId = null)
        {
            this.Command<WorkorderToMCCommand>().ThrowWorkorderToQueue(workorderId, type, partStatus, partlineId);
        }

        /// <summary>
        /// 获取商品物料信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="productId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public List<GoodsProductModel> GetChangeGoodsProductList(string orderId, string productId, int type)
        {
            return this.Command<Bll.new_srv_partlinebll>().GetChangeGoodsProductList(orderId, productId, type);
        }

        /// <summary>
        /// 创建商品档案更换件明细
        /// </summary>
        /// <param name="goodsModel"></param>
        /// <param name="orderId"></param>
        public void CreateGoodsPartLine(GoodsProductModel goodsModel, string orderId)
        {
            this.Command<Bll.new_srv_partlinebll>().CreateGoodsPartLine(goodsModel, orderId);
        }

        /// <summary>
        /// 特批申请单明细New machine name字段国家控制
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        public virtual bool ProductCustomerViewFiter(string entityId)
        {
            return this.Command<SpecialApplyCommand>().ProductCustomerViewFiter(entityId);
        }


        /// <summary>
        /// B2X异型号换货配置审批操作
        /// </summary>
        /// <param name="id">单据id</param>
        /// <param name="type">签核类型</param>
        /// <param name="memo">备注</param>
        public void B2XGoodsConfigApproveAction(string id, int type, string memo)
        {
            this.Command<Bll.new_b2x_goodsconfigCommand>().B2XGoodsConfigApproveAction(id, type, memo);
        }

        /// <summary>
        /// 获取B2X异型号配置数据
        /// </summary>
        /// <param name="stationId">网点id</param>
        /// <param name="goodsId">商品id</param>
        /// <returns></returns>
        public List<GoodsProductModel> GetB2XGoodsList(string stationId, string goodsId)
        {
            return this.Command<Bll.new_b2x_goodsconfigCommand>().GetB2XGoodsList(stationId, goodsId);
        }

        /// <summary>
        /// 工单明细报表查询方法
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public virtual ReportReturnData GetOrderDetailsList(OrderDetailCondition condition)
        {
            return Command<QueryDataCommand>().GetOrderDetailsList(condition);
        }

        /// <summary>
        /// 工单明细报表查询方法-创建附件明细
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public virtual object CreateAttachment(OrderDetailCondition condition)
        {
            return Command<QueryDataCommand>().CreateAttachment(condition);
        }
        /// <summary>
        /// 查询升级替换物料
        /// </summary>
        /// <param name="materials"></param>
        /// <returns></returns>
        public virtual List<ProductErroupModel> GetUpgradeMaterials(int type, string productId, string stationId, string workorderid, string model1Id, string model3Id)
        {
            return Command<Bll.new_srv_partlinebll>().GetUpgradeMaterials(type, productId, stationId, workorderid, model1Id, model3Id);
        }
        public virtual List<ProductErroupModel> GetColorMaterials(string productCode, string stationId, string model1Id, string workorderid, string materialsCode)
        {
            return Command<Bll.new_srv_partlinebll>().GetColorMaterials(productCode, stationId, model1Id, workorderid, materialsCode);
        }

        /// <summary>
        /// 获取工单详情
        /// </summary>
        /// <param name="orderNumber"></param>
        /// <returns></returns>
        public virtual Dictionary<string, object> GetWorkOrderDetail(string orderNumber)
        {
            return this.Command<WorkOrderCommand>().GetWorkOrderDetail(orderNumber);
        }

        /// <summary>
        /// 获取附件
        /// </summary>
        /// <param name="AttachmentId"></param>
        /// <returns></returns>
        public virtual Dictionary<string, object> GetAttachment(string AttachmentId)
        {
            return this.Command<WorkOrderCommand>().GetAttachment(AttachmentId);
        }

        /// <summary>
        /// Author：p-wuyao5
        /// Create Date：2023/3/23
        /// Depiction:根据imei,国家/地区查询出满足条件的写号池数据
        /// </summary>
        /// <param name="imei"></param>
        /// <param name="countryId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public string GetCueword(string imei, string countryId, int type)
        {
            return this.Command<WorkOrderCommand>().GetCueword(imei,countryId,type);
        }
        /// <summary>
        /// Author：p-songyongxiang
        /// Create Date：2024/1/25
        /// Depiction:返回迈创服务商生成的结算单下的工单，服务单更换件明细旧件编码，新建编码列需要动态展示
        /// </summary>
        /// <param name="expense_claimid"></param>
        /// <returns></returns>
        public string GetWorkorderMaitroxConsign(string expense_claimid) 
        {
            return this.Command<WorkOrderCommand>().GetWorkorderMaitroxConsign(expense_claimid);
        }

        /// <summary>
        /// 获取服务单处理方法可替换列表
        /// </summary>
        /// <param name="workorderId">服务单Id</param>
        /// <returns></returns>
        public List<WorkorderApproachQueryModel> GetWorkorderApproachReplace(string workorderId)
        {
            //入参检测
            if (string.IsNullOrEmpty(workorderId))
            {
                throw new ArgumentNullException(nameof(workorderId));
            }
            return this.Command<Bll.new_srv_partlinebll>().GetWorkorderApproachReplace(workorderId);
        }
        /// <summary>
        /// 上门服务范围的导入
        /// 导入的数据都会去做校验是否存在
        /// 不存在就创建
        /// 存在就校验更新的值是否与原值相同，相同就不更新、减少更新次数
        /// </summary>
        /// <param name="importLogId">导入Id</param>
        /// <param name="content">导入内容</param>
        /// <returns></returns>
        public virtual ImportResultModel CategoryareaImport(string importLogId, string content)
        {
            return this.Command<WorkOrderCommand>().CategoryareaImport(importLogId, content);
        }

        /// <summary>
        /// 创建手工费减免特批单
        /// </summary>
        /// <param name="manualSpecialModel"></param>
        /// <returns></returns>
        public virtual Guid CreateManualSpecial(ManualSpecialModel manualSpecialModel)
        {
            return this.Command<WorkOrderCommand>().CreateManualSpecial(manualSpecialModel);
        }

        /// <summary>
        /// 大礼包退货时，关联虚拟服务相关操作
        /// </summary>
        /// <param name="workorderId">大礼包退货工单ID</param>
        /// <returns></returns>
        public void GiftPackagesReturnVirtualServiceOperation(string workorderId)
        {
            if (string.IsNullOrWhiteSpace(workorderId))
                throw new ArgumentNullException(nameof(workorderId));

            var workorder = Bll.WorkOrderCommon.GetEntityWorkOrderAllFileds(new Guid(workorderId), OrganizationServiceAdmin);
            this.Command<WorkorderCommand_close>().GiftPackagesReturnVirtualServiceOperation(workorder);
        }

        /// <summary>
        /// 根据国家id和语言id查询国家、省、市、区数据，组装成options格式返回
        /// 入参json字符串，包含countryid（Guid），lanid（int，语言编码）
        /// </summary>
        public virtual AddressApiResponse<AddressOptionsRoot> GetCountryRegionOptions(string param)
        {
            var result = this.Command<WorkOrderCommand>().GetCountryRegionOptions(param);
            return new AddressApiResponse<AddressOptionsRoot>(200, result);
        }
  
    }
}
