﻿using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json.Linq;
using Rektec.ServiceSAP.Function.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Xml;


namespace Rektec.ServiceSAP.Function.Common
{
    public class CommonHelper
    {
        /// <summary>
        /// 连接组织服务
        /// </summary>
        /// <returns></returns>
        public static IOrganizationService CreateConnection()
        {
            IOrganizationService OrganizationService = new ServiceClient(Environment.GetEnvironmentVariable("ConnectionString"));
            return OrganizationService;
        }
        /// <summary>
        /// base64解密（UTF-8）
        /// </summary>
        /// <param name="base64Str"></param>
        /// <returns></returns>
        public static string Base64Decode(string base64Str)
        {
            if (string.IsNullOrWhiteSpace(base64Str))
            {
                return string.Empty;
            }
            var bytes = Convert.FromBase64String(base64Str);
            return System.Text.Encoding.UTF8.GetString(bytes);
        }
        /// <summary>
        /// 获取X5验证的data参数值
        /// add by jakezhang 2021-10-25 21:24:02
        /// </summary>
        /// <param name="bodyStr"></param>
        /// <returns></returns>
        public static string GetX5Data(string bodyStr)
        {
            if (string.IsNullOrWhiteSpace(bodyStr))
            {
                return string.Empty;
            }
            bodyStr = bodyStr.Replace("data=", "");
            bodyStr = System.Web.HttpUtility.UrlDecode(bodyStr);
            return bodyStr;
        }
        /// <summary>
        /// 检查X5的签名，并返回body参数信息
        /// add by jakezhang 2021-10-25 22:11:22
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="dataParam"></param>
        /// <returns></returns>
        public static string CheckX5AndGetBodyInfo(IOrganizationService service, ILogger log, string dataParam, string sys_bpmAppId, string sys_bpmAppKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(dataParam))
                {
                    return string.Empty;
                }
                //进行Base64解密
                string dataStr = CommonHelper.Base64Decode(dataParam);
                log.LogInformation($"data参数Base64解密后：{dataStr}");
                //获取入参并校验
                X5DataParam dataObj = Newtonsoft.Json.JsonConvert.DeserializeObject<X5DataParam>(dataStr);
                if (dataObj == null)
                {
                    throw new Exception("传入参数解析失败，结果为空！");
                }
                if (dataObj.header == null)
                {
                    throw new Exception("传入header参数解析失败，结果为空！");
                }
                if (string.IsNullOrWhiteSpace(dataObj.header.appid))
                {
                    throw new Exception("传入header.appid参数解析失败，结果为空！");
                }
                if (string.IsNullOrWhiteSpace(dataObj.header.sign))
                {
                    throw new Exception("传入header.sign参数解析失败，结果为空！");
                }
                //获取系统参数
                if (string.IsNullOrWhiteSpace(sys_bpmAppId))
                {
                    throw new Exception("请先联系crm管理员配置[sys_mwAppId]的参数值！");
                }
                if (string.IsNullOrWhiteSpace(sys_bpmAppKey))
                {
                    throw new Exception("请先联系crm管理员配置[sys_mwAppKey]的参数值！");
                }
                if (sys_bpmAppId != dataObj.header.appid)
                {
                    throw new Exception($"传入的appid[{dataObj.header.appid}]参数错误，在CRM系统中不存在！");
                }
                //获取X5验证的签名值
                string sign = Md5Hash(sys_bpmAppId + dataObj.body + sys_bpmAppKey);
                if (sign.ToUpper() != dataObj.header.sign.ToUpper())
                {
                    throw new Exception("X5签名验证失败！");
                }
                //返回正文信息
                return dataObj.body;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "获取Body参数失败：" + ex.Message);
                throw new Exception("获取Body参数失败：" + ex.Message);
            }
        }
        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Md5Hash(string input)
        {
            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            return sBuilder.ToString();
        }
        /// <summary>
        /// 获取系统参数
        /// </summary>
        /// <param name="service"></param>
        /// <param name="name">系统参数名称</param>
        /// <param name="isRequired">是否必须</param>
        /// <returns>系统参数值</returns>
        public static string GetSystemParamValue(IOrganizationService service, string name, bool isRequired = false)
        {
            try
            {
                QueryExpression query = new QueryExpression("new_systemparameter");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_name", ConditionOperator.Equal, name);
                query.ColumnSet.AddColumns("new_value");
                EntityCollection ec = service.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                {
                    if (isRequired)
                    {
                        throw new Exception($"请维护系统参数【{name}】");
                    }
                    return null;
                }
                return ec.Entities[0].GetAttributeValue<string>("new_value");
            }
            catch (Exception ex)
            {
                throw new Exception("获取系统参数错误：" + ex.Message);
            }
        }
        /// <summary>
        /// fetchxml分页
        /// </summary> 
        public static EntityCollection QueryXmlPage(IOrganizationService service, string fetchxml, int pagesize = 5000)
        {
            int fetchCount = pagesize;
            int pageNumber = 1;
            string pagingCookie = null;
            EntityCollection returnEc = new EntityCollection();
            EntityCollection ec = null;
            do
            {
                string xml = CreateXml(fetchxml, pagingCookie, pageNumber, fetchCount);
                ec = service.RetrieveMultiple(new FetchExpression(xml));
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                    pagingCookie = ec.PagingCookie;
                }
            } while (ec != null && ec.MoreRecords);
            return returnEc;
        }
        private static string CreateXml(string xml, string cookie, int page, int count)
        {
            System.IO.StringReader stringReader = new System.IO.StringReader(xml);
            XmlTextReader reader = new XmlTextReader(stringReader);
            XmlDocument doc = new XmlDocument();
            doc.Load(reader);
            return CreateXml(doc, cookie, page, count);
        }
        private static string CreateXml(XmlDocument doc, string cookie, int page, int count)
        {
            XmlAttributeCollection attrs = doc.DocumentElement.Attributes;
            if (cookie != null)
            {
                XmlAttribute pagingAttr = doc.CreateAttribute("paging-cookie");
                pagingAttr.Value = cookie;
                attrs.Append(pagingAttr);
            }
            XmlAttribute pageAttr = doc.CreateAttribute("page");
            pageAttr.Value = System.Convert.ToString(page);
            attrs.Append(pageAttr);

            XmlAttribute countAttr = doc.CreateAttribute("count");
            countAttr.Value = System.Convert.ToString(count);
            attrs.Append(countAttr);

            System.Text.StringBuilder sb = new System.Text.StringBuilder(1024);
            System.IO.StringWriter stringWriter = new System.IO.StringWriter(sb);

            XmlTextWriter writer = new XmlTextWriter(stringWriter);
            doc.WriteTo(writer);
            writer.Close();
            return sb.ToString();
        }
        /// <summary>
        /// QueryExpression分页
        /// </summary> 
        public static EntityCollection QueryExpressionPage(IOrganizationService service, QueryExpression query, int pageSize = 5000)
        {
            int pageNumber = 1;
            EntityCollection ec = null;
            EntityCollection returnEc = new EntityCollection();
            do
            {
                query.PageInfo = new PagingInfo { PageNumber = pageNumber, Count = pageSize };
                ec = service.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                }
            } while (ec != null && ec.MoreRecords);

            return returnEc;
        }
        /// <summary>
        /// 大批量事务执行
        /// </summary>
        /// <param name="service"></param>
        /// <param name="ents"></param>
        public static async Task BatchExecuteBatches(ILogger log, ServiceClient service, OrganizationRequestCollection ents)
        {

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            if (ents.Count > 1000)
            {
                try
                {
                    var list = ents.ToList();
                    var tasks = new List<Task>();

                    do
                    {
                        OrganizationRequestCollection requests = new OrganizationRequestCollection();
                        list.Take(300).ToList().ForEach((item) =>
                        {
                            requests.Add(item);

                        });

                        await BatchExecute(log, service, requests);
                        list = list.Skip(300).ToList();

                    } while (list.Count > 0);
                }
                catch (Exception ex)
                {

                    throw new Exception("批量执行失败：" + ex.Message);
                }
            }
            else
            {
                await BatchExecute(log, service, ents);

            }

            stopwatch.Stop();
            log.LogInformation("事务执行耗时：" + stopwatch.ElapsedMilliseconds);
        }

        /// <summary>
        /// 批量执行
        /// </summary>
        /// <param name="service"></param>
        /// <param name="ents"></param>
        public static async Task BatchExecute(ILogger log, ServiceClient service, OrganizationRequestCollection ents)
        {
            if (ents == null) return;

            try
            {
                var requestWithResults = new ExecuteMultipleRequest()
                {

                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = true,
                        ReturnResponses = true
                    },
                    Requests = ents
                };
                var resp = (ExecuteMultipleResponse)await service.ExecuteAsync(requestWithResults);
                if (resp.IsFaulted)
                {
                    var faultMess = "执行失败";
                    foreach (var item in resp.Responses.Where(m => m.Fault != null).ToList())
                    {

                        faultMess += item.Fault.Message + "\r\n\r\n";
                    }
                    log.LogInformation(faultMess);
                }
            }
            catch (Exception ex)
            {
                log.LogInformation($"批量执行失败：{ex.Message}");
                throw new Exception(ex.Message);
            }

        }
        /// <summary>
        /// 获取相应子节点的值
        /// </summary>
        /// <param name="childnodelist"></param>
        public static string JSON_SeleteNode(JToken json, string ReName)
        {
            try
            {
                string result = "";
                //这里6.0版块可以用正则匹配
                var node = json.SelectToken("$.." + ReName);
                if (node != null)
                {
                    //判断节点类型
                    if (node.Type == JTokenType.String || node.Type == JTokenType.Integer || node.Type == JTokenType.Float || node.Type == JTokenType.Array)
                    {
                        //返回string值
                        result = node.Value<object>().ToString();
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static JToken ReadJSON(string jsonStr)
        {
            JObject jobj = JObject.Parse(jsonStr);
            JToken result = jobj as JToken;
            return result;
        }
        #region 暂未使用方法
        /// <summary>
        /// 根据指定字段查询lookup数据
        /// </summary>
        /// <param name="service"></param>
        /// <param name="entityName"></param>
        /// <param name="filed"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static Guid? GetLookupId(ServiceClient service, string entityName, string filed, string value)
        {
            QueryExpression query = new QueryExpression();
            query.EntityName = entityName;
            query.Criteria.AddCondition(filed, ConditionOperator.Equal, value);
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var res = service.RetrieveMultiple(query);
            if (res?.Entities?.Count > 0)
                return res.Entities[0].Id;
            return (Guid?)null;
        }
        /// <summary>
        /// 获取时间戳
        /// </summary>
        /// <param name="time">UTC时间，为空默认当前时间</param>
        /// <returns></returns>
        public static long GetTimeStamp(DateTime? time = null)
        {
            time = time == null ? DateTime.UtcNow : time;
            TimeSpan ts = time.Value - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds);
        }
        /// <summary>
        /// 转换时间戳为C#时间
        /// </summary>
        /// <param name="timeStamp">时间戳 单位：毫秒</param>
        /// <returns>C#时间</returns>
        public static DateTime ConvertTimeStampToDateTime(long timeStamp)
        {
            DateTime startTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);
            DateTime dt = startTime.AddSeconds(timeStamp);
            return dt;
        }
        /// <summary>
        /// 根据指定字段查询lookup数据
        /// </summary>
        /// <param name="service"></param>
        /// <param name="entityName"></param>
        /// <param name="filed"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string? GetCode(ServiceClient service, string entityName, string filed, Guid value)
        {
            QueryExpression query = new QueryExpression();
            query.EntityName = entityName;
            query.ColumnSet = new ColumnSet("new_code");
            query.Criteria.AddCondition(filed, ConditionOperator.Equal, value);
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var res = service.RetrieveMultiple(query);
            if (res?.Entities?.Count > 0)
                return res.Entities[0].GetAttributeValue<string>("new_code");
            return "";
        }
        /// <summary>
        /// 获取签名
        /// </summary>
        /// <param name="bodyValues">body数据</param>
        /// <param name="AuthId">授权ID</param>
        /// <param name="AuthKey">授权Key</param>
        /// <returns></returns>
        public static string GetSign(string bodyValues, string AuthId, string AuthKey)
        {
            string sign = MD5Encrypt(AuthId + bodyValues + AuthKey).ToUpper();
            return sign;
        }
        /// <summary>
        /// MD5签名 (appid+body+appkey)
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string MD5Encrypt(string source)
        {
            string strResult = string.Empty;
            byte[] result = Encoding.UTF8.GetBytes(source);
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] output = md5.ComputeHash(result);
            strResult = BitConverter.ToString(output).Replace("-", "");
            return strResult;
        }
        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="coll"></param>
        public static void MultUpdateRequest(IOrganizationService service, ILogger log, EntityCollection coll)
        {
            //数据总数
            int dataCount = coll.Entities.Count;
            if (dataCount < 1)
            {
                log.LogInformation($"{DateTime.Now},暂无数据更新！");
                return;
            }
            //单次执行数量,分批处理
            int singleExcuteCount = 100;
            //批次数量
            int batchcount = 0;
            for (int batchIdx = 0; batchIdx < coll.Entities.Count; batchIdx += singleExcuteCount)
            {
                batchcount++;
                log.LogInformation($"{DateTime.Now},当前是第{batchIdx}批次");
                var batchdata = coll.Entities.Skip(batchIdx).Take(singleExcuteCount);
                //批量处理
                ExecuteMultipleRequest multipleRequest = new ExecuteMultipleRequest()
                {
                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = false,
                        ReturnResponses = false
                    },
                    Requests = new OrganizationRequestCollection()
                };
                foreach (var item in batchdata)
                {
                    Entity curEnt = item;
                    UpdateRequest updateRequest = new UpdateRequest { Target = curEnt };
                    multipleRequest.Requests.Add(updateRequest);
                }
                if (multipleRequest.Requests.Count > 0)
                {
                    ExecuteMultipleResponse response = (ExecuteMultipleResponse)service.Execute(multipleRequest);//批量请求
                    if (response.IsFaulted)
                    {
                        var faultMess = "执行失败";
                        foreach (var item in response.Responses.Where(m => m.Fault != null).ToList())
                        {

                            faultMess += item.Fault.Message + "\r\n\r\n";
                        }
                        log.LogInformation(faultMess);
                    }
                    multipleRequest.Requests.Clear();
                }
            }
            log.LogInformation($"{DateTime.Now},集合数据总数为:{coll.Entities.Count},一共{batchcount}批次");
        }
        #endregion
    }
}
