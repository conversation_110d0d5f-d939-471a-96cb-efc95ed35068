﻿using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json.Linq;
using RekTec.Crm.Common.Logger;
using RekTec.ServiceSettlement.Function.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using RekTec.Crm.OrganizationService.Common.Helper;
using Newtonsoft.Json;
using System.Net.Http.Headers;

namespace RekTec.ServiceSettlement.Function.Common
{
    public class CommonHelper
    {
        private static int i = 0;
        /// <summary>
        /// 连接组织服务
        /// </summary>
        /// <returns></returns>
        public static IOrganizationService CreateConnection()
        {
            string CONNECTION_STRING = Environment.GetEnvironmentVariable("ConnectionString");
            ServiceClient.MaxConnectionTimeout = new TimeSpan(0, 10, 0);
            IOrganizationService OrganizationService = new ServiceClient(CONNECTION_STRING);

            return OrganizationService;
        }
        /// <summary>
        /// 获取系统参数
        /// </summary>
        /// <param name="service"></param>
        /// <param name="name">系统参数名称</param>
        /// <param name="isRequired">是否必须</param>                                            
        /// <returns>系统参数值</returns>
        public static string GetSystemParamValue(IOrganizationService service, string name, bool isRequired = false)
        {
            try
            {
                QueryExpression query = new QueryExpression("new_systemparameter");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_name", ConditionOperator.Equal, name);
                query.ColumnSet.AddColumns("new_value");
                EntityCollection ec = service.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                {
                    if (isRequired)
                    {
                        throw new Exception($"请维护系统参数【{name}】");
                    }
                    return null;
                }
                return ec.Entities[0].GetAttributeValue<string>("new_value");
            }
            catch (Exception ex)
            {
                throw new Exception("获取系统参数错误：" + ex.Message);
            }
        }
        /// <summary>
        /// fetchxml分页
        /// </summary> 
        public static EntityCollection QueryXmlPage(IOrganizationService service, string fetchxml, int pagesize = 5000)
        {
            int fetchCount = pagesize;
            int pageNumber = 1;
            string pagingCookie = null;
            EntityCollection returnEc = new EntityCollection();
            EntityCollection ec = null;
            do
            {
                string xml = CreateXml(fetchxml, pagingCookie, pageNumber, fetchCount);
                ec = service.RetrieveMultiple(new FetchExpression(xml));
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                    pagingCookie = ec.PagingCookie;
                }
            } while (ec != null && ec.MoreRecords);
            return returnEc;
        }
        /// <summary>
        /// fetchxml分页——跳过多语言插件
        /// </summary> 
        public static EntityCollection QueryXmlPagePassPlugin(IOrganizationService service, string fetchxml, int pagesize = 5000)
        {
            int fetchCount = pagesize;
            int pageNumber = 1;
            string pagingCookie = null;
            EntityCollection returnEc = new EntityCollection();
            EntityCollection ec = null;
            do
            {
                string xml = CreateXml(fetchxml, pagingCookie, pageNumber, fetchCount);
                ec = service.RetrieveMultipleWithBypassPlugin(new FetchExpression(xml));
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                    pagingCookie = ec.PagingCookie;
                }
            } while (ec != null && ec.MoreRecords);
            return returnEc;
        }
        private static string CreateXml(string xml, string cookie, int page, int count)
        {
            System.IO.StringReader stringReader = new System.IO.StringReader(xml);
            XmlTextReader reader = new XmlTextReader(stringReader);
            XmlDocument doc = new XmlDocument();
            doc.Load(reader);
            return CreateXml(doc, cookie, page, count);
        }
        private static string CreateXml(XmlDocument doc, string cookie, int page, int count)
        {
            XmlAttributeCollection attrs = doc.DocumentElement.Attributes;
            if (cookie != null)
            {
                XmlAttribute pagingAttr = doc.CreateAttribute("paging-cookie");
                pagingAttr.Value = cookie;
                attrs.Append(pagingAttr);
            }
            XmlAttribute pageAttr = doc.CreateAttribute("page");
            pageAttr.Value = System.Convert.ToString(page);
            attrs.Append(pageAttr);

            XmlAttribute countAttr = doc.CreateAttribute("count");
            countAttr.Value = System.Convert.ToString(count);
            attrs.Append(countAttr);

            System.Text.StringBuilder sb = new System.Text.StringBuilder(1024);
            System.IO.StringWriter stringWriter = new System.IO.StringWriter(sb);

            XmlTextWriter writer = new XmlTextWriter(stringWriter);
            doc.WriteTo(writer);
            writer.Close();
            return sb.ToString();
        }
        /// <summary>
        /// QueryExpression分页
        /// </summary> 
        public static EntityCollection QueryExpressionPage(IOrganizationService service, QueryExpression query, int pageSize = 5000)
        {
            int pageNumber = 1;
            EntityCollection ec = null;
            EntityCollection returnEc = new EntityCollection();
            do
            {
                query.PageInfo = new PagingInfo { PageNumber = pageNumber, Count = pageSize };
                ec = service.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                }
            } while (ec != null && ec.MoreRecords);

            return returnEc;
        }
        /// <summary>
        /// QueryExpression分页——跳过多语言插件
        /// </summary> 
        public static EntityCollection QueryExpressionPagePassPlugin(IOrganizationService service, QueryExpression query, int pageSize = 5000)
        {
            int pageNumber = 1;
            EntityCollection ec = null;
            EntityCollection returnEc = new EntityCollection();
            do
            {
                query.PageInfo = new PagingInfo { PageNumber = pageNumber, Count = pageSize };
                ec = service.RetrieveMultipleWithBypassPlugin(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    returnEc.Entities.AddRange(ec.Entities);
                    pageNumber = pageNumber + 1;
                }
            } while (ec != null && ec.MoreRecords);

            return returnEc;
        }
        /// <summary>
        /// 截取小数点后位数
        /// </summary>
        /// <param name="d"></param>
        /// <param name="n"></param>
        /// <returns></returns>
        public static decimal CutDecimalWithN(decimal d, int n)
        {
            string strDecimal = d.ToString();
            int index = strDecimal.IndexOf(".");
            if (index == -1 || strDecimal.Length < index + n + 1)
            {
                strDecimal = string.Format("{0:F" + n + "}", d);
            }
            else
            {
                int length = index;
                if (n != 0)
                {
                    length = index + n + 1;
                }
                strDecimal = strDecimal.Substring(0, length);
            }
            return Decimal.Parse(strDecimal);
        }

        /// <summary>
        /// 大批量事务执行
        /// </summary>
        /// <param name="service"></param>
        /// <param name="ents"></param>
        public static async Task BatchExecuteBatches(ILogger log, ServiceClient service, OrganizationRequestCollection ents)
        {

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            if (ents.Count > 1000)
            {
                try
                {
                    var list = ents.ToList();
                    var tasks = new List<Task>();

                    do
                    {
                        OrganizationRequestCollection requests = new OrganizationRequestCollection();
                        list.Take(300).ToList().ForEach((item) =>
                        {
                            requests.Add(item);

                        });

                        await BatchExecute(log, service, requests);
                        list = list.Skip(300).ToList();

                    } while (list.Count > 0);
                }
                catch (Exception ex)
                {

                    throw new Exception("批量执行失败：" + ex.Message);
                }
            }
            else
            {
                await BatchExecute(log, service, ents);

            }

            stopwatch.Stop();
            log.LogInformation("事务执行耗时：" + stopwatch.ElapsedMilliseconds);
        }

        /// <summary>
        /// 批量执行
        /// </summary>
        /// <param name="service"></param>
        /// <param name="ents"></param>
        public static async Task BatchExecute(ILogger log, ServiceClient service, OrganizationRequestCollection ents)
        {
            if (ents == null) return;

            try
            {
                var requestWithResults = new ExecuteMultipleRequest()
                {

                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = true,
                        ReturnResponses = true
                    },
                    Requests = ents
                };
                var resp = (ExecuteMultipleResponse)await service.ExecuteAsync(requestWithResults);
                if (resp.IsFaulted)
                {
                    var faultMess = "执行失败";
                    foreach (var item in resp.Responses.Where(m => m.Fault != null).ToList())
                    {

                        faultMess += item.Fault.Message + "\r\n\r\n";
                    }
                    log.LogInformation(faultMess);
                }
            }
            catch (Exception ex)
            {
                log.LogInformation($"批量执行失败：{ex.Message}");
                throw new Exception(ex.Message);
            }

        }

        /// <summary>
        /// 随机获取组织服务
        /// </summary>
        /// <returns></returns>
        public static ServiceClient GetService(ILogger log)
        {
            int retry = 3;
            int count = 0;
            while (true)
            {
                try
                {
                    ServiceClient serviceclient = null;
                    var ConnectionString = Environment.GetEnvironmentVariable("ConnectionString_Adjustment");
                    string[] connlist = ConnectionString.Split('|');
                    Random random = new Random();
                    int r = random.Next(0, connlist.Count());//生成 0 - 6 之间的随机数，包含前一个，不包含后一个
                    serviceclient = new ServiceClient(connlist[r]);
                    serviceclient.EnableAffinityCookie = false;
                    return serviceclient;
                }
                catch (Exception)
                {
                    log.LogInformation($"第{count}次连接组织服务失败");
                    if (count >= retry)
                    {
                        throw;
                    }
                    count++;
                }
            }
        }
        public static bool isOKServiceClientConn(ServiceClient _ServiceClient)
        {
            if (_ServiceClient == null)
                return false;
            try
            {

                QueryExpression queryExpression = new QueryExpression();
                queryExpression.EntityName = "systemuser";
                queryExpression.NoLock = true;
                queryExpression.TopCount = 1;

                _ServiceClient.RetrieveMultiple(queryExpression);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

            return false;
        }
        /// <summary>
        ///根据SAP id,签约主体 获取服务商
        /// </summary>
        /// <param name="sapid"></param>
        /// <param name="contractbody"></param>
        /// <param name="serviceAdmin"></param>
        /// <returns></returns>
        public static Entity GetDNSstation(string sapid, string contractbody, IOrganizationService serviceAdmin)
        {
            return serviceAdmin.RetrieveMultiple(
                new QueryExpression("new_srv_station")
                {
                    ColumnSet = new ColumnSet(false),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_supplier", ConditionOperator.Equal, sapid),
                            new ConditionExpression("new_servicetype", ConditionOperator.Equal, 1),
                            new ConditionExpression("new_contractingbody", ConditionOperator.Equal, contractbody),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                }).Entities.FirstOrDefault();
        }
        #region 数据批量操作
        /// <summary>
        /// 批量新建
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="coll"></param>
        public static void MultCreateRequest(IOrganizationService service, ILogger log, EntityCollection coll)
        {
            //数据总数
            int dataCount = coll.Entities.Count;
            if (dataCount < 1)
            {
                log.LogInformation($"{DateTime.Now},暂无数据新增！");
                return;
            }
            //批次数量
            int batchCount = 0;
            //单次执行数量
            int singleExcuteCount = 300;
            ///批量请求最多限制是1000，我们每次请求设置1000条，分批处理
            //计算批次
            if (dataCount % singleExcuteCount == 0)
                batchCount = (dataCount / singleExcuteCount);
            else
                batchCount = ((dataCount / singleExcuteCount) + 1);
            log.LogInformation($"{DateTime.Now},当前数据总数是：{dataCount},每批次：{singleExcuteCount}，共{batchCount}批次");
            for (int batchIdx = 0; batchIdx < batchCount; batchIdx++)
            {
                log.LogInformation($"{DateTime.Now},当前是第{batchIdx}批次");
                //起始索引
                int startIdx = singleExcuteCount * batchIdx;
                //当前批次遍历次数
                int loopCount = (batchIdx + 1) == batchCount ? (startIdx + dataCount % singleExcuteCount) : singleExcuteCount * (batchIdx + 1);
                //批量处理
                ExecuteMultipleRequest multipleRequest = new ExecuteMultipleRequest()
                {
                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = false,
                        ReturnResponses = false
                    },
                    Requests = new OrganizationRequestCollection()
                };
                for (var i = startIdx; i < loopCount; i++)
                {
                    Entity curEnt = coll.Entities[i];
                    CreateRequest createRequest = new CreateRequest { Target = curEnt };
                    multipleRequest.Requests.Add(createRequest);
                }
                if (multipleRequest.Requests.Count > 0)
                {
                    ExecuteMultipleResponse response = (ExecuteMultipleResponse)service.Execute(multipleRequest);//批量新增
                    if (response.IsFaulted)
                    {
                        var faultMess = "执行失败";
                        foreach (var item in response.Responses.Where(m => m.Fault != null).ToList())
                        {

                            faultMess += item.Fault.Message + "\r\n\r\n";
                        }
                        log.LogInformation(faultMess);
                    }
                    multipleRequest.Requests.Clear();
                }
            }
        }
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="coll"></param>
        public static void MultDeleteRequest(IOrganizationService service, ILogger log, EntityCollection coll)
        {
            //数据总数
            int dataCount = coll.Entities.Count;
            if (dataCount < 1)
            {
                log.LogInformation($"{DateTime.Now},暂无数据删除！");
                return;
            }
            //批次数量
            int batchCount = 0;
            //单次执行数量
            int singleExcuteCount = 500;
            ///批量请求最多限制是1000，我们每次请求设置500条，分批处理
            //计算批次
            if (dataCount % singleExcuteCount == 0)
                batchCount = (dataCount / singleExcuteCount);
            else
                batchCount = ((dataCount / singleExcuteCount) + 1);
            log.LogInformation($"{DateTime.Now},当前删除数据总数是：{dataCount},每批次：{singleExcuteCount}，共{batchCount}批次");
            for (int batchIdx = 0; batchIdx < batchCount; batchIdx++)
            {
                log.LogInformation($"{DateTime.Now},当前是第{batchIdx}批次");
                //起始索引
                int startIdx = singleExcuteCount * batchIdx;
                //当前批次遍历次数
                int loopCount = (batchIdx + 1) == batchCount ? (startIdx + dataCount % singleExcuteCount) : singleExcuteCount * (batchIdx + 1);
                //批量处理
                ExecuteMultipleRequest multipleRequest = new ExecuteMultipleRequest()
                {
                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = false,
                        ReturnResponses = false
                    },
                    Requests = new OrganizationRequestCollection()
                };
                for (var i = startIdx; i < loopCount; i++)
                {
                    Entity curEnt = coll.Entities[i];
                    log.LogInformation($"{DateTime.Now},当前删除数据的ID是：{curEnt.Id}");
                    DeleteRequest delRequest = new DeleteRequest { Target = new EntityReference(curEnt.LogicalName, curEnt.Id) };
                    multipleRequest.Requests.Add(delRequest);
                }
                if (multipleRequest.Requests.Count > 0)
                {
                    service.Execute(multipleRequest);//批量更新
                    multipleRequest.Requests.Clear();
                }
            }
        }
        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="coll"></param>
        public static void MultUpdateRequest(IOrganizationService service, ILogger log, EntityCollection coll)
        {
            //数据总数
            int dataCount = coll.Entities.Count;
            if (dataCount < 1)
            {
                log.LogInformation($"{DateTime.Now},暂无数据更新！");
                return;
            }
            //单次执行数量,分批处理
            int singleExcuteCount = 100;
            //批次数量
            int batchcount = 0;
            for (int batchIdx = 0; batchIdx < coll.Entities.Count; batchIdx += singleExcuteCount)
            {
                batchcount++;
                log.LogInformation($"{DateTime.Now},当前是第{batchIdx}批次");
                var batchdata = coll.Entities.Skip(batchIdx).Take(singleExcuteCount);
                //批量处理
                ExecuteMultipleRequest multipleRequest = new ExecuteMultipleRequest()
                {
                    Settings = new ExecuteMultipleSettings()
                    {
                        ContinueOnError = false,
                        ReturnResponses = false
                    },
                    Requests = new OrganizationRequestCollection()
                };
                foreach (var item in batchdata)
                {
                    Entity curEnt = item;
                    UpdateRequest updateRequest = new UpdateRequest { Target = curEnt };
                    multipleRequest.Requests.Add(updateRequest);
                }
                if (multipleRequest.Requests.Count > 0)
                {
                    ExecuteMultipleResponse response = (ExecuteMultipleResponse)service.Execute(multipleRequest);//批量请求
                    if (response.IsFaulted)
                    {
                        var faultMess = "执行失败";
                        foreach (var item in response.Responses.Where(m => m.Fault != null).ToList())
                        {

                            faultMess += item.Fault.Message + "\r\n\r\n";
                        }
                        log.LogInformation(faultMess);
                    }
                    multipleRequest.Requests.Clear();
                }
            }
            log.LogInformation($"{DateTime.Now},集合数据总数为:{coll.Entities.Count},一共{batchcount}批次");
        }
        #endregion
        /// <summary>
        /// 获取相应子节点的值
        /// </summary>
        /// <param name="childnodelist"></param>
        public static string JSON_SeleteNode(JToken json, string ReName)
        {
            try
            {
                string result = "";
                //这里6.0版块可以用正则匹配
                var node = json.SelectToken("$.." + ReName);
                if (node != null)
                {
                    //判断节点类型
                    if (node.Type == JTokenType.String || node.Type == JTokenType.Integer || node.Type == JTokenType.Float || node.Type == JTokenType.Array)
                    {
                        //返回string值
                        result = node.Value<object>().ToString();
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static JToken ReadJSON(string jsonStr)
        {
            JObject jobj = JObject.Parse(jsonStr);
            JToken result = jobj as JToken;
            return result;
        }
        public static JToken ReadJToken(string jsonStr)
        {
            JToken result = JToken.Parse(jsonStr); ;
            return result;
        }
        /// <summary>
        /// 获取Post请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="headerDict"></param>
        /// <param name="Parameter"></param>
        /// <returns></returns>
        public static string GetPostHttpRequest(string url, Dictionary<string, string> headerDict, string Parameter)
        {
            try 
            {
                string result = string.Empty;
                HttpClient httpClient = new HttpClient();
                HttpContent httpContent = new StringContent(Parameter);
                foreach (var headerItem in headerDict)
                {
                    httpClient.DefaultRequestHeaders.Add(headerItem.Key, headerItem.Value);
                }
                httpContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/plain");
                HttpResponseMessage response = httpClient.PostAsync(url, httpContent).Result;
                if (response.StatusCode == HttpStatusCode.OK)
                    result = response.Content.ReadAsStringAsync().Result;
                return result;
            } catch (Exception ex) 
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// MD5签名 (appid+body+appkey)
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string MD5Encrypt(string source)
        {
            string strResult = string.Empty;
            byte[] result = Encoding.UTF8.GetBytes(source);
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] output = md5.ComputeHash(result);
            strResult = BitConverter.ToString(output).Replace("-", "");
            return strResult;
        }
        /// <summary>
        /// Base64加密 (将body传进来进行 Base64加密)
        /// </summary>
        /// <param name="source">加密字串</param>
        /// <param name="result">返回成功加密后的字符串</param>
        /// <returns>是否加密成功</returns>
        public static string EncodeBase64(string source)
        {
            Encoding encode = Encoding.UTF8;
            string result = "";
            byte[] bytes = encode.GetBytes(source);
            try
            {
                result = Convert.ToBase64String(bytes);
            }
            catch
            {
                result = source;
            }
            return result;
        }
        /// <summary>
        /// 发送飞书消息
        /// </summary>
        /// <param name="entityName"></param>
        /// <param name="entityId"></param>
        /// <param name="msg"></param>
        /// <param name="service"></param>
        /// <param name="reworkGroupChatName"></param>
        public static void SendFeiShuMessage(string entityName, Dictionary<string, string> idAndMsg, IOrganizationService service, string reworkGroupChatName = "BaseDataGroupChatName")
        {

            XiaoMi.Crm.Common.SendFeishuMsg.SendFeishuMsgHelper.SendFeishuMsgHelper.BatchSendFlyingBookMsgTopChat(entityName, idAndMsg, service, reworkGroupChatName, "结算消息告警");

        }
        /// <summary>
        /// x5协议调用外部系统接口
        /// </summary>
        /// <param name="batchModel">参数Model</param>
        /// <param name="appId">appid</param>
        /// <param name="appKey">appkey</param>
        /// <param name="method">method</param>
        /// <param name="url">sap接口url</param>
        /// <param name="sysid">系统id</param>
        /// <returns></returns>
        public static Result SendXMSerivceXMS(string body, string appId, string appKey, string method, string operator_id, string operator_name, string url)
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            using (var client = new HttpClient())
            {
                //string url = sap_url + sysid;
                //string body = JsonConvert.SerializeObject(batchModel, new JsonSerializerSettings() { DefaultValueHandling = DefaultValueHandling.Ignore });
                string sign = MD5Encrypt(appId + body + appKey);
                var header = new { appid = appId, sign = sign.ToUpper(), url = url, method = method, protocol = "x5", operator_id = operator_id, operator_name = operator_name};
                var param = new { body = body, header = header };
                string para = JsonConvert.SerializeObject(param);
                var base64 = EncodeBase64(para);
                HttpContent httpContent = new StringContent($"data={base64}");
                httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
                var response = client.PostAsync(url, httpContent).Result;
                string responseBody = response.Content.ReadAsStringAsync().Result;
                var result = JsonConvert.DeserializeObject<Result>(responseBody);
                return result;
            }
        }
        /// <summary>
        /// base64解密（UTF-8）
        /// </summary>
        /// <param name="base64Str"></param>
        /// <returns></returns>
        public static string Base64Decode(string base64Str)
        {
            if (string.IsNullOrWhiteSpace(base64Str))
            {
                return string.Empty;
            }
            var bytes = Convert.FromBase64String(base64Str);
            return System.Text.Encoding.UTF8.GetString(bytes);
        }
        /// <summary>
        /// 获取X5验证的data参数值
        /// add by jakezhang 2021-10-25 21:24:02
        /// </summary>
        /// <param name="bodyStr"></param>
        /// <returns></returns>
        public static string GetX5Data(string bodyStr)
        {
            if (string.IsNullOrWhiteSpace(bodyStr))
            {
                return string.Empty;
            }
            bodyStr = bodyStr.Replace("data=", "");
            bodyStr = System.Web.HttpUtility.UrlDecode(bodyStr);
            return bodyStr;
        }
        /// <summary>
        /// 检查X5的签名，并返回body参数信息
        /// add by jakezhang 2021-10-25 22:11:22
        /// </summary>
        /// <param name="service"></param>
        /// <param name="log"></param>
        /// <param name="dataParam"></param>
        /// <returns></returns>
        public static string CheckX5AndGetBodyInfo(IOrganizationService service, ILogger log, string dataParam, string sys_xmsAppId, string sys_xmsAppKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(dataParam))
                {
                    return string.Empty;
                }
                //进行Base64解密
                string dataStr = CommonHelper.Base64Decode(dataParam);
                log.LogInformation($"data参数Base64解密后：{dataStr}");
                //获取入参并校验
                XMSX5DataParam dataObj = Newtonsoft.Json.JsonConvert.DeserializeObject<XMSX5DataParam>(dataStr);
                if (dataObj == null)
                {
                    throw new Exception("传入参数解析失败，结果为空！");
                }
                if (dataObj.header == null)
                {
                    throw new Exception("传入header参数解析失败，结果为空！");
                }
                if (string.IsNullOrWhiteSpace(dataObj.header.appid))
                {
                    throw new Exception("传入header.appid参数解析失败，结果为空！");
                }
                if (string.IsNullOrWhiteSpace(dataObj.header.sign))
                {
                    throw new Exception("传入header.sign参数解析失败，结果为空！");
                }
                //获取系统参数
                if (string.IsNullOrWhiteSpace(sys_xmsAppId))
                {
                    throw new Exception("请先联系crm管理员配置[sys_xmsAppId]的参数值！");
                }
                if (string.IsNullOrWhiteSpace(sys_xmsAppKey))
                {
                    throw new Exception("请先联系crm管理员配置[sys_xmsAppKey]的参数值！");
                }
                if (sys_xmsAppId != dataObj.header.appid)
                {
                    throw new Exception($"传入的appid[{dataObj.header.appid}]参数错误，在CRM系统中不存在！");
                }
                //获取X5验证的签名值
                string sign = Md5Hash(sys_xmsAppId + dataObj.body + sys_xmsAppKey);
                if (sign.ToUpper() != dataObj.header.sign.ToUpper())
                {
                    throw new Exception("X5签名验证失败！");
                }
                //返回正文信息
                return dataObj.body;
            }
            catch (Exception ex)
            {
                log.LogError(ex, "获取Body参数失败：" + ex.Message);
                throw new Exception("获取Body参数失败：" + ex.Message);
            }
        }
        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Md5Hash(string input)
        {
            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            return sBuilder.ToString();
        }
    }
    #region Entity扩展方法
    /// <summary>
    /// Entity扩展方法
    /// </summary>
    public static class EntityFunction
    {

        #region entity 字段值转换
        /// <summary>
        /// Int
        /// </summary> 
        public static int ToInt(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<int>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        /// <summary>
        /// string
        /// </summary> 
        public static string ToStr(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<string>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }
        


        /// <summary>
        /// string
        /// </summary> 
        public static object ToStrtest(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<AliasedValue>(name).Value;

            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }
        public static string ToFormattedValue(this Entity entity, string name)
        {
            try
            {
                if (entity.FormattedValues.Contains(name))
                {
                    return entity.FormattedValues[name];
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        /// <summary>
        /// float
        /// </summary> 
        public static Double ToFloat(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<Double>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }
        public static Decimal ToDecimal(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<decimal>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        /// <summary>
        /// Money
        /// </summary> 
        public static decimal ToMoney(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<Money>(name).Value;
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        /// <summary>
        /// OptionSetValue
        /// </summary> 
        public static int ToOpInt(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<OptionSetValue>(name).Value;
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }
        public static int ToOpIntDefalut(this Entity entity, string name, int defaultValue = -1)
        {
            return entity.IsNotNull(name) ? entity.ToOpInt(name) : defaultValue;
        }

        /// <summary>
        /// EntityReference
        /// </summary> 
        public static EntityReference ToEr(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<EntityReference>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        /// <summary>
        /// 获取实体下指定的字段值,无则返回null
        /// </summary>
        /// <param name="name">字段名</param>
        /// <param name="defaultValue">默认值</param>
        public static object ToObject(this Entity en, string name)
        {
            if (en.IsNotNull(name))
                return en[name];
            return null;
        }

        public static Guid ToErDefalut(this Entity entity, string name, string defaultValue = null)
        {
            var dValue = defaultValue == null ? Guid.Empty : new Guid(defaultValue);

            return entity.IsNotNull(name) ? entity.ToEr(name).Id : dValue;
        }

        /// <summary>
        /// DateTime
        /// </summary>
        public static DateTime ToDateTime(this Entity entity, string name)
        {
            try
            {
                return entity.GetAttributeValue<DateTime>(name);
            }
            catch (Exception ex)
            {
                throw new Exception($"实体名:{entity.LogicalName} 字段:{name} 错误消息:{ex.Message}");
            }
        }

        #endregion
        /// <summary>
        /// 判断实体的某个字段是否为空
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="name">字段名称</param> 
        public static bool IsNotNull(this Entity entity, string name)
        {
            return entity.Contains(name) && entity.Attributes[name] != null && entity.Attributes[name].ToString() != string.Empty;
        }
        public static T ToDefault<T>(this Entity entity, string keyname)
        {
            object result;
            if (!entity.IsNotNull(keyname)) return default(T);
            switch (entity[keyname].GetType().Name)
            {
                case "String":
                    result = entity.IsNotNull(keyname) ? entity.ToStr(keyname) : string.Empty;
                    break;
                case "Int32":
                    result = entity.IsNotNull(keyname) ? entity.ToInt(keyname) : 0;
                    break;
                case "Double":
                    result = entity.IsNotNull(keyname) ? entity.ToFloat(keyname) : 0;
                    break;
                case "Decimal":
                    result = entity.IsNotNull(keyname) ? entity.ToDecimal(keyname) : 0;
                    break;
                case "Money":
                    result = entity.IsNotNull(keyname) ? entity.ToMoney(keyname) : 0;
                    break;
                case "OptionSetValue":
                    result = entity.IsNotNull(keyname) ? entity.ToOpInt(keyname) : -1;
                    break;
                case "EntityReference":
                    result = entity.IsNotNull(keyname) ? entity.ToEr(keyname).Id : Guid.Empty;
                    break;
                case "DateTime":
                    result = entity.IsNotNull(keyname) ? entity.ToDateTime(keyname) : default(DateTime);
                    break;
                case "Boolean":
                    result = entity.IsNotNull(keyname) ? Convert.ToBoolean(entity[keyname]) : false;
                    break;
                default:
                    throw new NotImplementedException();

            }
            if (result == null)
            {
                return default(T);
            }
            return (T)result;
        }


        /// <summary>
        /// 判断两个实体中某些字段值是否一致
        /// </summary>
        /// <param name="entity1">实体1</param>
        /// <param name="entity2">实体2</param>
        /// <param name="fieldlist">字段组</param>
        /// <returns>bool</returns>
        public static bool IsDifference(this Entity entity1, Entity entity2, string[] fieldlist)
        {
            if (entity1 == null)
            {
                return true;
            }
            else
            {
                foreach (var field in fieldlist)
                {
                    //if (!entity1.Contains(field) || !entity2.Contains(field)) //2019.12.19 zhoulin
                    //{
                    //    continue;
                    //}

                    object oldValue = entity1.IsNotNull(field) ? entity1.Attributes[field] : string.Empty;
                    object newValue = entity2.IsNotNull(field) ? entity2.Attributes[field] : string.Empty;
                    if (!oldValue.Equals(newValue))
                    {
                        return true;
                    }
                }

                return false;
            }
        }

        /// <summary>
        /// 判断两个实体中某些字段值是否一致
        /// </summary>
        /// <param name="entity1">实体1</param>
        /// <param name="entity2">实体2</param>
        /// <param name="fieldlist">字段组</param>
        /// <returns>bool</returns>
        public static bool IsDiff(this Entity entity1, Entity entity2, params string[] fieldlist)
        {
            if (entity1 == null || entity2 == null) return false;
            if (fieldlist.Count(a => entity1.Contains(a)) <= 0) return false;

            foreach (var field in fieldlist)
            {
                object result1 = entity1.ToObject(field) ?? "";
                object result2 = entity2.ToObject(field) ?? "";

                if (!result1.Equals(result2))
                    return true;
            }
            return false;

        }
    }
    #endregion Entity扩展方法
}
