﻿using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using RekTec.ServiceSettlement.Function.Model;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RekTec.ServiceSettlement.Function.Common
{
    /// <summary>
    /// Module ID：无
    /// Author：p-dongbinbib
    /// Create Date：2023-10-13
    /// Depiction：调差逻辑调整（优化）
    /// </summary>
    public class AdjustmentLineCommand
    {
        /// <summary>
        /// 组织服务
        /// </summary>
        private IOrganizationService OrganizationService;
        private static string SETT_ServiceBus_SignatureKeyName = Environment.GetEnvironmentVariable("SETT_ServiceBus_SignatureKeyName").Trim();
        private static string SETT_ServiceBus_SignatureKey = Environment.GetEnvironmentVariable("SETT_ServiceBus_SignatureKey").Trim();
        private static string SETT_ServiceBus_Url = Environment.GetEnvironmentVariable("SETT_ServiceBus_Url").Trim();
        private static string SETT_ServiceBus_QueuesName = Environment.GetEnvironmentVariable("SETT_ServiceBus_QueuesName").Trim();
        private static string ServiceBusConnectionString = Environment.GetEnvironmentVariable("ServiceBusConnectionString").Trim();
        //迈创相关配置信息
        private static string ServiceBusMaitroxConnectionString = Environment.GetEnvironmentVariable("ServiceBusMaitroxConnectionString").Trim();
        private static string SETT_ServiceBus_MaitroxQueuesName = Environment.GetEnvironmentVariable("SETT_MaitroxServiceBus_QueuesName").Trim();
        //激活费消息队列配置信息
        private static string ServiceBusActivateConnectionString = Environment.GetEnvironmentVariable("ServiceBusActivateConnectionString").Trim();
        private static string SETT_ServiceBus_ActivateQueuesName = Environment.GetEnvironmentVariable("SETT_ActivateServiceBus_QueuesName").Trim();
        //Handling费消息队列配置信息
        private static string ServiceBusHandlingConnectionString = Environment.GetEnvironmentVariable("ServiceBusHandlingConnectionString").Trim();
        private static string SETT_ServiceBus_HandlingQueuesName = Environment.GetEnvironmentVariable("SETT_HandlingServiceBus_QueuesName").Trim();
        //仓储结费消息队列配置信息
        private static string ServiceBusExecuteWorkorderCostConnectionString = Environment.GetEnvironmentVariable("ServiceBusExecuteWorkorderCostConnectionString").Trim();
        private static string SET_ServiceBus_ExecuteWorkorderCostQueuesName = Environment.GetEnvironmentVariable("SET_ServiceBus_ExecuteWorkorderCostQueuesName").Trim();

        //当前时间
        private static DateTime dateTime = DateTime.Now.AddMonths(-1);
        //开始时间
        private static DateTime beginDate = new DateTime(dateTime.Year, dateTime.Month, 1);
        //截止时间
        private static DateTime endDate = beginDate.AddMonths(1).AddMilliseconds(-1);
        public AdjustmentLineCommand()
        {
            OrganizationService = CommonHelper.CreateConnection();
        }
        /// <summary>
        /// 调差
        /// </summary>
        /// <param name="log"></param>
        public async Task SetDifference(ILogger log)
        {
            try
            {
                #region 物流费调差基础数据初始化
                SettlementHelp settlementHelp = new SettlementHelp(log);
                var ratioconfig = settlementHelp.GetRatioConfiglist();
                var logisticsfeedetail = settlementHelp.Getlogisticsfeedetail();
                #endregion
                //查询结算标准
                EntityCollection expensestandard = GetExpenseStandard();
                //查询结算单
                QueryExpression query = new QueryExpression("new_srv_expense_claim");
                query.NoLock = true;
                query.ColumnSet = new ColumnSet(true);
                query.Criteria.AddCondition("new_adjuststatus", ConditionOperator.Equal, 2);
                query.Criteria.AddCondition(new ConditionExpression("new_businesstype", ConditionOperator.In, new int[] { 1, 2, 7, 9, 10}));//结算单类型为非B2X,B2X，物流费，安装，高维工厂
                EntityCollection new_srv_expense_claim = OrganizationService.RetrieveMultiple(query);
                if (new_srv_expense_claim?.Entities?.Count > 0)
                {
                    log.LogInformation($"共有{new_srv_expense_claim?.Entities?.Count}条结算单");
                    //判断计算费用过程中是否出错
                    for (int j = 0; j < new_srv_expense_claim.Entities.Count; j++)
                    {
                        int businesstype = new_srv_expense_claim.Entities[j].Contains("new_businesstype") ? new_srv_expense_claim.Entities[j].GetAttributeValue<OptionSetValue>("new_businesstype").Value : -1;
                        if (businesstype == 1 || businesstype == 2)
                        {
                            SetdifferenceAftersalesServiceoutlet(new_srv_expense_claim.Entities[j], expensestandard, log);
                        }
                        else if (businesstype == 7)
                        {
                            //物流费
                            SetdifferenceLogistics(new_srv_expense_claim.Entities[j], ratioconfig, logisticsfeedetail, settlementHelp, log);
                        }
                        else if (businesstype == 9)
                        {
                            //结算单类型 = 安装
                            SetdifferenceInstall(new_srv_expense_claim.Entities[j], log);
                        }
                        else if (businesstype == 10)
                        {
                            log.LogInformation($"高维工厂结算单调差：{new_srv_expense_claim.Entities[j].Id}");
                            //结算单类型 = 高维工厂
                            SetdifferenceHighdimensionalfactory(new_srv_expense_claim.Entities[j], log);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }
        /// <summary>
        /// B2X,非B2X结算单执行调差
        /// </summary>
        /// <param name="new_srv_expense_claim"></param>
        /// <param name="expensestandard"></param>
        /// <param name="log"></param>
        public async void SetdifferenceAftersalesServiceoutlet(Entity new_srv_expense_claim, EntityCollection expensestandard, ILogger log)
        {
            try
            {
                ServriceBusCommon busCommon = new ServriceBusCommon(SETT_ServiceBus_SignatureKeyName, SETT_ServiceBus_SignatureKey, SETT_ServiceBus_Url, SETT_ServiceBus_QueuesName);
                bool iserror = false;
                #region 初始化数据
                //需要推送到servicebus的集合
                List<AdjustModel> amlist = new List<AdjustModel>();
                //劳务费总计（KPI）
                decimal sumAmountKPI = 0m;
                //检测费kpi总和（截取两位小数）
                decimal sumInspectFeeKPI = 0m;
                //特殊费用总和（截取两位小数）
                decimal sumSpecialexpense = 0M;
                //费用合计总和
                decimal sumSettlementMoney = 0M;
                //结算单服务商
                Guid new_srv_station_id = new_srv_expense_claim.GetAttributeValue<EntityReference>("new_srv_station_id").Id;
                //结算年份
                int new_year = new_srv_expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月份
                int new_month = new_srv_expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //获取特殊费用
                EntityCollection specialexpenselist = GetSpecialExpense(new_srv_expense_claim.Id.ToString(), log);
                #endregion
                //根据结算单查询服务单
                EntityCollection workorderCollection = GetWorkorderByExpense(new_srv_expense_claim.Id.ToString());
                var workorderidList = workorderCollection.Entities.Select(a => a.ToDefault<Guid>("new_repairprovider_id")).Distinct().ToList();
                //获取服务商kpi
                List<Entity> serviceKpi = GetServiceKpi(workorderidList, new_year, new_month);
                //if (serviceKpi == null) continue;
                if (workorderCollection?.Entities?.Count > 0)
                {
                    log.LogInformation($"当前结算单关联服务单数量{workorderCollection?.Entities?.Count}");
                    //既没有维护服务单，也没有维护SKU，需要将特殊费用平均分摊到所有的工单上(考虑反冲，重维服务单不在该结算单下的情况)
                    var specialfeecount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && a.ToDefault<int>("new_feetype") != 25).Count();
                    if (specialfeecount > 0)
                    {
                        //特殊费用总和
                        decimal totalAmount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && a.ToDefault<int>("new_feetype") != 25).Sum(x => x.GetAttributeValue<decimal>("new_specialamount"));
                        //工单数量
                        int count = workorderCollection.Entities.Count;
                        //分摊金额
                        decimal aveAmount = Math.Round(totalAmount / count, 2);
                        for (int k = 0; k < workorderCollection.Entities.Count; k++)
                        {
                            int index = k;
                            try
                            {
                                Entity order = workorderCollection.Entities[index];
                                AdjustModel am = new AdjustModel();
                                am.new_srv_workorderid = order.Id.ToString();
                                am.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                                #region 特殊费用分摊
                                log.LogInformation($"特殊总费用:{totalAmount}");
                                //特殊费分摊
                                am.new_specialfeeshare = aveAmount;
                                #endregion
                                #region 计算费用
                                //加减项费用分摊
                                var new_specialfeeshare = am.new_specialfeeshare;
                                if (index == workorderCollection.Entities.Count - 1)
                                {
                                    decimal lastspecialfeeshare = totalAmount - sumSpecialexpense;
                                    log.LogInformation($"最后一条分摊：{lastspecialfeeshare}");
                                    //特殊费用分摊
                                    am.new_specialfeeshare = lastspecialfeeshare;
                                    //最后一条
                                    am.new_isend = true;
                                }
                                //特殊费用总和（截取两位小数）
                                sumSpecialexpense += am.new_specialfeeshare;
                                #endregion
                                amlist.Add(am);
                            }
                            catch (Exception ex)
                            {
                                Entity ent = new Entity("new_srv_expense_claim");
                                ent.Id = new_srv_expense_claim.Id;
                                ent["new_adjustmenterror"] = $"kpi批量操作异常：{ex.Message}，服务单id：{workorderCollection.Entities[index].Id.ToString()}";
                                OrganizationService.Update(ent);
                                iserror = true;
                                break;
                            }
                        }
                    }
                    //既没有维护服务单，也没有维护SKU，单独处理Local buy 费(Buysell)，Local buy 费(Buysell)也要分摊
                    var localbuyspecialfeecount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                   && !a.Contains("new_goodsfiles_id") && a.ToDefault<int>("new_feetype") == 25).Count();
                    if (localbuyspecialfeecount > 0)
                    {
                        //特殊费用总和
                        decimal localbuytotalAmount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && a.ToDefault<int>("new_feetype") == 25).Sum(x => x.GetAttributeValue<decimal>("new_specialamount"));
                        //工单数量
                        int count = workorderCollection.Entities.Count;
                        //分摊金额
                        decimal aveAmount = Math.Round(localbuytotalAmount / count, 2);
                        decimal sumspecialamount = 0;
                        for (int k = 0; k < workorderCollection.Entities.Count; k++)
                        {
                            decimal updatespecialfee = 0;
                            if (k == count - 1)
                            {
                                decimal lastspecialfee = localbuytotalAmount - sumspecialamount;
                                updatespecialfee = lastspecialfee;
                            }
                            else
                            {
                                updatespecialfee = aveAmount;
                                sumspecialamount += aveAmount;
                            }
                            var updateworkorder = amlist.Where(a => a.new_srv_workorderid == workorderCollection.Entities[k].Id.ToString()).FirstOrDefault();
                            if (updateworkorder != null)
                            {
                                updateworkorder.new_localbuyfeebuysell = updatespecialfee;
                            }
                            else
                            {
                                var order = workorderCollection.Entities.Where(a => a.Id == workorderCollection.Entities[k].Id).FirstOrDefault();
                                AdjustModel am = new AdjustModel();
                                am.new_srv_workorderid = order.Id.ToString();
                                am.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                                am.new_localbuyfeebuysell = updatespecialfee;
                                amlist.Add(am);
                            }
                        }
                    }
                    //维护了服务单的特殊费用(排除反冲，重维不在该结算单下的情况的服务单)
                    var specialfeebyworkorder = specialexpenselist.Entities.Where(a => a.Contains("new_workorder_id")
                    && workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id"))).ToList();
                    foreach (var item in specialfeebyworkorder)
                    {
                        var feeType = item.ToDefault<int>("new_feetype");
                        var updateworkorder = amlist.Where(a => a.new_srv_workorderid == item.ToDefault<Guid>("new_workorder_id").ToString()).FirstOrDefault();
                        if (updateworkorder != null)
                        {
                            if (feeType == 25)
                                updateworkorder.new_localbuyfeebuysell = item.ToDefault<decimal>("new_specialamount");
                            else
                                updateworkorder.new_specialfeeshare += item.ToDefault<decimal>("new_specialamount");
                        }
                        else
                        {
                            var order = workorderCollection.Entities.Where(a => a.Id == item.ToDefault<Guid>("new_workorder_id")).FirstOrDefault();
                            AdjustModel am = new AdjustModel();
                            am.new_srv_workorderid = order.Id.ToString();
                            am.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                            if (feeType == 25)
                                am.new_localbuyfeebuysell = item.ToDefault<decimal>("new_specialamount");
                            else
                                am.new_specialfeeshare = item.ToDefault<decimal>("new_specialamount");
                            amlist.Add(am);
                        }
                    }
                    //未维护服务单，维护了SKU，需要根据SKU平均分摊到工单上
                    var specialfeebysku = specialexpenselist.Entities.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                    foreach (var item in specialfeebysku)
                    {
                        var feeType = item.ToDefault<int>("new_feetype");
                        var workordergroup = workorderCollection.Entities.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item.ToDefault<Guid>("new_goodsfiles_id")).ToList();
                        if (workordergroup.Count > 0)
                        {
                            decimal aveageamount = Math.Round(item.ToDefault<decimal>("new_specialamount") / workordergroup.Count, 2);
                            decimal sumspecialfee = 0;
                            decimal updatespecialfee = 0;
                            for (int i = 0; i < workordergroup.Count; i++)
                            {
                                if (i == workordergroup.Count - 1)
                                {
                                    decimal lastspecialfee = item.ToDefault<decimal>("new_specialamount") - sumspecialfee;
                                    updatespecialfee = lastspecialfee;
                                }
                                else
                                {
                                    updatespecialfee = aveageamount;
                                    sumspecialfee += aveageamount;
                                }
                                var updateworkorder = amlist.Where(a => a.new_srv_workorderid == workordergroup[i].Id.ToString()).FirstOrDefault();
                                if (updateworkorder != null)
                                {
                                    if (feeType == 25)
                                        updateworkorder.new_localbuyfeebuysell = updatespecialfee;
                                    else
                                        updateworkorder.new_specialfeeshare += updatespecialfee;
                                }
                                else
                                {
                                    var order = workorderCollection.Entities.Where(a => a.Id == workordergroup[i].Id).FirstOrDefault();
                                    AdjustModel am = new AdjustModel();
                                    am.new_srv_workorderid = order.Id.ToString();
                                    am.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                                    if (feeType == 25)
                                        am.new_localbuyfeebuysell = updatespecialfee;
                                    else
                                        am.new_specialfeeshare = updatespecialfee;
                                    amlist.Add(am);
                                }
                            }
                        }
                    }
                }
                //计算维修劳务费（KPI）、检测费（KPI）、换机mark up费（Buysell）
                for (int i = 0; i < workorderCollection.Entities.Count; i++)
                {
                    //服务商kpi
                    decimal new_ratio = CalculateKpitype(serviceKpi, workorderCollection.Entities[i]);
                    Entity order = workorderCollection.Entities[i];
                    //预提费
                    var new_withholdingmoney = order.Contains("new_withholdingmoney") ? order.GetAttributeValue<decimal>("new_withholdingmoney") : 0M;
                    //维修劳务费
                    var new_repairfee = order.Contains("new_repairfee") ? order.GetAttributeValue<decimal>("new_repairfee") : 0M;
                    //维修劳务费*kpi系数
                    decimal repairfeeKPI = order.GetAttributeValue<decimal>("new_repairfee") * new_ratio;
                    //检测费
                    decimal new_inspect_fee = order.GetAttributeValue<decimal>("new_inspect_fee");
                    //检测费*kpi系数
                    decimal inspectFeeKPI = order.GetAttributeValue<decimal>("new_inspect_fee") * new_ratio;
                    // local buy markup费（Buysell）
                    var standard = expensestandard.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id") == workorderCollection.Entities[i].ToDefault<Guid>("new_repairprovider_id")).FirstOrDefault();
                    var markupratio = standard != null ? standard.ToDefault<decimal>("new_ratio") / 100 : 0;
                    var am = amlist.Where(a => a.new_srv_workorderid == workorderCollection.Entities[i].Id.ToString()).FirstOrDefault();
                    if (am != null)
                    {
                        am.new_repairfeekpi = Math.Round(repairfeeKPI, 2, MidpointRounding.AwayFromZero);
                        am.new_detectionapi = Math.Round(inspectFeeKPI, 2, MidpointRounding.AwayFromZero);
                        am.new_localbuyfeemarkupbuysell = Math.Round(am.new_localbuyfeebuysell * markupratio, 2, MidpointRounding.AwayFromZero);
                    }
                    else
                    {
                        am = new AdjustModel();
                        am.new_srv_workorderid = order.Id.ToString();
                        am.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                        am.new_repairfeekpi = Math.Round(repairfeeKPI, 2, MidpointRounding.AwayFromZero);
                        am.new_detectionapi = Math.Round(inspectFeeKPI, 2, MidpointRounding.AwayFromZero);
                        amlist.Add(am);
                    }
                    if (workorderCollection.Entities[i].Contains("new_workorder_costtable_id"))
                    {
                        am.new_workorder_costtable_id = workorderCollection.Entities[i].GetAttributeValue<EntityReference>("new_workorder_costtable_id").Id.ToString();
                    }
                    am.new_withholdingfeerepair = workorderCollection.Entities[i].GetAliasAttributeValue<decimal>("cost.new_withholdingfeerepair");
                    am.new_withholdingfeerecoilrepair = am.new_withholdingfeerepair * -1;
                    //总费用合计 = 预提金额+KPI劳务费-劳务费+检测费KPI-检测费+加减项费用 (Buysell工单需要加上换机local buy费（Buysell），换机mark up费（Buysell）)
                    am.new_settlementmoney = new_withholdingmoney + am.new_repairfeekpi - new_repairfee + am.new_detectionapi - new_inspect_fee + am.new_specialfeeshare + am.new_localbuyfeebuysell + am.new_localbuyfeemarkupbuysell + am.new_withholdingfeerecoilrepair;
                    am.new_changemoney = am.new_settlementmoney - new_withholdingmoney;
                    sumSettlementMoney += am.new_settlementmoney;
                    sumAmountKPI += Math.Round(repairfeeKPI, 2, MidpointRounding.AwayFromZero);
                    sumInspectFeeKPI += Math.Round(inspectFeeKPI, 2, MidpointRounding.AwayFromZero);

                }
                #region 更新结算单
                if (!iserror)
                {
                    //调差批次id
                    Guid adjuestbatchid = Guid.NewGuid();
                    #region 抛到servicebus
                    int index = 1;
                    log.LogInformation($"amlist.count：" + amlist.Count);
                    int batchSize = 100;
                    Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                    for (int i = 0; i < amlist.Count; i += batchSize)
                    {
                        var batchdata = amlist.Skip(i).Take(batchSize);
                        #region 按批次生成结算异步执行作业日志
                        Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                        settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", new_srv_expense_claim.Id);
                        settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                        settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                        settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                        var messagebody = batchdata.Select(a => a.new_srv_workorderid).ToList();
                        settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                        settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                        var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                        #endregion
                        AdjustModelRequest requestbody = new AdjustModelRequest();
                        requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                        requestbody.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                        requestbody.settlementType = 1;
                        requestbody.amlist = batchdata.ToList();
                        messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                        index++;
                    }
                    await busCommon.sendservicebus2(messages, ServiceBusConnectionString, SETT_ServiceBus_QueuesName, log);
                    #endregion
                    log.LogInformation("总金额：" + sumSettlementMoney);
                    decimal localbuyfeebuysellTotal = amlist.Sum(a => a.new_localbuyfeebuysell);
                    decimal localbuymarkupfeebuysellTotal = amlist.Sum(a => a.new_localbuyfeemarkupbuysell);
                    decimal new_withholdingfeerecoil = amlist.Sum(a => a.new_withholdingfeerecoilrepair);
                    //税率
                    decimal taxrate = new_srv_expense_claim.GetAttributeValue<decimal>("new_taxrate");
                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                    expenseClaimEntity.Id = new_srv_expense_claim.Id;
                    //维修劳务费
                    expenseClaimEntity["new_repairmoney"] = sumAmountKPI;
                    //检测费 调差后检测费 = 检测费kpi
                    expenseClaimEntity["new_totaldetection"] = sumInspectFeeKPI;
                    //检测费kpi
                    expenseClaimEntity["new_totaldetectionkpi"] = sumInspectFeeKPI;
                    //local buy 费(buysell)
                    expenseClaimEntity["new_localbuycostbuysell"] = localbuyfeebuysellTotal;
                    //local buy markup费(buysell)
                    expenseClaimEntity["new_localbuymarkupcostbuysell"] = localbuymarkupfeebuysellTotal;
                    //结算单版本
                    //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                    //总费用合计
                    expenseClaimEntity["new_totalcost"] = sumSettlementMoney;
                    expenseClaimEntity["new_withholdingfeerecoil"] = new_withholdingfeerecoil;
                    //结算费用=总费用*（1+税率）
                    expenseClaimEntity["new_approvalamount"] = sumSettlementMoney * (1 + taxrate);
                    //调差批次id
                    expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                    expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                    OrganizationService.Update(expenseClaimEntity);
                    log.LogInformation($"分摊结算单id：{new_srv_expense_claim.Id}处理已完成");
                    //foreach (var item in serviceKpi)
                    //{
                    //    item["new_kpistatecode"] = new OptionSetValue(3);
                    //    OrganizationService.Update(item);
                    //}
                }
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError(ex.ToString());
                Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                expenseClaimEntity.Id = new_srv_expense_claim.Id;
                expenseClaimEntity["new_adjustmenterror"] = ex.ToString();
                OrganizationService.Update(expenseClaimEntity);
            }
        }
        /// <summary>
        /// 物流结算单执行调差
        /// </summary>
        public async void SetdifferenceLogistics(Entity expense_claim, EntityCollection ratiocols, EntityCollection logisticsfeedetail, SettlementHelp settlementHelp, ILogger log)
        {
            try
            {
                ServriceBusCommon busCommon = new ServriceBusCommon(SETT_ServiceBus_SignatureKeyName, SETT_ServiceBus_SignatureKey, SETT_ServiceBus_Url, SETT_ServiceBus_QueuesName);
                //查询预提费（物流）结算标准
                EntityCollection yuticols = new EntityCollection();
                QueryExpression qe = new QueryExpression("new_srv_expense_standard");
                qe.ColumnSet = new ColumnSet("new_station_id", "new_amount", "new_transactioncurrency_id", "new_feetype", "new_stationservice_id", "new_stietype");
                qe.NoLock = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_transactioncurrency_id", ConditionOperator.Equal, expense_claim.ToDefault<Guid>("new_transactioncurrency_id"));//币种
                qe.Criteria.AddCondition(new ConditionExpression("new_feetype", ConditionOperator.In, new int[] { 41 }));//费用类型 = 预提费
                yuticols = CommonHelper.QueryExpressionPage(OrganizationService, qe);
                //需要推送到servicebus的集合
                List<AdjustModelStorage> amlist = new List<AdjustModelStorage>();
                //获取特殊费用
                EntityCollection specialexpenselist = GetSpecialExpense(expense_claim.Id.ToString(), log);
                //根据结算单查询工单费用
                EntityCollection workordercostcols = GetWorkorderCostLogistics(expense_claim.Id.ToString());
                log.LogInformation($"当前结算单关联工单费用数量{workordercostcols?.Entities?.Count}");
                //结算年份
                int new_year = expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月份
                int new_month = expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //服务商
                Guid serviceprovider_id = expense_claim.ToDefault<Guid>("new_srv_station_id");
                //其他费用
                decimal new_logisticsotherfee = 0m;
                Entity specialotherfee = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 14).FirstOrDefault();
                if (specialotherfee != null)
                    new_logisticsotherfee = specialotherfee.GetAttributeValue<decimal>("new_specialamount");
                var workorderlist = GetWorkorderSettlelogistics(expense_claim.Id);
                var logisticsfeedetailfee = settlementHelp.Getlogisticsfeedetailfee(serviceprovider_id.ToString(), logisticsfeedetail, yuticols);
                logisticsfeedetailfee.logisticsfeelist.Add("new_otherfeelogistics", new_logisticsotherfee);
                EntityCollection weightcols = settlementHelp.Createallocationweightdata(workorderlist, serviceprovider_id, ratiocols, logisticsfeedetailfee.logisticsfeelist, dateTime.Year, dateTime.Month, 20);
                var getweightlist = settlementHelp.Getallocationweightdata(weightcols.Entities.Select(a => a.Id).ToList());
                EntityCollection workordercostcols1 = settlementHelp.AssociateSettlement(workorderlist.Entities.ToList(), getweightlist, logisticsfeedetailfee.logisticsfeelist, 7);
                EntityCollection workordercostcolsmatch = new EntityCollection();//匹配到的集合
                List<workorder_costlogistics> costtableslist = new List<workorder_costlogistics>();
                List<string> logisticfeefields = logisticsfeedetailfee.logisticsfeelist.Keys.ToList();
                foreach (var costitem in workordercostcols.Entities)
                {
                    //预提费用
                    decimal new_withholdingmoneylogistics = costitem.GetAttributeValue<decimal>("new_withholdingmoneylogistics");
                    var workordercosten = workordercostcols1.Entities.Where(a => a.ToDefault<Guid>("new_workorder_settlement_id") == costitem.ToDefault<Guid>("new_workorder_settlement_id")
                    && a.Contains("new_workorder_settlement_id")).FirstOrDefault();
                    if (workordercosten != null)
                    {
                        workordercostcolsmatch.Entities.Add(workordercosten);
                        workorder_costlogistics costtable = new workorder_costlogistics();
                        costtable.new_name = costitem.GetAttributeValue<string>("new_name");
                        costtable.new_workorder_costtableid = costitem.Id;
                        if (costitem.Contains("new_workorder_settlement_id"))
                            costtable.new_workorder_settlement_id = costitem.GetAttributeValue<EntityReference>("new_workorder_settlement_id").Id;
                        costtable.new_expenseclaimidlogistics = expense_claim.Id;
                        costtable.new_apifee = workordercosten.GetAttributeValue<decimal>("new_apifee");
                        costtable.new_doafee = workordercosten.GetAttributeValue<decimal>("new_doafee");
                        costtable.new_adcfilefee = workordercosten.GetAttributeValue<decimal>("new_adcfilefee");
                        costtable.new_allocatefee = workordercosten.GetAttributeValue<decimal>("new_allocatefee");
                        costtable.new_storagefee = workordercosten.GetAttributeValue<decimal>("new_storagefee");
                        costtable.new_operatefee = workordercosten.GetAttributeValue<decimal>("new_operatefee");
                        costtable.new_warehousesmovingfee = workordercosten.GetAttributeValue<decimal>("new_warehousesmovingfee");
                        costtable.new_insurancefee = workordercosten.GetAttributeValue<decimal>("new_insurancefee");
                        costtable.new_operateservicefee = workordercosten.GetAttributeValue<decimal>("new_operateservicefee");
                        costtable.new_packing2fee = workordercosten.GetAttributeValue<decimal>("new_packing2fee");
                        costtable.new_magneticfee = workordercosten.GetAttributeValue<decimal>("new_magneticfee");
                        costtable.new_freightforwardingfee = workordercosten.GetAttributeValue<decimal>("new_freightforwardingfee");
                        costtable.new_tarifffee = workordercosten.GetAttributeValue<decimal>("new_tarifffee");
                        costtable.new_registrationfee = workordercosten.GetAttributeValue<decimal>("new_registrationfee");
                        costtable.new_servicechargefee = workordercosten.GetAttributeValue<decimal>("new_servicechargefee");
                        costtable.new_portsurchargefee = workordercosten.GetAttributeValue<decimal>("new_portsurchargefee");
                        costtable.new_customscommissionfee = workordercosten.GetAttributeValue<decimal>("new_customscommissionfee");
                        costtable.new_domesticfreightfee = workordercosten.GetAttributeValue<decimal>("new_domesticfreightfee");
                        costtable.new_foreigninspectionfee = workordercosten.GetAttributeValue<decimal>("new_foreigninspectionfee");
                        costtable.new_foreigntelexfee = workordercosten.GetAttributeValue<decimal>("new_foreigntelexfee");
                        costtable.new_foreigndeliveryfee = workordercosten.GetAttributeValue<decimal>("new_foreigndeliveryfee");
                        costtable.new_foreigncustomsfee = workordercosten.GetAttributeValue<decimal>("new_foreigncustomsfee");
                        costtable.new_foreigndeclarationfee = workordercosten.GetAttributeValue<decimal>("new_foreigndeclarationfee");
                        costtable.new_foreignpickfee = workordercosten.GetAttributeValue<decimal>("new_foreignpickfee");
                        costtable.new_foreignmiscellaneousfee = workordercosten.GetAttributeValue<decimal>("new_foreignmiscellaneousfee");
                        costtable.new_foreignthcfee = workordercosten.GetAttributeValue<decimal>("new_foreignthcfee");
                        costtable.new_transitfee = workordercosten.GetAttributeValue<decimal>("new_transitfee");
                        costtable.new_oceanfreightfee = workordercosten.GetAttributeValue<decimal>("new_oceanfreightfee");
                        costtable.new_foreigndocumentmakingfee = workordercosten.GetAttributeValue<decimal>("new_foreigndocumentmakingfee");
                        costtable.new_documenttransferfee = workordercosten.GetAttributeValue<decimal>("new_documenttransferfee");
                        costtable.new_airfreightfee = workordercosten.GetAttributeValue<decimal>("new_airfreightfee");
                        costtable.new_landfreightfee = workordercosten.GetAttributeValue<decimal>("new_landfreightfee");
                        costtable.new_timberingfee = workordercosten.GetAttributeValue<decimal>("new_timberingfee");
                        costtable.new_deliveryfee = workordercosten.GetAttributeValue<decimal>("new_deliveryfee");
                        costtable.new_containerloadfee = workordercosten.GetAttributeValue<decimal>("new_containerloadfee");
                        costtable.new_imofee = workordercosten.GetAttributeValue<decimal>("new_imofee");
                        costtable.new_packagingfee = workordercosten.GetAttributeValue<decimal>("new_packagingfee");
                        costtable.new_commissionfee = workordercosten.GetAttributeValue<decimal>("new_commissionfee");
                        costtable.new_agencyfee = workordercosten.GetAttributeValue<decimal>("new_agencyfee");
                        costtable.new_registeredparkingfee = workordercosten.GetAttributeValue<decimal>("new_registeredparkingfee");
                        costtable.new_bookingservicefee = workordercosten.GetAttributeValue<decimal>("new_bookingservicefee");
                        costtable.new_correctionsurchargefee = workordercosten.GetAttributeValue<decimal>("new_correctionsurchargefee");
                        costtable.new_factoryloadingfee = workordercosten.GetAttributeValue<decimal>("new_factoryloadingfee");
                        costtable.new_internationaltransportfee = workordercosten.GetAttributeValue<decimal>("new_internationaltransportfee");
                        costtable.new_domestictransportfee = workordercosten.GetAttributeValue<decimal>("new_domestictransportfee");
                        costtable.new_domesticportmiscellaneousfee = workordercosten.GetAttributeValue<decimal>("new_domesticportmiscellaneousfee");
                        costtable.new_exportgroundfee = workordercosten.GetAttributeValue<decimal>("new_exportgroundfee");
                        costtable.new_otherincidentalfee = workordercosten.GetAttributeValue<decimal>("new_otherincidentalfee");
                        costtable.new_goodsdeclarationfee = workordercosten.GetAttributeValue<decimal>("new_goodsdeclarationfee");
                        costtable.new_dangerousgoodssurchargefee = workordercosten.GetAttributeValue<decimal>("new_dangerousgoodssurchargefee");
                        costtable.new_hksurchargefee = workordercosten.GetAttributeValue<decimal>("new_hksurchargefee");
                        costtable.new_indonesiantaxfee = workordercosten.GetAttributeValue<decimal>("new_indonesiantaxfee");
                        costtable.new_unstackablesurchargefee = workordercosten.GetAttributeValue<decimal>("new_unstackablesurchargefee");
                        costtable.new_landingfee = workordercosten.GetAttributeValue<decimal>("new_landingfee");
                        costtable.new_milkrunfee = workordercosten.GetAttributeValue<decimal>("new_milkrunfee");
                        costtable.new_freighttruckfee = workordercosten.GetAttributeValue<decimal>("new_freighttruckfee");
                        costtable.new_freightfee = workordercosten.GetAttributeValue<decimal>("new_freightfee");
                        costtable.new_lastmilefee = workordercosten.GetAttributeValue<decimal>("new_lastmilefee");
                        costtable.new_indonesiasurchargefee = workordercosten.GetAttributeValue<decimal>("new_indonesiasurchargefee");
                        costtable.new_capitalservicefee = workordercosten.GetAttributeValue<decimal>("new_capitalservicefee");
                        costtable.new_indonesiastampfee = workordercosten.GetAttributeValue<decimal>("new_indonesiastampfee");
                        costtable.new_foreignhandlingfee = workordercosten.GetAttributeValue<decimal>("new_foreignhandlingfee");
                        costtable.new_foreigntradefee = workordercosten.GetAttributeValue<decimal>("new_foreigntradefee");
                        costtable.new_foreigndangerousoperatefee = workordercosten.GetAttributeValue<decimal>("new_foreigndangerousoperatefee");
                        costtable.new_documentationfee = workordercosten.GetAttributeValue<decimal>("new_documentationfee");
                        costtable.new_fuchrgfuefee = workordercosten.GetAttributeValue<decimal>("new_fuchrgfuefee");
                        costtable.new_essfee = workordercosten.GetAttributeValue<decimal>("new_essfee");
                        costtable.new_handwritinglabelfee = workordercosten.GetAttributeValue<decimal>("new_handwritinglabelfee");
                        costtable.new_dgcheckingfee = workordercosten.GetAttributeValue<decimal>("new_dgcheckingfee");
                        costtable.new_dglicencefee = workordercosten.GetAttributeValue<decimal>("new_dglicencefee");
                        costtable.new_racfee = workordercosten.GetAttributeValue<decimal>("new_racfee");
                        costtable.new_tollfee = workordercosten.GetAttributeValue<decimal>("new_tollfee");
                        costtable.new_dghandlingfee = workordercosten.GetAttributeValue<decimal>("new_dghandlingfee");
                        costtable.new_remotepickupfee = workordercosten.GetAttributeValue<decimal>("new_remotepickupfee");
                        costtable.new_9typeelectricfee = workordercosten.GetAttributeValue<decimal>("new_9typeelectricfee");
                        costtable.new_lssfee = workordercosten.GetAttributeValue<decimal>("new_lssfee");
                        costtable.new_surchargeoverfee = workordercosten.GetAttributeValue<decimal>("new_surchargeoverfee");
                        costtable.new_terminalfee = workordercosten.GetAttributeValue<decimal>("new_terminalfee");
                        costtable.new_vgmfee = workordercosten.GetAttributeValue<decimal>("new_vgmfee");
                        costtable.new_unboxfee = workordercosten.GetAttributeValue<decimal>("new_unboxfee");
                        costtable.new_exportspecialfee = workordercosten.GetAttributeValue<decimal>("new_exportspecialfee");
                        costtable.new_greenplusfee = workordercosten.GetAttributeValue<decimal>("new_greenplusfee");
                        costtable.new_returnshippingfee = workordercosten.GetAttributeValue<decimal>("new_returnshippingfee");
                        costtable.new_otherfeelogistics = workordercosten.GetAttributeValue<decimal>("new_otherfeelogistics");
                        costtable.new_withholdingfeelogistics = workordercosten.GetAttributeValue<decimal>("new_withholdingfeelogistics");
                        costtable.new_withholdingfeerecoillogistics = costtable.new_withholdingfeelogistics * -1;
                        decimal settlementmoneylogistics = 0;
                        logisticfeefields.ForEach(field =>
                        {
                            settlementmoneylogistics += workordercosten.GetAttributeValue<decimal>(field);
                        });
                        decimal withholdingmoneylogistics = costitem.GetAttributeValue<decimal>("new_withholdingmoneylogistics");
                        //反冲预提费
                        settlementmoneylogistics += costtable.new_withholdingfeerecoillogistics;
                        costtable.new_settlementmoneylogistics = settlementmoneylogistics;
                        costtable.new_changemoneylogistics = settlementmoneylogistics - withholdingmoneylogistics;
                        costtableslist.Add(costtable);
                    }
                }
                //调差批次id
                Guid adjuestbatchid = Guid.NewGuid();
                //更新结算单，推送service bus
                Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                int batchcount = 100;
                for (int j = 0; j < costtableslist.Count; j += batchcount)
                {
                    var batchdata = costtableslist.Skip(j).Take(batchcount).ToList();
                    #region 按批次生成结算异步执行作业日志
                    Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                    settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", expense_claim.Id);
                    settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                    settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                    settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                    var messagebody = batchdata.Select(a => a.new_workorder_costtableid).ToList();
                    settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                    settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                    var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                    #endregion
                    workorder_costlogisticsrequest logisticfeerequest = new workorder_costlogisticsrequest();
                    logisticfeerequest.new_srv_expense_claimid = expense_claim.Id.ToString();
                    logisticfeerequest.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                    logisticfeerequest.settlementType = 7;//物流
                    logisticfeerequest.logisticfeelist = batchdata;
                    logisticfeerequest.isadjust = true;
                    messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(logisticfeerequest)));
                }
                await busCommon.sendservicebus2(messages, ServiceBusExecuteWorkorderCostConnectionString, SET_ServiceBus_ExecuteWorkorderCostQueuesName, log);
                //税率
                decimal taxrate = expense_claim.GetAttributeValue<decimal>("new_taxrate");
                Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                expenseClaimEntity.Id = expense_claim.Id;
                decimal new_totalcost = 0;
                decimal new_withholdingfeerecoil = 0;
                foreach (var field in logisticfeefields)
                {
                    expenseClaimEntity[field] = workordercostcolsmatch.Entities.Sum(a => a.GetAttributeValue<decimal>(field));
                    new_totalcost += expenseClaimEntity.GetAttributeValue<decimal>(field);
                    //预提费（物流），预提反冲费（物流）计算
                    if (field == "new_withholdingfeelogistics")
                    {
                        expenseClaimEntity["new_withholdingfeerecoillogistics"] = workordercostcolsmatch.Entities.Sum(a => a.GetAttributeValue<decimal>(field) * -1);
                        new_totalcost += workordercostcolsmatch.Entities.Sum(a => a.GetAttributeValue<decimal>(field) * -1);
                        new_withholdingfeerecoil += workordercostcolsmatch.Entities.Sum(a => a.GetAttributeValue<decimal>(field) * -1);
                    }
                }
                //总费用合计
                expenseClaimEntity["new_totalcost"] = new_totalcost;
                expenseClaimEntity["new_withholdingfeerecoillogistics"] = new_withholdingfeerecoil;
                //结算费用=总费用*（1+税率）
                expenseClaimEntity["new_approvalamount"] = new_totalcost * (1 + taxrate);
                //调差批次id
                expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                OrganizationService.Update(expenseClaimEntity);
                log.LogInformation($"分摊结算单id：{expense_claim.Id}处理已完成");
            }
            catch (Exception ex)
            {
                Entity ent = new Entity("new_srv_expense_claim");
                ent.Id = expense_claim.Id;
                ent["new_adjustmenterror"] = $"调差操作异常：{ex.Message}";
                OrganizationService.Update(ent);
            }
        }
        /// <summary>
        /// 安装结算单执行调差
        /// </summary>
        /// <param name="expense_claim"></param>
        /// <param name="log"></param>
        public async void SetdifferenceInstall(Entity new_srv_expense_claim, ILogger log)
        {
            try
            {
                ServriceBusCommon busCommon = new ServriceBusCommon(SETT_ServiceBus_SignatureKeyName, SETT_ServiceBus_SignatureKey, SETT_ServiceBus_Url, SETT_ServiceBus_QueuesName);
                bool iserror = false;
                string adjustmenterror = "";//调差失败原因
                #region 初始化数据
                //需要推送到servicebus的集合
                List<AdjustModelInstall> amlist = new List<AdjustModelInstall>();
                //费用合计总和
                decimal sumSettlementMoney = 0M;
                //结算单服务商
                Guid new_srv_station_id = new_srv_expense_claim.GetAttributeValue<EntityReference>("new_srv_station_id").Id;
                //国家/地区
                Guid new_country_id = new_srv_expense_claim.ToDefault<Guid>("new_country_id");
                //结算年份
                int new_year = new_srv_expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月份
                int new_month = new_srv_expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //获取特殊费用
                EntityCollection specialexpenselist = GetSpecialExpense(new_srv_expense_claim.Id.ToString(), log);
                #endregion
                //根据结算单查询服务单
                EntityCollection workorderCollection = GetWorkorderInstall(new_srv_expense_claim.Id.ToString());
                //获取KPI明细
                //QueryExpression qe = new QueryExpression("new_station_countrykpiline");
                //qe.ColumnSet = new ColumnSet("new_category1_id", "new_category2_id", "new_category3_id", "new_ratio", "new_country_id", "new_serviceprovider_id");
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var link = new LinkEntity("new_station_countrykpiline", "new_station_actualkpi", "new_station_actualkpi_id", "new_station_actualkpiid", JoinOperator.Inner);
                //link.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
                //link.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
                //link.LinkCriteria.AddCondition("new_type", ConditionOperator.Equal, 5);//类型 = 安装
                //link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //qe.LinkEntities.Add(link);
                //EntityCollection kpidetails = OrganizationService.RetrieveMultiple(qe);
                //if (serviceKpi == null) continue;
                if (workorderCollection?.Entities?.Count > 0)
                {
                    log.LogInformation($"当前结算单关联服务单数量{workorderCollection?.Entities?.Count}");
                    var workorderidlist = workorderCollection.Entities.Select(a => a.Id).ToList();//服务单id集合
                    var repairprovider_idList = workorderCollection.Entities.Select(a => a.ToDefault<Guid>("new_servicestation_id")).Distinct().ToList();
                    //获取服务商kpi
                    List<Entity> serviceKpi = GetServiceKpi(repairprovider_idList, new_year, new_month);
                    #region 加减项未维护服务单，未维护SKU的，将加减项费用平均分摊赋值到对应服务单的其他费用上（反冲，重维的也需要平均分摊）
                    var specialfeelist = specialexpenselist.Entities.Where(a => (!a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id"))
                    || (a.Contains("new_workorder_id") && !workorderidlist.Contains(a.GetAttributeValue<EntityReference>("new_workorder_id").Id))).ToList();
                    decimal specialfee = specialfeelist.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    decimal aveamount = Math.Round(specialfee / workorderCollection.Entities.Count, 2);
                    decimal sumamount = 0m;
                    foreach (var item in workorderCollection.Entities)
                    {
                        decimal kpi = CalculateKpitype(serviceKpi, item);
                        int index = workorderCollection.Entities.IndexOf(item);
                        if (index == workorderCollection.Entities.Count - 1)
                        {
                            decimal lastamount = specialfee - sumamount;
                            AdjustModelInstall am = new AdjustModelInstall();
                            am.new_srv_workorderid = item.Id.ToString();
                            am.new_workorder_costtable_id = item.Contains("new_workorder_costtable_id") ? item.GetAttributeValue<EntityReference>("new_workorder_costtable_id").Id.ToString() : "";
                            am.new_specialfee = lastamount;
                            am.new_installfeekpi = Math.Round(item.GetAliasAttributeValue<decimal>("link.new_installfee") * kpi, 2, MidpointRounding.AwayFromZero);
                            am.new_withholdingfeeinstall = item.GetAliasAttributeValue<decimal>("link.new_withholdingfeeinstall");
                            am.new_withholdingmoneyinstall = item.GetAliasAttributeValue<decimal>("link.new_withholdingmoneyinstall");
                            am.new_fixedmonthfeeinstall = item.GetAliasAttributeValue<decimal>("link.new_fixedmonthfeeinstall");
                            amlist.Add(am);
                        }
                        else
                        {
                            AdjustModelInstall am = new AdjustModelInstall();
                            am.new_srv_workorderid = item.Id.ToString();
                            am.new_workorder_costtable_id = item.Contains("new_workorder_costtable_id") ? item.GetAttributeValue<EntityReference>("new_workorder_costtable_id").Id.ToString() : "";
                            am.new_specialfee = aveamount;
                            am.new_installfeekpi = Math.Round(item.GetAliasAttributeValue<decimal>("link.new_installfee") * kpi, 2);
                            am.new_withholdingfeeinstall = item.GetAliasAttributeValue<decimal>("link.new_withholdingfeeinstall");
                            am.new_withholdingmoneyinstall = item.GetAliasAttributeValue<decimal>("link.new_withholdingmoneyinstall");
                            am.new_fixedmonthfeeinstall = item.GetAliasAttributeValue<decimal>("link.new_fixedmonthfeeinstall");
                            sumamount += aveamount;
                            amlist.Add(am);
                        }
                    }
                    #endregion
                    #region 加减项填写了服务单号的，将费用赋值到对应服务单的其他费用上(不包括反冲，重维的)
                    if (!iserror)
                    {
                        var specialfeelistbyorder = specialexpenselist.Entities.Where(a => a.Contains("new_workorder_id") && workorderidlist.Contains(a.GetAttributeValue<EntityReference>("new_workorder_id").Id)).ToList();
                        foreach (var item in specialfeelistbyorder)
                        {
                            var adjustmodel = amlist.Where(a => a.new_srv_workorderid == item.GetAttributeValue<EntityReference>("new_workorder_id").Id.ToString()).FirstOrDefault();
                            if (adjustmodel != null)
                                adjustmodel.new_specialfee += item.GetAttributeValue<decimal>("new_specialamount");
                        }
                    }
                    #endregion
                    #region 加减项未填写服务单，填写了SKU的，将加减项费用平均分摊赋值到对应SKU服务单的其他费用上
                    if (!iserror)
                    {
                        var specialfeelistbysku = specialexpenselist.Entities.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                        foreach (var item in specialfeelistbysku)
                        {
                            var workorderlistbysku = workorderCollection.Entities.Where(a => a.Contains("new_goodsfiles_id")
                            && a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id == item.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id).ToList();
                            var workorderidbyskulist = workorderlistbysku.Select(a => a.Id.ToString()).ToList();
                            var amlist1 = amlist.Where(a => workorderidbyskulist.Contains(a.new_srv_workorderid)).ToList();
                            decimal specialfeebysku = item.GetAttributeValue<decimal>("new_specialamount");
                            decimal aveamountbysku = Math.Round(specialfeebysku / amlist1.Count, 2);
                            decimal sumamountbysku = 0m;
                            foreach (var am in amlist1)
                            {
                                int index = amlist1.IndexOf(am);
                                if (index == amlist1.Count - 1)
                                {
                                    decimal lastamount = specialfeebysku - sumamountbysku;
                                    am.new_specialfee += lastamount;
                                }
                                else
                                {
                                    am.new_specialfee += aveamountbysku;
                                    sumamountbysku += aveamountbysku;
                                }
                            }
                        }
                    }
                    #endregion
                }
                #region 更新结算单
                if (!iserror)
                {
                    //调差批次id
                    Guid adjuestbatchid = Guid.NewGuid();
                    #region 抛到servicebus
                    int index = 1;
                    log.LogInformation($"amlist.count：" + amlist.Count);
                    int batchSize = 100;
                    Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                    for (int i = 0; i < amlist.Count; i += batchSize)
                    {
                        var batchdata = amlist.Skip(i).Take(batchSize);
                        #region 按批次生成结算异步执行作业日志
                        Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                        settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", new_srv_expense_claim.Id);
                        settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                        settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                        settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                        var messagebody = batchdata.Select(a => a.new_srv_workorderid).ToList();
                        settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                        settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                        var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                        #endregion
                        AdjustModelInstallRequest requestbody = new AdjustModelInstallRequest();
                        requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                        requestbody.new_srv_expense_claimid = new_srv_expense_claim.Id.ToString();
                        requestbody.settlementType = 9;//结算单类型 = 安装
                        requestbody.amlist = batchdata.ToList();
                        messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                        index++;
                    }
                    await busCommon.sendservicebus2(messages, ServiceBusConnectionString, SETT_ServiceBus_QueuesName, log);
                    #endregion
                    decimal specialfeeTotal = amlist.Sum(a => a.new_specialfee);
                    decimal installfeekpiTotal = amlist.Sum(a => a.new_installfeekpi);
                    //税率
                    decimal taxrate = new_srv_expense_claim.GetAttributeValue<decimal>("new_taxrate");
                    //预提费
                    decimal withholdingfee = new_srv_expense_claim.GetAttributeValue<decimal>("new_withholdingfee");
                    sumSettlementMoney = specialfeeTotal + installfeekpiTotal;
                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                    expenseClaimEntity.Id = new_srv_expense_claim.Id;
                    //其他特殊费用
                    expenseClaimEntity["new_specialmoney"] = specialfeeTotal;
                    //安装劳务费
                    expenseClaimEntity["new_installfee"] = installfeekpiTotal;
                    //预提反冲费
                    expenseClaimEntity["new_withholdingfeerecoil"] = withholdingfee * -1;
                    //总费用合计
                    expenseClaimEntity["new_totalcost"] = sumSettlementMoney;
                    //结算费用=总费用*（1+税率）
                    expenseClaimEntity["new_approvalamount"] = sumSettlementMoney * (1 + taxrate);
                    //调差批次id
                    expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                    expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                    OrganizationService.Update(expenseClaimEntity);
                    log.LogInformation($"分摊结算单id：{new_srv_expense_claim.Id}处理已完成");
                }
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError(ex.ToString());
                Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                expenseClaimEntity.Id = new_srv_expense_claim.Id;
                expenseClaimEntity["new_adjustmenterror"] = ex.ToString();
                OrganizationService.Update(expenseClaimEntity);
            }
        }
        /// <summary>
        /// 高维工厂结算单调差
        /// </summary>
        /// <param name="expense_claim"></param>
        /// <param name="log"></param>
        public async void SetdifferenceHighdimensionalfactory(Entity expense_claim, ILogger log) 
        {
            ServriceBusCommon busCommon = new ServriceBusCommon(SETT_ServiceBus_SignatureKeyName, SETT_ServiceBus_SignatureKey, SETT_ServiceBus_Url, SETT_ServiceBus_QueuesName);
            try 
            {
                //结算年份
                int new_year = expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月份
                int new_month = expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //服务商
                Guid new_srv_station_id = expense_claim.GetAttributeValue<EntityReference>("new_srv_station_id").Id;
                //需要推送到servicebus的集合
                List<AdjustModelHighdimensionalfactory> amlist = new List<AdjustModelHighdimensionalfactory>();
                //查询结算单下高维工厂修整单明细
                var trimmorderdetaillist = GetExpenseTrimorderdetail(expense_claim.Id.ToString());
                //查询结算单下的加减项费用单
                var specialexpenselist = GetSpecialExpense(expense_claim.Id.ToString(), log);
                string adjustmenterror = "";
                bool iserror = false;
                //获取KPI明细
                QueryExpression qe = new QueryExpression("new_station_actualkpi");
                qe.ColumnSet = new ColumnSet("new_month", "new_type", "new_year", "new_ratio", "new_serviceprovider_id", "new_performancename");
                qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
                qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
                qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 6);//类型 = 高维工厂
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_serviceprovider_id", ConditionOperator.Equal, new_srv_station_id);
                EntityCollection highdimensionalfactorykpi = OrganizationService.RetrieveMultiple(qe);
                decimal kpi = 1.0m;
                if (highdimensionalfactorykpi.Entities.Count > 1)
                {
                    adjustmenterror = $"服务商 {new_srv_station_id.ToString()} 匹配到多条KPI";
                    iserror = true;
                }
                else if (highdimensionalfactorykpi.Entities.Count == 0)
                {
                    kpi = 1.0m;
                }
                else if (highdimensionalfactorykpi.Entities.Count == 1)
                {
                    kpi = highdimensionalfactorykpi.Entities.FirstOrDefault().GetAttributeValue<decimal>("new_ratio");
                }
                #region 高维工厂劳务费KPI计算
                foreach (var item in trimmorderdetaillist.Entities)
                {
                    AdjustModelHighdimensionalfactory am = new AdjustModelHighdimensionalfactory();
                    am.new_trimming_orderdetailid = item.Id.ToString();
                    am.new_expense_claim_id = expense_claim.Id.ToString();
                    am.new_high_dimensionalfee = item.GetAttributeValue<decimal>("new_high_dimensionalfee");
                    am.new_high_dimensionalfeekpi = Math.Round(am.new_high_dimensionalfee * kpi, 2);
                    am.new_withholdingmoney = item.GetAttributeValue<decimal>("new_withholdingmoney");
                    am.new_withholdingfeeHighdimensionalfactory = item.GetAttributeValue<decimal>("new_withholdingfeehigh");
                    am.new_withholdingfeerecoilHighdimensionalfactory = am.new_withholdingfeeHighdimensionalfactory * -1;
                    amlist.Add(am);
                }
                #endregion
                #region 维护SKU了，根据SKU平均分摊到对应SKU的修整单明细上
                var skuspecialexpenselist = specialexpenselist.Entities.Where(a => a.Contains("new_goodsfiles_id")).ToList();
                List<Guid> goodsfileslist = new List<Guid>();
                goodsfileslist.AddRange(skuspecialexpenselist.Select(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id));
                goodsfileslist.Distinct();
                foreach (var goodsfiles in goodsfileslist)
                {
                    decimal skuspecialamount = skuspecialexpenselist.Where(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id == goodsfiles).Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    var skutrimmorderdetail = trimmorderdetaillist.Entities.Where(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id == goodsfiles).ToList();
                    decimal specialamount = Math.Round(skuspecialamount / skutrimmorderdetail.Count, 2);
                    decimal sumspeciamlfee = 0;
                    for (int i = 0; i < skutrimmorderdetail.Count; i++)
                    {
                        if (i == skutrimmorderdetail.Count - 1)
                        {
                            decimal lastspecialamount = skuspecialamount - sumspeciamlfee;
                            var am = amlist.Where(a => a.new_trimming_orderdetailid == skutrimmorderdetail[i].Id.ToString()).FirstOrDefault();
                            if (am != null)
                            {
                                am.new_specialfeeshare = lastspecialamount;
                                am.new_settlementmoney = am.new_high_dimensionalfeekpi + am.new_specialfeeshare;
                            }
                            else
                            {
                                AdjustModelHighdimensionalfactory am1 = new AdjustModelHighdimensionalfactory();
                                am1.new_trimming_orderdetailid = skutrimmorderdetail[i].Id.ToString();
                                am1.new_expense_claim_id = expense_claim.Id.ToString();
                                am1.new_withholdingfeeHighdimensionalfactory = skutrimmorderdetail[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                                am1.new_withholdingfeerecoilHighdimensionalfactory = am1.new_withholdingfeeHighdimensionalfactory * -1;
                                am1.new_withholdingmoney = skutrimmorderdetail[i].GetAttributeValue<decimal>("new_withholdingmoney");
                                am1.new_specialfeeshare = lastspecialamount;
                                am1.new_settlementmoney = am1.new_high_dimensionalfeekpi + am1.new_specialfeeshare;
                                amlist.Add(am1);
                            }
                        }
                        else
                        {
                            var am = amlist.Where(a => a.new_trimming_orderdetailid == skutrimmorderdetail[i].Id.ToString()).FirstOrDefault();
                            if (am != null)
                            {
                                am.new_specialfeeshare = specialamount;
                                am.new_settlementmoney = am.new_high_dimensionalfeekpi + am.new_specialfeeshare;
                                sumspeciamlfee += specialamount;
                            }
                            else
                            {
                                AdjustModelHighdimensionalfactory am1 = new AdjustModelHighdimensionalfactory();
                                am1.new_trimming_orderdetailid = skutrimmorderdetail[i].Id.ToString();
                                am1.new_expense_claim_id = expense_claim.Id.ToString();
                                am1.new_withholdingfeeHighdimensionalfactory = skutrimmorderdetail[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                                am1.new_withholdingfeerecoilHighdimensionalfactory = am1.new_withholdingfeeHighdimensionalfactory * -1;
                                am1.new_withholdingmoney = skutrimmorderdetail[i].GetAttributeValue<decimal>("new_withholdingmoney");
                                am1.new_specialfeeshare = specialamount;
                                am1.new_settlementmoney = am1.new_high_dimensionalfeekpi + am1.new_specialfeeshare;
                                amlist.Add(am1);
                            }
                        }
                    }
                }
                #endregion
                #region 未维护SKU,汇总加减项费用单的特殊费用，平均分摊到高维工厂修整单明细上
                var noskuspecialexpenselist = specialexpenselist.Entities.Where(a => !a.Contains("new_goodsfiles_id")).ToList();
                decimal noskuspecialamount = noskuspecialexpenselist.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                decimal noskuaverageamount = Math.Round(noskuspecialamount / trimmorderdetaillist.Entities.Count, 2);
                decimal sumspeciamlfeenosku = 0;
                for (int i = 0; i < trimmorderdetaillist.Entities.Count; i++)
                {
                    if (i == trimmorderdetaillist.Entities.Count - 1)
                    {
                        decimal lastspecialamount = noskuspecialamount - sumspeciamlfeenosku;
                        var am = amlist.Where(a => a.new_trimming_orderdetailid == trimmorderdetaillist.Entities[i].Id.ToString()).FirstOrDefault();
                        if (am != null)
                        {
                            am.new_withholdingfeeHighdimensionalfactory = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                            am.new_withholdingfeerecoilHighdimensionalfactory = am.new_withholdingfeeHighdimensionalfactory * -1;
                            am.new_specialfeeshare += lastspecialamount;
                            am.new_settlementmoney = am.new_high_dimensionalfeekpi + am.new_specialfeeshare;
                        }
                        else
                        {
                            AdjustModelHighdimensionalfactory am1 = new AdjustModelHighdimensionalfactory();
                            am1.new_trimming_orderdetailid = trimmorderdetaillist.Entities[i].Id.ToString();
                            am1.new_withholdingfeeHighdimensionalfactory = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                            am1.new_withholdingfeerecoilHighdimensionalfactory = am1.new_withholdingfeeHighdimensionalfactory * -1;
                            am1.new_expense_claim_id = expense_claim.Id.ToString();
                            am1.new_withholdingmoney = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingmoney");
                            am1.new_specialfeeshare = lastspecialamount;
                            am1.new_settlementmoney = am1.new_high_dimensionalfeekpi + am1.new_specialfeeshare;
                            amlist.Add(am1);
                        }
                    }
                    else
                    {
                        var am = amlist.Where(a => a.new_trimming_orderdetailid == trimmorderdetaillist.Entities[i].Id.ToString()).FirstOrDefault();
                        if (am != null)
                        {
                            am.new_withholdingfeeHighdimensionalfactory = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                            am.new_withholdingfeerecoilHighdimensionalfactory = am.new_withholdingfeeHighdimensionalfactory * -1;
                            am.new_specialfeeshare += noskuaverageamount;
                            am.new_settlementmoney = am.new_high_dimensionalfeekpi + am.new_specialfeeshare;
                            sumspeciamlfeenosku += noskuaverageamount;
                        }
                        else
                        {
                            AdjustModelHighdimensionalfactory am1 = new AdjustModelHighdimensionalfactory();
                            am1.new_trimming_orderdetailid = trimmorderdetaillist.Entities[i].Id.ToString();
                            am1.new_withholdingfeeHighdimensionalfactory = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeehigh");
                            am1.new_withholdingfeerecoilHighdimensionalfactory = am1.new_withholdingfeeHighdimensionalfactory * -1;
                            am1.new_expense_claim_id = expense_claim.Id.ToString();
                            am1.new_withholdingmoney = trimmorderdetaillist.Entities[i].GetAttributeValue<decimal>("new_withholdingmoney");
                            am1.new_specialfeeshare = noskuaverageamount;
                            am1.new_settlementmoney = am1.new_high_dimensionalfeekpi + am1.new_specialfeeshare;
                            amlist.Add(am1);
                            sumspeciamlfeenosku += noskuaverageamount;
                        }
                    }
                }
                #endregion

                if (!iserror && string.IsNullOrWhiteSpace(adjustmenterror))
                {
                    #region 更新结算单
                    #region 抛到servicebus
                    log.LogInformation($"amlist.count：" + amlist.Count);
                    int index = 1;
                    //调差批次id
                    Guid adjuestbatchid = Guid.NewGuid();
                    Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                    int batchSize = 100;
                    for (int i = 0; i < amlist.Count; i += batchSize)
                    {
                        var batchdata = amlist.Skip(i).Take(batchSize);
                        #region 按批次生成结算异步执行作业日志
                        Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                        settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", expense_claim.Id);
                        settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                        settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                        settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                        var messagebody = batchdata.Select(a => a.new_trimming_orderdetailid).ToList();
                        settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                        settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                        var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                        #endregion
                        AdjustModelHighdimensionalfactoryRequest requestbody = new AdjustModelHighdimensionalfactoryRequest();
                        requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                        requestbody.new_srv_expense_claimid = expense_claim.Id.ToString();
                        requestbody.settlementType = 10;//结算单类型 = 高维工厂
                        requestbody.amlist = batchdata.ToList();
                        messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                        index++;
                    }
                    await busCommon.sendservicebus2(messages, ServiceBusConnectionString, SETT_ServiceBus_QueuesName, log);
                    #endregion
                    //加减项分摊费用
                    decimal specialfeeshareTotal = amlist.Sum(a => a.new_specialfeeshare);
                    decimal withholdingmoneyTotal = amlist.Sum(a => a.new_withholdingmoney);
                    decimal new_withholdingfee = amlist.Sum(a => a.new_withholdingfeeHighdimensionalfactory);
                    decimal sum_high_dimensionalfeekpi = amlist.Sum(a => a.new_high_dimensionalfeekpi);
                    decimal new_withholdingfeerecoil = new_withholdingfee * -1;
                    decimal sumSettlementMoney = sum_high_dimensionalfeekpi + specialfeeshareTotal + new_withholdingfee + new_withholdingfeerecoil;
                    //税率
                    decimal taxrate = expense_claim.GetAttributeValue<decimal>("new_taxrate");
                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                    expenseClaimEntity.Id = expense_claim.Id;
                    //结算单版本
                    //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                    //总费用合计
                    expenseClaimEntity["new_totalcost"] = sumSettlementMoney;
                    expenseClaimEntity["new_high_dimensionalfeekpi"] = sum_high_dimensionalfeekpi;
                    expenseClaimEntity["new_withholdingfeerecoil"] = new_withholdingfeerecoil;
                    //结算费用=总费用*（1+税率）
                    expenseClaimEntity["new_approvalamount"] = sumSettlementMoney * (1 + taxrate);
                    //调差批次id
                    expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                    expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                    OrganizationService.Update(expenseClaimEntity);
                    log.LogInformation($"分摊结算单id：{expense_claim.Id}处理已完成");
                    #endregion
                }
                else
                {
                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                    expenseClaimEntity.Id = expense_claim.Id;
                    expenseClaimEntity["new_adjustmenterror"] = adjustmenterror;
                    OrganizationService.Update(expenseClaimEntity);
                    log.LogInformation($"分摊结算单id：{expense_claim.Id}处理已完成");
                }
            } catch (Exception ex) 
            {
                log.LogInformation($"高维工厂结算单调差异常：{ex.Message}");
                Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                expenseClaimEntity.Id = expense_claim.Id;
                expenseClaimEntity["new_adjustmenterror"] = ex.Message;
                OrganizationService.Update(expenseClaimEntity);
            }
        }

        /// <summary>
        /// 迈创结费调差
        /// </summary>
        /// <param name="log"></param>
        public async Task SetDifferenceMaitrox(ILogger log)
        {
            try
            {
                ServriceBusCommon busCommon = new ServriceBusCommon("", "", "", SETT_ServiceBus_MaitroxQueuesName);
                //查询结算标准
                EntityCollection expensestandard = GetExpenseStandard();
                //查询结算单
                QueryExpression query = new QueryExpression("new_srv_expense_claim");
                query.NoLock = true;
                query.ColumnSet = new ColumnSet("new_srv_station_id", "new_year", "new_month", "new_withholdingmoney", "new_totalcost", "new_approvalamount", "new_sparepartscost", "new_partservicecost", "new_country_id", "new_taxrate");
                query.Criteria.AddCondition("new_adjuststatus", ConditionOperator.Equal, 2);
                query.Criteria.AddCondition("new_businesstype", ConditionOperator.Equal, 3);//类型 = 备件
                EntityCollection new_srv_expense_claim = OrganizationService.RetrieveMultiple(query);
                if (new_srv_expense_claim?.Entities?.Count > 0)
                {
                    log.LogInformation($"共有{new_srv_expense_claim?.Entities?.Count}条迈创结算单");
                    //判断计算费用过程中是否出错
                    for (int j = 0; j < new_srv_expense_claim.Entities.Count; j++)
                    {
                        try
                        {
                            bool iserror = false;
                            string adjustmenterror = "";//调差失败原因
                            #region 初始化数据
                            //需要推送到servicebus的集合
                            List<AdjustModelMaitrox> amlist = new List<AdjustModelMaitrox>();
                            //结算单服务商
                            Guid new_srv_station_id = new_srv_expense_claim[j].GetAttributeValue<EntityReference>("new_srv_station_id").Id;
                            //国家/地区
                            Guid new_country_id = new_srv_expense_claim[j].ToDefault<Guid>("new_country_id");
                            //结算年份
                            int new_year = new_srv_expense_claim[j].GetAttributeValue<OptionSetValue>("new_year").Value;
                            //结算月份
                            int new_month = new_srv_expense_claim[j].GetAttributeValue<OptionSetValue>("new_month").Value;
                            //预提金额
                            decimal new_withholdingmoney = new_srv_expense_claim[j].ToDefault<decimal>("new_withholdingmoney");
                            //获取特殊费用
                            EntityCollection specialexpenselist = GetSpecialExpenseMaitrox(new_srv_expense_claim.Entities[j].Id.ToString(), log);
                            //获取服务商kpi
                            //Entity serviceKpi = GetServiceKpiMaitrox(new_year, new_month);
                            bool isidn = CheckCountryIsIDN(new_country_id);
                            #endregion
                            //if (serviceKpi == null) continue;
                            //获取换机local buy费用
                            var localbuyexpense = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 19);
                            //获取客户退款
                            var customerrefund = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 21);
                            //获取生态链品类回购费
                            var ecosystemcategorybuybackfee = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 22);
                            //获取迈创其他费用
                            var othermiscellaneouscharges = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 23);
                            //获取物流费用
                            var markuplogisticsfee = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 4);
                            //获取仓储费用
                            var warehousingfee = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 20);
                            //换机local buy费-迈创责
                            var localbuyexpenseMaitrox = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 24);
                            //高维费用
                            var refurbishFee = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 26);
                            //根据结算单查询服务单
                            EntityCollection workorderCollection = GetWorkorderByExpenseMaitrox(new_srv_expense_claim.Entities[j].Id.ToString());
                            if (workorderCollection?.Entities?.Count > 0)
                            {
                                log.LogInformation($"当前结算单关联服务单数量{workorderCollection?.Entities?.Count}");
                                //获取国家KPI明细
                                QueryExpression qe = new QueryExpression("new_station_countrykpiline");
                                qe.ColumnSet = new ColumnSet("new_category1_id", "new_category2_id", "new_category3_id", "new_ratio", "new_country_id", "new_serviceprovider_id");
                                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                var link = new LinkEntity("new_station_countrykpiline", "new_station_actualkpi", "new_station_actualkpi_id", "new_station_actualkpiid", JoinOperator.Inner);
                                link.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
                                link.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
                                link.LinkCriteria.AddCondition("new_type", ConditionOperator.Equal, 2);//类型 = 备件
                                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                qe.LinkEntities.Add(link);
                                EntityCollection kpidetails = OrganizationService.RetrieveMultiple(qe);
                                for (int k = 0; k < workorderCollection.Entities.Count; k++)
                                {
                                    int index = k;
                                    try
                                    {
                                        Entity order = workorderCollection.Entities[index];
                                        AdjustModelMaitrox am = new AdjustModelMaitrox();
                                        am.new_srv_workorderid = order.Id.ToString();
                                        am.new_srv_expense_claimid = new_srv_expense_claim[j].Id.ToString();
                                        am.new_fixedservicefee = order.ToDefault<decimal>("new_fixedservicefee");
                                        am.new_capitalinterestexpense = order.ToDefault<decimal>("new_capitalinterestexpense");
                                        am.new_minimumworkorderfee = order.ToDefault<decimal>("new_minimumworkorderfee");
                                        am.new_directshippingcost = order.ToDefault<decimal>("new_directshippingcost");
                                        am.new_withholdingfeemaitrox = order.ToDefault<decimal>("new_withholdingfeemaitrox");
                                        am.new_withholdingfeerecoil = am.new_withholdingfeemaitrox * -1;//预提反冲费 = 预提费 * -1
                                        #region 备件服务费KPI、换机local buy费、客户退款、仓储费、物流费、迈创其他费用、换机local buy费 - 迈创责，高维费用
                                        //备件服务费
                                        decimal partservicecost = workorderCollection.Entities[index].ToDefault<decimal>("new_partservicecost");
                                        am.new_withholdingmoney = workorderCollection.Entities[index].ToDefault<decimal>("new_withholdingmoneymaitrox");
                                        am.new_sparepartscost = workorderCollection.Entities[index].ToDefault<decimal>("new_sparepartscost");
                                        am.new_partservicecost = partservicecost;
                                        var kpidetaillist = GetStationKPIdetail(new_srv_station_id, new_country_id, order, kpidetails);
                                        //如果找不到满足条件的 kpi 数据，则设置kpi值为1
                                        if (kpidetaillist.Count == 0)
                                        {
                                            Entity kpidetail = new Entity("new_station_countrykpiline");
                                            kpidetail["new_ratio"] = 1m;
                                            kpidetaillist.Add(kpidetail);
                                        }
                                        if (kpidetaillist.Count == 0)
                                        {
                                            //var new_category1_idname = order.Contains("new_category1_id") ? order.ToEr("new_category1_id").Name : "";
                                            //adjustmenterror = $"服务单{order.ToDefault<string>("new_name")}{new_category1_idname}未关联到KPI";
                                            //break;
                                        }
                                        else if (kpidetaillist.Count > 1)
                                        {
                                            var new_category1_idname = order.Contains("new_category1_id") ? order.ToEr("new_category1_id").Name : "";
                                            adjustmenterror = $"服务单{order.ToDefault<string>("new_name")}{new_category1_idname}关联到多条KPI";
                                            break;
                                        }
                                        else
                                        {
                                            var kpidetail = kpidetaillist.FirstOrDefault();
                                            am.kpivalue = kpidetail.ToDefault<decimal>("new_ratio");
                                            am.new_partservicecostkpi = Math.Round(partservicecost * kpidetail.ToDefault<decimal>("new_ratio"), 2);
                                            log.LogInformation($"开始修改第{index}条,工单号：{order.GetAttributeValue<string>("new_name")},KPI:{kpidetail?.ToDefault<decimal>("new_ratio")}");
                                        }
                                        if (index == workorderCollection.Entities.Count - 1)
                                        {
                                            am.new_isend = true;
                                        }
                                        //换机local buy
                                        Entity localbuyspecialexpense = localbuyexpense.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (localbuyspecialexpense != null)
                                        {
                                            am.new_localbuyreplacementcost = localbuyspecialexpense.ToDefault<decimal>("new_specialamount");
                                            //换机markup 费
                                            var markupstandard = GetLocalbuymarkupstandard(new_country_id, expensestandard, order);
                                            if (markupstandard != null)
                                            {
                                                am.new_markupreplacementcost = localbuyspecialexpense.ToDefault<decimal>("new_specialamount") * markupstandard.ToDefault<decimal>("new_ratio") / 100;
                                            }
                                        }
                                        //客户退款
                                        Entity customerrefundexpense = customerrefund.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (customerrefundexpense != null)
                                        {
                                            am.new_customerrefund = customerrefundexpense.ToDefault<decimal>("new_specialamount");
                                        }
                                        //生态链品类回购费
                                        Entity ecosystemcategorybuybackexpense = ecosystemcategorybuybackfee.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (ecosystemcategorybuybackexpense != null)
                                        {
                                            am.new_ecosystemcategorybuybackfee = ecosystemcategorybuybackexpense.ToDefault<decimal>("new_specialamount");
                                        }
                                        //物流费
                                        Entity markuplogisticsexpense = markuplogisticsfee.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (markuplogisticsexpense != null)
                                        {
                                            //费用类型 = 物流费markup的结算标准
                                            //var logisticsstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 23 && a.ToDefault<Guid>("new_country_id") == new_country_id).FirstOrDefault();
                                            //if (logisticsstandard != null)
                                            //{
                                            //    am.new_markuplogisticsfee = markuplogisticsexpense.ToDefault<decimal>("new_specialamount") * (1 + logisticsstandard.ToDefault<decimal>("new_ratio") / 100);
                                            //}
                                            //物流费不再乘markup百分比 by p-songyongxiang 20250409
                                            am.new_markuplogisticsfee = markuplogisticsexpense.ToDefault<decimal>("new_specialamount");
                                        }
                                        //仓储费
                                        Entity warehousingexpense = warehousingfee.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (warehousingexpense != null)
                                        {
                                            if (isidn)
                                            {
                                                am.new_warehousingfee = warehousingexpense.ToDefault<decimal>("new_specialamount") * am.kpivalue;
                                            }
                                            else
                                            {
                                                am.new_warehousingfee = warehousingexpense.ToDefault<decimal>("new_specialamount");
                                            }
                                        }
                                        //迈创其他费用
                                        Entity othermiscellaneousexpense = othermiscellaneouscharges.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (othermiscellaneousexpense != null)
                                        {
                                            am.new_othermiscellaneouscharges = othermiscellaneousexpense.ToDefault<decimal>("new_specialamount");
                                        }
                                        //换机local buy 费-迈创责
                                        Entity localbuyMaitroxexpense = localbuyexpenseMaitrox.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (localbuyMaitroxexpense != null)
                                        {
                                            Entity standard = CalcPartsServiceCost(order, new_country_id, expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 20).ToList(), OrganizationService);
                                            am.new_partservicecost = standard != null ? standard.ToDefault<decimal>("new_amount") : 0;
                                            am.new_partservicecostkpi = am.new_partservicecost * am.kpivalue;
                                        }
                                        //高维费用
                                        Entity refurbishFeeexpense = refurbishFee.Where(a => a.ToDefault<Guid>("new_workorder_id") == order.Id).FirstOrDefault();
                                        if (refurbishFeeexpense != null)
                                        {
                                            am.new_refurbishfee = refurbishFeeexpense.ToDefault<decimal>("new_specialamount");
                                        }
                                        #endregion
                                        amlist.Add(am);
                                    }
                                    catch (Exception ex)
                                    {
                                        Entity ent = new Entity("new_srv_expense_claim");
                                        ent.Id = new_srv_expense_claim[j].Id;
                                        ent["new_adjustmenterror"] = $"kpi批量操作异常：{ex.Message}，服务单id：{workorderCollection.Entities[index].Id.ToString()}";
                                        OrganizationService.Update(ent);
                                        iserror = true;
                                        break;
                                    }
                                }
                            }
                            if (string.IsNullOrWhiteSpace(adjustmenterror))
                            {
                                #region 加减项费用单维护了商品SKU，未维护服务单
                                //换机local buy 特殊费用
                                List<Entity> localbuyisskuspecialexpenseList = localbuyexpense.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //客户退款 特殊费用
                                List<Entity> customerrefundspecialexpenseList = customerrefund.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //生态链品类回购费 特殊费用
                                List<Entity> ecosystemcategorybuybackfeespecialexpenseList = ecosystemcategorybuybackfee.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //迈创其他费用 特殊费用
                                List<Entity> othermiscellaneouschargespecialexpenseList = othermiscellaneouscharges.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //物流markup费用 特殊费用
                                List<Entity> logisticspecialexpenseList = markuplogisticsfee.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //固定仓储费 特殊费用
                                List<Entity> warehousingfeespecialexpenseList = warehousingfee.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                //高维费用 特殊费用
                                List<Entity> refurbishfeespecialexpenseList = refurbishFee.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                                List<Guid> goodsfileslist = new List<Guid>();
                                goodsfileslist.AddRange(localbuyisskuspecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(customerrefundspecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(ecosystemcategorybuybackfeespecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(othermiscellaneouschargespecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(logisticspecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(warehousingfeespecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist.AddRange(refurbishfeespecialexpenseList.Select(a => a.ToDefault<Guid>("new_goodsfiles_id")));
                                goodsfileslist = goodsfileslist.Distinct().ToList();
                                EntityCollection workorderOnsku = null;
                                if (goodsfileslist.Count > 0)
                                {
                                    workorderOnsku = GetWorkorderBysku(goodsfileslist, new_srv_expense_claim.Entities[j].Id);
                                    if (workorderOnsku.Entities.Count > 0)
                                    {
                                        foreach (var item in goodsfileslist)
                                        {
                                            var workorderlist = workorderOnsku.Entities.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).ToList();
                                            if (workorderlist.Count > 0)
                                            {
                                                decimal localbuyamount = 0;
                                                decimal avelocalbuyamount = 0;
                                                decimal sumlocalbuyamount = 0;
                                                decimal customerrefundamount = 0;
                                                decimal avecustomerrefundamount = 0;
                                                decimal sumcustomerrefundamount = 0;
                                                decimal ecosystemcategorybuybackfeeamount = 0;
                                                decimal aveecosystemcategorybuybackfeeamount = 0;
                                                decimal sumecosystemcategorybuybackfeeamount = 0;
                                                decimal othermiscellaneouschargesamount = 0;
                                                decimal aveothermiscellaneouschargesamount = 0;
                                                decimal sumothermiscellaneouschargesamount = 0;
                                                decimal logisticamount = 0;
                                                decimal avelogisticamount = 0;
                                                decimal sumlogisticamount = 0;
                                                decimal warehousingfeeamount = 0;
                                                decimal avewarehousingfeeamount = 0;
                                                decimal sumwarehousingfeeamount = 0;
                                                decimal refurbishfeeamount = 0;
                                                decimal averefurbishfeeamount = 0;
                                                decimal sumrefurbishfeeamount = 0;
                                                if (localbuyisskuspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var localbuyisskuspecialexpense = localbuyisskuspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    localbuyamount = localbuyisskuspecialexpense.ToDefault<decimal>("new_specialamount");
                                                    avelocalbuyamount = Math.Round(localbuyamount / workorderlist.Count, 2);
                                                }
                                                if (customerrefundspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var customerrefundspecialexpense = customerrefundspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    customerrefundamount = customerrefundspecialexpense.ToDefault<decimal>("new_specialamount");
                                                    avecustomerrefundamount = Math.Round(customerrefundamount / workorderlist.Count, 2);
                                                }
                                                if (ecosystemcategorybuybackfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var ecosystemcategorybuybackfeespecialexpense = ecosystemcategorybuybackfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    ecosystemcategorybuybackfeeamount = ecosystemcategorybuybackfeespecialexpense.ToDefault<decimal>("new_specialamount");
                                                    aveecosystemcategorybuybackfeeamount = Math.Round(ecosystemcategorybuybackfeeamount / workorderlist.Count, 2);
                                                }
                                                if (othermiscellaneouschargespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var othermiscellaneouschargespecialexpense = othermiscellaneouschargespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    othermiscellaneouschargesamount = othermiscellaneouschargespecialexpense.ToDefault<decimal>("new_specialamount");
                                                    aveothermiscellaneouschargesamount = Math.Round(othermiscellaneouschargesamount / workorderlist.Count);
                                                }
                                                if (logisticspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var logisticspecialexpense = logisticspecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    logisticamount = logisticspecialexpense.ToDefault<decimal>("new_specialamount");
                                                    //费用类型 = 物流费markup的结算标准
                                                    //var logisticsstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 23 && a.ToDefault<Guid>("new_country_id") == new_country_id).FirstOrDefault();
                                                    //if (logisticsstandard != null)
                                                    //{
                                                    //    logisticamount = Math.Round(logisticamount * (1 + logisticsstandard.ToDefault<decimal>("new_ratio") / 100), 2);
                                                    //}
                                                    //物流费不再乘markup百分比 by p-songyongxiang 20250409
                                                    avelogisticamount = Math.Round(logisticamount / workorderlist.Count, 2);
                                                }
                                                if (warehousingfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var warehousingfeespecialexpense = warehousingfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    warehousingfeeamount = warehousingfeespecialexpense.ToDefault<decimal>("new_specialamount");
                                                    avewarehousingfeeamount = Math.Round(warehousingfeeamount / workorderlist.Count, 2);
                                                }
                                                if (refurbishfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault() != null)
                                                {
                                                    var refurbishfeespecialexpense = refurbishfeespecialexpenseList.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item).FirstOrDefault();
                                                    refurbishfeeamount = refurbishfeespecialexpense.ToDefault<decimal>("new_specialamount");
                                                    averefurbishfeeamount = Math.Round(refurbishfeeamount / workorderlist.Count, 2);
                                                }
                                                for (int i = 0; i < workorderlist.Count; i++)
                                                {
                                                    //换机markup 费结算标准
                                                    var markupstandard = GetLocalbuymarkupstandard(new_country_id, expensestandard, workorderlist[i]);
                                                    var am = amlist.Where(a => a.new_srv_workorderid == workorderlist[i].Id.ToString()).FirstOrDefault();
                                                    if (am != null)
                                                    {
                                                        if (i == workorderlist.Count - 1)
                                                        {
                                                            am.new_localbuyreplacementcost += (localbuyamount - sumlocalbuyamount);
                                                            if (markupstandard != null)
                                                            {
                                                                am.new_markupreplacementcost += (am.new_localbuyreplacementcost * markupstandard.ToDefault<decimal>("new_ratio") / 100);
                                                            }
                                                            am.new_customerrefund += (customerrefundamount - sumcustomerrefundamount);
                                                            am.new_ecosystemcategorybuybackfee += (ecosystemcategorybuybackfeeamount - sumecosystemcategorybuybackfeeamount);
                                                            am.new_othermiscellaneouscharges += (othermiscellaneouschargesamount - sumothermiscellaneouschargesamount);
                                                            am.new_markuplogisticsfee += (logisticamount - sumlogisticamount);
                                                            am.new_warehousingfee += (warehousingfeeamount - sumwarehousingfeeamount);
                                                            am.new_refurbishfee += (refurbishfeeamount - sumrefurbishfeeamount);
                                                        }
                                                        else
                                                        {
                                                            am.new_localbuyreplacementcost += avelocalbuyamount;
                                                            if (markupstandard != null)
                                                            {
                                                                am.new_markupreplacementcost += (am.new_localbuyreplacementcost * markupstandard.ToDefault<decimal>("new_ratio") / 100);
                                                            }
                                                            am.new_customerrefund += avecustomerrefundamount;
                                                            am.new_ecosystemcategorybuybackfee += aveecosystemcategorybuybackfeeamount;
                                                            am.new_othermiscellaneouscharges += aveothermiscellaneouschargesamount;
                                                            am.new_markuplogisticsfee += avelogisticamount;
                                                            am.new_warehousingfee += avewarehousingfeeamount;
                                                            am.new_refurbishfee += averefurbishfeeamount;
                                                            sumcustomerrefundamount += avecustomerrefundamount;
                                                            sumlocalbuyamount += avelocalbuyamount;
                                                            sumecosystemcategorybuybackfeeamount += aveecosystemcategorybuybackfeeamount;
                                                            sumlogisticamount += avelogisticamount;
                                                            sumothermiscellaneouschargesamount += aveothermiscellaneouschargesamount;
                                                            sumwarehousingfeeamount += avewarehousingfeeamount;
                                                            sumrefurbishfeeamount += averefurbishfeeamount;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                #endregion

                                #region 加减项费用既没有维护服务单，也没有维护商品SKU，权重分摊金额（物流费，仓储费，客户退款，其他特殊费用,生态链品类回购费按照平均分摊）
                                var markuplogisticsfeeexpense = markuplogisticsfee.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                //物流费
                                if (markuplogisticsfeeexpense != null)
                                {
                                    //费用类型 = 物流费markup的结算标准
                                    //var logisticsstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 23 && a.ToDefault<Guid>("new_country_id") == new_country_id).FirstOrDefault();
                                    //if (logisticsstandard != null)
                                    //{
                                    //    decimal specialexpensefee = Math.Round(markuplogisticsfeeexpense.ToDefault<decimal>("new_specialamount") * (1 + logisticsstandard.ToDefault<decimal>("new_ratio") / 100), 2);
                                    //    Weightedallocationamount(20, specialexpensefee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                    //}
                                    decimal specialexpensefee = Math.Round(markuplogisticsfeeexpense.ToDefault<decimal>("new_specialamount") /** (1 + logisticsstandard.ToDefault<decimal>("new_ratio") / 100)*/, 2);
                                    Weightedallocationamount(20, specialexpensefee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                //仓储费
                                var warehousingsepcialexpense = warehousingfee.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                if (warehousingsepcialexpense != null)
                                {
                                    decimal warehousingsepcialfee = warehousingsepcialexpense.ToDefault<decimal>("new_specialamount");
                                    Weightedallocationamount(10, warehousingsepcialfee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                //客户退款
                                var customerrefundnoskuspecialexpense = customerrefund.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                if (customerrefundnoskuspecialexpense != null)
                                {
                                    decimal customerrefundfee = customerrefundnoskuspecialexpense.ToDefault<decimal>("new_specialamount");
                                    Weightedallocationamount(50, customerrefundfee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                //迈创其他费用
                                var otherspecialexpense = othermiscellaneouscharges.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                if (otherspecialexpense != null)
                                {
                                    decimal otherspecialfee = otherspecialexpense.ToDefault<decimal>("new_specialamount");
                                    Weightedallocationamount(60, otherspecialfee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                //生态链品类回购费
                                var ecosystemcategorybuybackspecialexpense = ecosystemcategorybuybackfee.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                if (ecosystemcategorybuybackspecialexpense != null)
                                {
                                    decimal ecospecialfee = ecosystemcategorybuybackspecialexpense.ToDefault<decimal>("new_specialamount");
                                    Weightedallocationamount(70, ecospecialfee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                //高维费用
                                var highrefurbishfeespecialexpense = refurbishFee.Where(a => !a.Contains("new_workorder_id") && !a.Contains("new_goodsfiles_id")).FirstOrDefault();
                                if (highrefurbishfeespecialexpense != null)
                                {
                                    decimal refurbishspecialfee = highrefurbishfeespecialexpense.ToDefault<decimal>("new_specialamount");
                                    Weightedallocationamount(80, refurbishspecialfee, expensestandard, workorderCollection, amlist, new_country_id, isidn);
                                }
                                #endregion
                                #region 更新结算单
                                if (!iserror)
                                {
                                    //调差批次id
                                    Guid adjuestbatchid = Guid.NewGuid();
                                    #region 抛到servicebus
                                    int index = 1;
                                    log.LogInformation($"amlist.count：" + amlist.Count);
                                    Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                                    int batchSize = 100;
                                    for (int i = 0; i < amlist.Count; i += batchSize)
                                    {
                                        var batchdata = amlist.Skip(i).Take(batchSize);
                                        #region 按批次生成结算异步执行作业日志
                                        Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                                        settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", new_srv_expense_claim[j].Id);
                                        settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                                        settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                                        settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                                        var messagebody = batchdata.Select(a => a.new_srv_workorderid).ToList();
                                        settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                                        settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                                        var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                                        #endregion
                                        AdjustModelMaitroxRequest requestbody = new AdjustModelMaitroxRequest();
                                        requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                                        requestbody.new_srv_expense_claimid = new_srv_expense_claim[j].Id.ToString();
                                        requestbody.amlist = batchdata.ToList();
                                        messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                                        index++;
                                    }
                                    await busCommon.sendservicebus2(messages, ServiceBusMaitroxConnectionString, SETT_ServiceBus_MaitroxQueuesName, log);
                                    #endregion
                                    //备件费
                                    decimal sparepartscostTotal = amlist.Sum(a => a.new_sparepartscost);
                                    //备件服务费
                                    decimal partservicecostTotal = amlist.Sum(a => a.new_partservicecost);
                                    //备件服务费KPI
                                    decimal partservicecostkpiTotal = amlist.Sum(a => a.new_partservicecostkpi);
                                    //物流费合计
                                    decimal logisticsfeeTotal = amlist.Sum(a => a.new_markuplogisticsfee);
                                    //换机local buy费用合计
                                    decimal localbuyfeeTotal = amlist.Sum(a => a.new_localbuyreplacementcost);
                                    //换机local buy markup费用合计
                                    decimal localbuymarkupfeeTotal = amlist.Sum(a => a.new_markupreplacementcost);
                                    //仓储费合计
                                    decimal warehousingfeeTotal = amlist.Sum(a => a.new_warehousingfee);
                                    //客户退款合计
                                    decimal customerrefundTotal = amlist.Sum(a => a.new_customerrefund);
                                    //生态链品类回购费合计
                                    decimal ecosystemcategorybuybackfeeTotal = amlist.Sum(a => a.new_ecosystemcategorybuybackfee);
                                    //迈创其他费用合计
                                    decimal othermiscellaneouschargesTotal = amlist.Sum(a => a.new_othermiscellaneouscharges);
                                    //工单量保底费用合计
                                    decimal minimumworkorderfeeTotal = amlist.Sum(a => a.new_minimumworkorderfee);
                                    //资本利息费合计
                                    decimal capitalinterestexpenseTotal = amlist.Sum(a => a.new_capitalinterestexpense);
                                    //直发费用合计
                                    decimal directshippingcostTotal = amlist.Sum(a => a.new_directshippingcost);
                                    //固定服务费合计
                                    decimal fixedservicefeeTotal = amlist.Sum(a => a.new_fixedservicefee);
                                    //预提费合计
                                    decimal withholdingfeemaitroxTotal = amlist.Sum(a => a.new_withholdingfeemaitrox);
                                    //预提反冲费合计
                                    decimal withholdingfeerecoilTotal = amlist.Sum(a => a.new_withholdingfeerecoil);
                                    decimal sumSettlementMoney = sparepartscostTotal + partservicecostkpiTotal + logisticsfeeTotal
                                        + localbuyfeeTotal + localbuymarkupfeeTotal + warehousingfeeTotal + customerrefundTotal + ecosystemcategorybuybackfeeTotal
                                        + othermiscellaneouschargesTotal + minimumworkorderfeeTotal + capitalinterestexpenseTotal + directshippingcostTotal
                                        + fixedservicefeeTotal + withholdingfeemaitroxTotal + withholdingfeerecoilTotal;
                                    //税率
                                    decimal taxrate = new_srv_expense_claim[j].GetAttributeValue<decimal>("new_taxrate");
                                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                                    expenseClaimEntity.Id = new_srv_expense_claim[j].Id;
                                    //结算单版本
                                    //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                                    expenseClaimEntity["new_partservicecost"] = partservicecostTotal;
                                    expenseClaimEntity["new_partservicecostkpi"] = partservicecostkpiTotal;
                                    expenseClaimEntity["new_markuplogisticsfee"] = logisticsfeeTotal;
                                    expenseClaimEntity["new_localbuyreplacementcost"] = localbuyfeeTotal;
                                    expenseClaimEntity["new_markupreplacementcost"] = localbuymarkupfeeTotal;
                                    expenseClaimEntity["new_warehousingfee"] = warehousingfeeTotal;
                                    expenseClaimEntity["new_customerrefund"] = customerrefundTotal;
                                    expenseClaimEntity["new_ecosystemcategorybuybackfee"] = ecosystemcategorybuybackfeeTotal;
                                    expenseClaimEntity["new_othermiscellaneouscharges"] = othermiscellaneouschargesTotal;
                                    expenseClaimEntity["new_withholdingfeemaitrox"] = withholdingfeemaitroxTotal;
                                    expenseClaimEntity["new_withholdingfeerecoil"] = withholdingfeerecoilTotal;
                                    //总费用合计
                                    expenseClaimEntity["new_totalcost"] = sumSettlementMoney;
                                    //结算费用=总费用*（1+税率）
                                    expenseClaimEntity["new_approvalamount"] = sumSettlementMoney * (1 + taxrate);
                                    //调差批次id
                                    expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                                    expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                                    OrganizationService.Update(expenseClaimEntity);
                                    log.LogInformation($"分摊结算单id：{new_srv_expense_claim.Entities[j].Id}处理已完成");
                                }
                                #endregion
                            }
                            else
                            {
                                //调差失败,更新调差失败原因
                                Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                                expenseClaimEntity.Id = new_srv_expense_claim[j].Id;
                                expenseClaimEntity["new_adjustmenterror"] = adjustmenterror;
                                OrganizationService.Update(expenseClaimEntity);
                            }
                        }
                        catch (Exception ex)
                        {
                            log.LogError(ex.ToString());
                            Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                            expenseClaimEntity.Id = new_srv_expense_claim[j].Id;
                            expenseClaimEntity["new_adjustmenterror"] = ex.ToString();
                            OrganizationService.Update(expenseClaimEntity);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 激活结算单调差
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public async Task SetDifferenceActivate(ILogger log)
        {
            ServriceBusCommon busCommon = new ServriceBusCommon("", "", "", SETT_ServiceBus_ActivateQueuesName);
            //查询结算单
            QueryExpression query = new QueryExpression("new_srv_expense_claim");
            query.NoLock = true;
            query.ColumnSet = new ColumnSet("new_srv_station_id", "new_year", "new_month", "new_withholdingmoney", "new_totalcost", "new_approvalamount", "new_country_id", "new_taxrate");
            query.Criteria.AddCondition("new_adjuststatus", ConditionOperator.Equal, 2);
            query.Criteria.AddCondition("new_businesstype", ConditionOperator.Equal, 6);//结算单类型 = 激活
            EntityCollection new_srv_expense_claim = OrganizationService.RetrieveMultiple(query);
            if (new_srv_expense_claim?.Entities?.Count > 0)
            {
                log.LogInformation($"共有{new_srv_expense_claim?.Entities?.Count}条激活结算单");
                foreach (var expense_claim in new_srv_expense_claim.Entities)
                {
                    //需要推送到servicebus的集合
                    List<AdjustModelActivate> amlist = new List<AdjustModelActivate>();
                    //查询结算单下的激活结算单明细
                    var activatelinelist = GetExpenseActivateline(expense_claim.Id.ToString());
                    //查询结算单下的加减项费用单
                    var specialexpenselist = GetSpecialExpenseActivate(expense_claim.Id.ToString());
                    #region 维护SKU了，根据SKU平均分摊到对应SKU的激活结算单明细上
                    var skuspecialexpenselist = specialexpenselist.Entities.Where(a => a.Contains("new_goodsfiles_id")).ToList();
                    List<Guid> goodsfileslist = new List<Guid>();
                    goodsfileslist.AddRange(skuspecialexpenselist.Select(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id));
                    goodsfileslist.Distinct();
                    foreach (var goodsfiles in goodsfileslist)
                    {
                        decimal skuspecialamount = skuspecialexpenselist.Where(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id == goodsfiles).Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                        var skuactivateline = activatelinelist.Entities.Where(a => a.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id == goodsfiles).ToList();
                        decimal specialamount = Math.Round(skuspecialamount / skuactivateline.Count, 2);
                        decimal sumspeciamlfee = 0;
                        for (int i = 0; i < skuactivateline.Count; i++)
                        {
                            AdjustModelActivate am = new AdjustModelActivate();
                            am.new_srv_expense_activationlineid = skuactivateline[i].Id.ToString();
                            am.new_expense_claim_id = expense_claim.Id.ToString();
                            am.new_withholdingmoney = skuactivateline[i].GetAttributeValue<decimal>("new_withholdingmoney");
                            if (i == skuactivateline.Count - 1)
                            {
                                decimal lastspecialamount = skuspecialamount - sumspeciamlfee;
                                am.new_specialfeeshare = lastspecialamount;
                                am.new_settlementmoney = am.new_withholdingmoney + am.new_specialfeeshare;
                            }
                            else
                            {
                                am.new_specialfeeshare = specialamount;
                                am.new_settlementmoney = am.new_withholdingmoney + am.new_specialfeeshare;
                                sumspeciamlfee += specialamount;
                            }
                            amlist.Add(am);
                        }
                    }
                    #endregion
                    #region 未维护SKU,汇总加减项费用单的特殊费用，平均分摊到激活结算单明细上
                    var noskuspecialexpenselist = specialexpenselist.Entities.Where(a => !a.Contains("new_goodsfiles_id")).ToList();
                    decimal noskuspecialamount = noskuspecialexpenselist.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    decimal noskuaverageamount = Math.Round(noskuspecialamount / activatelinelist.Entities.Count, 2);
                    decimal sumspeciamlfeenosku = 0;
                    for (int i = 0; i < activatelinelist.Entities.Count; i++)
                    {
                        if (i == activatelinelist.Entities.Count - 1)
                        {
                            decimal lastspecialamount = noskuspecialamount - sumspeciamlfeenosku;
                            var am = amlist.Where(a => a.new_srv_expense_activationlineid == activatelinelist.Entities[i].Id.ToString()).FirstOrDefault();
                            if (am != null)
                            {
                                am.new_withholdingfeeactivate = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeeactivate");
                                am.new_withholdingfeerecoilactivate = am.new_withholdingfeeactivate * -1;
                                am.new_specialfeeshare += lastspecialamount;
                                am.new_settlementmoney = am.new_withholdingmoney + am.new_specialfeeshare + am.new_withholdingfeerecoilactivate;
                            }
                            else
                            {
                                AdjustModelActivate am1 = new AdjustModelActivate();
                                am1.new_srv_expense_activationlineid = activatelinelist.Entities[i].Id.ToString();
                                am1.new_withholdingfeeactivate = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeeactivate");
                                am1.new_withholdingfeerecoilactivate = am1.new_withholdingfeeactivate * -1;
                                am1.new_expense_claim_id = expense_claim.Id.ToString();
                                am1.new_withholdingmoney = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingmoney");
                                am1.new_specialfeeshare = lastspecialamount;
                                am1.new_settlementmoney = am1.new_withholdingmoney + am1.new_specialfeeshare + am1.new_withholdingfeerecoilactivate;
                                amlist.Add(am1);
                            }
                        }
                        else
                        {
                            var am = amlist.Where(a => a.new_srv_expense_activationlineid == activatelinelist.Entities[i].Id.ToString()).FirstOrDefault();
                            if (am != null)
                            {
                                am.new_withholdingfeeactivate = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeeactivate");
                                am.new_withholdingfeerecoilactivate = am.new_withholdingfeeactivate * -1;
                                am.new_specialfeeshare += noskuaverageamount;
                                am.new_settlementmoney = am.new_withholdingmoney + am.new_specialfeeshare + am.new_withholdingfeerecoilactivate;
                                sumspeciamlfeenosku += noskuaverageamount;
                            }
                            else
                            {
                                AdjustModelActivate am1 = new AdjustModelActivate();
                                am1.new_srv_expense_activationlineid = activatelinelist.Entities[i].Id.ToString();
                                am1.new_withholdingfeeactivate = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeeactivate");
                                am1.new_withholdingfeerecoilactivate = am1.new_withholdingfeeactivate * -1;
                                am1.new_expense_claim_id = expense_claim.Id.ToString();
                                am1.new_withholdingmoney = activatelinelist.Entities[i].GetAttributeValue<decimal>("new_withholdingmoney");
                                am1.new_specialfeeshare = noskuaverageamount;
                                am1.new_settlementmoney = am1.new_withholdingmoney + noskuaverageamount + am1.new_withholdingfeerecoilactivate;
                                amlist.Add(am1);
                                sumspeciamlfeenosku += noskuaverageamount;
                            }
                        }
                    }
                    #endregion
                    #region 更新结算单
                    #region 抛到servicebus
                    log.LogInformation($"amlist.count：" + amlist.Count);
                    int index = 1;
                    //调差批次id
                    Guid adjuestbatchid = Guid.NewGuid();
                    Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                    int batchSize = 100;
                    for (int i = 0; i < amlist.Count; i += batchSize)
                    {
                        var batchdata = amlist.Skip(i).Take(batchSize);
                        #region 按批次生成结算异步执行作业日志
                        Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                        settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", expense_claim.Id);
                        settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                        settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                        settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                        var messagebody = batchdata.Select(a => a.new_srv_expense_activationlineid).ToList();
                        settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                        settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                        var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                        #endregion
                        AdjustModelActivateRequest requestbody = new AdjustModelActivateRequest();
                        requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                        requestbody.new_srv_expense_claimid = expense_claim.Id.ToString();
                        requestbody.amlist = batchdata.ToList();
                        messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                        index++;
                    }
                    await busCommon.sendservicebus2(messages, ServiceBusActivateConnectionString, SETT_ServiceBus_ActivateQueuesName, log);
                    #endregion
                    //加减项分摊费用
                    decimal specialfeeshareTotal = amlist.Sum(a => a.new_specialfeeshare);
                    decimal withholdingmoneyTotal = amlist.Sum(a => a.new_withholdingmoney);
                    decimal new_withholdingfee = amlist.Sum(a => a.new_withholdingfeeactivate);
                    decimal new_withholdingfeerecoil = new_withholdingfee * -1;
                    decimal sumSettlementMoney = specialfeeshareTotal + withholdingmoneyTotal + new_withholdingfeerecoil;
                    //税率
                    decimal taxrate = expense_claim.GetAttributeValue<decimal>("new_taxrate");
                    Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                    expenseClaimEntity.Id = expense_claim.Id;
                    //结算单版本
                    //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                    //总费用合计
                    expenseClaimEntity["new_totalcost"] = sumSettlementMoney;
                    expenseClaimEntity["new_withholdingfeerecoil"] = new_withholdingfeerecoil;
                    //结算费用=总费用*（1+税率）
                    expenseClaimEntity["new_approvalamount"] = sumSettlementMoney * (1 + taxrate);
                    //调差批次id
                    expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                    expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                    OrganizationService.Update(expenseClaimEntity);
                    log.LogInformation($"分摊结算单id：{expense_claim.Id}处理已完成");
                    #endregion
                }
            }
        }
        /// <summary>
        /// Handling结算单调差
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public async Task SetDifferenceHandling(ILogger log)
        {
            ServriceBusCommon busCommon = new ServriceBusCommon("", "", "", SETT_ServiceBus_HandlingQueuesName);
            //查询结算单
            QueryExpression query = new QueryExpression("new_srv_expense_claim");
            query.NoLock = true;
            query.ColumnSet = new ColumnSet("new_srv_station_id", "new_year", "new_month", "new_withholdingmoney", "new_totalcost", "new_approvalamount", "new_country_id", "new_taxrate");
            query.Criteria.AddCondition("new_adjuststatus", ConditionOperator.Equal, 2);
            query.Criteria.AddCondition(new ConditionExpression("new_businesstype", ConditionOperator.In, new int[] { 4, 8 }));//结算单类型为运营商或收集点
            EntityCollection expense_claimlist = OrganizationService.RetrieveMultiple(query);
            if (expense_claimlist?.Entities?.Count > 0)
            {
                for (int i = 0; i < expense_claimlist.Entities.Count; i++)
                {
                    bool iserror = false;
                    #region 初始化数据
                    //需要推送到servicebus的集合
                    List<AdjustModelHandling> amlist = new List<AdjustModelHandling>();
                    //获取特殊费用
                    EntityCollection specialexpenselist = GetSpecialExpense(expense_claimlist.Entities[i].Id.ToString(), log);
                    //根据结算单查询服务单
                    EntityCollection workorderCollection = GetWorkorderByExpenseHandling(expense_claimlist.Entities[i].Id.ToString());
                    var workorderidList = workorderCollection.Entities.Select(a => a.ToDefault<Guid>("new_repairprovider_id")).Distinct().ToList();
                    log.LogInformation($"当前结算单关联服务单数量{workorderCollection?.Entities?.Count}");
                    //结算年份
                    int new_year = expense_claimlist[i].GetAttributeValue<OptionSetValue>("new_year").Value;
                    //结算月份
                    int new_month = expense_claimlist[i].GetAttributeValue<OptionSetValue>("new_month").Value;
                    //获取服务商kpi
                    List<Entity> serviceKpi = GetServiceKpi(workorderidList, new_year, new_month);
                    #endregion
                    #region 既没有维护服务单，也没有维护SKU，需要将特殊费用平均分摊到所有的工单上(考虑反冲，重维服务单不在该结算单下的情况)
                    //排除呼叫费和销售额费用类型，呼叫费和销售额费用类型单独处理
                    var specialfeecount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && !new int[] { 29, 30 }.Contains(a.ToDefault<int>("new_feetype"))).Count();
                    if (specialfeecount > 0)
                    {
                        //特殊费用总和
                        decimal totalAmount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && !new int[] { 29, 30 }.Contains(a.ToDefault<int>("new_feetype"))).Sum(x => x.GetAttributeValue<decimal>("new_specialamount"));
                        //工单数量
                        int count = workorderCollection.Entities.Count;
                        //分摊金额
                        decimal aveAmount = Math.Round(totalAmount / count, 2);
                        decimal sumSpecialexpense = 0m;
                        for (int k = 0; k < workorderCollection.Entities.Count; k++)
                        {
                            int index = k;
                            try
                            {
                                Entity order = workorderCollection.Entities[index];
                                AdjustModelHandling am = new AdjustModelHandling();
                                am.new_srv_workorderid = order.Id.ToString();
                                am.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                                am.new_handlingfee = order.GetAttributeValue<decimal>("new_handlingfee");
                                #region 计算费用
                                //加减项费用分摊
                                var new_specialfeeshare = am.new_othermiscellaneouschargeshandling;
                                if (index == workorderCollection.Entities.Count - 1)
                                {
                                    decimal lastspecialfeeshare = totalAmount - sumSpecialexpense;
                                    log.LogInformation($"最后一条分摊：{lastspecialfeeshare}");
                                    //特殊费用分摊
                                    am.new_othermiscellaneouschargeshandling += lastspecialfeeshare;
                                }
                                else
                                {
                                    am.new_othermiscellaneouschargeshandling += aveAmount;
                                }
                                //特殊费用总和（截取两位小数）
                                sumSpecialexpense += am.new_othermiscellaneouschargeshandling;
                                #endregion
                                amlist.Add(am);
                            }
                            catch (Exception ex)
                            {
                                Entity ent = new Entity("new_srv_expense_claim");
                                ent.Id = expense_claimlist[i].Id;
                                ent["new_adjustmenterror"] = $"调差批量操作异常：{ex.Message}，服务单id：{workorderCollection.Entities[index].Id.ToString()}";
                                OrganizationService.Update(ent);
                                iserror = true;
                                break;
                            }
                        }
                    }
                    #endregion
                    #region 既没有维护服务单，也没有维护SKU，单独处理呼叫费(Handling),销售额，呼叫费(Handling)，销售额也要分摊
                    var callfeehandlingcount = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                   && !a.Contains("new_goodsfiles_id") && new int[] { 29, 30 }.Contains(a.ToDefault<int>("new_feetype"))).Count();
                    if (callfeehandlingcount > 0)
                    {
                        //特殊费用总和
                        List<Entity> specialAmountlist = specialexpenselist.Entities.Where(a => (!workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id")) || !a.Contains("new_workorder_id"))
                    && !a.Contains("new_goodsfiles_id") && new int[] { 29, 30 }.Contains(a.ToDefault<int>("new_feetype"))).ToList();
                        foreach (var item in specialAmountlist)
                        {
                            //工单数量
                            int count = workorderCollection.Entities.Count;
                            decimal separatespecialamount = item.GetAttributeValue<decimal>("new_specialamount");
                            int feetype = item.ToDefault<int>("new_feetype");
                            //分摊金额
                            decimal aveAmount = Math.Round(separatespecialamount / count, 2);
                            decimal sumspecialamount = 0;
                            for (int k = 0; k < workorderCollection.Entities.Count; k++)
                            {
                                decimal updatespecialfee = 0;
                                if (k == count - 1)
                                {
                                    decimal lastspecialfee = separatespecialamount - sumspecialamount;
                                    updatespecialfee = lastspecialfee;
                                }
                                else
                                {
                                    updatespecialfee = aveAmount;
                                    sumspecialamount += aveAmount;
                                }
                                var updateworkorder = amlist.Where(a => a.new_srv_workorderid == workorderCollection.Entities[k].Id.ToString()).FirstOrDefault();
                                if (updateworkorder != null)
                                {
                                    if (feetype == 29)
                                        updateworkorder.new_callfeehandling += updatespecialfee;
                                    else
                                        updateworkorder.new_saleamounthandling += updatespecialfee;
                                }
                                else
                                {
                                    var order = workorderCollection.Entities.Where(a => a.Id == workorderCollection.Entities[k].Id).FirstOrDefault();
                                    AdjustModelHandling am = new AdjustModelHandling();
                                    am.new_srv_workorderid = order.Id.ToString();
                                    am.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                                    am.new_handlingfee = order.GetAttributeValue<decimal>("new_handlingfee");
                                    if (feetype == 29)
                                        am.new_callfeehandling += updatespecialfee;
                                    else
                                        am.new_saleamounthandling += updatespecialfee;
                                    amlist.Add(am);
                                }
                            }
                        }
                    }
                    #endregion
                    #region 维护了服务单的特殊费用(排除反冲，重维不在该结算单下的情况的服务单)
                    var specialfeebyworkorder = specialexpenselist.Entities.Where(a => a.Contains("new_workorder_id")
                    && workorderCollection.Entities.Select(a => a.Id).ToList().Contains(a.ToDefault<Guid>("new_workorder_id"))).ToList();
                    foreach (var item in specialfeebyworkorder)
                    {
                        var feeType = item.ToDefault<int>("new_feetype");
                        var updateworkorder = amlist.Where(a => a.new_srv_workorderid == item.ToDefault<Guid>("new_workorder_id").ToString()).FirstOrDefault();
                        if (updateworkorder != null)
                        {
                            if (feeType == 29)
                                updateworkorder.new_callfeehandling += item.ToDefault<decimal>("new_specialamount");
                            else if (feeType == 30)
                                updateworkorder.new_saleamounthandling += item.ToDefault<decimal>("new_specialamount");
                            else
                                updateworkorder.new_othermiscellaneouschargeshandling += item.ToDefault<decimal>("new_specialamount");
                        }
                        else
                        {
                            var order = workorderCollection.Entities.Where(a => a.Id == item.ToDefault<Guid>("new_workorder_id")).FirstOrDefault();
                            AdjustModelHandling am = new AdjustModelHandling();
                            am.new_srv_workorderid = order.Id.ToString();
                            am.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                            am.new_handlingfee = order.GetAttributeValue<decimal>("new_handlingfee");
                            if (feeType == 29)
                                am.new_callfeehandling = item.ToDefault<decimal>("new_specialamount");
                            else if (feeType == 30)
                                am.new_saleamounthandling += item.ToDefault<decimal>("new_specialamount");
                            else
                                am.new_othermiscellaneouschargeshandling += item.ToDefault<decimal>("new_specialamount");
                            amlist.Add(am);
                        }
                    }
                    #endregion
                    #region 未维护服务单，维护了SKU，需要根据SKU平均分摊到工单上
                    var specialfeebysku = specialexpenselist.Entities.Where(a => !a.Contains("new_workorder_id") && a.Contains("new_goodsfiles_id")).ToList();
                    foreach (var item in specialfeebysku)
                    {
                        var feeType = item.ToDefault<int>("new_feetype");
                        var workordergroup = workorderCollection.Entities.Where(a => a.ToDefault<Guid>("new_goodsfiles_id") == item.ToDefault<Guid>("new_goodsfiles_id")).ToList();
                        if (workordergroup.Count > 0)
                        {
                            decimal aveageamount = Math.Round(item.ToDefault<decimal>("new_specialamount") / workordergroup.Count, 2);
                            decimal sumspecialfee = 0;
                            decimal updatespecialfee = 0;
                            for (int j = 0; j < workordergroup.Count; j++)
                            {
                                if (j == workordergroup.Count - 1)
                                {
                                    decimal lastspecialfee = item.ToDefault<decimal>("new_specialamount") - sumspecialfee;
                                    updatespecialfee = lastspecialfee;
                                }
                                else
                                {
                                    updatespecialfee = aveageamount;
                                    sumspecialfee += aveageamount;
                                }
                                var updateworkorder = amlist.Where(a => a.new_srv_workorderid == workordergroup[j].Id.ToString()).FirstOrDefault();
                                if (updateworkorder != null)
                                {
                                    if (feeType == 29)
                                        updateworkorder.new_callfeehandling += updatespecialfee;
                                    else if (feeType == 30)
                                        updateworkorder.new_saleamounthandling += updatespecialfee;
                                    else
                                        updateworkorder.new_othermiscellaneouschargeshandling += updatespecialfee;
                                }
                                else
                                {
                                    var order = workorderCollection.Entities.Where(a => a.Id == workordergroup[j].Id).FirstOrDefault();
                                    AdjustModelHandling am = new AdjustModelHandling();
                                    am.new_srv_workorderid = order.Id.ToString();
                                    am.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                                    am.new_handlingfee = order.GetAttributeValue<decimal>("new_handlingfee");
                                    if (feeType == 29)
                                        am.new_othermiscellaneouschargeshandling += updatespecialfee;
                                    else if (feeType == 30)
                                        am.new_saleamounthandling += updatespecialfee;
                                    else
                                        am.new_othermiscellaneouschargeshandling += updatespecialfee;
                                    amlist.Add(am);
                                }
                            }
                        }
                    }
                    #endregion
                    //计算工单的结算费用，调差金额
                    for (int j = 0; j < workorderCollection.Entities.Count; j++)
                    {
                        //服务商kpi
                        decimal new_ratio = CalculateKpitype(serviceKpi, workorderCollection.Entities[j]);
                        Entity order = workorderCollection.Entities[j];
                        //预提费用
                        decimal new_withholdingmoneyhandling = order.GetAttributeValue<decimal>("new_withholdingmoneyhandling");
                        var am = amlist.Where(a => a.new_srv_workorderid == workorderCollection.Entities[j].Id.ToString()).FirstOrDefault();
                        if (am == null)
                        {
                            am = new AdjustModelHandling();
                            am.new_srv_workorderid = order.Id.ToString();
                            am.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                            am.new_handlingfee = order.GetAttributeValue<decimal>("new_handlingfee");
                            amlist.Add(am);
                        }
                        if (workorderCollection.Entities[j].Contains("new_workorder_costtable_id"))
                        {
                            am.new_workorder_costtable_id = workorderCollection.Entities[j].GetAttributeValue<EntityReference>("new_workorder_costtable_id").Id.ToString();
                        }
                        am.new_withholdingfeehandling = workorderCollection.Entities[j].GetAliasAttributeValue<decimal>("cost.new_withholdingfeehandling");
                        am.new_withholdingfeerecoilhandling = am.new_withholdingfeehandling * -1;
                        am.new_handlingfeekpi = Math.Round(am.new_handlingfee * new_ratio, 2, MidpointRounding.AwayFromZero);
                        //总费用合计 = 预提金额+其他特殊费用（Handling）+呼叫费+销售额-收集费+收集费KPI+预提反冲费
                        am.new_settlementmoneyhandling = new_withholdingmoneyhandling + am.new_othermiscellaneouschargeshandling + am.new_callfeehandling + am.new_saleamounthandling - am.new_handlingfee + am.new_handlingfeekpi + am.new_withholdingfeerecoilhandling;
                        am.new_changemoneyhandling = am.new_settlementmoneyhandling - new_withholdingmoneyhandling;
                    }
                    #region 更新结算单
                    if (!iserror)
                    {
                        //调差批次id
                        Guid adjuestbatchid = Guid.NewGuid();
                        #region 抛到servicebus
                        log.LogInformation($"amlist.count：" + amlist.Count);
                        decimal new_othermoneyhandling = 0m;
                        decimal new_callfeehandling = 0m;
                        decimal new_saleamounthandling = 0m;
                        decimal new_handlingfeekpi = 0m;
                        decimal new_totalcost = 0m;
                        decimal new_withholdingfeerecoil = 0m;
                        Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                        foreach (var item in amlist)
                        {
                            new_othermoneyhandling += item.new_othermiscellaneouschargeshandling;
                            new_callfeehandling += item.new_callfeehandling;
                            new_saleamounthandling += item.new_saleamounthandling;
                            new_handlingfeekpi += item.new_handlingfeekpi;
                            new_totalcost += item.new_settlementmoneyhandling;
                            new_withholdingfeerecoil += item.new_withholdingfeerecoilhandling;
                        }
                        int batchSize = 100;
                        for (int j = 0; j < amlist.Count; j += batchSize)
                        {
                            var batchdata = amlist.Skip(j).Take(batchSize);
                            #region 按批次生成结算异步执行作业日志
                            Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                            settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", expense_claimlist[i].Id);
                            settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                            settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                            settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                            var messagebody = batchdata.Select(a => a.new_srv_workorderid).ToList();
                            settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                            settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                            var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                            #endregion
                            AdjustModelHandlingRequest requestbody = new AdjustModelHandlingRequest();
                            requestbody.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                            requestbody.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                            requestbody.amlist = batchdata.ToList();
                            messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(requestbody)));
                        }
                        await busCommon.sendservicebus2(messages, ServiceBusHandlingConnectionString, SETT_ServiceBus_HandlingQueuesName, log);
                        #endregion
                        //税率
                        decimal taxrate = expense_claimlist[i].GetAttributeValue<decimal>("new_taxrate");
                        Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                        expenseClaimEntity.Id = expense_claimlist[i].Id;
                        //其他特殊费用(handling)
                        expenseClaimEntity["new_othermoneyhandling"] = new_othermoneyhandling;
                        //呼叫费
                        expenseClaimEntity["new_callfeehandling"] = new_callfeehandling;
                        //销售额
                        expenseClaimEntity["new_saleamounthandling"] = new_callfeehandling;
                        //收集费KPI
                        expenseClaimEntity["new_handlingfeekpi"] = new_handlingfeekpi;
                        //结算单版本
                        //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                        //总费用合计
                        expenseClaimEntity["new_totalcost"] = new_totalcost;
                        expenseClaimEntity["new_withholdingfeerecoil"] = new_withholdingfeerecoil;
                        //结算费用=总费用*（1+税率）
                        expenseClaimEntity["new_approvalamount"] = new_totalcost * (1 + taxrate);
                        //调差批次id
                        expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                        expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                        OrganizationService.Update(expenseClaimEntity);
                        log.LogInformation($"分摊结算单id：{expense_claimlist[i].Id}处理已完成");
                    }
                    #endregion
                }
            }
        }
        /// <summary>
        /// 仓储结费结算单调差
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public async Task SetDifferenceStorage(ILogger log)
        {
            ServriceBusCommon busCommon = new ServriceBusCommon("", "", "", SET_ServiceBus_ExecuteWorkorderCostQueuesName);
            SettlementHelp settlementHelp = new SettlementHelp(log);
            var scopecols = settlementHelp.Getallocationscope(10);
            var ratioconfig = settlementHelp.GetRatioConfiglist();
            var expensetypeconfig = settlementHelp.Getexpensetypeconfig();
            var expensestandard = settlementHelp.GetExpenseStandard();
            //查询结算单
            QueryExpression query = new QueryExpression("new_srv_expense_claim");
            query.NoLock = true;
            query.ColumnSet = new ColumnSet("new_srv_station_id", "new_year", "new_month", "new_withholdingmoney", "new_totalcost", "new_approvalamount", "new_country_id", "new_taxrate", "new_withholdingfeestorage");
            query.Criteria.AddCondition("new_adjuststatus", ConditionOperator.Equal, 2);
            query.Criteria.AddCondition("new_businesstype", ConditionOperator.Equal, 5);//类型 = 仓储
            EntityCollection expense_claimlist = OrganizationService.RetrieveMultiple(query);
            for (int i = 0; i < expense_claimlist.Entities.Count; i++)
            {
                try
                {
                    //需要推送到servicebus的集合
                    List<workorder_costtable> amlist = new List<workorder_costtable>();
                    //获取特殊费用
                    EntityCollection specialexpenselist = GetSpecialExpense(expense_claimlist.Entities[i].Id.ToString(), log);
                    //根据结算单查询工单费用
                    EntityCollection workordercostcols = GetWorkorderCost(expense_claimlist.Entities[i].Id.ToString());
                    log.LogInformation($"当前结算单关联工单费用数量{workordercostcols?.Entities?.Count}");
                    //结算年份
                    int new_year = expense_claimlist[i].GetAttributeValue<OptionSetValue>("new_year").Value;
                    //结算月份
                    int new_month = expense_claimlist[i].GetAttributeValue<OptionSetValue>("new_month").Value;
                    //服务商
                    Guid serviceprovider_id = expense_claimlist[i].ToDefault<Guid>("new_srv_station_id");
                    //获取服务商kpi
                    EntityCollection kpidetails = GetStorageKpidetail(new_year, new_month);
                    //查询仓储单价明细
                    var warehousing_unitprice = settlementHelp.Getwarehousing_unitprice(new_year, new_month, serviceprovider_id.ToString());
                    //其他费用
                    decimal new_storageotherfee = 0m;
                    var storageotherfee_en = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 14).ToList();
                    new_storageotherfee = storageotherfee_en.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    //其他费用-包材费
                    decimal new_storageotherfee1 = 0m;
                    var storageotherfee1_en = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 31).ToList();
                    new_storageotherfee1 = storageotherfee1_en.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    //其他费用-保险费
                    decimal new_storageotherfee2 = 0m;
                    var storageotherfee2_en = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 32).ToList();
                    new_storageotherfee2 = storageotherfee2_en.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    //其他费用-进口报关费
                    decimal new_storageotherfee3 = 0m;
                    var storageotherfee3_en = specialexpenselist.Entities.Where(a => a.ToDefault<int>("new_feetype") == 33).ToList();
                    new_storageotherfee3 = storageotherfee3_en.Sum(a => a.GetAttributeValue<decimal>("new_specialamount"));
                    #region 创建分摊权重数据，计算工单费用
                    StoragefeeModel storagefeeModel = settlementHelp.CalcStoragefee(serviceprovider_id, scopecols, ratioconfig, expensetypeconfig, warehousing_unitprice, expensestandard, "new_final_count");
                    EntityCollection ratiocols = settlementHelp.GetRatioConfiglist();
                    var workorderlist = GetWorkorderSettle(expense_claimlist.Entities[i].Id);
                    storagefeeModel.allocationstoragefeelist.Add("new_storageotherfee", new_storageotherfee);
                    storagefeeModel.allocationstoragefeelist.Add("new_storageotherfee1", new_storageotherfee1);
                    storagefeeModel.allocationstoragefeelist.Add("new_storageotherfee2", new_storageotherfee2);
                    storagefeeModel.allocationstoragefeelist.Add("new_storageotherfee3", new_storageotherfee3);
                    foreach (var key in storagefeeModel.allocationstoragefeelist.Keys.ToList())
                    {
                        decimal kpi = 1;
                        int expensetype = -1;
                        #region 计算KPI值
                        if (key == "new_shelfrentalfee")
                        {
                            expensetype = 10;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedoperationcosts_management")
                        {
                            expensetype = 120;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedoperationcosts_other")
                        {
                            expensetype = 140;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedoperationcosts_packaging")
                        {
                            expensetype = 110;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedoperationcosts_scrapped")
                        {
                            expensetype = 130;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedoperationcosts_utilities")
                        {
                            expensetype = 100;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_inboundshelvingcharge")
                        {
                            expensetype = 70;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_itemsizehandlingfee")
                        {
                            expensetype = 60;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_packingfee")
                        {
                            expensetype = 80;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_qualityinspectionfee")
                        {
                            expensetype = 90;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_variablelaborcost_basic")
                        {
                            expensetype = 30;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_variablelaborcost_overtime")
                        {
                            expensetype = 40;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_variablelaborcost_overtime1")
                        {
                            expensetype = 50;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_warehouseexpansionfee")
                        {
                            expensetype = 20;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedlaborcost")
                        {
                            expensetype = 170;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_fixedrentalfee")
                        {
                            expensetype = 160;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_storageandlogisticscost")
                        {
                            expensetype = 150;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_storageotherfee")
                        {
                            expensetype = 180;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_storageotherfee1")
                        {
                            expensetype = 190;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_storageotherfee2")
                        {
                            expensetype = 200;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_storageotherfee3")
                        {
                            expensetype = 210;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_televisionoperationfee")
                        {
                            //电视操作费
                            expensetype = 220;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        else if (key == "new_entryexithandlingfee")
                        {
                            //出入库操作费
                            expensetype = 230;
                            kpi = CalculateKpiStorage(kpidetails, serviceprovider_id, expensetype);
                        }
                        #endregion
                        storagefeeModel.allocationstoragefeelist[key] = Math.Round(storagefeeModel.allocationstoragefeelist[key] * kpi, 2);
                        //预提反冲费
                        if (key == "new_withholdingfeestorage")
                            storagefeeModel.allocationstoragefeelist[key] = Math.Round(expense_claimlist.Entities[i].GetAttributeValue<decimal>("new_withholdingfeestorage"), 2);
                    }
                    if (workorderlist.Entities.Count > 0)
                    {
                        EntityCollection weightcols = settlementHelp.Createallocationweightdata(workorderlist, serviceprovider_id, ratiocols, storagefeeModel.allocationstoragefeelist, dateTime.Year, dateTime.Month, 20);
                        var getweightlist = settlementHelp.Getallocationweightdata(weightcols.Entities.Select(a => a.Id).ToList());
                        log.LogInformation("仓储结费调差构造工单费用开始");
                        EntityCollection workordercostcols1 = settlementHelp.AssociateSettlement(workorderlist.Entities.ToList(), getweightlist, storagefeeModel.allocationstoragefeelist, 5);
                        log.LogInformation("仓储结费调差构造工单费用结束");
                        #endregion
                        //计算工单的结算费用，调差金额
                        log.LogInformation("调差对象amlist构造开始");
                        for (int j = 0; j < workordercostcols1.Entities.Count; j++)
                        {
                            var am = new workorder_costtable();
                            var workordercosten = workordercostcols1[j];
                            //预提费用
                            decimal new_withholdingmoneystorage = workordercosten.GetAttributeValue<decimal>("new_withholdingmoneystorage");
                            if (workordercosten.Id != Guid.Empty)
                            {
                                am.new_workorder_costtableid = workordercosten.Id;
                                if (workordercosten.Contains("new_workorder_settlement_id"))
                                    am.new_workorder_settlement_id = workordercosten.GetAttributeValue<EntityReference>("new_workorder_settlement_id").Id;
                                am.new_expenseclaimidstorage = expense_claimlist[i].Id;
                                am.new_shelfrentalfee = workordercosten.GetAttributeValue<decimal>("new_shelfrentalfee");
                                am.new_televisionoperationfee = workordercosten.GetAttributeValue<decimal>("new_televisionoperationfee");
                                am.new_fixedoperationcosts_management = workordercosten.GetAttributeValue<decimal>("new_fixedoperationcosts_management");
                                am.new_fixedoperationcosts_other = workordercosten.GetAttributeValue<decimal>("new_fixedoperationcosts_other");
                                am.new_fixedoperationcosts_packaging = workordercosten.GetAttributeValue<decimal>("new_fixedoperationcosts_packaging");
                                am.new_fixedoperationcosts_scrapped = workordercosten.GetAttributeValue<decimal>("new_fixedoperationcosts_scrapped");
                                am.new_fixedoperationcosts_utilities = workordercosten.GetAttributeValue<decimal>("new_fixedoperationcosts_utilities");
                                am.new_inboundshelvingcharge = workordercosten.GetAttributeValue<decimal>("new_inboundshelvingcharge");
                                am.new_itemsizehandlingfee = workordercosten.GetAttributeValue<decimal>("new_itemsizehandlingfee");
                                am.new_packingfee = workordercosten.GetAttributeValue<decimal>("new_packingfee");
                                am.new_qualityinspectionfee = workordercosten.GetAttributeValue<decimal>("new_qualityinspectionfee");
                                am.new_variablelaborcost_basic = workordercosten.GetAttributeValue<decimal>("new_variablelaborcost_basic");
                                am.new_variablelaborcost_overtime = workordercosten.GetAttributeValue<decimal>("new_variablelaborcost_overtime");
                                am.new_variablelaborcost_overtime1 = workordercosten.GetAttributeValue<decimal>("new_variablelaborcost_overtime1");
                                am.new_warehouseexpansionfee = workordercosten.GetAttributeValue<decimal>("new_warehouseexpansionfee");
                                am.new_entryexithandlingfee = workordercosten.GetAttributeValue<decimal>("new_entryexithandlingfee");
                                am.new_fixedlaborcost = workordercosten.GetAttributeValue<decimal>("new_fixedlaborcost");
                                am.new_fixedrentalfee = workordercosten.GetAttributeValue<decimal>("new_fixedrentalfee");
                                am.new_storageandlogisticscost = workordercosten.GetAttributeValue<decimal>("new_storageandlogisticscost");
                                am.new_storageotherfeekpi = workordercosten.GetAttributeValue<decimal>("new_storageotherfee");
                                am.new_storageotherfeekpi1 = workordercosten.GetAttributeValue<decimal>("new_storageotherfee1");
                                am.new_storageotherfeekpi2 = workordercosten.GetAttributeValue<decimal>("new_storageotherfee2");
                                am.new_storageotherfeekpi3 = workordercosten.GetAttributeValue<decimal>("new_storageotherfee3");
                                am.new_withholdingfeestorage = workordercosten.GetAttributeValue<decimal>("new_withholdingfeestorage");
                                am.new_withholdingfeerecoilstorage = am.new_withholdingfeestorage * -1;
                            }

                            //总费用合计 = 各种kpi费用汇总
                            am.new_settlementmoneystorage = am.new_shelfrentalfee + am.new_televisionoperationfee + am.new_fixedoperationcosts_management + am.new_fixedoperationcosts_other
                                + am.new_fixedoperationcosts_packaging + am.new_fixedoperationcosts_scrapped + am.new_fixedoperationcosts_utilities + am.new_inboundshelvingcharge + am.new_itemsizehandlingfee
                                + am.new_packingfee + am.new_qualityinspectionfee + am.new_variablelaborcost_basic + am.new_variablelaborcost_overtime + am.new_variablelaborcost_overtime1 + am.new_warehouseexpansionfee
                                + am.new_entryexithandlingfee + am.new_fixedlaborcost + am.new_fixedrentalfee + am.new_storageotherfeekpi + am.new_storageotherfeekpi1 + am.new_storageotherfeekpi2 + am.new_storageotherfeekpi3
                                + am.new_storageandlogisticscost;
                            am.new_changemoneystorage = am.new_settlementmoneystorage - new_withholdingmoneystorage;
                            amlist.Add(am);
                        }
                        log.LogInformation("调差对象amlist构造结束");
                        //调差批次id
                        Guid adjuestbatchid = Guid.NewGuid();
                        //更新结算单，推送service bus
                        Queue<ServiceBusMessage> messages = new Queue<ServiceBusMessage>();
                        int batchcount = 100;
                        for (int j = 0; j < amlist.Count; j += batchcount)
                        {
                            var batchdata = amlist.Skip(j).Take(batchcount).ToList();
                            #region 按批次生成结算异步执行作业日志
                            Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                            settlementasyncjoblog["new_srv_expense_claim_id"] = new EntityReference("new_srv_expense_claim", expense_claimlist[i].Id);
                            settlementasyncjoblog["new_executionjob"] = new OptionSetValue(10);//执行作业 = 调差
                            settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(10);//执行状态 = 待执行
                            settlementasyncjoblog["new_remark"] = "调差任务作业执行日志";
                            var messagebody = batchdata.Select(a => a.new_workorder_costtableid).ToList();
                            settlementasyncjoblog["new_messagebody"] = JsonConvert.SerializeObject(messagebody);
                            settlementasyncjoblog["new_adjustbatchid"] = adjuestbatchid.ToString();
                            var settlementasyncjoblogId = OrganizationService.Create(settlementasyncjoblog);
                            #endregion
                            workorder_coststoragerequest storagefeerequest = new workorder_coststoragerequest();
                            storagefeerequest.new_srv_expense_claimid = expense_claimlist[i].Id.ToString();
                            storagefeerequest.new_settlementasyncjoblogId = settlementasyncjoblogId.ToString();
                            storagefeerequest.settlementType = 5;//仓储
                            storagefeerequest.storagefeelist = batchdata;
                            storagefeerequest.isadjust = true;
                            messages.Enqueue(new ServiceBusMessage(JsonConvert.SerializeObject(storagefeerequest)));
                        }
                        await busCommon.sendservicebus2(messages, ServiceBusExecuteWorkorderCostConnectionString, SET_ServiceBus_ExecuteWorkorderCostQueuesName, log);
                        decimal new_shelfrentalfee = amlist.Sum(a => a.new_shelfrentalfee);
                        decimal new_televisionoperationfee = amlist.Sum(a => a.new_televisionoperationfee);
                        decimal new_fixedoperationalcost = amlist.Sum(a => a.new_fixedoperationcosts_management) + amlist.Sum(a => a.new_fixedoperationcosts_other)
                            + amlist.Sum(a => a.new_fixedoperationcosts_packaging) + amlist.Sum(a => a.new_fixedoperationcosts_scrapped) + amlist.Sum(a => a.new_fixedoperationcosts_utilities);
                        decimal new_inboundshelvingcharge = amlist.Sum(a => a.new_inboundshelvingcharge);
                        decimal new_itemsizehandlingfee = amlist.Sum(a => a.new_itemsizehandlingfee);
                        decimal new_packingfee = amlist.Sum(a => a.new_packingfee);
                        decimal new_qualityinspectionfee = amlist.Sum(a => a.new_qualityinspectionfee);
                        decimal new_variablelaborcost = amlist.Sum(a => a.new_variablelaborcost_basic) + amlist.Sum(a => a.new_variablelaborcost_overtime) + amlist.Sum(a => a.new_variablelaborcost_overtime1);
                        decimal new_warehouseexpansionfee = amlist.Sum(a => a.new_warehouseexpansionfee);
                        decimal new_entryexithandlingfee = amlist.Sum(a => a.new_entryexithandlingfee);
                        decimal new_fixedlaborcost = amlist.Sum(a => a.new_fixedlaborcost);
                        decimal new_fixedrentalfee = amlist.Sum(a => a.new_fixedrentalfee);
                        decimal new_storageandlogisticscost = amlist.Sum(a => a.new_storageandlogisticscost);
                        decimal storageotherfeesum = amlist.Sum(a => a.new_storageotherfeekpi1) + amlist.Sum(a => a.new_storageotherfeekpi2) + amlist.Sum(a => a.new_storageotherfeekpi3) + amlist.Sum(a => a.new_storageotherfeekpi);
                        decimal new_totalcost = amlist.Sum(a => a.new_settlementmoneystorage);
                        decimal new_withholdingfeerecoilstorage = amlist.Sum(a => a.new_withholdingfeerecoilstorage);
                        //税率
                        decimal taxrate = expense_claimlist[i].GetAttributeValue<decimal>("new_taxrate");
                        Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                        expenseClaimEntity.Id = expense_claimlist[i].Id;
                        expenseClaimEntity["new_shelfrentalfee"] = new_shelfrentalfee;
                        expenseClaimEntity["new_televisionoperationfee"] = new_televisionoperationfee;
                        expenseClaimEntity["new_fixedoperationalcost"] = new_fixedoperationalcost;
                        expenseClaimEntity["new_inboundshelvingcharge"] = new_inboundshelvingcharge;
                        expenseClaimEntity["new_itemsizehandlingfee"] = new_itemsizehandlingfee;
                        expenseClaimEntity["new_packingfee"] = new_packingfee;
                        expenseClaimEntity["new_qualityinspectionfee"] = new_qualityinspectionfee;
                        expenseClaimEntity["new_variablelaborcost"] = new_variablelaborcost;
                        expenseClaimEntity["new_warehouseexpansionfee"] = new_warehouseexpansionfee;
                        expenseClaimEntity["new_entryexithandlingfee"] = new_entryexithandlingfee;
                        expenseClaimEntity["new_fixedlaborcost"] = new_fixedlaborcost;
                        expenseClaimEntity["new_fixedrentalfee"] = new_fixedrentalfee;
                        expenseClaimEntity["new_storageandlogisticscost"] = new_storageandlogisticscost;
                        expenseClaimEntity["new_storageotherfee"] = storageotherfeesum;
                        expenseClaimEntity["new_withholdingfeerecoilstorage"] = new_withholdingfeerecoilstorage;
                        //结算单版本
                        //expenseClaimEntity["new_edition"] = new OptionSetValue(2);
                        //总费用合计
                        expenseClaimEntity["new_totalcost"] = new_totalcost;
                        //结算费用=总费用*（1+税率）
                        expenseClaimEntity["new_approvalamount"] = new_totalcost * (1 + taxrate);
                        //调差批次id
                        expenseClaimEntity["new_adjustbatchid"] = adjuestbatchid.ToString();
                        expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(4);//消息队列处理中
                        OrganizationService.Update(expenseClaimEntity);
                        log.LogInformation($"分摊结算单id：{expense_claimlist[i].Id}处理已完成");
                    }
                    else
                    {
                        Entity new_srv_station = OrganizationService.Retrieve("new_srv_station", serviceprovider_id, new ColumnSet(true));
                        string nosku_supplier = CommonHelper.GetSystemParamValue(OrganizationService, "NoSKUallocationwarehousing");
                        if (new_srv_station.GetAttributeValue<string>("new_supplier") == nosku_supplier)
                        {
                            //税率
                            decimal taxrate = expense_claimlist[i].GetAttributeValue<decimal>("new_taxrate");
                            decimal new_shelfrentalfee = storagefeeModel.allocationstoragefeelist["new_shelfrentalfee"];
                            decimal new_televisionoperationfee = storagefeeModel.allocationstoragefeelist["new_televisionoperationfee"];
                            decimal new_fixedoperationalcost = storagefeeModel.allocationstoragefeelist["new_fixedoperationcosts_management"] + storagefeeModel.allocationstoragefeelist["new_fixedoperationcosts_other"]
                                + storagefeeModel.allocationstoragefeelist["new_fixedoperationcosts_packaging"] + storagefeeModel.allocationstoragefeelist["new_fixedoperationcosts_scrapped"] + storagefeeModel.allocationstoragefeelist["new_fixedoperationcosts_utilities"];
                            decimal new_inboundshelvingcharge = storagefeeModel.allocationstoragefeelist["new_inboundshelvingcharge"];
                            decimal new_itemsizehandlingfee = storagefeeModel.allocationstoragefeelist["new_itemsizehandlingfee"];
                            decimal new_packingfee = storagefeeModel.allocationstoragefeelist["new_packingfee"];
                            decimal new_qualityinspectionfee = storagefeeModel.allocationstoragefeelist["new_qualityinspectionfee"];
                            decimal new_variablelaborcost = storagefeeModel.allocationstoragefeelist["new_variablelaborcost_basic"] + storagefeeModel.allocationstoragefeelist["new_variablelaborcost_overtime"] + storagefeeModel.allocationstoragefeelist["new_variablelaborcost_overtime1"];
                            decimal new_warehouseexpansionfee = storagefeeModel.allocationstoragefeelist["new_warehouseexpansionfee"];
                            decimal new_entryexithandlingfee = storagefeeModel.allocationstoragefeelist["new_entryexithandlingfee"];
                            decimal new_fixedlaborcost = storagefeeModel.allocationstoragefeelist["new_fixedlaborcost"];
                            decimal new_fixedrentalfee = storagefeeModel.allocationstoragefeelist["new_fixedrentalfee"];
                            decimal new_storageandlogisticscost = storagefeeModel.allocationstoragefeelist["new_storageandlogisticscost"];
                            decimal storageotherfeesum = storagefeeModel.allocationstoragefeelist["new_storageotherfee1"] + storagefeeModel.allocationstoragefeelist["new_storageotherfee2"] + storagefeeModel.allocationstoragefeelist["new_storageotherfee3"] + storagefeeModel.allocationstoragefeelist["new_storageotherfee"];
                            decimal new_totalcost = storagefeeModel.allocationstoragefeelist.Sum(a => a.Value);
                            decimal new_withholdingfeerecoil = storagefeeModel.allocationstoragefeelist["new_withholdingfeestorage"] * -1;
                            Entity expenseClaimEntity = new Entity("new_srv_expense_claim");
                            expenseClaimEntity.Id = expense_claimlist[i].Id;
                            expenseClaimEntity["new_shelfrentalfee"] = new_shelfrentalfee;
                            expenseClaimEntity["new_televisionoperationfee"] = new_televisionoperationfee;
                            expenseClaimEntity["new_fixedoperationalcost"] = new_fixedoperationalcost;
                            expenseClaimEntity["new_inboundshelvingcharge"] = new_inboundshelvingcharge;
                            expenseClaimEntity["new_itemsizehandlingfee"] = new_itemsizehandlingfee;
                            expenseClaimEntity["new_packingfee"] = new_packingfee;
                            expenseClaimEntity["new_qualityinspectionfee"] = new_qualityinspectionfee;
                            expenseClaimEntity["new_variablelaborcost"] = new_variablelaborcost;
                            expenseClaimEntity["new_warehouseexpansionfee"] = new_warehouseexpansionfee;
                            expenseClaimEntity["new_entryexithandlingfee"] = new_entryexithandlingfee;
                            expenseClaimEntity["new_fixedlaborcost"] = new_fixedlaborcost;
                            expenseClaimEntity["new_fixedrentalfee"] = new_fixedrentalfee;
                            expenseClaimEntity["new_storageandlogisticscost"] = new_storageandlogisticscost;
                            expenseClaimEntity["new_storageotherfee"] = storageotherfeesum;
                            expenseClaimEntity["new_withholdingfeerecoilstorage"] = new_withholdingfeerecoil;
                            //总费用合计
                            expenseClaimEntity["new_totalcost"] = new_totalcost;
                            //结算费用=总费用*（1+税率）
                            expenseClaimEntity["new_approvalamount"] = new_totalcost * (1 + taxrate);
                            expenseClaimEntity["new_adjuststatus"] = new OptionSetValue(3);//已调差
                            expenseClaimEntity["new_edition"] = new OptionSetValue(2);//结算单版本 = 结算单
                            OrganizationService.Update(expenseClaimEntity);
                            log.LogInformation($"分摊结算单id：{expense_claimlist[i].Id}处理已完成");
                            //创建发票数据
                            settlementHelp.CreateSAPLogStorageNoSKU(new_srv_station, expense_claimlist[i], 3, "发票", log);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Entity ent = new Entity("new_srv_expense_claim");
                    ent.Id = expense_claimlist[i].Id;
                    ent["new_adjustmenterror"] = $"调差操作异常：{ex.Message}";
                    OrganizationService.Update(ent);
                }
            }
        }
        #region MyRegion
        /// <summary>
        /// 获取特殊费用
        /// </summary>
        /// <param name="v"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        private EntityCollection GetSpecialExpense(string id, ILogger log)
        {
            QueryExpression query = new QueryExpression("new_srv_specialexpense");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
            //query.Criteria.AddCondition("new_goodsfiles_id", ConditionOperator.Null);
            query.ColumnSet.AddColumns("new_specialamount", "new_workorder_id", "new_feetype", "new_goodsfiles_id");
            return OrganizationService.RetrieveMultiple(query);
        }
        /// <summary>
        /// 根据工单服务商匹配KPI
        /// </summary>
        /// <param name="new_srv_station_id"></param>
        /// <param name="new_year"></param>
        /// <param name="new_month"></param>
        /// <returns></returns>
        public List<Entity> GetServiceKpi(List<Guid> new_srv_station_idList, int new_year, int new_month)
        {
            if (new_srv_station_idList.Count > 0)
            {
                QueryExpression qe = new QueryExpression("new_station_actualkpi");
                qe.NoLock = true;
                qe.ColumnSet = new ColumnSet("new_serviceprovider_id", "new_station_id", "new_category1_id", "new_category2_id", "new_category3_id", "new_stietype", "new_ratio", "new_kpitype_id");
                qe.Criteria.AddCondition(new ConditionExpression("new_serviceprovider_id", ConditionOperator.In, new_srv_station_idList));
                qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
                qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
                //qe.Criteria.AddCondition("new_kpistatecode", ConditionOperator.Equal, 1);
                qe.Criteria.AddCondition(new ConditionExpression("new_type", ConditionOperator.In, 1, 5));//类型 = 工单、安装
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var ec = OrganizationService.RetrieveMultiple(qe);
                if (ec?.Entities.Count > 0)
                    return ec.Entities.ToList();
            }
            return new List<Entity>();
        }
        /// <summary>
        /// 查询结算单关联工单数据
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <param name="new_station_id">服务网点id</param>
        /// <returns></returns>
        public EntityCollection GetWorkorderByExpense(string id)
        {
            QueryExpression qe = new QueryExpression("new_srv_workorder");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_name", "new_repairfee", "new_inspect_fee", "new_repairstation_id", "new_repairsubsidy", "new_servicestation_id",
                "new_boxfee", "new_returnvisitfee", "new_distancesubsidy", "new_logisticsfee", "new_recordingfee", "new_reverselogisticsfee", "new_station_id",
                "new_b2xshare", "new_guaranteefeeshare", "new_specialsubsubsidy", "new_specialfeeshare", "new_withholdingmoney", "new_inspect_fee", "new_kpitype_id",
                "new_category1_id", "new_category2_id", "new_category3_id", "new_repairprovider_id", "new_goodsfiles_id", "new_workorder_costtable_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_expenseclaimid", ConditionOperator.Equal, id);
            LinkEntity le = new LinkEntity("new_srv_workorder", "new_srv_station", "new_repairstation_id", "new_srv_stationid", JoinOperator.Inner);
            le.EntityAlias = "le";
            le.Columns = new ColumnSet("new_stietype");
            LinkEntity le1 = new LinkEntity("new_srv_workorder", "new_workorder_costtable", "new_workorder_costtable_id", "new_workorder_costtableid", JoinOperator.LeftOuter);
            le1.EntityAlias = "cost";
            le1.Columns = new ColumnSet("new_withholdingfeerepair");
            qe.LinkEntities.Add(le1);
            qe.LinkEntities.Add(le);

            EntityCollection result = new EntityCollection();
            int pageNum = 1;
            int pageSize = 5000;
            while (true)
            {
                qe.PageInfo = new PagingInfo
                {
                    Count = pageSize,
                    PageNumber = pageNum,
                    PagingCookie = pageNum == 1 ? null : result.PagingCookie
                };
                pageNum++;
                EntityCollection pageResult = OrganizationService.RetrieveMultiple(qe);
                result.Entities.AddRange(pageResult.Entities);
                result.PagingCookie = pageResult.PagingCookie;
                if (!pageResult.MoreRecords)
                {
                    break;
                }
            }

            return result;
        }
        /// <summary>
        /// 查询Handling结算单关联的工单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderByExpenseHandling(string id)
        {
            QueryExpression qe = new QueryExpression("new_srv_workorder");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_name", "new_repairstation_id", "new_withholdingmoneyhandling", "new_handlingfee", "new_station_id", "new_servicestation_id", "new_kpitype_id",
                "new_category1_id", "new_category2_id", "new_category3_id", "new_repairprovider_id", "new_goodsfiles_id", "new_workorder_costtable_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_new_expenseclaimidhandling", ConditionOperator.Equal, id);
            LinkEntity le = new LinkEntity("new_srv_workorder", "new_srv_station", "new_repairstation_id", "new_srv_stationid", JoinOperator.Inner);
            le.EntityAlias = "le";
            le.Columns = new ColumnSet("new_stietype");
            LinkEntity le1 = new LinkEntity("new_srv_workorder", "new_workorder_costtable", "new_workorder_costtable_id", "new_workorder_costtableid", JoinOperator.LeftOuter);
            le1.EntityAlias = "cost";
            le1.Columns = new ColumnSet("new_withholdingfeehandling");
            qe.LinkEntities.Add(le1);
            EntityCollection cols = CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
            return cols;
        }

        /// <summary>
        /// 服务商匹配KPI
        /// </summary>
        /// <param name="serviceKpiList"></param>
        /// <param name="workorder"></param>
        /// <returns></returns>
        public decimal CalculateKpi(List<Entity> serviceKpiList, Entity workorder)
        {
            Entity serviceKpi = new Entity();
            //维修服务商
            Guid new_repairprovider_id = workorder.Contains("new_repairprovider_id") ? workorder.GetAttributeValue<EntityReference>("new_repairprovider_id").Id : Guid.Empty;
            //维修网点
            Guid new_repairstation_id = workorder.Contains("new_repairstation_id") ? workorder.GetAttributeValue<EntityReference>("new_repairstation_id").Id : Guid.Empty;
            //一级品类
            Guid new_category1_id = workorder.Contains("new_category1_id") ? workorder.GetAttributeValue<EntityReference>("new_category1_id").Id : Guid.Empty;
            //二级品类
            Guid new_category2_id = workorder.Contains("new_category2_id") ? workorder.GetAttributeValue<EntityReference>("new_category2_id").Id : Guid.Empty;
            //三级品类
            Guid new_category3_id = workorder.Contains("new_category3_id") ? workorder.GetAttributeValue<EntityReference>("new_category3_id").Id : Guid.Empty;
            //网点类型
            int new_stietype = workorder.Contains("le.new_stietype") ? ((OptionSetValue)workorder.GetAttributeValue<AliasedValue>("le.new_stietype").Value).Value : 0;
            switch (new_stietype)
            {
                case 1:
                    new_stietype = 100000001;//msc
                    break;
                case 2:
                    new_stietype = 100000000;//esc
                    break;
                case 3:
                    new_stietype = 100000003;//cp
                    break;
                case 4:
                    new_stietype = 100000002;//sis
                    break;
                case 5:
                    new_stietype = 100000004;
                    break;
                case 6:
                    new_stietype = 100000005;//小米之家
                    break;
            }
            //服务商/一三级品类/网点类型
            if (new_repairprovider_id != Guid.Empty && new_category1_id != Guid.Empty && new_category3_id != Guid.Empty && new_stietype > 0)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && !m.Contains("new_station_id")
                    && m.Contains("new_category1_id") && m.GetAttributeValue<EntityReference>("new_category1_id").Id == new_category1_id
                    && m.Contains("new_category3_id") && m.GetAttributeValue<EntityReference>("new_category3_id").Id == new_category3_id
                    && m.Contains("new_stietype") && m.GetAttributeValue<OptionSetValue>("new_stietype").Value == new_stietype

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }

            //服务商/一级品类/网点类型
            if (new_repairprovider_id != Guid.Empty && new_category1_id != Guid.Empty && new_stietype > 0)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && !m.Contains("new_station_id")
                    && m.Contains("new_category1_id") && m.GetAttributeValue<EntityReference>("new_category1_id").Id == new_category1_id
                    && !m.Contains("new_category2_id")
                    && !m.Contains("new_category3_id")
                    && m.Contains("new_stietype") && m.GetAttributeValue<OptionSetValue>("new_stietype").Value == new_stietype

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //服务商/网点类型
            if (new_repairprovider_id != Guid.Empty && new_stietype > 0)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && !m.Contains("new_station_id")
                    && !m.Contains("new_category1_id")
                    && !m.Contains("new_category2_id")
                    && !m.Contains("new_category3_id")
                    && m.Contains("new_stietype") && m.GetAttributeValue<OptionSetValue>("new_stietype").Value == new_stietype

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //服务商+一三级
            if (new_repairprovider_id != Guid.Empty && new_repairstation_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && m.Contains("new_category1_id") && m.GetAttributeValue<EntityReference>("new_category1_id").Id == new_category1_id
                    && m.Contains("new_category3_id") && m.GetAttributeValue<EntityReference>("new_category3_id").Id == new_category3_id
                    && !m.Contains("new_stietype")

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //服务商+一级
            if (new_repairprovider_id != Guid.Empty && new_repairstation_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && m.Contains("new_category1_id") && m.GetAttributeValue<EntityReference>("new_category1_id").Id == new_category1_id
                    && !m.Contains("new_category2_id")
                    && !m.Contains("new_category3_id")
                    && !m.Contains("new_stietype")

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //维修服务商
            if (new_repairprovider_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && !m.Contains("new_station_id")
                    && !m.Contains("new_category1_id")
                    && !m.Contains("new_category2_id")
                    && !m.Contains("new_category3_id")
                    && !m.Contains("new_stietype")

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }

            if (serviceKpi != null && serviceKpi.Id != Guid.Empty)
                return serviceKpi.GetAttributeValue<decimal>("new_ratio");

            return 1m;//找不到满足条件的KPI，则设置KPI的值为1
        }
        /// <summary>
        /// 服务商匹配KPI
        /// 增加KPI类型字段，通过工单上KPI类型字段匹配服务商KPI上的KPI类型字段
        /// </summary>
        /// <param name="serviceKpiList"></param>
        /// <param name="workorder"></param>
        /// <returns></returns>
        public decimal CalculateKpitype(List<Entity> serviceKpiList, Entity workorder)
        {
            Entity serviceKpi = new Entity();
            //维修服务商
            Guid new_repairprovider_id = workorder.Contains("new_repairprovider_id") ? workorder.GetAttributeValue<EntityReference>("new_repairprovider_id").Id :
                (workorder.Contains("new_servicestation_id") ? workorder.GetAttributeValue<EntityReference>("new_servicestation_id").Id : Guid.Empty);
            //维修网点
            Guid new_repairstation_id = workorder.Contains("new_repairstation_id") ? workorder.GetAttributeValue<EntityReference>("new_repairstation_id").Id :
                (workorder.Contains("new_station_id") ? workorder.GetAttributeValue<EntityReference>("new_station_id").Id : Guid.Empty);
            //KPI类型
            Guid new_kpitype_id = workorder.Contains("new_kpitype_id") ? workorder.GetAttributeValue<EntityReference>("new_kpitype_id").Id : Guid.Empty;
            //一级品类
            Guid new_category1_id = workorder.Contains("new_category1_id") ? workorder.GetAttributeValue<EntityReference>("new_category1_id").Id : Guid.Empty;
            //二级品类
            Guid new_category2_id = workorder.Contains("new_category2_id") ? workorder.GetAttributeValue<EntityReference>("new_category2_id").Id : Guid.Empty;
            //三级品类
            Guid new_category3_id = workorder.Contains("new_category3_id") ? workorder.GetAttributeValue<EntityReference>("new_category3_id").Id : Guid.Empty;
            //网点类型
            int new_stietype = workorder.Contains("le.new_stietype") ? ((OptionSetValue)workorder.GetAttributeValue<AliasedValue>("le.new_stietype").Value).Value : 0;
            switch (new_stietype)
            {
                case 1:
                    new_stietype = 100000001;//msc
                    break;
                case 2:
                    new_stietype = 100000000;//esc
                    break;
                case 3:
                    new_stietype = 100000003;//cp
                    break;
                case 4:
                    new_stietype = 100000002;//sis
                    break;
                case 5:
                    new_stietype = 100000004;
                    break;
                case 6:
                    new_stietype = 100000005;//小米之家
                    break;
            }

            //服务商 + 服务网点 + 网点类型 + KPI类型
            if (new_repairprovider_id != Guid.Empty && new_repairstation_id != Guid.Empty && new_stietype > 0 && new_kpitype_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && m.Contains("new_station_id") && m.GetAttributeValue<EntityReference>("new_station_id").Id == new_repairstation_id
                    && m.Contains("new_stietype") && m.GetAttributeValue<OptionSetValue>("new_stietype").Value == new_stietype
                    && m.Contains("new_kpitype_id") && m.GetAttributeValue<EntityReference>("new_kpitype_id").Id == new_kpitype_id
                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //服务商 + 网点类型 + KPI类型
            if (new_repairprovider_id != Guid.Empty && new_stietype > 0 && new_kpitype_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                    m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                    && !m.Contains("new_station_id")
                    && m.Contains("new_stietype") && m.GetAttributeValue<OptionSetValue>("new_stietype").Value == new_stietype
                    && m.Contains("new_kpitype_id") && m.GetAttributeValue<EntityReference>("new_kpitype_id").Id == new_kpitype_id

                ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }
            //服务商 + KPI类型
            if (new_repairprovider_id != Guid.Empty && new_kpitype_id != Guid.Empty)
            {
                serviceKpi = serviceKpiList.Where(
                   m => m.Contains("new_serviceprovider_id") && m.GetAttributeValue<EntityReference>("new_serviceprovider_id").Id == new_repairprovider_id
                   && !m.Contains("new_station_id")
                   && !m.Contains("new_stietype")
                   && m.Contains("new_kpitype_id") && m.GetAttributeValue<EntityReference>("new_kpitype_id").Id == new_kpitype_id

               ).OrderByDescending(m => m.GetAttributeValue<decimal>("new_ratio")).FirstOrDefault();

                if (serviceKpi != null)
                    return serviceKpi.GetAttributeValue<decimal>("new_ratio");
            }

            return 1m;//找不到满足条件的KPI，则设置KPI的值为1
        }
        /// <summary>
        /// 推送到servicebus
        /// </summary>
        /// <param name="bodymessage"></param>
        /// <param name="serviceBusUrl"></param>
        /// <param name="token"></param>
        /// <param name="sessionid"></param>
        /// <returns></returns>
        public static async void sendservricebus(string bodymessage, string serviceBusUrl, string token)
        {
            try
            {
                var client = new RestClient(serviceBusUrl)
                {
                    Timeout = -1
                };
                var request = new RestRequest(Method.POST);
                request.AddHeader("Authorization", token);
                request.AddHeader("Content-Type", "application/xml");
                //request.AddHeader("BrokerProperties", "{\"SessionId\": \"" + sessionid + "\"}");
                var body = @"";
                body = bodymessage;
                request.AddParameter("application/xml", body, ParameterType.RequestBody);
                IRestResponse response = await client.ExecuteAsync(request);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("error：" + ex);
            }

        }
        #endregion
        #region 迈创结算
        /// <summary>
        /// 查询结算单下在商品档案集合中的服务单
        /// </summary>
        /// <param name="goodsfileslist"></param>
        /// <param name="expenseclaimid"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderBysku(List<Guid> goodsfileslist, Guid expenseclaimid)
        {
            EntityCollection cols = new EntityCollection();
            if (goodsfileslist.Count > 0)
            {
                QueryExpression qe = new QueryExpression("new_srv_workorder");
                qe.ColumnSet = new ColumnSet("new_expenseclaimid", "new_goodsfiles_id");
                qe.Criteria.AddCondition("new_expenseclaimidmaitrox", ConditionOperator.Equal, expenseclaimid);
                qe.Criteria.AddCondition(new ConditionExpression("new_goodsfiles_id", ConditionOperator.In, goodsfileslist));
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                cols = CommonHelper.QueryExpressionPage(OrganizationService, qe);
            }
            return cols;
        }
        /// <summary>
        /// 查询结算标准
        /// Local buy markup费（21）、物流费markup（23）、最低工单量（27）、分摊权重（28）、备件服务费（20）、 local buy markup费（Buysell）
        /// </summary>
        /// <returns></returns>
        public EntityCollection GetExpenseStandard()
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_srv_expense_standard");
            qe.ColumnSet = new ColumnSet("new_country_id", "new_workordernumber", "new_station_id", "new_amount", "new_transactioncurrency_id", "new_feetype", "new_category1_id", "new_category2_id", "new_category3_id", "new_costweight", "new_optionalcharges", "new_ratio", "new_stationservice_id", "new_category3id");
            qe.NoLock = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition(new ConditionExpression("new_feetype", ConditionOperator.In, new int[] { 20, 21, 23, 27, 28, 40 }));
            cols = CommonHelper.QueryExpressionPage(OrganizationService, qe);
            return cols;
        }
        /// <summary>
        /// 根据年月查找服务商KPI主档
        /// </summary>
        /// <param name="new_year"></param>
        /// <param name="new_month"></param>
        /// <returns></returns>
        public Entity GetServiceKpiMaitrox(int new_year, int new_month)
        {
            QueryExpression qe = new QueryExpression("new_station_actualkpi");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet(false);
            qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
            qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
            qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 2);//类型 = 备件
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            return OrganizationService.RetrieveMultiple(qe).Entities.FirstOrDefault();
        }
        /// <summary>
        /// 仓储费和物流费，如果没有填写服务单号或者SKU，分摊逻辑要从平均分摊改为按照权重分摊
        /// 新增高维费用 分摊 edit by p-songyongxiang 20240718
        /// </summary>
        /// <param name="type">10：仓储费、20:物流费、50：客户退款、60：其他特殊费用</param>
        /// <param name="expensestandard"></param>
        /// <param name="workorderCols"></param>
        /// <param name="specialexpense"></param>
        /// <param name="amlist"></param>
        /// <param name="new_country_id"></param>
        public void Weightedallocationamount(int type, decimal specialexpensefee, EntityCollection expensestandard, EntityCollection workorderCols, List<AdjustModelMaitrox> amlist, Guid new_country_id, bool isidn)
        {
            Dictionary<int, string> dicfee = new Dictionary<int, string>() { { 10, "new_warehousingfee" }, { 20, "new_markuplogisticsfee" }, { 50, "new_customerrefund" }, { 60, "new_othermiscellaneouscharges" }, { 70, "new_ecosystemcategorybuybackfee" }, { 80, "new_refurbishfee" } };
            List<new_srv_workorderMaitrox> orderList = new List<new_srv_workorderMaitrox>();
            workorderCols.Entities.ToList().ForEach(item =>
            {
                new_srv_workorderMaitrox workOrder = SettlementHelp.WorkOrderToModelsMaitrox(item);
                orderList.Add(workOrder);
            });
            var workorderGroupcategory3 = orderList.GroupBy(a => a.new_category3_id).ToDictionary(g => g.Key, g => g.ToList());
            List<CostWeightConfig> weightconfiglist = SettlementHelp.GetCostWeight(type, specialexpensefee, expensestandard, new_country_id, workorderGroupcategory3);
            decimal sumresult = weightconfiglist.Sum(a => a.result);
            if (sumresult != 0)
            {
                foreach (KeyValuePair<string, List<new_srv_workorderMaitrox>> groupItems in workorderGroupcategory3)
                {
                    CostWeightConfig weightConfig = weightconfiglist.Where(a => a.new_category3_id == groupItems.Key).FirstOrDefault();
                    if (weightConfig.result != 0)
                    {
                        decimal weightcost = weightConfig.apportionfee;
                        decimal aveweightcost = Math.Round(weightcost / groupItems.Value.Count, 2);
                        decimal sumweightcost = 0;
                        for (int i = 0; i < groupItems.Value.Count; i++)
                        {
                            if (i == groupItems.Value.Count - 1)
                            {
                                decimal lastweightcost = weightcost - sumweightcost;
                                var am = amlist.Where(a => a.new_srv_workorderid == groupItems.Value[i].new_srv_workorderid.ToString()).FirstOrDefault();
                                if (am != null)
                                {
                                    var feeoption = dicfee.SingleOrDefault(a => a.Key == type);
                                    if (feeoption.Key != 0)
                                    {
                                        if (feeoption.Key == 10 && isidn)
                                            am[feeoption.Value] += (lastweightcost * am.kpivalue);
                                        else
                                            am[feeoption.Value] += lastweightcost;
                                    }
                                }
                            }
                            else
                            {
                                var am = amlist.Where(a => a.new_srv_workorderid == groupItems.Value[i].new_srv_workorderid.ToString()).FirstOrDefault();
                                if (am != null)
                                {
                                    var feeoption = dicfee.SingleOrDefault(a => a.Key == type);
                                    if (feeoption.Key != 0)
                                    {
                                        if (feeoption.Key == 10 && isidn)
                                            am[feeoption.Value] += (aveweightcost * am.kpivalue);
                                        else
                                            am[feeoption.Value] += aveweightcost;
                                    }
                                    sumweightcost += aveweightcost;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                //所有的一级品类都找不到分摊权重结算标准，则进行平均分摊
                //没有找到权重配置，则按照平均分摊
                decimal avespecialfee = Math.Round(specialexpensefee / workorderCols.Entities.Count, 2);
                decimal sumspecialfee = 0;
                for (int i = 0; i < workorderCols.Entities.Count; i++)
                {
                    if (i == workorderCols.Entities.Count - 1)
                    {
                        decimal lastweightcost = specialexpensefee - sumspecialfee;
                        var am = amlist.Where(a => a.new_srv_workorderid == workorderCols.Entities[i].Id.ToString()).FirstOrDefault();
                        if (am != null)
                        {
                            var feeoption = dicfee.SingleOrDefault(a => a.Key == type);
                            if (feeoption.Key != 0)
                            {
                                if (feeoption.Key == 10 && isidn)
                                    am[feeoption.Value] += (lastweightcost * am.kpivalue);
                                else
                                    am[feeoption.Value] += lastweightcost;
                            }
                        }
                    }
                    else
                    {
                        var am = amlist.Where(a => a.new_srv_workorderid == workorderCols.Entities[i].Id.ToString()).FirstOrDefault();
                        if (am != null)
                        {
                            var feeoption = dicfee.SingleOrDefault(a => a.Key == type);
                            if (feeoption.Key != 0)
                            {
                                if (feeoption.Key == 10 && isidn)
                                    am[feeoption.Value] += (avespecialfee * am.kpivalue);
                                else
                                    am[feeoption.Value] += avespecialfee;
                                sumspecialfee = sumspecialfee + avespecialfee;
                            }
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 获取服务商kpi下的kpi明细
        /// </summary>
        /// <param name="workorder"></param>
        /// <param name="stationkpi"></param>
        /// <returns></returns>
        public List<Entity> GetStationKPIdetail(Guid new_srv_stationid, Guid new_country_id, Entity workorder, EntityCollection kpidetails)
        {
            //一级品类				
            Guid new_category1_id = workorder.Contains("new_category1_id") ? workorder.GetAttributeValue<EntityReference>("new_category1_id").Id : Guid.Empty;
            //二级品类				
            Guid new_category2_id = workorder.Contains("new_category2_id") ? workorder.GetAttributeValue<EntityReference>("new_category2_id").Id : Guid.Empty;
            //三级品类				
            Guid new_category3_id = workorder.Contains("new_category3_id") ? workorder.GetAttributeValue<EntityReference>("new_category3_id").Id : Guid.Empty;
            List<Entity> kpidetailcol = new List<Entity>();
            //服务商 + 国家 + 一二三级品类				
            if (new_category1_id != Guid.Empty && new_category2_id != Guid.Empty && new_category3_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id
               && a.ToDefault<Guid>("new_category3_id") == new_category3_id
               && a.ToDefault<Guid>("new_serviceprovider_id") == new_srv_stationid
               && a.ToDefault<Guid>("new_country_id") == new_country_id).ToList();
            //国家 + 一二三级品类				
            if (kpidetailcol.Count == 0
                && new_category1_id != Guid.Empty && new_category2_id != Guid.Empty && new_category3_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id
               && a.ToDefault<Guid>("new_category3_id") == new_category3_id
               && a.ToDefault<Guid>("new_country_id") == new_country_id
               && !a.Contains("new_serviceprovider_id")).ToList();
            //服务商 + 国家 + 一二级品类				
            if (kpidetailcol.Count == 0
                && new_category1_id != Guid.Empty && new_category2_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id
               && a.ToDefault<Guid>("new_serviceprovider_id") == new_srv_stationid
               && a.ToDefault<Guid>("new_country_id") == new_country_id
               && !a.Contains("new_category3_id")).ToList();
            //国家 + 一二级品类				
            if (kpidetailcol.Count == 0
                && new_category1_id != Guid.Empty && new_category2_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id
               && a.ToDefault<Guid>("new_country_id") == new_country_id
               && !a.Contains("new_category3_id")
               && !a.Contains("new_serviceprovider_id")).ToList();
            //服务商 + 国家 + 一级品类			
            if (kpidetailcol.Count == 0 && new_category1_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_serviceprovider_id") == new_srv_stationid
               && a.ToDefault<Guid>("new_country_id") == new_country_id
               && !a.Contains("new_category2_id")
               && !a.Contains("new_category3_id")).ToList();
            ///国家 + 一级品类				
            if (kpidetailcol.Count == 0 && new_category1_id != Guid.Empty)
                kpidetailcol = kpidetails.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
             && !a.Contains("new_category2_id") && !a.Contains("new_category3_id")
             && !a.Contains("new_serviceprovider_id")
             && a.ToDefault<Guid>("new_country_id") == new_country_id).ToList();
            return kpidetailcol;
        }
        /// <summary>
        /// 获取换机local buy markup结算标准
        /// </summary>
        /// <param name="expensestandard"></param>
        /// <param name="workorder"></param>
        /// <returns></returns>
        public Entity GetLocalbuymarkupstandard(Guid new_country_id, EntityCollection expensestandard, Entity workorder)
        {
            //一级品类
            Guid new_category1_id = workorder.Contains("new_category1_id") ? workorder.GetAttributeValue<EntityReference>("new_category1_id").Id : Guid.Empty;
            //二级品类
            Guid new_category2_id = workorder.Contains("new_category2_id") ? workorder.GetAttributeValue<EntityReference>("new_category2_id").Id : Guid.Empty;
            //三级品类
            Guid new_category3_id = workorder.Contains("new_category3_id") ? workorder.GetAttributeValue<EntityReference>("new_category3_id").Id : Guid.Empty;
            Entity markupstandard = null;
            //国家 + 一二三级品类
            if (new_category1_id != Guid.Empty && new_category2_id != Guid.Empty && new_category3_id != Guid.Empty && new_country_id != Guid.Empty)
                markupstandard = expensestandard.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id && a.ToDefault<Guid>("new_category3_id") == new_category3_id
               && a.ToDefault<Guid>("new_country_id") == new_country_id && a.ToDefault<int>("new_feetype") == 21).FirstOrDefault();
            //国家 + 一二级品类
            if (markupstandard == null && new_category1_id != Guid.Empty && new_category2_id != Guid.Empty && new_country_id != Guid.Empty)
                markupstandard = expensestandard.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && a.ToDefault<Guid>("new_category2_id") == new_category2_id && !a.Contains("new_category3_id")
               && a.ToDefault<Guid>("new_country_id") == new_country_id && a.ToDefault<int>("new_feetype") == 21).FirstOrDefault();
            //国家 + 一级品类
            if (markupstandard == null && new_category1_id != Guid.Empty && new_country_id != Guid.Empty)
                markupstandard = expensestandard.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
               && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")
               && a.ToDefault<Guid>("new_country_id") == new_country_id && a.ToDefault<int>("new_feetype") == 21).FirstOrDefault();
            return markupstandard;
        }
        /// <summary>
        /// 查询迈创服务商结算单关联工单数据
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <param name="new_station_id">服务网点id</param>
        /// <returns></returns>
        public EntityCollection GetWorkorderByExpenseMaitrox(string id)
        {
            QueryExpression qe = new QueryExpression("new_srv_workorder");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_name", "new_repairstation_id", "new_station_id",
                "new_category1_id", "new_category2_id", "new_category3_id", "new_repairprovider_id",
                "new_sparepartscost", "new_markuplogisticsfee", "new_warehousingfee", "new_minimumworkorderfee",
                "new_fixedservicefee", "new_capitalinterestexpense", "new_ecosystemcategorybuybackfee",
                "new_localbuyreplacementcost", "new_markupreplacementcost", "new_directshippingcost",
                "new_customerrefund", "new_othermiscellaneouscharges", "new_withholdingfeemaitrox",
                "new_partservicecost", "new_partservicecostkpi", "new_withholdingmoneymaitrox", "new_withholdingfeerecoil");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_expenseclaimidmaitrox", ConditionOperator.Equal, id);
            EntityCollection orders = CommonHelper.QueryExpressionPage(OrganizationService, qe);
            return orders;
        }
        /// <summary>
        /// 迈创结算单获取加减项费用
        /// </summary>
        /// <param name="v"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        private EntityCollection GetSpecialExpenseMaitrox(string id, ILogger log)
        {
            QueryExpression query = new QueryExpression("new_srv_specialexpense");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
            query.ColumnSet.AllColumns = true;
            return OrganizationService.RetrieveMultiple(query);
        }
        /// <summary>
        /// 判断国家是否是印度尼西亚
        /// </summary>
        /// <param name="new_country_id"></param>
        /// <returns></returns>
        public bool CheckCountryIsIDN(Guid new_country_id)
        {
            QueryExpression qe = new QueryExpression("new_country");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet(false);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, new_country_id);
            qe.Criteria.AddCondition("new_code", ConditionOperator.Equal, "ID");//两位简码
            qe.Criteria.AddCondition("new_code1", ConditionOperator.Equal, "IDN");//三位简码
            Entity country = OrganizationService.RetrieveMultiple(qe).Entities.FirstOrDefault();
            if (country != null)
                return true;
            else
                return false;
        }
        /// <summary>
        /// Author：p-songyongxiang
        /// Create Date：2024/3/19
        /// 根据一二三级品类，国家去匹配结算标准，关联逻辑优先级如下：一二三级品类+国家、一二级品类+国家、一级品类+国家，获取匹配到的结算标准中的第一条数据，将金额赋值给服务单上的备件服务费
        /// </summary>
        /// <param name="workorder"></param>
        /// <param name="service"></param>
        /// <returns></returns>
        public Entity CalcPartsServiceCost(Entity workorder, Guid countryid, List<Entity> expense_standardlist, IOrganizationService service)
        {
            Entity expense_standard = null;
            //一级品类
            Guid new_category1_id = workorder.Contains("new_category1_id") ? workorder.GetAttributeValue<EntityReference>("new_category1_id").Id : Guid.Empty;
            //二级品类
            Guid new_category2_id = workorder.Contains("new_category2_id") ? workorder.GetAttributeValue<EntityReference>("new_category2_id").Id : Guid.Empty;
            //三级品类
            Guid new_category3_id = workorder.Contains("new_category3_id") ? workorder.GetAttributeValue<EntityReference>("new_category3_id").Id : Guid.Empty;
            //国家 + 一二三级品类
            if (new_category1_id != Guid.Empty && new_category2_id != Guid.Empty && new_category3_id != Guid.Empty)
                expense_standard = expense_standardlist.Where(a => a.ToDefault<Guid>("new_category1_id") == new_category1_id
                && a.ToDefault<Guid>("new_category2_id") == new_category2_id && a.ToDefault<Guid>("new_category3_id") == new_category3_id
                && a.ToDefault<Guid>("new_country_id") == countryid).FirstOrDefault();
            //国家 + 一二级品类
            if (expense_standard == null && new_category1_id != Guid.Empty && new_category2_id != Guid.Empty)
                expense_standard = expense_standardlist.Where(a => !a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category2_id") == new_category2_id
                && a.ToDefault<Guid>("new_category1_id") == new_category1_id && a.ToDefault<Guid>("new_country_id") == countryid).FirstOrDefault();
            //国家 + 一级品类
            if (expense_standard == null && new_category1_id != Guid.Empty)
                expense_standard = expense_standardlist.Where(a => !a.Contains("new_category2_id") && !a.Contains("new_category3_id")
                && a.ToDefault<Guid>("new_category1_id") == new_category1_id && a.ToDefault<Guid>("new_country_id") == countryid).FirstOrDefault();

            return expense_standard;
        }
        #endregion
        #region 激活结算
        /// <summary>
        /// 激活结算单获取加减项费用
        /// </summary>
        /// <param name="v"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        private EntityCollection GetSpecialExpenseActivate(string id)
        {
            QueryExpression query = new QueryExpression("new_srv_specialexpense");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
            query.ColumnSet.AllColumns = true;
            return OrganizationService.RetrieveMultiple(query);
        }
        /// <summary>
        /// 查询激活结算单明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private EntityCollection GetExpenseActivateline(string id)
        {
            QueryExpression query = new QueryExpression("new_srv_expense_activationline");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
            query.ColumnSet.AllColumns = true;
            return CommonHelper.QueryExpressionPage(OrganizationService, query);
        }
        #endregion
        #region 仓储物流
        /// <summary>
        /// 根据结算单查询工单费用
        /// </summary>
        /// <param name="expenseclaimidstorage"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderCost(string expenseclaimidstorage)
        {
            QueryExpression qe = new QueryExpression("new_workorder_costtable");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_expenseclaimidstorage", ConditionOperator.Equal, expenseclaimidstorage);
            LinkEntity link = new LinkEntity("new_workorder_costtable", "new_workorder_settlement", "new_workorder_settlement_id", "new_workorder_settlementid", JoinOperator.Inner);
            link.Columns = new ColumnSet("new_sku");
            link.EntityAlias = "workorder_settlement";
            qe.LinkEntities.Add(link);
            return CommonHelper.QueryExpressionPage(OrganizationService, qe);
        }
        /// <summary>
        /// 根据结算单id（物流）查询工单费用
        /// </summary>
        /// <param name="expenseclaimidlogistics"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderCostLogistics(string expenseclaimidlogistics)
        {
            QueryExpression qe = new QueryExpression("new_workorder_costtable");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_expenseclaimidlogistics", ConditionOperator.Equal, expenseclaimidlogistics);
            return CommonHelper.QueryExpressionPage(OrganizationService, qe);
        }
        /// <summary>
        /// 根据结算单查询工单信息同步表
        /// </summary>
        /// <param name="expensecalimid"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderSettle(Guid expensecalimid)
        {
            QueryExpression qe = new QueryExpression("new_workorder_settlement");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.ColumnSet = new ColumnSet("new_category1_code", "new_category2_code", "new_category3_code", "new_model1_code", "new_model2_code", "new_sparepartscoststorage");
            LinkEntity link = new LinkEntity("new_workorder_settlement", "new_workorder_costtable", "new_workorder_settlementid", "new_workorder_settlement_id", JoinOperator.Inner);
            link.LinkCriteria.AddCondition("new_expenseclaimidstorage", ConditionOperator.Equal, expensecalimid);
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            link.Columns = new ColumnSet("new_workorder_costtableid");
            link.EntityAlias = "costtable";
            qe.LinkEntities.Add(link);
            return CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
        }
        /// <summary>
        /// 根据结算单id（物流）查询工单信息同步表
        /// </summary>
        /// <param name="expensecalimid"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderSettlelogistics(Guid expensecalimid)
        {
            QueryExpression qe = new QueryExpression("new_workorder_settlement");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.ColumnSet = new ColumnSet("new_category1_code", "new_category2_code", "new_category3_code", "new_model1_code", "new_model2_code", "new_sparepartscoststorage");
            LinkEntity link = new LinkEntity("new_workorder_settlement", "new_workorder_costtable", "new_workorder_settlementid", "new_workorder_settlement_id", JoinOperator.Inner);
            link.LinkCriteria.AddCondition("new_expenseclaimidlogistics", ConditionOperator.Equal, expensecalimid);
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.LinkEntities.Add(link);
            return CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
        }
        /// <summary>
        /// 查询仓储类型kpi明细
        /// </summary>
        /// <param name="new_year"></param>
        /// <param name="new_month"></param>
        /// <returns></returns>
        public EntityCollection GetStorageKpidetail(int new_year, int new_month)
        {
            QueryExpression qe = new QueryExpression("new_station_countrykpiline");
            qe.ColumnSet = new ColumnSet("new_feetype", "new_ratio", "new_serviceprovider_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var link = new LinkEntity("new_station_countrykpiline", "new_station_actualkpi", "new_station_actualkpi_id", "new_station_actualkpiid", JoinOperator.Inner);
            link.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, new_year);
            link.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, new_month);
            link.LinkCriteria.AddCondition("new_type", ConditionOperator.Equal, 4);//类型 = 仓储
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.LinkEntities.Add(link);
            EntityCollection kpidetails = OrganizationService.RetrieveMultiple(qe);
            return kpidetails;
        }
        /// <summary>
        /// 匹配仓储结费kpi明细，匹配不到kpi则默认kpi为1；
        /// </summary>
        /// <param name="kpicols"></param>
        /// <param name="expensetype"></param>
        /// <returns></returns>
        public decimal CalculateKpiStorage(EntityCollection kpicols, Guid serviceprovider_id, int expensetype)
        {
            decimal radio = 1;
            if (expensetype == -1)
            {
                var kpidetail = kpicols.Entities.Where(a => a.Contains("new_serviceprovider_id")
               && !a.Contains("new_feetype")
               && a.ToDefault<Guid>("new_serviceprovider_id") == serviceprovider_id).FirstOrDefault();
                if (kpidetail != null)
                    radio = kpidetail.GetAttributeValue<decimal>("new_ratio");
            }
            else
            {
                //服务商+费用类型
                var kpidetail = kpicols.Entities.Where(a => a.Contains("new_serviceprovider_id")
                && a.Contains("new_feetype")
                && a.ToDefault<Guid>("new_serviceprovider_id") == serviceprovider_id
                && a.ToDefault<int>("new_feetype") == expensetype).FirstOrDefault();
                //服务商
                if (kpidetail == null)
                    kpidetail = kpicols.Entities.Where(a => a.Contains("new_serviceprovider_id")
                && !a.Contains("new_feetype")
                && a.ToDefault<Guid>("new_serviceprovider_id") == serviceprovider_id).FirstOrDefault();
                if (kpidetail != null)
                    radio = kpidetail.GetAttributeValue<decimal>("new_ratio");
            }

            return radio;
        }
        #endregion
        #region 安装结算
        /// <summary>
        /// 查询安装结算单的工单费用
        /// </summary>
        /// <param name="expense_claimid"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderInstall(string expense_claimid)
        {
            QueryExpression qe = new QueryExpression("new_srv_workorder");
            qe.ColumnSet = new ColumnSet("new_workorder_costtable_id", "new_category1_id", "new_category2_id", "new_category3_id", "new_servicestation_id", "new_station_id", "new_repairprovider_id", "new_repairstation_id", "new_kpitype_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link = new LinkEntity("new_srv_workorder", "new_workorder_costtable", "new_workorder_costtable_id", "new_workorder_costtableid", JoinOperator.Inner);
            link.EntityAlias = "link";
            link.Columns = new ColumnSet("new_withholdingmoneyinstall", "new_settlementmoneyinstall", "new_changemoneyinstall", "new_expenseclaimidinstall", "new_installfee",
                "new_installfeekpi", "new_othermiscellaneouschargesinstall", "new_transactioncurrency_installservice_id", "new_withholdingfeeinstall", "new_withholdingfeerecoilinstall", "new_fixedmonthfeeinstall");
            link.LinkCriteria.AddCondition("new_expenseclaimidinstall", ConditionOperator.Equal, expense_claimid);
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.LinkEntities.Add(link);
            return CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
        }
        #endregion
        #region 高维工厂结算
        /// <summary>
        /// 查询修整单明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private EntityCollection GetExpenseTrimorderdetail(string id)
        {
            QueryExpression query = new QueryExpression("new_trimming_orderdetail");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_expenseclaim_id", ConditionOperator.Equal, id);
            query.ColumnSet.AllColumns = true;
            return CommonHelper.QueryExpressionPage(OrganizationService, query);
        }
        #endregion
    }
}
