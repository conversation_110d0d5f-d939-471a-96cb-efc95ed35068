﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Api.Biz;
using RekTec.Api.Biz.Model;
using RekTec.Crm.Common.Helper;
using RekTec.Service1.Service.Bll;
using RekTec.Service1.Service.Helper;
using RekTec.Service1.Service.Model;

namespace RekTec.Service1.Service.Command
{
    /// <summary>
    /// 费用结算抛BPM Command类
    /// </summary>
    public class BpmExpenseCommand : BaseBpmInterface
    {
        /// <summary>
        /// 费用结算提交BPM审批
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <param name="formType">单据类型</param>
        /// <returns></returns>
        public override Bpmbody Interfacebody(string entityname, string entityid, string formType)
        {
            Bpmbody body = new Bpmbody();
            BpmExpenseModel model = new BpmExpenseModel();
            try
            {
                #region 数据校验
                var entity = OrganizationService.Retrieve(entityname, new Guid(entityid), new ColumnSet(true));
                if (entity == null)
                {
                    throw new Exception(GetResource("Record_IsNotFound", "未获取到当前单据信息"));
                }
                #endregion

                #region 请求体字段

                #region 常规字段

                // 服务商
                if (entity.Contains("new_srv_station_id"))
                {
                    model.new_srv_station_id = entity.GetAttributeValue<EntityReference>("new_srv_station_id").Name;
                }
                // 结算年份
                if (entity.Contains("new_year"))
                {
                    model.new_year = entity.GetAttributeValue<OptionSetValue>("new_year").Value.ToString();
                }
                // 结算月份
                if (entity.Contains("new_month"))
                {
                    model.new_month = entity.GetAttributeValue<OptionSetValue>("new_month").Value.ToString();
                }
                //单号
                model.new_name = model.new_srv_station_id + model.new_month + "月";//entity.GetAttributeValue<string>("new_name");
                // 总费用合计
                decimal totalCost = decimal.Zero;
                if (entity.Contains("new_approvalamount"))
                {
                    model.new_totalcost = Math.Round(entity.GetAttributeValue<decimal>("new_approvalamount"), 2).ToString();
                    totalCost = Math.Round(entity.GetAttributeValue<decimal>("new_approvalamount"), 2);
                }
                // 币种
                if (entity.Contains("new_transactioncurrency_id"))
                {
                    model.new_transactioncurrency_id = entity.GetAttributeValue<EntityReference>("new_transactioncurrency_id").Name;
                }
                #endregion

                #region 发票信息
                // 发票编号
                model.new_invoiceno = entity.GetAttributeValue<string>("new_invoiceno");
                // 附件
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("entityId", entityid.Replace("{", "").Replace("}", ""));
                dic.Add("entityName", entityname);
                dic.Add("filetype", "");
                dic.Add("new_mimetype", "");
                string file = CrmHelper.InvokeHiddenApi(OrganizationService, "new_comattachment", "Attachment/GetFileByRecordByMimetype", dic);
                if (!string.IsNullOrWhiteSpace(file))
                {

                    List<AttachmentModel> list = JsonHelper.Deserialize<List<AttachmentModel>>(file);
                    foreach (var item in list)
                    {
                        //ExpenseFileModel fmoel = new ExpenseFileModel();
                        //fmoel.url = item.new_filepath;
                        //model.fileList.Add(fmoel);
                        model.fileList.Add(item.new_filepath);
                    }
                }
                #endregion

                #region  服务单信息
                // 维修劳务费
                if (entity.Contains("new_repairmoney"))
                {
                    model.new_repairmoney = Math.Round(entity.GetAttributeValue<decimal>("new_repairmoney"), 2).ToString();
                }
                // 箱子使用费
                if (entity.Contains("new_casemoney"))
                {
                    model.new_casemoney = Math.Round(entity.GetAttributeValue<decimal>("new_casemoney"), 2).ToString();
                }
                // 寄修补贴
                if (entity.Contains("new_mailrepairmoney"))
                {
                    model.new_mailrepairmoney = Math.Round(entity.GetAttributeValue<decimal>("new_mailrepairmoney"), 2).ToString();
                }
                // 路程补贴费
                if (entity.Contains("new_distancemoney"))
                {
                    model.new_distancemoney = Math.Round(entity.GetAttributeValue<decimal>("new_distancemoney"), 2).ToString();
                }

                // 备件费用
                if (entity.Contains("new_partcost"))
                {
                    model.new_partcost = Math.Round(entity.GetAttributeValue<decimal>("new_partcost"), 2).ToString();
                }
                // 上门物流费
                if (entity.Contains("new_comedoormoney"))
                {
                    model.new_comedoormoney = Math.Round(entity.GetAttributeValue<decimal>("new_comedoormoney"), 2).ToString();
                }
                // 录单费
                if (entity.Contains("new_recordingmoney"))
                {
                    model.new_recordingmoney = Math.Round(entity.GetAttributeValue<decimal>("new_recordingmoney"), 2).ToString();
                }

                // 其他费用
                if (entity.Contains("new_othermoney"))
                {
                    model.new_othermoney = Math.Round(entity.GetAttributeValue<decimal>("new_othermoney"), 2).ToString();
                }
                // 结算劳务费
                //if (entity.Contains("new_servicemoney"))
                //{
                //    model.new_servicemoney = Math.Round(entity.GetAttributeValue<decimal>("new_servicemoney"), 2).ToString();
                //}
                // 保底费
                if (entity.Contains("new_minimummoney"))
                {
                    model.new_minimummoney = Math.Round(entity.GetAttributeValue<decimal>("new_minimummoney"), 2).ToString();
                }

                // 服务单明细
                model.orderList = GetWorkOrderList(entityid);
                #endregion

                #region 特殊费用
                // 特殊费用
                if (entity.Contains("new_specialmoney"))
                {
                    model.new_specialmoney = Math.Round(entity.GetAttributeValue<decimal>("new_specialmoney"), 2).ToString();
                }

                //明细
                model.specialExpenseList = GetSpecialExpenseList(entityid, formType);
                #endregion

                #region 激活结费
                // 激活结费
                if (entity.Contains("new_activationmoney"))
                {
                    model.new_activationmoney = Math.Round(entity.GetAttributeValue<decimal>("new_activationmoney"), 2).ToString();
                }

                // 明细
                model.expenseActivesList = GetExpenseActiveList(entityid);
                #endregion

                #region  受理单
                // 结算收集点补贴
                if (entity.Contains("new_collectionmoney"))
                {
                    model.new_collectionmoney = Math.Round(entity.GetAttributeValue<decimal>("new_collectionmoney"), 2).ToString();
                }

                // 明细
                model.incidentList = GetIncidentList(entityid);
                #endregion

                #region  运行商HF
                // 运行商HF
                if (entity.Contains("new_operatehfmoney"))
                {
                    model.new_operatehfmoney = Math.Round(entity.GetAttributeValue<decimal>("new_operatehfmoney"), 2).ToString();
                }
                // 明细
                model.expenseHFList = GetExpenseHFList(entityid);
                #endregion

                #region 小数调差
                model.expenseAdjustmentList = GetExpenseAdjustmentList(entityid);
                #endregion

                #endregion

                #region 扩展字段
                Variables variables = new Variables();
                //costBody： 支付主体；supplyType：服务商类型；totalCost：结算金额
                if (entity.Contains("new_srv_station_id"))
                {
                    QueryExpression query = new QueryExpression("new_srv_station");
                    query.ColumnSet = new ColumnSet("new_contractingbody");
                    query.Criteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, entity.GetAttributeValue<EntityReference>("new_srv_station_id").Id);

                    LinkEntity le = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                    le.Columns = new ColumnSet("new_code");
                    le.EntityAlias = "le";
                    query.LinkEntities.Add(le);
                    var ec = OrganizationService.RetrieveMultiple(query);
                    if (ec?.Entities?.Count > 0)
                    {
                        string costBody = ec.Entities[0].GetAttributeValue<string>("new_contractingbody");
                        variables.model.Add("costBody", costBody);
                        if (ec.Entities[0].Contains("le.new_code"))
                        {
                            variables.model.Add("supplyType", ec.Entities[0].GetAttributeValue<AliasedValue>("le.new_code").Value.ToString());
                        }
                    }
                }
                variables.model.Add("totalCost", totalCost);
                #endregion

                #region 参数处理

                BpmModel bpmmodel = new BpmModel();
                bpmmodel.businessKey = model.businessKey;
                string Key = string.Empty;
                string Title = string.Empty;
                // 发票申请 和付款申请
                if (formType == "3")
                {
                    Key = "new_srv_expense_claim_BPMPROCESSKeyForBILL";
                    Title = GetResource("ExpenseClaimBillsApply", "费用结算发票申请");
                }
                else if (formType == "6")
                {
                    Key = "new_srv_expense_claim_BPMPROCESSKeyForPAY";
                    Title = GetResource("ExpenseClaimPayApply", "费用结算付款申请");
                }
                bpmmodel.processKey = RekTec.Crm.Common.Helper.CrmHelper.GetSystemParameterValue(OrganizationService, Key);
                bpmmodel.processTitle = "ISP 付款申请 " + entity.GetAttributeValue<string>("new_name");
                bpmmodel.business = JsonHelper.Serialize(model);

                body.bpmModel = bpmmodel;
                body.variables = variables;
                #endregion
                Log.InfoMsg($"抛BPM参数：{formType}__" + JsonHelper.Serialize(model));

                return body;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 备件费用结算提交BPM审批
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <param name="formType">单据类型</param>
        /// <returns></returns>
        public override BpmPartbody InterfacebodyParts(string entityname, string entityid) 
        {
            BpmPartbody body = new BpmPartbody();
            BpmPartsExpenseModel model = new BpmPartsExpenseModel();
            try
            {
                #region 数据校验
                var entity = OrganizationService.Retrieve(entityname, new Guid(entityid), new ColumnSet(true));
                if (entity == null)
                {
                    throw new Exception(GetResource("Record_IsNotFound", "未获取到当前单据信息"));
                }
                #endregion
                var errorForOwner = string.Empty;
                var requestuser = "";
                var requestuserName = "";
                string bpmstartuser = GetSystemParameterValue(OrganizationService, "BPMStartUser");
                if (!string.IsNullOrWhiteSpace(bpmstartuser))
                {
                    var splitstr = bpmstartuser.Split(';');
                    if (splitstr.Count() == 2)
                    {
                        var email = Cast.ConToString(splitstr[0]).Split('@');
                        if (email.Length >= 1)
                            requestuser = email[0];
                        requestuserName = splitstr[1];
                    }
                }
                if (string.IsNullOrWhiteSpace(requestuser))
                {
                    requestuser = GetCreatedIdByOwner(OrganizationServiceAdmin, this.UserId.ToString(), out errorForOwner);//动作执行人
                    requestuserName = requestuser;
                }
                if (string.IsNullOrWhiteSpace(requestuser))
                {
                    throw new InvalidPluginExecutionException(errorForOwner);
                }
                //获取币种
                string currency = "";
                string countrycode = "";//国家代码
                string fetchlinkentity = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='new_srv_expense_claim'>
    <attribute name='new_name' />
    <attribute name='new_srv_expense_claimid' />
    <order attribute='new_name' descending='false' />
    <filter type='and'>
      <condition attribute='new_srv_expense_claimid' operator='eq' uitype='new_srv_expense_claim' value='{entityid}' />
    </filter>
    <link-entity name='new_country' from='new_countryid' to='new_country_id' visible='false' link-type='outer' alias='ab'>
      <attribute name='new_code' />
    </link-entity>
    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' visible='false' link-type='outer' alias='ac'>
      <attribute name='isocurrencycode' />
    </link-entity>
  </entity>
</fetch>";
                var linkEntity = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchlinkentity)).Entities.FirstOrDefault();
                if (linkEntity != null)
                {
                    if (linkEntity.Contains("ab.new_code"))
                    {
                        countrycode = linkEntity.GetAliasAttributeValue<string>("ab.new_code");
                    }
                    if (linkEntity.Contains("ac.isocurrencycode"))
                    {
                        currency = linkEntity.GetAliasAttributeValue<string>("ac.isocurrencycode");
                    }
                }
                //获取SAP供应商编码
                string sapcode = "";
                //区域
                string new_region_id = "";
                //服务商简称
                string stationabbreviation = "";
                if (entity.IsNotNull("new_srv_station_id")) 
                {
                    string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='new_srv_station'>
    <attribute name='new_srv_stationid' />
    <attribute name='new_name' />
    <attribute name='createdon' />
    <attribute name='new_supplier' />
    <attribute name='new_stationabbreviation' />
    <order attribute='new_name' descending='false' />
    <filter type='and'>
      <condition attribute='new_srv_stationid' operator='eq' uitype='new_srv_station' value='{entity.ToDefault<Guid>("new_srv_station_id")}' />
    </filter>
    <link-entity name='new_region' from='new_regionid' to='new_region_id' visible='false' link-type='outer' alias='ar'>
      <attribute name='new_code' />
      <attribute name='new_name' />
    </link-entity>
  </entity>
</fetch>";
                    Entity new_srv_station = OrganizationService.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    if (new_srv_station != null)
                    {
                        sapcode = new_srv_station?.ToDefault<string>("new_supplier");
                        new_region_id = new_srv_station.Contains("ar.new_code") ? new_srv_station.GetAliasAttributeValue<string>("ar.new_code") : "";
                        stationabbreviation = new_srv_station.ToDefault<string>("new_stationabbreviation");
                    }
                }
                //查询支付公司主体
                Entity new_paymentcompanybody = GetPaymentcompanybody(entity.ToDefault<string>("new_contractingbody"));
                string paymentcompanybody = new_paymentcompanybody != null ? new_paymentcompanybody.ToDefault<string>("new_companyname") + "(" + new_paymentcompanybody.ToDefault<string>("new_contractingbody") + ")" : "";
                #region 请求体字段

                #region 基础信息
                //申请人
                model.input_137bfef89a20 = requestuserName;
                //申请人部门
                model.input_f071d5147411 = GetSystemParameterValue(OrganizationService, "BPMStartUserDepartment");
                //支付公司
                model.input_5e33b62e046b = paymentcompanybody;
                //承担公司
                model.input_fa2973ef5f79 = paymentcompanybody;
                //付款单号
                model.input_5aea43494178 = entity.ToDefault<string>("new_name");
                //付款金额
                model.input_e74a0b9bcf56 = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                //付款方式
                model.input_b3a49ceddd6e = entity.FormattedValues.ContainsKey("new_payway") ? entity.FormattedValues["new_payway"] : "电汇";
                //提交审批日期
                model.input_df86a76189eb = DateTime.Now.ToString("yyyy-MM-dd");
                //最迟付款日期
                model.input_7014975e3ac6 = entity.FormattedValues.Contains("new_latestpaymentdate") ? Convert.ToDateTime(entity["new_latestpaymentdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                //服务商名称
                //model.input_689672f471d2 = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                //SAP供应商编码
                model.input_ba0e1e612c48 = "0000" + sapcode;
                //国际业务性质
                model.input_5194f429fe93 = "售后";
                //费用类型
                model.input_598d2d8d10f9 = "备件运营-迈创侧费用";
                #endregion
                #region 付款单信息
                //国家
                model.input_ac2890dca977 = entity.IsNotNull("new_country_id") ? entity.ToEr("new_country_id").Name : "";
                //国家代码
                model.input_e837cf0ceca5 = countrycode;
                //服务商名称
                model.textarea_003372613a8d = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                //结算年份
                model.input_3ed54e02a87e = entity.ToDefault<int>("new_year").ToString();
                //结算月份
                model.input_3ef6442527a7 = entity.ToDefault<int>("new_month").ToString();
                //预提费用
                model.input_24f259c23c02 = entity.ToDefault<decimal>("new_withholdingmoney").ToString("f2") + "(" + currency + ")";
                //结算费用
                model.input_2eb132132cc0 = entity.ToDefault<decimal>("new_totalcost").ToString("f2") + "(" + currency + ")";
                //增值税率
                model.input_ebc61c4ee991 = entity.ToDefault<decimal>("new_taxrate").ToString("f2");
                //增值税额
                model.input_1db56adf999d = entity.ToDefault<decimal>("new_vatamount").ToString("f2") + "(" + currency + ")";
                //代扣税率
                model.input_2269950f67a7 = entity.ToDefault<decimal>("new_withholdingtax").ToString("f2");
                //代扣税额
                model.input_95f97c096609 = entity.ToDefault<decimal>("new_withholdingamount").ToString("f2") + "(" + currency + ")";
                //付款金额
                model.input_27271fb86b9d = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                //开户银行
                model.textarea_614427d1e7a1 = entity.ToDefault<string>("new_bank");
                //开户名称
                model.textarea_1a1dabebb02b = entity.ToDefault<string>("new_accountbankname");
                //银行账号
                model.input_63b9cb7aa1bd = entity.ToDefault<string>("new_bankaccount");
                //SWIFT CODE
                model.input_5aa28b5252d1 = entity.ToDefault<string>("new_swiftcode");
                //签约主体
                model.input_5a3ee052b7e6 = entity.ToDefault<string>("new_contractingbody");
                //付款金额（隐藏）
                model.inputFloat_07d2f59f5d7e = entity.ToDefault<decimal>("new_approvalamount");
                //区域
                model.input_592d42d196f8 = new_region_id;
                //币种
                model.input_f9bc0d7a4554 = currency;
                //国家
                model.input_073fb66e2bbc = countrycode;
                //是否reverse charge
                model.input_fcf4a85bb57d = entity.Contains("new_isreversecharge") ? entity.FormattedValues["new_isreversecharge"] : "";
                //主题 = 国家+服务商简称+年月+费用类型
                model.input_fb91992140e9 = model.input_ac2890dca977 + stationabbreviation + model.input_3ed54e02a87e + "年" + model.input_3ef6442527a7 + "月" + model.input_598d2d8d10f9;
                #endregion
                #region 服务单明细
                //工单数量
                model.input_fd30466bc07a = entity.ToDefault<int>("new_workordercount").ToString();
                //备件费
                model.input_e374d4c0a7f4 = entity.ToDefault<decimal>("new_sparepartscost").ToString("f2");
                //备件服务费
                model.input_a6b388db4c48 = entity.ToDefault<decimal>("new_partservicecost").ToString("f2");
                //单量保底费
                model.input_328e34536d2f = entity.ToDefault<decimal>("new_minimumworkorderfee").ToString("f2");
                //换机local buy费
                model.input_a2fcfcd4007d = entity.ToDefault<decimal>("new_localbuyreplacementcost").ToString("f2");
                //换机markup费
                model.input_16d20f109f70 = entity.ToDefault<decimal>("new_markupreplacementcost").ToString("f2");
                //物流费
                model.input_9ce6241a80d3 = entity.ToDefault<decimal>("new_markuplogisticsfee").ToString("f2");
                //仓储费
                model.input_8217963f70d4 = entity.ToDefault<decimal>("new_warehousingfee").ToString("f2");
                //墨西哥固定服务费
                model.input_3bbea73e4fad = entity.ToDefault<decimal>("new_fixedservicefee").ToString("f2");
                //客户退款
                model.input_e9d51de00487 = entity.ToDefault<decimal>("new_customerrefund").ToString("f2");
                //资本利息费
                model.input_d4bd815889f9 = entity.ToDefault<decimal>("new_capitalinterestexpense").ToString("f2");
                //其他特殊费用
                model.input_49ab64a41661 = entity.ToDefault<decimal>("new_othermiscellaneouscharges").ToString("f2");
                //生态链品类回购费
                model.input_97a764351491 = entity.ToDefault<decimal>("new_ecosystemcategorybuybackfee").ToString("f2");
                //箱子使用费
                model.input_f547cd82ffd5 = entity.ToDefault<decimal>("new_casemoney").ToString("f2");
                EntityCollection kpicols = GetCountrykpiline(entity.ToDefault<int>("new_year"), entity.ToDefault<int>("new_month"), entity.ToDefault<Guid>("new_srv_station_id"), entity.ToDefault<Guid>("new_country_id"));
                EntityCollection category1 = Getcategory1();
                EntityCollection category2 = Getcategory2();
                EntityCollection category3 = Getcategory3(new string[] { "30000001", "30000008", "30000047", "30000011", "30000063" });
                Entity ecologicalchain = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000005").FirstOrDefault();//生态链
                Entity sweepingrobot = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000063").FirstOrDefault();//扫地机器人
                Entity phone3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000001").FirstOrDefault();//手机
                Entity phone2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000001").FirstOrDefault();//手机
                Entity phone1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000001").FirstOrDefault();//手机
                Entity television3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000008").FirstOrDefault();//电视
                Entity television2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000008").FirstOrDefault();//电视
                Entity television1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000003").FirstOrDefault();//电视
                Entity scooter = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000047").FirstOrDefault();//滑板车
                Entity notebook3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000011").FirstOrDefault();//笔记本
                Entity notebook2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000011").FirstOrDefault();//笔记本
                Entity notebook1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000004").FirstOrDefault();//笔记本
                var ecologicalchainkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == ecologicalchain?.Id
                && !a.Contains("new_category2_id") && !a.Contains("new_category3_id")).FirstOrDefault();
                var phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == phone3?.Id).FirstOrDefault();
                if (phonekpi == null)
                    phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == phone2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (phonekpi == null)
                    phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == phone1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == television3?.Id).FirstOrDefault();
                if (televisionkpi == null)
                    televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == television2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (televisionkpi == null)
                    televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == television1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == notebook3?.Id).FirstOrDefault();
                if (notebookkpi == null)
                    notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == notebook2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (notebookkpi == null)
                    notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == notebook1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var scooterkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == scooter?.Id).FirstOrDefault();
                var sweepingrobotkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == sweepingrobot?.Id).FirstOrDefault();
                //手机KPI
                model.input_23d13d6201fb = phonekpi != null? phonekpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //电视KPI
                model.input_de99c3fe2cda = televisionkpi != null? televisionkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //滑板车KPI
                model.input_7841b3299f84 = scooterkpi != null? scooterkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //笔记本KPI
                model.input_5632b5088d84 = notebookkpi != null ? notebookkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //扫拖KPI
                model.input_df5afbd50c47 = sweepingrobotkpi != null ? sweepingrobotkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //生态链KPI
                model.input_ab7e4bb0b648 = ecologicalchainkpi != null? ecologicalchainkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                #endregion
                #region 特殊费用明细
                EntityCollection specialexpensedetails = OrganizationService.RetrieveMultiple(
                    new QueryExpression("new_srv_specialexpense") 
                    {
                        ColumnSet = new ColumnSet("new_feetype", "new_specialamount", "new_reason"),
                        Criteria = new FilterExpression 
                        {
                            Conditions = 
                            {
                                new ConditionExpression("new_expense_claim_id", ConditionOperator.Equal, entity.Id),
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                            }
                        }
                    });
                List<SpecialExpenseFeeModel> specialfeelist = new List<SpecialExpenseFeeModel>();
                foreach (var item in specialexpensedetails.Entities)
                {
                    SpecialExpenseFeeModel fee = new SpecialExpenseFeeModel();
                    fee.input_1710317073481 = item.FormattedValues.ContainsKey("new_feetype") ? item.FormattedValues["new_feetype"] : "";
                    fee.input_1710317075808 = item.ToDefault<decimal>("new_specialamount").ToString();
                    fee.textarea_1717039394892 = item.ToDefault<string>("new_reason");
                    specialfeelist.Add(fee);
                }
                model.formTable_1f7d83792fd4 = specialfeelist;
                #endregion
                #region 发票信息
                // 发票编号
                //发票号码
                model.input_a0baf697605f = entity.ToDefault<string>("new_invoiceno");
                //发票代码
                model.input_610877ce81d1 = entity.ToDefault<string>("new_invoicenumber");
                //开票日期
                model.input_37e5e5a42e0d = entity.FormattedValues.Contains("new_confirmdate") ? Convert.ToDateTime(entity["new_confirmdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                //税号
                model.input_0cc00a6008b9 =  entity.Contains("new_taxno") ? entity.ToDefault<string>("new_taxno") : "null";
                //交易附言
                model.textarea_fd7a92ba8b5e =  entity.Contains("new_transactionpostscript") ? entity.ToDefault<string>("new_transactionpostscript") : "null";
                // 附件
                List<PartInvoiceModel> invoicefileList = new List<PartInvoiceModel>();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("entityId", entityid.Replace("{", "").Replace("}", ""));
                dic.Add("entityName", entityname);
                dic.Add("filetype", "");
                dic.Add("new_mimetype", "");
                string file = CrmHelper.InvokeHiddenApi(OrganizationService, "new_comattachment", "Attachment/GetFileByRecordByMimetype", dic);
                if (!string.IsNullOrWhiteSpace(file))
                {

                    List<AttachmentModel> list = JsonHelper.Deserialize<List<AttachmentModel>>(file);
                    foreach (var item in list)
                    {
                        PartInvoiceModel invoice = new PartInvoiceModel();
                        invoice.upload_1710316197800 = item.new_filepath;
                        invoicefileList.Add(invoice);
                    }
                }
                model.formTable_253fa9a62466 = invoicefileList;
                //结算单链接
                string expense_url = GetSystemParameterValue(OrganizationService, "new_srv_expense_claim_url");
                LinkModelPart linkmodel = new LinkModelPart();
                linkmodel.link_1710316920832 = string.Format(expense_url, entityid);
                List<LinkModelPart> linklist = new List<LinkModelPart>();
                linklist.Add(linkmodel);
                model.formTable_ff7240cecfc1 = linklist;
                #endregion
                #endregion

                #region 扩展字段
                Variables variables = new Variables();
                variables.model.Add("new_name", entity.ToDefault<string>("new_name"));
                #endregion

                #region 参数处理
                model.businessKey = entity.ToDefault<string>("new_businesskey");
                body.formData = model;
                body.businessKey = model.businessKey;
                body.modelCode = GetSystemParameterValue(OrganizationService, "sys_Bpm3.0partmodelCode");
                //标题 = 主题 + 付款单号 + 申请人 
                body.processInstanceName = model.input_fb91992140e9 + model.input_5aea43494178 + requestuserName;
                body.startUserId = requestuser;
                body.variables = variables;
                #endregion
                Log.InfoMsg($"抛BPM参数："+ JsonHelper.Serialize(model));
                return body;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 劳务费费用结算提交BPM审批（BPM3.0升级）
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <param name="formType">单据类型</param>
        /// <returns></returns>
        public override BpmServiceOrderbody InterfacebodyServiceOrder(string entityname, string entityid)
        {
            BpmServiceOrderbody body = new BpmServiceOrderbody();
            BpmServiceOrderExpenseModel model = new BpmServiceOrderExpenseModel();
            try
            {
                #region 数据校验
                var entity = OrganizationService.Retrieve(entityname, new Guid(entityid), new ColumnSet(true));
                if (entity == null)
                {
                    throw new Exception(GetResource("Record_IsNotFound", "未获取到当前单据信息"));
                }
                #endregion
                var errorForOwner = string.Empty;
                var requestuser = "";
                var requestuserName = "";
                string bpmstartuser = GetSystemParameterValue(OrganizationService, "BPMStartUser");
                if (!string.IsNullOrWhiteSpace(bpmstartuser))
                {
                    var splitstr = bpmstartuser.Split(';');
                    if (splitstr.Count() == 2)
                    {
                        var email = Cast.ConToString(splitstr[0]).Split('@');
                        if (email.Length >= 1)
                            requestuser = email[0];
                        requestuserName = splitstr[1];
                    }
                }
                if (string.IsNullOrWhiteSpace(requestuser))
                {
                    requestuser = GetCreatedIdByOwner(OrganizationServiceAdmin, this.UserId.ToString(), out errorForOwner);//动作执行人
                    requestuserName = requestuser;
                } 
                if (string.IsNullOrWhiteSpace(requestuser))
                {
                    throw new InvalidPluginExecutionException(errorForOwner);
                }
                //结算单类型
                int expense_type = entity.GetAttributeValue<OptionSetValue>("new_businesstype").Value;
                //获取币种
                string currency = "";
                string countrycode = "";//国家代码
                string fetchlinkentity = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='new_srv_expense_claim'>
    <attribute name='new_name' />
    <attribute name='new_srv_expense_claimid' />
    <order attribute='new_name' descending='false' />
    <filter type='and'>
      <condition attribute='new_srv_expense_claimid' operator='eq' uitype='new_srv_expense_claim' value='{entityid}' />
    </filter>
    <link-entity name='new_country' from='new_countryid' to='new_country_id' visible='false' link-type='outer' alias='ab'>
      <attribute name='new_code' />
    </link-entity>
    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' visible='false' link-type='outer' alias='ac'>
      <attribute name='isocurrencycode' />
    </link-entity>
  </entity>
</fetch>";
                var linkEntity = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchlinkentity)).Entities.FirstOrDefault();
                if (linkEntity != null) 
                {
                    if (linkEntity.Contains("ab.new_code")) 
                    {
                        countrycode = linkEntity.GetAliasAttributeValue<string>("ab.new_code");
                    }
                    if (linkEntity.Contains("ac.isocurrencycode"))
                    {
                        currency = linkEntity.GetAliasAttributeValue<string>("ac.isocurrencycode");
                    }
                }
                //获取SAP供应商编码
                string sapcode = "";
                //区域
                string new_region_id = "";
                //服务商简称
                string stationabbreviation = "";
                if (entity.IsNotNull("new_srv_station_id"))
                {
                    string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                      <entity name='new_srv_station'>
                        <attribute name='new_srv_stationid' />
                        <attribute name='new_name' />
                        <attribute name='createdon' />
                        <attribute name='new_supplier' />
                        <attribute name='new_stationabbreviation' />
                        <order attribute='new_name' descending='false' />
                        <filter type='and'>
                          <condition attribute='new_srv_stationid' operator='eq' uitype='new_srv_station' value='{entity.ToDefault<Guid>("new_srv_station_id")}' />
                        </filter>
                        <link-entity name='new_region' from='new_regionid' to='new_region_id' visible='false' link-type='outer' alias='ar'>
                          <attribute name='new_code' />
                          <attribute name='new_name' />
                        </link-entity>
                      </entity>
                    </fetch>";
                    Entity new_srv_station = OrganizationService.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    if (new_srv_station != null) 
                    {
                        sapcode = new_srv_station?.ToDefault<string>("new_supplier");
                        new_region_id = new_srv_station.Contains("ar.new_code") ? new_srv_station.GetAliasAttributeValue<string>("ar.new_code") : "";
                        stationabbreviation = new_srv_station.ToDefault<string>("new_stationabbreviation");
                    }
                }
                //查询支付公司主体
                Entity new_paymentcompanybody = GetPaymentcompanybody(entity.ToDefault<string>("new_contractingbody"));
                string paymentcompanybody = new_paymentcompanybody != null ? new_paymentcompanybody.ToDefault<string>("new_companyname") + "(" + new_paymentcompanybody.ToDefault<string>("new_contractingbody") + ")" : "";
                #region 请求体字段

                #region 基础信息
                //申请人
                model.input_b5f0d5367833 = requestuserName;
                //申请人部门
                model.input_0c4630eca2ac = GetSystemParameterValue(OrganizationService, "BPMStartUserDepartment");
                //支付公司
                model.input_8889972d0f41 = paymentcompanybody;
                //承担公司
                model.input_c7189fb1e285 = paymentcompanybody;
                //付款单号
                model.input_f67d9d7a0540 = entity.ToDefault<string>("new_name");
                //付款金额（含币种）
                model.input_1ded2e239add = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                //付款方式
                model.input_ca9d4f4017ee = entity.FormattedValues.ContainsKey("new_payway") ? entity.FormattedValues["new_payway"] : "电汇";
                //提交审批日期
                model.input_1c8ea85e1dd6 = DateTime.Now.ToString("yyyy-MM-dd");
                //最迟付款日期
                model.input_9f343327c1ec = entity.FormattedValues.Contains("new_latestpaymentdate") ? Convert.ToDateTime(entity["new_latestpaymentdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                //服务商名称
                //model.input_ed848e5fc57e = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                //SAP供应商编码
                model.input_bd92cb77da2a = "0000" + sapcode;
                //国际业务性质
                model.input_ac05ac6af3ab = "售后";
                //费用类型
                if (expense_type == 9)
                {
                    //结算单类型 = 安装
                    model.input_d7116be54dc2 = "安装费";
                } else if (expense_type == 4 || expense_type == 8)
                {
                    //结算单类型 = 运营商 或者 收集点
                    model.input_d7116be54dc2 = "运营商handling fee";
                } else if (expense_type == 6) 
                {
                    //结算单类型 = 激活
                    model.input_d7116be54dc2 = "激活费";
                } else if (expense_type == 1) {
                    //结算单类型 = 非B2X
                    model.input_d7116be54dc2 = "售后网点劳务费";
                } else if(expense_type == 2) {
                    //结算单类型 = B2X
                    model.input_d7116be54dc2 = "B2X";
                }
                //付款金额（隐藏）
                model.inputFloat_a7c224e83898 = entity.ToDefault<decimal>("new_approvalamount");
                //签约主体
                model.input_0084c1478948 = entity.ToDefault<string>("new_contractingbody");
                //区域
                model.input_61e7dce5f012 = new_region_id;
                //币种
                model.input_90cae210d2cb = currency;
                #endregion
                #region 付款单信息
                //国家
                model.input_170864bdf47b = entity.IsNotNull("new_country_id") ? entity.ToEr("new_country_id").Name : "";
                //国家代码
                model.input_db9579324bbd = countrycode;
                //服务商名称
                model.textarea_675899ad0736 = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                //网点类型
                model.input_7017fca92b77 = entity.FormattedValues.ContainsKey("new_stietype") ? entity.FormattedValues["new_stietype"] : "null";
                //结算年份
                model.input_d85f6917671f = entity.ToDefault<int>("new_year").ToString();
                //结算月份
                model.input_7539e944f720 = entity.ToDefault<int>("new_month").ToString();
                //预提费用
                model.input_db8f5acb13d5 = entity.ToDefault<decimal>("new_withholdingmoney").ToString("f2") + "(" + currency + ")"; 
                //结算费用
                model.input_11bf2f0b3f56 = entity.ToDefault<decimal>("new_totalcost").ToString("f2") + "(" + currency + ")";
                //增值税率
                model.input_cecfa127c101 = entity.ToDefault<decimal>("new_taxrate").ToString("f2");
                //增值税额
                model.input_d9760991e9a5 = entity.ToDefault<decimal>("new_vatamount").ToString("f2") + "(" + currency + ")";
                //代扣税率
                model.input_64a5dc3633cd = entity.ToDefault<decimal>("new_withholdingtax").ToString("f2");
                //代扣税额
                model.input_ed222d04c639 = entity.ToDefault<decimal>("new_withholdingamount").ToString("f2") + "(" + currency + ")";
                //付款金额
                model.input_f47fb7ae965f = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                //开户银行
                model.textarea_f856868fc19a = entity.ToDefault<string>("new_bank");
                //开户名称
                model.textarea_78e50be63edf = entity.ToDefault<string>("new_accountbankname");
                //银行账号
                model.input_857fd8dadd4c = entity.ToDefault<string>("new_bankaccount");
                //SWIFT CODE
                model.input_ce6f9039e337 = entity.ToDefault<string>("new_swiftcode");
                #endregion
                #region 服务单明细
                //工单数量
                model.input_42ad78b83bac = entity.ToDefault<int>("new_workordercount").ToString();
                //维修劳务费
                model.input_68a26176926f = entity.ToDefault<decimal>("new_repairmoney").ToString("f2");
                //寄修物流费
                model.input_6e9987d5f9df = entity.ToDefault<decimal>("new_mailrepairmoney").ToString("f2");
                //上门物流费
                model.input_c1f68deb221c = entity.ToDefault<decimal>("new_comedoormoney").ToString("f2");
                //保底费分摊
                model.input_db80d42613db = entity.ToDefault<decimal>("new_guaranteedfee_sharing").ToString("f2");
                //b2x管理费
                model.input_689f0089bbe4 = entity.ToDefault<decimal>("new_managementmoney").ToString("f2");
                //录单费
                model.input_0796797027b7 = entity.ToDefault<decimal>("new_recordingmoney").ToString("f2");
                //转派工单检测费
                model.input_b700b828ba99 = entity.ToDefault<decimal>("new_totaldetection").ToString("f2");
                //电话回访费
                model.input_54b608959ff7 = entity.ToDefault<decimal>("new_returnvisitfee").ToString("f2");
                //箱子费
                model.input_70faeca6c20c = entity.ToDefault<decimal>("new_casemoney").ToString("f2");
                //远程维修费
                model.input_90fbe9787be3 = entity.ToDefault<decimal>("new_remotemaintenance_fee").ToString("f2");
                //固定运营补贴
                model.input_82fe6b4aacbf = entity.ToDefault<decimal>("new_new_totalspecialsubsubsidy").ToString("f2");
                //路程补贴费
                model.input_b1f51f45e642 = entity.ToDefault<decimal>("new_distancemoney").ToString("f2");
                //加减项费用
                model.input_0efa62e16b42 = entity.ToDefault<decimal>("new_specialmoney").ToString("f2");
                //激活费
                model.input_2aaf7289df4a = entity.ToDefault<decimal>("new_activationmoney").ToString("f2");
                //结算运营商hf
                model.input_a11a11b1a537 = entity.ToDefault<decimal>("new_operatehfmoney").ToString("f2");
                //收集点补贴
                model.input_88c4a231269c = entity.ToDefault<decimal>("new_collectionmoney").ToString("f2");
                //B2X相关费用
                //对外呼叫费
                model.inputFloat_895293cee755 = entity.ToDefault<decimal>("new_externalcallfee");
                //空箱费
                model.inputFloat_5a6b77fc820d = entity.ToDefault<decimal>("new_emptycontainerfee");
                //翻新费
                model.inputFloat_b2533db65cf7 = entity.ToDefault<decimal>("new_refurbishmentfee");
                //物流费
                model.inputFloat_e1098363fc84 = entity.ToDefault<decimal>("new_logisticsfeeb2x");
                //空箱物流费
                model.inputFloat_f0d415eae926 = entity.ToDefault<decimal>("new_emptycontainerlogisticsfee");
                //电视材料保护费
                model.inputFloat_fcc1164954a2 = entity.ToDefault<decimal>("new_tvprotectionmaterialfee");
                //清关费
                model.inputFloat_551ef79823e6 = entity.ToDefault<decimal>("new_customsclearancefee");
                //备件费
                model.input_fdb9d1ca36d6 = entity.ToDefault<decimal>("new_sparepartscostbuysell").ToString("f2");
                //备件markup
                model.input_a80a6d15d844 = entity.ToDefault<decimal>("new_sparepartscostmarkup").ToString("f2");
                //Local buy markup费
                model.input_0ef6a2edcb8f = entity.ToDefault<decimal>("new_localbuycostbuysell").ToString("f2");
                //Local buy markup费
                model.input_a9aa64208f64 = entity.ToDefault<decimal>("new_localbuymarkupcostbuysell").ToString("f2");
                #region handling 费字段
                //加减项费用
                model.input_0efa62e16b42 = entity.ToDefault<decimal>("new_othermoneyhandling").ToString("f2");
                //结算运营商hf
                model.input_a11a11b1a537 = entity.ToDefault<decimal>("new_handlingfeekpi").ToString("f2");
                //物流费
                model.inputFloat_e1098363fc84 = entity.ToDefault<decimal>("new_logisticsfeehandling");
                //呼叫费（ Calling Fee）
                model.inputFloat_895293cee755 = entity.ToDefault<decimal>("new_callfeehandling");
                //销售额
                model.input_b52a7d792947 = entity.ToDefault<decimal>("new_saleamounthandling").ToString("f2");
                #endregion
                //安装劳务费
                model.input_06ce473b4fdf = entity.ToDefault<decimal>("new_installfee").ToString("f2");
                //安装固定月度费
                model.input_5bdda7e76512 = entity.ToDefault<decimal>("new_fixedmonthfeeinstall").ToString("f2");
                //售后网点杂费-DOA回购
                model.input_37b00ad6a594 = entity.ToDefault<decimal>("new_doabuyback").ToString("f2");
                //售后网点杂费-Refund退款
                model.input_735f450e4266 = entity.ToDefault<decimal>("new_aftersalesrefund").ToString("f2");
                //售后网点杂费-SWAP结费
                model.input_ca6f0d180dde = entity.ToDefault<decimal>("new_swapsettlement").ToString("f2");
                //售后网点杂费-保外免费换屏
                model.input_5a548f6d9edb = entity.ToDefault<decimal>("new_freescreenchange").ToString("f2");
                //售后网点杂费-清关费
                model.input_323504a1961a = entity.ToDefault<decimal>("new_aftersalecustomsclearfee").ToString("f2");
                //售后网点杂费-其他
                model.input_b9c37f0db011 = entity.ToDefault<decimal>("new_aftersalesotherfee").ToString("f2");
                //拆机费用
                model.input_5b0b9a27543f = entity.ToDefault<decimal>("new_disassemblyfee").ToString("f2");
                //售后网点-半自营费
                model.input_79b201e34695 = entity.ToDefault<decimal>("new_semiselfemployedfee").ToString("f2");
                //清关费（转派）
                model.input_53a4ef227bcc = entity.ToDefault<decimal>("new_customsclearancefeetransfer").ToString("f2");
                //是否reverse charge
                model.input_dc3dfbfabb90 = entity.Contains("new_isreversecharge") ? entity.FormattedValues["new_isreversecharge"] : "";
                //主题 = 国家+服务商简称+年月+费用类型
                model.input_d54e10a93aab = model.input_170864bdf47b + stationabbreviation + model.input_d85f6917671f + "年" + model.input_7539e944f720 + "月" + model.input_d7116be54dc2;
                EntityCollection kpicols = new EntityCollection();
                if (expense_type == 9)
                    kpicols = GetCountrykpiline(entity.ToDefault<int>("new_year"), entity.ToDefault<int>("new_month"), entity.ToDefault<Guid>("new_srv_station_id"), entity.ToDefault<Guid>("new_country_id"));
                else
                    kpicols = Getactualkpi(entity.ToDefault<int>("new_year"), entity.ToDefault<int>("new_month"), entity.ToDefault<Guid>("new_srv_station_id"));
                EntityCollection category1 = Getcategory1();
                EntityCollection category2 = Getcategory2();
                EntityCollection category3 = Getcategory3(new string[] { "30000001", "30000008", "30000047", "30000011", "30000063" });
                Entity ecologicalchain = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000005").FirstOrDefault();//生态链
                Entity sweepingrobot = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000063").FirstOrDefault();//扫地机器人
                Entity phone3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000001").FirstOrDefault();//手机
                Entity phone2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000001").FirstOrDefault();//手机
                Entity phone1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000001").FirstOrDefault();//手机
                Entity television3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000008").FirstOrDefault();//电视
                Entity television2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000008").FirstOrDefault();//电视
                Entity television1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000003").FirstOrDefault();//电视
                Entity scooter = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000047").FirstOrDefault();//滑板车
                Entity notebook3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000011").FirstOrDefault();//笔记本
                Entity notebook2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000011").FirstOrDefault();//笔记本
                Entity notebook1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000004").FirstOrDefault();//笔记本
                Entity homeappliance3 = category3.Entities.Where(a => a.ToDefault<string>("new_code") == "30000122").FirstOrDefault();//大家电
                Entity homeappliance2 = category2.Entities.Where(a => a.ToDefault<string>("new_code") == "20000122").FirstOrDefault();//大家电
                Entity homeappliance1 = category1.Entities.Where(a => a.ToDefault<string>("new_code") == "10000006").FirstOrDefault();//大家电
                var ecologicalchainkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == ecologicalchain?.Id
                && !a.Contains("new_category2_id") && !a.Contains("new_category3_id")).FirstOrDefault();
                var phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == phone3?.Id).FirstOrDefault();
                if (phonekpi == null)
                    phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == phone2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (phonekpi == null)
                    phonekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == phone1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == television3?.Id).FirstOrDefault();
                if (televisionkpi == null)
                    televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == television2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (televisionkpi == null)
                    televisionkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == television1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == notebook3?.Id).FirstOrDefault();
                if (notebookkpi == null)
                    notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == notebook2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (notebookkpi == null)
                    notebookkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == notebook1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var homeappliancekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == homeappliance3?.Id).FirstOrDefault();
                if (homeappliancekpi == null)
                    homeappliancekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category2_id") == homeappliance2?.Id && !a.Contains("new_category3_id")).FirstOrDefault();
                if (homeappliancekpi == null)
                    homeappliancekpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category1_id") == homeappliance1?.Id && !a.Contains("new_category3_id") && !a.Contains("new_category2_id")).FirstOrDefault();
                var scooterkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == scooter?.Id).FirstOrDefault();
                var sweepingrobotkpi = kpicols.Entities.Where(a => a.ToDefault<Guid>("new_category3_id") == sweepingrobot?.Id).FirstOrDefault();
                //手机KPI
                model.input_630ddad9d60a = phonekpi != null ? phonekpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //电视KPI
                model.input_b103f7f3acd0 = televisionkpi != null ? televisionkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //滑板车KPI
                model.input_bc4295e32b5e = scooterkpi != null ? scooterkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //笔记本KPI
                model.input_316dca7ec090 = notebookkpi != null ? notebookkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //扫拖KPI
                model.input_202dc03cca65 = sweepingrobotkpi != null ? sweepingrobotkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //生态链KPI
                model.input_71f3904679f7 = ecologicalchainkpi != null ? ecologicalchainkpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                //大家电KPI
                model.input_405d988b3bd2 = homeappliancekpi != null ? homeappliancekpi.ToDefault<decimal>("new_ratio").ToString("f2") : "0.00";
                #endregion
                #region 特殊费用明细
                EntityCollection specialexpensedetails = OrganizationService.RetrieveMultiple(
                    new QueryExpression("new_srv_specialexpense")
                    {
                        ColumnSet = new ColumnSet("new_feetype", "new_specialamount", "new_reason"),
                        Criteria = new FilterExpression
                        {
                            Conditions =
                            {
                                new ConditionExpression("new_expense_claim_id", ConditionOperator.Equal, entity.Id),
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                            }
                        }
                    });
                List<SpecialExpenseServiceOrderModel> specialfeelist = new List<SpecialExpenseServiceOrderModel>();
                foreach (var item in specialexpensedetails.Entities)
                {
                    SpecialExpenseServiceOrderModel fee = new SpecialExpenseServiceOrderModel();
                    fee.input_1710297576448 = item.FormattedValues.ContainsKey("new_feetype") ? item.FormattedValues["new_feetype"] : "";
                    fee.input_1710297766608 = item.ToDefault<decimal>("new_specialamount").ToString("f2");
                    fee.textarea_1717039316084 = item.ToDefault<string>("new_reason");
                    specialfeelist.Add(fee);
                }
                model.formTable_c1f8126b7b3c = specialfeelist;
                #endregion
                #region 发票信息
                // 发票编号
                //发票号码
                model.input_8cd3e7a4a205 = entity.ToDefault<string>("new_invoiceno");
                //发票代码
                model.input_63654c5ba2e9 = entity.ToDefault<string>("new_invoicenumber");
                //开票日期
                model.input_d4c209f0cc7c = entity.FormattedValues.Contains("new_confirmdate") ? Convert.ToDateTime(entity["new_confirmdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                //税号
                model.input_bc28343e97b5 = entity.Contains("new_taxno") ? entity.ToDefault<string>("new_taxno") : "null";
                //交易附言
                model.textarea_ba6262d23e23 = entity.Contains("new_transactionpostscript") ? entity.ToDefault<string>("new_transactionpostscript") : "null";
                // 附件
                List<ServiceOrderInvoiceModel> invoicefileList = new List<ServiceOrderInvoiceModel>();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("entityId", entityid.Replace("{", "").Replace("}", ""));
                dic.Add("entityName", entityname);
                dic.Add("filetype", "");
                dic.Add("new_mimetype", "");
                string file = CrmHelper.InvokeHiddenApi(OrganizationService, "new_comattachment", "Attachment/GetFileByRecordByMimetype", dic);
                if (!string.IsNullOrWhiteSpace(file))
                {

                    List<AttachmentModel> list = JsonHelper.Deserialize<List<AttachmentModel>>(file);
                    foreach (var item in list)
                    {
                        ServiceOrderInvoiceModel invoice = new ServiceOrderInvoiceModel();
                        invoice.upload_1710314821176 = item.new_filepath;
                        invoicefileList.Add(invoice);
                    }
                }
                model.formTable_57dc94ce8e6b = invoicefileList;
                //结算单链接
                string expense_url = GetSystemParameterValue(OrganizationService, "new_srv_expense_claim_url");
                LinkModelServiceOrder linkmodel = new LinkModelServiceOrder();
                linkmodel.link_1710314891456 = string.Format(expense_url, entityid);
                List<LinkModelServiceOrder> linklist = new List<LinkModelServiceOrder>();
                linklist.Add(linkmodel);
                model.formTable_8de0f4ddf57e = linklist;
                #endregion
                #endregion

                #region 扩展字段
                Variables variables = new Variables();
                variables.model.Add("new_name", entity.ToDefault<string>("new_name"));
                #endregion

                #region 参数处理
                model.businessKey = entity.ToDefault<string>("new_businesskey");
                body.formData = model;
                body.businessKey = model.businessKey;
                body.modelCode = GetSystemParameterValue(OrganizationService, "sys_Bpm3.0serviceordermodelCode");
               
                body.startUserId = requestuser;
                //标题 = 主题 + 付款单号 + 申请人 
                body.processInstanceName = model.input_d54e10a93aab + model.input_f67d9d7a0540 + requestuserName;
                body.variables = variables;
                #endregion
                Log.InfoMsg($"抛BPM参数：" + JsonHelper.Serialize(model));

                return body;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 仓储费费用结算提交BPM审批（BPM3.0升级）
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <param name="formType">单据类型</param>
        /// <returns></returns>
        public override BpmWarehousingbody InterfacebodyWarehousingOrder(string entityname, string entityid) {
            BpmWarehousingbody body = new BpmWarehousingbody();
            BpmWarehousingExpenseModel model = new BpmWarehousingExpenseModel();
            try {
                #region 数据校验
                var entity = OrganizationService.Retrieve(entityname, new Guid(entityid), new ColumnSet(true));
                if (entity == null) {
                    throw new Exception(GetResource("Record_IsNotFound", "未获取到当前单据信息"));
                }
                #endregion

                #region 流程发起人
                var errorForOwner = string.Empty;
                var requestuser = "";
                var requestuserName = "";
                string bpmstartuser = GetSystemParameterValue(OrganizationService, "BPMStartUser");
                if (!string.IsNullOrWhiteSpace(bpmstartuser)) {
                    var splitstr = bpmstartuser.Split(';');
                    if (splitstr.Count() == 2) {
                        var email = Cast.ConToString(splitstr[0]).Split('@');
                        if (email.Length >= 1)
                            requestuser = email[0];
                        requestuserName = splitstr[1];
                    }
                }
                if (string.IsNullOrWhiteSpace(requestuser)) {
                    requestuser = GetCreatedIdByOwner(OrganizationServiceAdmin, this.UserId.ToString(), out errorForOwner);//动作执行人
                    requestuserName = requestuser;
                }
                if (string.IsNullOrWhiteSpace(requestuser)) {
                    throw new InvalidPluginExecutionException(errorForOwner);
                }
                #endregion

                #region 币种、国家代码 等
                string currency = ""; // 获取币种
                string countrycode = "";// 国家代码
                string fetchlinkentity = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                  <entity name='new_srv_expense_claim'>
                    <attribute name='new_name' />
                    <attribute name='new_srv_expense_claimid' />
                    <order attribute='new_name' descending='false' />
                    <filter type='and'>
                      <condition attribute='new_srv_expense_claimid' operator='eq' uitype='new_srv_expense_claim' value='{entityid}' />
                    </filter>
                    <link-entity name='new_country' from='new_countryid' to='new_country_id' visible='false' link-type='outer' alias='ab'>
                      <attribute name='new_code' />
                    </link-entity>
                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' visible='false' link-type='outer' alias='ac'>
                      <attribute name='isocurrencycode' />
                    </link-entity>
                  </entity>
                </fetch>";
                var linkEntity = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchlinkentity)).Entities.FirstOrDefault();
                if (linkEntity != null) {
                    if (linkEntity.Contains("ab.new_code")) {
                        countrycode = linkEntity.GetAliasAttributeValue<string>("ab.new_code");
                    }
                    if (linkEntity.Contains("ac.isocurrencycode")) {
                        currency = linkEntity.GetAliasAttributeValue<string>("ac.isocurrencycode");
                    }
                }

                string sapcode = ""; // 获取SAP供应商编码
                string station_code = ""; // 服务商编码
                string new_region_id = ""; // 区域
                string stationabbreviation = ""; // 服务商简称
                if (entity.IsNotNull("new_srv_station_id")) {
                    string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                      <entity name='new_srv_station'>
                        <attribute name='new_srv_stationid' />
                        <attribute name='new_name' />
                        <attribute name='createdon' />
                        <attribute name='new_supplier' />
                        <attribute name='new_code' />
                        <attribute name='new_stationabbreviation' />
                        <order attribute='new_name' descending='false' />
                        <filter type='and'>
                          <condition attribute='new_srv_stationid' operator='eq' uitype='new_srv_station' value='{entity.ToDefault<Guid>("new_srv_station_id")}' />
                        </filter>
                        <link-entity name='new_region' from='new_regionid' to='new_region_id' visible='false' link-type='outer' alias='ar'>
                          <attribute name='new_code' />
                          <attribute name='new_name' />
                        </link-entity>
                      </entity>
                    </fetch>";
                    Entity new_srv_station = OrganizationService.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    if (new_srv_station != null) {
                        sapcode = new_srv_station?.ToDefault<string>("new_supplier");
                        station_code = new_srv_station?.ToDefault<string>("new_code");
                        new_region_id = new_srv_station.Contains("ar.new_code") ? new_srv_station.GetAliasAttributeValue<string>("ar.new_code") : "";
                        stationabbreviation = new_srv_station.ToDefault<string>("new_stationabbreviation");
                    }
                }

                // 查询支付公司主体
                Entity new_paymentcompanybody = GetPaymentcompanybody(entity.ToDefault<string>("new_contractingbody"));
                string paymentcompanybody = new_paymentcompanybody != null ? new_paymentcompanybody.ToDefault<string>("new_companyname") + "(" + new_paymentcompanybody.ToDefault<string>("new_contractingbody") + ")" : "";
                #endregion

                #region 基础信息(不含主题)
                // 付款单号
                model.input_c654397de1bd = entity.ToDefault<string>("new_name");
                // 申请人
                model.input_d2f86f5b61e8 = requestuserName;
                // 申请人部门
                model.input_8d021dd15b3e = GetSystemParameterValue(OrganizationService, "BPMStartUserDepartment");
                // 支付公司
                model.input_140cbe98bb25 = paymentcompanybody;
                // 承担公司
                model.input_461b6e6f3e81 = paymentcompanybody;
                // 付款金额
                model.input_ef18e1381e79 = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                // 付款方式
                model.input_7dc2e8b6b631 = entity.FormattedValues.ContainsKey("new_payway") ? entity.FormattedValues["new_payway"] : "电汇";
                // 提交审批日期
                model.datepicker_9fff85484b28 = DateTime.Now.ToString("yyyy-MM-dd");
                // 最迟付款日期
                model.datepicker_ef3d09efbf26 = entity.FormattedValues.Contains("new_latestpaymentdate") ? Convert.ToDateTime(entity["new_latestpaymentdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                // 服务商名称
                model.input_c7988e20cf5c = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                // SAP供应商编码
                model.input_90ec538e0d78 = "0000" + sapcode;
                // 国际业务性质
                model.input_3af233b12d65 = "售后";
                var settlementType = entity.ToDefault<int>("new_businesstype");
                // 费用类型
                if (settlementType == 5)
                    model.input_ff0685db1a82 = "备件运营-仓储费";
                else if (settlementType == 10)
                    model.input_ff0685db1a82 = "高维工厂劳务费";
                #endregion

                #region 付款单信息
                // 币种
                model.input_e37b695ab117 = currency;
                // 付款金额（不含币种）
                model.inputFloat_23b844fbd14f = Convert.ToDecimal(entity.ToDefault<decimal>("new_approvalamount").ToString("f2"));
                // 服务商编码
                model.input_4ba5fe38f028 = station_code;
                // 主体
                model.input_b0b1f972fc27 = entity.ToDefault<string>("new_contractingbody");
                // 国家
                model.input_5e9aead82791 = entity.IsNotNull("new_country_id") ? entity.ToEr("new_country_id").Name : "";
                // 预提费用（不含税）
                model.input_fa68279df0e1 = entity.ToDefault<decimal>("new_withholdingmoney").ToString("f2") + "(" + currency + ")";
                // 结算年份
                model.input_dcf8af1d26a8 = entity.ToDefault<int>("new_year").ToString();
                // 结算费用（不含税）
                model.input_aa3a84d769d4 = entity.ToDefault<decimal>("new_totalcost").ToString("f2") + "(" + currency + ")";
                // 结算月份
                model.input_4601412f4b00 = entity.ToDefault<int>("new_month").ToString();
                // 增值税率
                model.textarea_83927f68a2de = entity.ToDefault<decimal>("new_taxrate").ToString("f2");
                // 代扣税率
                model.input_d2002a8f8e47 = entity.ToDefault<decimal>("new_withholdingtax").ToString("f2");
                // 增值税额
                model.input_6dcb277f1c7e = entity.ToDefault<decimal>("new_vatamount").ToString("f2") + "(" + currency + ")";
                // 代扣税额
                model.input_7be059d76aae = entity.ToDefault<decimal>("new_withholdingamount").ToString("f2") + "(" + currency + ")";
                // 开户名称
                model.textarea_de3f3a1e1e90 = entity.ToDefault<string>("new_accountbankname");
                // 单行文本框
                model.input_10a4c8af59b6 = "";
                // 开户银行
                model.textarea_33dc8ae1f5a0 = entity.ToDefault<string>("new_bank");
                // 银行账号
                model.input_871fd48b8265 = entity.ToDefault<string>("new_bankaccount");
                // SWIFT CODE
                model.input_df6e33330d67 = entity.ToDefault<string>("new_swiftcode");
                // 发票号码
                model.input_e577da65d75c = entity.ToDefault<string>("new_invoiceno");
                // 发票代码
                model.input_23b073f11399 = entity.ToDefault<string>("new_invoicenumber");
                // 开票日期
                model.datepicker_618f0d62ae68 = entity.FormattedValues.Contains("new_confirmdate") ? Convert.ToDateTime(entity["new_confirmdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                // 交易附言
                model.textarea_007c752a50c8 = entity.ToDefault<string>("new_transactionpostscript");
                //是否reverse charge
                model.input_08d8839ada31 = entity.Contains("new_isreversecharge") ? entity.FormattedValues["new_isreversecharge"] : "";
                // 发票附件
                List<WarehousingInvoiceModel> invoicefileList = new List<WarehousingInvoiceModel>();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("entityId", entityid.Replace("{", "").Replace("}", ""));
                dic.Add("entityName", entityname);
                dic.Add("filetype", "");
                dic.Add("new_mimetype", "");
                string file = CrmHelper.InvokeHiddenApi(OrganizationService, "new_comattachment", "Attachment/GetFileByRecordByMimetype", dic);
                if (!string.IsNullOrWhiteSpace(file)) {
                    List<AttachmentModel> list = JsonHelper.Deserialize<List<AttachmentModel>>(file);
                    foreach (var item in list) {
                        WarehousingInvoiceModel invoice = new WarehousingInvoiceModel();
                        invoice.upload_1725518373540 = item.new_filepath;
                        invoicefileList.Add(invoice);
                    }
                }
                model.formTable_cbe62c59e19d = invoicefileList;
                #endregion

                #region 结算单明细(不含税)
                // 租金固定费
                model.input_dbd0d369e657 = entity.ToDefault<decimal>("new_fixedrentalfee").ToString("f2");
                // 租金货架费
                model.input_e6516f308c22 = entity.ToDefault<decimal>("new_shelfrentalfee").ToString("f2");
                // 租金扩仓费
                model.input_54eeb0136dd1 = entity.ToDefault<decimal>("new_warehouseexpansionfee").ToString("f2");
                // 人力固定费
                model.input_d33158c0e51d = entity.ToDefault<decimal>("new_fixedlaborcost").ToString("f2");
                // 人力变动费
                model.input_3477864acf47 = entity.ToDefault<decimal>("new_variablelaborcost").ToString("f2");
                // 运营固定费
                model.input_726c18dad277 = entity.ToDefault<decimal>("new_fixedoperationalcost").ToString("f2");
                // 电视操作费
                model.input_b67a47b3403b = entity.ToDefault<decimal>("new_televisionoperationfee").ToString("f2");
                // 出入库操作费
                model.input_910160df04f6 = entity.ToDefault<decimal>("new_entryexithandlingfee").ToString("f2");
                // 大小件操作费
                model.input_0498d97a97d5 = entity.ToDefault<decimal>("new_itemsizehandlingfee").ToString("f2");
                // 入库上架费
                model.input_db4e30cccf5e = entity.ToDefault<decimal>("new_inboundshelvingcharge").ToString("f2");
                // 打包费
                model.input_9b96918b4d21 = entity.ToDefault<decimal>("new_packingfee").ToString("f2");
                // 质检费
                model.input_fdae8bfdb4ed = entity.ToDefault<decimal>("new_qualityinspectionfee").ToString("f2");
                // 物流费
                model.input_90c3b0c9cc12 = entity.ToDefault<decimal>("new_storageandlogisticscost").ToString("f2");
                // 其他费用
                model.input_8e10bde197cc = entity.ToDefault<decimal>("new_storageotherfee").ToString("f2");
                //高维工厂劳务费
                model.input_c85727963c7e = entity.ToDefault<decimal>("new_high_dimensionalfeekpi").ToString("f2");
                // 加减项费用（不含税）
                EntityCollection specialexpensedetails = OrganizationService.RetrieveMultiple(
                    new QueryExpression("new_srv_specialexpense") {
                        ColumnSet = new ColumnSet("new_feetype", "new_specialamount", "new_reason"),
                        Criteria = new FilterExpression {
                            Conditions =
                            {
                                new ConditionExpression("new_expense_claim_id", ConditionOperator.Equal, entity.Id),
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                            }
                        }
                    });
                List<SpecialExpenseWarehousingModel> specialfeelist = new List<SpecialExpenseWarehousingModel>();
                foreach (var item in specialexpensedetails.Entities) {
                    SpecialExpenseWarehousingModel fee = new SpecialExpenseWarehousingModel();
                    fee.input_1725518608243 = item.FormattedValues.ContainsKey("new_feetype") ? item.FormattedValues["new_feetype"] : "";
                    fee.input_1725518613371 = item.ToDefault<decimal>("new_specialamount").ToString();
                    fee.input_1725518611344 = item.ToDefault<string>("new_reason");
                    specialfeelist.Add(fee);
                }
                model.formTable_3f8eb4d509ec = specialfeelist;
                // 结算单链接
                string expense_url = GetSystemParameterValue(OrganizationService, "new_srv_expense_claim_url");
                model.link_2fdc8e25aa3b = string.Format(expense_url, entityid);
                #endregion

                #region 扩展字段
                Variables variables = new Variables();
                variables.model.Add("new_name", entity.ToDefault<string>("new_name"));
                #endregion

                #region 参数处理
                // 主题 = 国家 + 服务商简称 + 年月 + 费用类型
                model.textarea_0f2fe10ef189 = model.input_5e9aead82791 + stationabbreviation + model.input_dcf8af1d26a8 + "年" + model.input_4601412f4b00 + "月" + model.input_ff0685db1a82;
                model.businessKey = entity.ToDefault<string>("new_businesskey");
                body.formData = model;
                body.businessKey = model.businessKey;
                body.modelCode = GetSystemParameterValue(OrganizationService, "sys_Bpm3.0warehousingmodelCode");
                body.startUserId = requestuser;
                // 标题 = 主题 + 付款单号 + 申请人 
                body.processInstanceName = model.textarea_0f2fe10ef189 + model.input_c654397de1bd + requestuserName;
                body.variables = variables;
                #endregion

                Log.InfoMsg($"抛BPM参数：" + JsonHelper.Serialize(model));
                return body;
            }
            catch (Exception ex) {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 物流费费用结算提交BPM审批（BPM3.0升级）
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        /// <param name="formType">单据类型</param>
        /// <returns></returns>
        public override BpmLogisticsbody InterfacebodyLogisticsOrder(string entityname, string entityid) {
            BpmLogisticsbody body = new BpmLogisticsbody();
            BpmLogisticsExpenseModel model = new BpmLogisticsExpenseModel();
            try {
                #region 数据校验
                var entity = OrganizationService.Retrieve(entityname, new Guid(entityid), new ColumnSet(true));
                if (entity == null) {
                    throw new Exception(GetResource("Record_IsNotFound", "未获取到当前单据信息"));
                }
                #endregion

                #region 流程发起人
                var errorForOwner = string.Empty;
                var requestuser = "";
                var requestuserName = "";
                string bpmstartuser = GetSystemParameterValue(OrganizationService, "BPMStartUser");
                if (!string.IsNullOrWhiteSpace(bpmstartuser)) {
                    var splitstr = bpmstartuser.Split(';');
                    if (splitstr.Count() == 2) {
                        var email = Cast.ConToString(splitstr[0]).Split('@');
                        if (email.Length >= 1)
                            requestuser = email[0];
                        requestuserName = splitstr[1];
                    }
                }
                if (string.IsNullOrWhiteSpace(requestuser)) {
                    requestuser = GetCreatedIdByOwner(OrganizationServiceAdmin, this.UserId.ToString(), out errorForOwner);//动作执行人
                    requestuserName = requestuser;
                }
                if (string.IsNullOrWhiteSpace(requestuser)) {
                    throw new InvalidPluginExecutionException(errorForOwner);
                }
                #endregion

                #region 币种、国家代码 等
                string currency = ""; // 获取币种
                string countrycode = "";// 国家代码
                string fetchlinkentity = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                  <entity name='new_srv_expense_claim'>
                    <attribute name='new_name' />
                    <attribute name='new_srv_expense_claimid' />
                    <order attribute='new_name' descending='false' />
                    <filter type='and'>
                      <condition attribute='new_srv_expense_claimid' operator='eq' uitype='new_srv_expense_claim' value='{entityid}' />
                    </filter>
                    <link-entity name='new_country' from='new_countryid' to='new_country_id' visible='false' link-type='outer' alias='ab'>
                      <attribute name='new_code' />
                    </link-entity>
                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' visible='false' link-type='outer' alias='ac'>
                      <attribute name='isocurrencycode' />
                    </link-entity>
                  </entity>
                </fetch>";
                var linkEntity = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchlinkentity)).Entities.FirstOrDefault();
                if (linkEntity != null) {
                    if (linkEntity.Contains("ab.new_code")) {
                        countrycode = linkEntity.GetAliasAttributeValue<string>("ab.new_code");
                    }
                    if (linkEntity.Contains("ac.isocurrencycode")) {
                        currency = linkEntity.GetAliasAttributeValue<string>("ac.isocurrencycode");
                    }
                }

                string sapcode = ""; // 获取SAP供应商编码
                string station_code = ""; // 服务商编码
                string new_region_id = ""; // 区域
                string stationabbreviation = ""; // 服务商简称
                if (entity.IsNotNull("new_srv_station_id")) {
                    string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
                      <entity name='new_srv_station'>
                        <attribute name='new_srv_stationid' />
                        <attribute name='new_name' />
                        <attribute name='createdon' />
                        <attribute name='new_supplier' />
                        <attribute name='new_code' />
                        <attribute name='new_stationabbreviation' />
                        <order attribute='new_name' descending='false' />
                        <filter type='and'>
                          <condition attribute='new_srv_stationid' operator='eq' uitype='new_srv_station' value='{entity.ToDefault<Guid>("new_srv_station_id")}' />
                        </filter>
                        <link-entity name='new_region' from='new_regionid' to='new_region_id' visible='false' link-type='outer' alias='ar'>
                          <attribute name='new_code' />
                          <attribute name='new_name' />
                        </link-entity>
                      </entity>
                    </fetch>";
                    Entity new_srv_station = OrganizationService.RetrieveMultiple(new FetchExpression(fetchxml)).Entities.FirstOrDefault();
                    if (new_srv_station != null) {
                        sapcode = new_srv_station?.ToDefault<string>("new_supplier");
                        station_code = new_srv_station?.ToDefault<string>("new_code");
                        new_region_id = new_srv_station.Contains("ar.new_code") ? new_srv_station.GetAliasAttributeValue<string>("ar.new_code") : "";
                        stationabbreviation = new_srv_station.ToDefault<string>("new_stationabbreviation");
                    }
                }

                // 查询支付公司主体
                Entity new_paymentcompanybody = GetPaymentcompanybody(entity.ToDefault<string>("new_contractingbody"));
                string paymentcompanybody = new_paymentcompanybody != null ? new_paymentcompanybody.ToDefault<string>("new_companyname") + "(" + new_paymentcompanybody.ToDefault<string>("new_contractingbody") + ")" : "";
                #endregion

                #region 基础信息(不含主题)
                // 付款单号
                model.input_c654397de1bd = entity.ToDefault<string>("new_name");
                // 申请人
                model.input_d2f86f5b61e8 = requestuserName;
                // 申请人部门
                model.input_8d021dd15b3e = GetSystemParameterValue(OrganizationService, "BPMStartUserDepartment");
                // 支付公司
                model.input_140cbe98bb25 = paymentcompanybody;
                // 承担公司
                model.input_461b6e6f3e81 = paymentcompanybody;
                // 付款金额
                model.input_ef18e1381e79 = entity.ToDefault<decimal>("new_approvalamount").ToString("f2") + "(" + currency + ")";
                // 付款方式
                model.input_7dc2e8b6b631 = entity.FormattedValues.ContainsKey("new_payway") ? entity.FormattedValues["new_payway"] : "电汇";
                // 提交审批日期
                model.datepicker_9fff85484b28 = DateTime.Now.ToString("yyyy-MM-dd");
                // 最迟付款日期
                model.datepicker_ef3d09efbf26 = entity.FormattedValues.Contains("new_latestpaymentdate") ? Convert.ToDateTime(entity["new_latestpaymentdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                // 服务商名称
                model.input_c7988e20cf5c = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                // SAP供应商编码
                model.input_90ec538e0d78 = "0000" + sapcode;
                // 国际业务性质
                model.input_3af233b12d65 = "售后";
                // 费用类型
                model.input_ff0685db1a82 = "备件运营-物流费";
                #endregion

                #region 付款单信息
                // 国家
                model.input_5e9aead82791 = entity.IsNotNull("new_country_id") ? entity.ToEr("new_country_id").Name : "";
                // 服务商名称
                model.textarea_cde46e6a3d2f = entity.IsNotNull("new_srv_station_id") ? entity.ToEr("new_srv_station_id").Name : "";
                // 国家（二字码）
                model.input_492bc60166c2 = countrycode;
                // 结算年份
                model.input_dcf8af1d26a8 = entity.ToDefault<int>("new_year").ToString();
                // 结算月份
                model.input_4601412f4b00 = entity.ToDefault<int>("new_month").ToString();
                // 预提费用（不含税）
                model.input_fa68279df0e1 = entity.ToDefault<decimal>("new_withholdingmoney").ToString("f2") + "(" + currency + ")";
                // 结算费用（不含税）
                model.input_aa3a84d769d4 = entity.ToDefault<decimal>("new_totalcost").ToString("f2") + "(" + currency + ")";
                // 增值税率
                model.textarea_83927f68a2de = entity.ToDefault<decimal>("new_taxrate").ToString("f2");
                // 增值税额
                model.input_6dcb277f1c7e = entity.ToDefault<decimal>("new_vatamount").ToString("f2") + "(" + currency + ")";
                // 代扣税率
                model.input_d2002a8f8e47 = entity.ToDefault<decimal>("new_withholdingtax").ToString("f2");
                // 代扣税额
                model.input_7be059d76aae = entity.ToDefault<decimal>("new_withholdingamount").ToString("f2") + "(" + currency + ")";
                // 开户银行
                model.textarea_33dc8ae1f5a0 = entity.ToDefault<string>("new_bank");
                // 银行账号
                model.input_871fd48b8265 = entity.ToDefault<string>("new_bankaccount");
                // SWIFT CODE
                model.input_df6e33330d67 = entity.ToDefault<string>("new_swiftcode");
                // 发票号码
                model.input_e577da65d75c = entity.ToDefault<string>("new_invoiceno");
                // 发票代码
                model.input_23b073f11399 = entity.ToDefault<string>("new_invoicenumber");
                // 开票日期
                model.datepicker_618f0d62ae68 = entity.FormattedValues.Contains("new_confirmdate") ? Convert.ToDateTime(entity["new_confirmdate"]).AddHours(8).ToString("yyyy-MM-dd") : "";
                // 付款金额（不含币种）
                model.inputFloat_23b844fbd14f = Convert.ToDecimal(entity.ToDefault<decimal>("new_approvalamount").ToString("f2"));
                // 开户名称
                model.textarea_de3f3a1e1e90 = entity.ToDefault<string>("new_accountbankname");
                // 币种
                model.input_e37b695ab117 = currency;
                // 主体
                model.input_b0b1f972fc27 = entity.ToDefault<string>("new_contractingbody");
                // 服务商编码
                model.input_67c4bbf988fc = station_code;
                // 交易附言
                model.textarea_007c752a50c8 = entity.ToDefault<string>("new_transactionpostscript");
                //是否Reverse charge
                model.input_1e03103db5d5 = entity.Contains("new_isreversecharge") ? entity.FormattedValues["new_isreversecharge"] : "";
                // 发票附件
                List<LogisticsInvoiceModel> invoicefileList = new List<LogisticsInvoiceModel>();
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("entityId", entityid.Replace("{", "").Replace("}", ""));
                dic.Add("entityName", entityname);
                dic.Add("filetype", "");
                dic.Add("new_mimetype", "");
                string file = CrmHelper.InvokeHiddenApi(OrganizationService, "new_comattachment", "Attachment/GetFileByRecordByMimetype", dic);
                if (!string.IsNullOrWhiteSpace(file)) {
                    List<AttachmentModel> list = JsonHelper.Deserialize<List<AttachmentModel>>(file);
                    foreach (var item in list) {
                        LogisticsInvoiceModel invoice = new LogisticsInvoiceModel();
                        invoice.upload_1725518373540 = item.new_filepath;
                        invoicefileList.Add(invoice);
                    }
                }
                model.formTable_cbe62c59e19d = invoicefileList;
                #endregion

                #region 结算单明细(不含税)
                // 加减项费用（不含税）
                EntityCollection specialexpensedetails = OrganizationService.RetrieveMultiple(
                    new QueryExpression("new_srv_specialexpense") {
                        ColumnSet = new ColumnSet("new_feetype", "new_specialamount", "new_reason"),
                        Criteria = new FilterExpression {
                            Conditions =
                            {
                                new ConditionExpression("new_expense_claim_id", ConditionOperator.Equal, entity.Id),
                                new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                            }
                        }
                    });
                List<SpecialExpenseLogisticsModel> specialfeelist = new List<SpecialExpenseLogisticsModel>();
                foreach (var item in specialexpensedetails.Entities) {
                    SpecialExpenseLogisticsModel fee = new SpecialExpenseLogisticsModel();
                    fee.input_1725518608243 = item.FormattedValues.ContainsKey("new_feetype") ? item.FormattedValues["new_feetype"] : "";
                    fee.input_1725518613371 = item.ToDefault<decimal>("new_specialamount").ToString();
                    fee.input_1725518611344 = item.ToDefault<string>("new_reason");
                    specialfeelist.Add(fee);
                }
                model.formTable_3f8eb4d509ec = specialfeelist;
                // 结算单链接
                string expense_url = GetSystemParameterValue(OrganizationService, "new_srv_expense_claim_url");
                model.link_2fdc8e25aa3b = string.Format(expense_url, entityid);
                #endregion

                #region 扩展字段
                Variables variables = new Variables();
                variables.model.Add("new_name", entity.ToDefault<string>("new_name"));
                #endregion

                #region 参数处理
                // 主题 = 国家 + 服务商简称 + 年月 + 费用类型
                model.textarea_0f2fe10ef189 = model.input_5e9aead82791 + stationabbreviation + model.input_dcf8af1d26a8 + "年" + model.input_4601412f4b00 + "月" + model.input_ff0685db1a82;
                model.businessKey = entity.ToDefault<string>("new_businesskey");
                body.formData = model;
                body.businessKey = model.businessKey;
                body.modelCode = GetSystemParameterValue(OrganizationService, "sys_Bpm3.0logisticsmodelCode");
                body.startUserId = requestuser;
                // 标题 = 主题 + 付款单号 + 申请人 
                body.processInstanceName = model.textarea_0f2fe10ef189 + model.input_c654397de1bd + requestuserName;
                body.variables = variables;
                #endregion

                Log.InfoMsg($"抛BPM参数：" + JsonHelper.Serialize(model));
                return body;
            }
            catch (Exception ex) {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }


        /// <summary>
        /// 抛转BPM后更新结算状态
        /// </summary>
        /// <param name="entityId">单据id</param>
        /// <param name="Status">结算单状态</param>
        public void UpdateStatus(string entityId, string Status)
        {
            try
            {
                Entity e = new Entity("new_srv_expense_claim");
                e.Id = new Guid(entityId);
                if (Status == "3")
                {
                    e["new_formstatus"] = new OptionSetValue(4);// 结算单状态：待审核
                }
                else
                {
                    e["new_formstatus"] = new OptionSetValue(6);//待付款申请
                                                                //  e["new_approvalstatus"] = new OptionSetValue(2); // 第二次抛转BPM，修改审核状态：审核中
                }

                OrganizationService.Update(e);
            }
            catch (Exception ex)
            {
                Log.InfoMsg("修改结算单状态错误：" + ex.Message);
                Log.LogException(ex);
            }
        }
        #region 帮助方法
        /// <summary>
        /// 根据结算单获取服务单
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <returns></returns>
        private List<WorkOrderdetailModel> GetWorkOrderList(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<WorkOrderdetailModel> list = new List<WorkOrderdetailModel>();
            try
            {
                QueryExpression queryExpression = new QueryExpression("new_srv_workorder");
                queryExpression.Criteria.AddCondition("new_expenseclaimid", ConditionOperator.Equal, id);
                queryExpression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryExpression.ColumnSet.AddColumns("new_name", "new_station_id", "new_type",
                                                    "new_repairfee", "new_repairfeekpi", "new_goodsfiles_id",
                                                      "new_boxfee", "new_repairsubsidy", "new_distancesubsidy",
                                                      "new_logisticsfee", "new_recordingfee", "new_othercost",
                                                      "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(queryExpression);
                if (ec == null || ec.Entities.Count == 0)
                    return null;

                foreach (Entity item in ec.Entities)
                {
                    WorkOrderdetailModel detail = new WorkOrderdetailModel();
                    // 服务单号
                    detail.new_name = item.GetAttributeValue<string>("new_name");
                    //服务网点
                    if (item.Contains("new_station_id"))
                    {
                        detail.new_station_id = item.GetAttributeValue<EntityReference>("new_station_id").Name;
                    }
                    // 所属网点
                    if (item.Contains("new_type"))
                    {
                        detail.new_type = item.GetAttributeValue<OptionSetValue>("new_type").Value.ToString();
                    }
                    // 维修劳务费
                    if (item.Contains("new_repairfee"))
                    {
                        detail.new_repairfee = Math.Round(item.GetAttributeValue<decimal>("new_repairfee"), 2).ToString();
                    }
                    // 维修劳务费kpi
                    if (item.Contains("new_repairfeekpi"))
                    {
                        detail.new_repairfeekpi = Math.Round(item.GetAttributeValue<decimal>("new_repairfeekpi"), 2).ToString();
                    }
                    //商品
                    if (item.Contains("new_goodsfiles_id"))
                    {
                        detail.new_goodsfiles_id = item.GetAttributeValue<EntityReference>("new_goodsfiles_id").Name;
                    }
                    // 箱子使用费
                    if (item.Contains("new_boxfee"))
                    {
                        detail.new_boxfee = Math.Round(item.GetAttributeValue<decimal>("new_boxfee"), 2).ToString();
                    }
                    // 机修补贴
                    if (item.Contains("new_repairsubsidy"))
                    {
                        detail.new_repairsubsidy = Math.Round(item.GetAttributeValue<decimal>("new_repairsubsidy"), 2).ToString();
                    }
                    // 路程补贴
                    if (item.Contains("new_distancesubsidy"))
                    {
                        detail.new_distancesubsidy = Math.Round(item.GetAttributeValue<decimal>("new_distancesubsidy"), 2).ToString();
                    }
                    // 上门物流费
                    if (item.Contains("new_logisticsfee"))
                    {
                        detail.new_logisticsfee = Math.Round(item.GetAttributeValue<decimal>("new_logisticsfee"), 2).ToString();
                    }
                    // 录单费
                    if (item.Contains("new_recordingfee"))
                    {
                        detail.new_recordingfee = Math.Round(item.GetAttributeValue<decimal>("new_recordingfee"), 2).ToString();
                    }
                    // 其他费用
                    if (item.Contains("new_othercost"))
                    {
                        detail.new_othercost = Math.Round(item.GetAttributeValue<decimal>("new_othercost"), 2).ToString();
                    }
                    // 创建时间
                    if (item.Contains("createdon"))
                    {
                        detail.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    list.Add(detail);
                }
                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询服务单错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据结算单查询特殊费用
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <returns></returns>
        private List<SpecialExpenseModel> GetSpecialExpenseList(string id, string formType)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<SpecialExpenseModel> list = new List<SpecialExpenseModel>();
            try
            {
                QueryExpression query = new QueryExpression("new_srv_specialexpense");
                query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_name", "new_workorder_id", "new_stationservice_id",
                                            "new_station_id", "new_feetype", "new_specialamount",
                                             "new_submittime", "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count == 0)
                    return list;

                foreach (Entity item in ec.Entities)
                {
                    SpecialExpenseModel model = new SpecialExpenseModel();
                    model.new_name = item.GetAttributeValue<string>("new_name");
                    //服务单
                    if (item.Contains("new_workorder_id"))
                    {
                        model.new_workorder_id = item.GetAttributeValue<EntityReference>("new_workorder_id").Name;
                    }
                    //服务商
                    if (item.Contains("new_stationservice_id"))
                    {
                        model.new_stationservice_id = item.GetAttributeValue<EntityReference>("new_stationservice_id").Name;
                    }
                    //服务网点
                    if (item.Contains("new_station_id"))
                    {
                        model.new_station_id = item.GetAttributeValue<EntityReference>("new_station_id").Name;
                    }
                    // 费用类型
                    if (item.Contains("new_feetype"))
                    {
                        model.new_feetype = item.FormattedValues["new_feetype"];
                    }
                    // 特殊费用金额
                    if (item.Contains("new_specialamount"))
                    {
                        model.new_specialamount = Math.Round(item.GetAttributeValue<decimal>("new_specialamount"), 2).ToString();
                    }
                    // 提交时间
                    if (item.Contains("new_submittime"))
                    {
                        model.new_submittime = item.GetAttributeValue<DateTime>("new_submittime").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    // 创建时间
                    if (item.Contains("createdon"))
                    {
                        model.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    #region 特殊费用审批记录
                    if (formType == "6")
                    {
                        QueryExpression expression = new QueryExpression("new_approvallog");
                        expression.Criteria.AddCondition("new_entityname", ConditionOperator.Equal, "new_srv_expense_claim");
                        expression.Criteria.AddCondition("new_entityid", ConditionOperator.Equal, item.Id);
                        expression.ColumnSet.AddColumns("new_approvalstatus", "new_memo", "new_name", "new_approvaluser");
                        EntityCollection collection = OrganizationService.RetrieveMultiple(expression);
                        if (collection != null && collection.Entities.Count > 0)
                        {
                            model.approvalList = new List<ApprovallogModel>();
                            foreach (var e in collection.Entities)
                            {
                                ApprovallogModel approvallog = new ApprovallogModel();
                                if (e.Contains("new_approvalstatus"))
                                {
                                    approvallog.new_approvalstatus = e.FormattedValues["new_approvalstatus"];
                                }
                                approvallog.new_memo = e.GetAttributeValue<string>("new_memo");
                                approvallog.new_name = e.GetAttributeValue<string>("new_name");
                                approvallog.new_approvaluser = e.GetAttributeValue<string>("new_approvaluser");
                                model.approvalList.Add(approvallog);
                            }
                        }
                    }
                    #endregion

                    list.Add(model);
                }
                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询特殊费用错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据结算单获取激活费用
        /// </summary>结算单id</param>
        /// <returns></returns>
        private List<ExpenseActiveModel> GetExpenseActiveList(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<ExpenseActiveModel> list = new List<ExpenseActiveModel>();
            try
            {
                QueryExpression query = new QueryExpression("new_srv_expense_activationline");
                query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_country_id", "new_category1_id",
                                            "new_category2_id", "new_category3_id", "new_number",
                                             "new_poprice", "new_totalmoney", "new_costrate",
                                             "new_settlementmoney", "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count == 0)
                    return list;

                foreach (Entity item in ec.Entities)
                {
                    ExpenseActiveModel model = new ExpenseActiveModel();
                    // 国家
                    if (item.Contains("new_country_id"))
                    {
                        model.new_country_id = item.GetAttributeValue<EntityReference>("new_country_id").Name;
                    }
                    // 一级品类
                    if (item.Contains("new_category1_id"))
                    {
                        model.new_category1_id = item.GetAttributeValue<EntityReference>("new_category1_id").Name;
                    }
                    // 二级品类
                    if (item.Contains("new_category2_id"))
                    {
                        model.new_category2_id = item.GetAttributeValue<EntityReference>("new_category2_id").Name;
                    }
                    // 三级品类
                    if (item.Contains("new_category3_id"))
                    {
                        model.new_category3_id = item.GetAttributeValue<EntityReference>("new_category3_id").Name;
                    }
                    // 激活数量
                    if (item.Contains("new_number"))
                    {
                        model.new_number = Math.Round(item.GetAttributeValue<decimal>("new_number"), 2).ToString();
                    }
                    // po均价
                    if (item.Contains("new_poprice"))
                    {
                        model.new_poprice = Math.Round(item.GetAttributeValue<decimal>("new_poprice"), 2).ToString();
                    }
                    // 费用合计
                    if (item.Contains("new_totalmoney"))
                    {
                        model.new_totalmoney = Math.Round(item.GetAttributeValue<decimal>("new_totalmoney"), 2).ToString();
                    }
                    // 售后费用率
                    if (item.Contains("new_costrate"))
                    {
                        model.new_costrate = Math.Round(item.GetAttributeValue<decimal>("new_costrate"), 2).ToString();
                    }
                    // 结算金额
                    if (item.Contains("new_settlementmoney"))
                    {
                        model.new_settlementmoney = Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney"), 2).ToString();
                    }
                    // 创建时间
                    if (item.Contains("createdon"))
                    {
                        model.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    list.Add(model);
                }
                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询激活费用错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据结算获取受理
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <returns></returns>
        private List<IncidentModel> GetIncidentList(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<IncidentModel> list = new List<IncidentModel>();
            try
            {
                QueryExpression query = new QueryExpression("new_service_handing");
                query.Criteria.AddCondition("new_srv_expense_claim_id", ConditionOperator.Equal, id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_name", "new_settlementmoney", "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count == 0)
                    return null;

                foreach (Entity item in ec.Entities)
                {
                    IncidentModel model = new IncidentModel();
                    model.new_name = item.GetAttributeValue<string>("new_name");
                    if (item.Contains("new_settlementmoney"))
                    {
                        model.new_settlementmoney = Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney"), 2).ToString();
                    }
                    // 创建时间
                    if (item.Contains("createdon"))
                    {
                        model.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    list.Add(model);
                }

                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询服务受理单错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 结算单获取运行商HF
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private List<ExpenseHFModel> GetExpenseHFList(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<ExpenseHFModel> list = new List<ExpenseHFModel>();

            try
            {
                QueryExpression query = new QueryExpression("new_srv_expense_hfline");
                query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_workorder_id", "new_station_id", "new_category1_id",
                                            "new_category2_id", "new_category3_id", "new_settlementmoney",
                                            "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count == 0)
                    return null;

                foreach (Entity item in ec.Entities)
                {
                    ExpenseHFModel model = new ExpenseHFModel();
                    // 服务单
                    if (item.Contains("new_workorder_id"))
                    {
                        model.new_workorder_id = item.GetAttributeValue<EntityReference>("new_workorder_id").Name;
                    }
                    // 服务商
                    if (item.Contains("new_station_id"))
                    {
                        model.new_station_id = item.GetAttributeValue<EntityReference>("new_station_id").Name;
                    }
                    // 一级品类
                    if (item.Contains("new_category1_id"))
                    {
                        model.new_category1_id = item.GetAttributeValue<EntityReference>("new_category1_id").Name;
                    }
                    // 二级品类
                    if (item.Contains("new_category2_id"))
                    {
                        model.new_category2_id = item.GetAttributeValue<EntityReference>("new_category2_id").Name;
                    }
                    // 三级品类
                    if (item.Contains("new_category3_id"))
                    {
                        model.new_category3_id = item.GetAttributeValue<EntityReference>("new_category3_id").Name;
                    }
                    // 结算金额
                    if (item.Contains("new_settlementmoney"))
                    {
                        model.new_settlementmoney = Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney"), 2).ToString();
                    }
                    if (item.Contains("createdon"))
                    {
                        model.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    list.Add(model);
                }

                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询运行商HF错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据结算单获取调差
        /// </summary>
        /// <param name="id">结算单id</param>
        /// <returns></returns>
        private List<ExpenseAdjustmentModel> GetExpenseAdjustmentList(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            List<ExpenseAdjustmentModel> list = new List<ExpenseAdjustmentModel>();

            try
            {
                QueryExpression query = new QueryExpression("new_srv_expense_adjustmentline");
                query.Criteria.AddCondition("new_expense_claim_id", ConditionOperator.Equal, id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_name", "new_totalmoney", "new_adjustment_money",
                                            "new_settlementmoney", "createdon");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count == 0)
                    return null;

                foreach (Entity item in ec.Entities)
                {
                    ExpenseAdjustmentModel model = new ExpenseAdjustmentModel();
                    model.new_name = item.GetAttributeValue<string>("new_name");
                    // 总费用
                    if (item.Contains("new_totalmoney"))
                    {
                        model.new_totalmoney = Math.Round(item.GetAttributeValue<decimal>("new_totalmoney"), 2).ToString();
                    }
                    // 调差金额
                    if (item.Contains("new_adjustment_money"))
                    {
                        model.new_adjustment_money = Math.Round(item.GetAttributeValue<decimal>("new_adjustment_money"), 2).ToString();
                    }
                    // 结算金额
                    if (item.Contains("new_settlementmoney"))
                    {
                        model.new_settlementmoney = Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney"), 2).ToString();
                    }
                    // 创建时间
                    if (item.Contains("createdon"))
                    {
                        model.createdon = item.GetAttributeValue<DateTime>("createdon").ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    list.Add(model);
                }

                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("结算单查询运行商HF错误：" + ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 附件model
        /// </summary>
        public class AttachmentModel
        {
            /// <summary>
            /// 关联实体数据Id
            /// </summary>
            public string new_entityid { get; set; }
            /// <summary>
            ///关联实体数据实体名
            /// </summary>
            public string new_entityname { get; set; }
            /// <summary>
            /// 文件名
            /// </summary>
            public string new_filename { get; set; }
            /// <summary>
            /// 文件类型
            /// </summary>
            public string new_mimetype { get; set; }
            /// <summary>
            /// 文件大小
            /// </summary>
            public decimal new_filesize { get; set; }
            /// <summary>
            /// 文件路径
            /// </summary>
            public string new_filepath { get; set; }
            /// <summary>
            /// 文件描述
            /// </summary>
            public string new_filedesc { get; set; }
            /// <summary>
            /// 文件内容 
            /// </summary>
            public string documentbody { get; set; }
            /// <summary>
            /// 附件记录Id
            /// </summary>
            public string new_attachmentid { get; set; }
            /// <summary>
            /// 附件标题
            /// </summary>
            public string new_filetitle { get; set; }
            /// <summary>
            /// 创建人
            /// </summary>
            public string createdby { get; set; }
            /// <summary>
            /// 创建时间
            /// </summary>
            public string createdon { get; set; }
            /// <summary>
            /// 上传附件类型
            /// </summary>
            public string new_filetype { get; set; }
        }
        #endregion
        /// <summary>
        /// 根据年份，月份，服务商查询KPI
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="stationid"></param>
        /// <returns></returns>
        public EntityCollection Getactualkpi(int year, int month, Guid stationid) 
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_station_actualkpi");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, year);
            qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, month);
            qe.Criteria.AddCondition("new_serviceprovider_id", ConditionOperator.Equal, stationid);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 1);//类型 = 工单
            cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 获取国家KPI明细
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="stationid"></param>
        /// <param name="countryid"></param>
        /// <returns></returns>
        public EntityCollection GetCountrykpiline(int year, int month, Guid stationid, Guid countryid) 
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_station_countrykpiline");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            FilterExpression filter = new FilterExpression(LogicalOperator.Or);
            filter.AddCondition("new_serviceprovider_id", ConditionOperator.Equal, stationid);
            filter.AddCondition("new_country_id", ConditionOperator.Equal, countryid);
            qe.Criteria.AddFilter(filter);
            LinkEntity le = new LinkEntity("new_station_countrykpiline", "new_station_actualkpi", "new_station_actualkpi_id", "new_station_actualkpiid", JoinOperator.Inner);
            le.Columns = new ColumnSet("new_year", "new_month", "new_type");
            le.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, year);
            le.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, month);
            le.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            le.EntityAlias = "le";
            qe.LinkEntities.Add(le);
            cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 获取三级品类
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getcategory3(string[] category) 
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_category3");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_code", ConditionOperator.In, category);
            cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 获取二级品类
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getcategory2()
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_category2");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 获取一级品类
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getcategory1()
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_category1");
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 根据签约主体查询支付公司主体名称
        /// </summary>
        /// <returns></returns>
        public Entity GetPaymentcompanybody(string contractingbody) 
        {
            QueryExpression qe = new QueryExpression("new_paymentcompanybody");
            qe.ColumnSet = new ColumnSet("new_companyname", "new_contractingbody", "new_name");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_contractingbody", ConditionOperator.Equal, contractingbody);
            return OrganizationServiceAdmin.RetrieveMultiple(qe).Entities.FirstOrDefault();
        }
    }
}
