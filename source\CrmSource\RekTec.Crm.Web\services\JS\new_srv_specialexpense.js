﻿//#region 文件描述
/******************************************************************
** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved. 
** 创建人   :   郭斌
** 创建时间 : 2021-9-15 
** 说明     :    特殊费用脚本
******************************************************************/
//#endregion

function form_load() {
	///	<summary>窗体加载执行方法</summary>
	setnew_stationservice_id();
	var formType = Xrm.Page.ui.getFormType();
	if (formType == 1) {
		//formType == 1表创建
		setnew_station_id();
	} 
	feetypeSetOptions();
}
function new_stationservice_idOnchange() {
	rtcrm("#new_station_id").val(null);
	setnew_station_id();
}

function setnew_station_id() {
	var new_stationservice_id = rtcrm.getLookupId("new_stationservice_id");
	if (new_stationservice_id) {
		rtcrm("#new_station_id").disabled(false);
		new_srv_stationLookup(new_stationservice_id);
	} else {
		rtcrm("#new_station_id").disabled(true);
	}
}
//服务网点根据服务商过滤
function new_srv_stationLookup(new_stationservice_id) {
	var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>" +
		"<entity name='new_srv_station'>" +
		"<attribute name='new_srv_stationid' />" +
		"<attribute name='new_name' />" +
		"<attribute name='new_code' />" +
		"<attribute name='new_level' />" +
		"<attribute name='createdon' />" +
		"<filter type='and'>" +
		"<condition attribute='new_srv_station_id' operator='eq' value='" + new_stationservice_id + "' />" +
		"<condition attribute='statecode' operator='eq' value='0' />" +
		"</filter>" +
		"</entity >" +
		"</fetch >";
	var layoutXml = "<grid name='resultset' object='1' jump='name' select='1' icon='1' preview='1'>" +
		"<row name='result' id='new_srv_stationid'>" +
		"<cell name='new_name' width='120' />" +
		"<cell name='new_code' width='120' />" +
		"<cell name='new_level' width='120' />" +
		"<cell name='createdon' width='120' />" +
		"</row>" +
		"</grid>";
	try {
		rtcrm.customizeLookupView("new_station_id", "new_srv_station", fetchXml, layoutXml);
	} catch (e) {
		alert(e.message);
	}
}

//提交
function submitBPM() {
	var entityId = rtcrm.getEntityId();
	if (entityId) {
		var new_approvestatus = rtcrm("#new_approvestatus").val();
		if (new_approvestatus == 1 || new_approvestatus == 4) {
			rtcrm.invokeHiddenApiAsync("new_service", "BpmInterface/BpmSpecialSubmit", { id: entityId })
				.then(res => {
					rtcrm.alertDialog($t("common.submitSucessfully", "提交成功！"));
					rtcrm("#new_submittime").val(new Date());
					Xrm.Page.data.save();
					//Xrm.Utility.openEntityForm(Xrm.Page.data.entity.getEntityName(), Xrm.Page.data.entity.getId(), null);
					
				}).catch(e => {
					rtcrm.alertDialog(e.message);
                })
				
		}
    }
}
//确认按钮设置隐藏
function showSubmit(){
	//var new_approvestatus = rtcrm("#new_approvestatus").val();
	//var formType = Xrm.Page.ui.getFormType();
	//if ((new_approvestatus == 1 || new_approvestatus == 4) && formType!=1) {
	//	return true;
	//} else {
	//	return false;
	//   }
	return false;
}
//确认按钮设置隐藏
function ConfirmEnabled() {
	//var new_approvestatus = rtcrm("#new_approvestatus").val();
	//var formType = Xrm.Page.ui.getFormType();
	//if ((new_approvestatus == 1 || new_approvestatus == 4) && formType!=1) {
	//	return true;
	//} else {
	//	return false;
	//   }
	return false;
}
//撤回
function recallBPM() {
	var entityId = rtcrm.getEntityId();
	if (entityId) {
		var new_approvestatus = rtcrm("#new_approvestatus").val();
		if (new_approvestatus == 2) {
			rtcrm.invokeHiddenApiAsync("new_service", "BpmInterface/BpmSpecialRecall", { id: entityId })
				.then(res => {
					rtcrm.alertDialog($t("common.operationSucessfully","操作成功！"));
					rtcrm("#new_submittime").val(null);
					Xrm.Page.data.save();
				}).catch(e => {
					rtcrm.alertDialog(e);
				})

		}
	}
}
function showRecall() {
	var new_approvestatus = rtcrm("#new_approvestatus").val();
	if (new_approvestatus == 2) {
		return true;
	} else {
		return false;
	}
}
//根据结算单带出服务商,币种
function setnew_stationservice_id() {
	var new_expense_claim_id = rtcrm.getLookupId("new_expense_claim_id");
	if (new_expense_claim_id) {
		var responseJSON = rtcrm.getFieldValue(new_expense_claim_id, "new_srv_expense_claims", "_new_srv_station_id_value,_new_transactioncurrency_id_value", true);
		if (responseJSON != null && responseJSON != undefined) {
			rtcrm.setLookupValue("new_stationservice_id", rtcrm.buildLookup(responseJSON._new_srv_station_id_value, responseJSON["<EMAIL>"], "new_srv_station"));
			rtcrm.setLookupValue("new_transactioncurrency_id", rtcrm.buildLookup(responseJSON._new_transactioncurrency_id_value, responseJSON["<EMAIL>"], "transactioncurrency"));
		}
	}
}
//费用类型根据迈创和非迈创进行设置
function feetypeSetOptions() {
	var expense_claim = rtcrm.getLookupId("new_expense_claim_id");
	if (expense_claim == null || expense_claim == undefined) {
		return;
	}
	var optionsMaitrox = [4,19,20,21,22,23,24,26];//迈创费用类型
	var optionsNonMaitrox = [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 25, 27, 28, 29, 30];//独立服务商和B2X
	var optionsWarehousing = [14, 31, 32, 33];//仓储结费费用类型
	var picklist = Xrm.Page.getControl("new_feetype");
	var options = picklist.getOptions();
	var new_expense_claim_id = expense_claim.replace("{", "").replace("}", "");
	var data = rtcrm.retrieve('new_srv_expense_claims(' + new_expense_claim_id + ')?$expand=new_srv_station_id($select=new_providertype)', true)
	if (data != null) {
		var providertype = data.new_srv_station_id.new_providertype;
		if (providertype == 2 || providertype == 6) {
			for (var i = 0; i < options.length; i++) {
				if (optionsNonMaitrox.indexOf(options[i].value) == -1) {
					//匹配不到的移除
					Xrm.Page.getControl("new_feetype").removeOption(options[i].value);
				}
			}
		} else if (providertype == 5) {
			for (var i = 0; i < options.length; i++) {
				if (optionsMaitrox.indexOf(options[i].value) == -1) {
					Xrm.Page.getControl("new_feetype").removeOption(options[i].value);
				}
			}
		} else if (providertype == 10) {
			for (var i = 0; i < options.length; i++) {
				if (optionsWarehousing.indexOf(options[i].value) == -1) {
					Xrm.Page.getControl("new_feetype").removeOption(options[i].value);
				}
			}
		}
		//仓储结费隐藏服务单，Goods SKU字段
		if (providertype == 10) {
			Xrm.Page.getControl("new_workorder_id").setVisible(false);
			Xrm.Page.getControl("new_goodsfiles_id").setVisible(false);
		} 
	}
}
