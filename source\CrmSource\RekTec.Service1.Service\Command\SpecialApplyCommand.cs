﻿using System;
using System.Collections.Generic;
using System.Linq;
using RekTec.Crm.OrganizationService.Common.Helper;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Crm.BizCommon;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.HiddenApi;
using RekTec.Service1.Service.Bll;
using RekTec.Service1.Service.Helper;
using RekTec.Service1.Service.Model;

namespace RekTec.Service1.Service.Command
{
    /// <summary>
    /// 工单--特批申请单Command类
    /// </summary>
    public class SpecialApplyCommand : HiddenCommand
    {
        /// <summary>
        /// <summary>
        /// 特批单申请
        ///</summary>
        ///<param name = "Id" >特批申请id</ param >
        ///<param name="type">1：撤回；2：提交；3：同意；4：驳回</param>
        ///<param name="memo"></param>
        public void SpecialOrderSubmit(string Id, int type, string memo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(Id))
                    throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.require.id", "特批申请单id为空"));

                Entity e = OrganizationService.Retrieve("new_srv_specialapply", new Guid(Id), new ColumnSet(true));
                if (!e.Contains("new_type"))
                    throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.required.new_type", "特批申请单【特批单类型为空】"));

                Entity order = null;


                int new_type = e.GetAttributeValue<OptionSetValue>("new_type").Value;
                // 审核状态
                int new_approvalstatus = e.Contains("new_approvalstatus") ? e.GetAttributeValue<OptionSetValue>("new_approvalstatus").Value : 0;
                // 服务单id
                string orderId = e.Contains("new_workorder_id") ? e.GetAttributeValue<EntityReference>("new_workorder_id").Id.ToString() : null;

                // 工单类型
                int orderType = 0;
                // 工单换货方式
                int new_exchange_way = 0;
                if (!string.IsNullOrWhiteSpace(orderId))
                {
                    order = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_srv_workorder", new Guid(orderId), new ColumnSet("new_dealstatus", "new_type", "new_exchange_way", "new_salechannel", "new_origin", "new_userprofile_id", "new_manualfee"));
                    if (order == null)
                        throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                    orderType = order.Contains("new_type") ? order.GetAttributeValue<OptionSetValue>("new_type").Value : 0;
                    new_exchange_way = order.Contains("new_exchange_way") ? order.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;
                }

                #region 更新特批单签核状态
                Entity apply = new Entity("new_srv_specialapply");
                apply.Id = new Guid(Id);
                apply["new_approvalstatus"] = new OptionSetValue(type);// 审批状态
                apply["new_approvalmemo"] = memo;// 审批备注
                apply["new_approvaltime"] = DateTime.UtcNow; // 审批时间
                apply["new_approvaluser"] = new EntityReference("systemuser", UserId); // 审批人
                switch (type)
                {
                    case 2:
                        #region 提交校验

                        #region 特批类型：三包换机。如果是保内维修状态的工单，在特批单提交时报错提示
                        if (new_type == 1 || new_type == 6)
                        {
                            QueryExpression query = new QueryExpression("new_srv_specialapply");
                            query.ColumnSet = new ColumnSet("new_type");
                            query.Criteria.AddCondition("new_srv_specialapplyid", ConditionOperator.Equal, Id);
                            //query.Criteria.AddCondition("new_type", ConditionOperator.Equal, 1);// 维修
                            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            query.Criteria.AddCondition("new_workorder_id", ConditionOperator.NotNull); // 服务单不为空
                            query.ColumnSet.AddColumns("new_srv_specialapplyid");
                            LinkEntity link = new LinkEntity("new_srv_specialapply", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            //link.LinkCriteria.AddCondition("new_warranty", ConditionOperator.Equal, 1);// 服务单：保内
                            link.EntityAlias = "order";
                            link.Columns.AddColumns(new string[] { "new_isparallelgoods", "new_warranty" });
                            query.LinkEntities.Add(link);
                            EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                            if (ec != null && ec.Entities.Count > 0)
                            {
                                //东西欧非行货
                                var isparallelgoods = ec.Entities.Where(x => x.Contains("order.new_isparallelgoods") && x.GetAliasAttributeValue<bool>("order.new_isparallelgoods")).ToList();
                                if (isparallelgoods != null && isparallelgoods.Count > 0)
                                    throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.isparallelgoods", "东西欧非行货订单，不允许创建三包超期、服务类型转换特批单"));
                                if (new_type == 1)
                                {
                                    //维修、保内单
                                    var warrantyList = ec.Entities.Where(x => x.Contains("new_type") && x.GetAttributeValue<OptionSetValue>("new_type").Value == 1 && x.Contains("order.new_warranty") && x.GetAliasAttributeValue<OptionSetValue>("order.new_warranty").Value == 1).ToList();
                                    if (warrantyList != null && warrantyList.Count > 0)
                                        throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.Workorder_Inwarranty", "当前工单本是维修保内工单！"));
                                }
                            }
                        }

                        #endregion

                        #region 特批类型：异型换机。检测特批明细数量，只能允许一条明细
                        if (new_type == 5)
                        {
                            if (order != null && order.Contains("new_origin"))
                            {
                                int new_origin = order.GetAttributeValue<OptionSetValue>("new_origin").Value;
                                if (new_origin == 60)
                                    throw new InvalidPluginExecutionException(GetResource("Specialapply_Upload", "当前工单来源=工单上传，请通过异型号配置表进行配置"));
                            }
                            QueryExpression expression = new QueryExpression("new_srv_specialapplyline");
                            expression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            expression.Criteria.AddCondition("new_specialapply_id", ConditionOperator.Equal, Id);
                            expression.ColumnSet.AddColumns("new_srv_specialapplylineid", "new_warechangeway");
                            EntityCollection collection = OrganizationService.RetrieveMultiple(expression);
                            if (collection == null || collection.Entities.Count <= 0)
                            {
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.DetailsIsEmpty", "特批类型【异形换机】必须要有特批申请明细"));
                            }
                            if (collection != null && collection.Entities.Count > 1)
                            {
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.Detailcount", "【异形换机】特批明细数量只能小于2"));
                            }

                            #region 校验换货方式和特批单发货方式是否一致 add by Hyacinthhuang 2022-7-8
                            if (orderType == 2)
                            {
                                int new_warechangeway = collection.Entities[0].Contains("new_warechangeway") ? collection.Entities[0].GetAttributeValue<OptionSetValue>("new_warechangeway").Value : 0;
                                if (new_exchange_way <= 0)
                                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.ExchangeWayNull", "工单的换货方式为空，请在【工单-退换信息】中维护换货方式！"));
                                if (new_exchange_way != new_warechangeway)
                                    throw new InvalidPluginExecutionException(GetResource("SpecialOrderSubmit.WayNotSame", "特批单换机明细发货方式和工单换货方式不一致，提交失败！"));
                            }
                            #endregion
                        }
                        #endregion

                        #region 服务类型转换
                        if (new_type == 6)
                        {
                            if (string.IsNullOrWhiteSpace(orderId))
                                throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                            if (!e.Contains("new_servicetype"))
                                throw new InvalidPluginExecutionException(GetResource("new_srv_handing.selecttype", "请选择服务类型"));
                            //特批单服务类型
                            int new_servicetype = e.GetAttributeValue<OptionSetValue>("new_servicetype").Value;
                            if (!e.Contains("new_changetype"))
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.Nochangetype", "服务类型转换，请维护变更类型"));
                            if (e.GetAttributeValue<OptionSetValue>("new_changetype").Value == 3 && !e.Contains("new_returntype"))
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.Noreturntype", "服务类型转换退货单，请维护退货类型"));
                            if (e.GetAttributeValue<OptionSetValue>("new_changetype").Value == 3 && !e.Contains("new_refund_way"))
                                throw new InvalidPluginExecutionException(GetResource("TranTypeAction.new_refund_way", "服务类型转换退货单，请选择退货方式"));
                            if (e.GetAttributeValue<OptionSetValue>("new_changetype").Value == 2 && !e.Contains("new_exchange_way"))
                                throw new InvalidPluginExecutionException(GetResource("TranTypeAction.new_exchange_way", "请选择换货方式"));
                            //变更服务类型
                            int new_changetype = e.GetAttributeValue<OptionSetValue>("new_changetype").Value;
                            var workorderentity = OrganizationService.Retrieve("new_srv_workorder", new Guid(orderId), new ColumnSet("new_station_id", "new_dealstatus", "new_isfenqi", "new_warranty", "new_salechannel", "new_begintime", "new_type", "new_shipordernumber", "new_isoutletpickup", "new_occurrency", "new_ifinvoice", "new_invoice_num"));
                            if (workorderentity == null)
                                throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                            var new_ordertype = workorderentity.Contains("new_type") ? workorderentity.GetAttributeValue<OptionSetValue>("new_type").Value : 0;

                            if (new_ordertype != new_servicetype)
                                throw new InvalidPluginExecutionException(GetResource("TranTypeAction.notequalType", "特批申请服务类型与关联工单服务类型不一致，提交失败"));
                            if (new_servicetype == new_changetype)
                                throw new InvalidPluginExecutionException(GetResource("TranTypeAction.equalType", "转换服务类型与工单服务类型一致，提交失败"));

                            Command<TranTypeCommand>().CheckSubmitTranType(workorderentity, orderId, new_changetype);
                            //Entity productlineEntity = 
                            var plEc = Command<TranTypeCommand>().SearchProductline(orderId, e.GetAttributeValue<OptionSetValue>("new_servicetype").Value);
                            foreach (var productlineEntity in plEc.Entities)
                            {
                                if (!productlineEntity.Contains("new_repairrightsrule_id"))
                                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotRepairrightsrule", "受理物品政策为空，无法转换类型"));

                                var repairrightsruleEntity = Command<TranTypeCommand>().SearchRepairrightsrule(productlineEntity.GetAttributeValue<EntityReference>("new_repairrightsrule_id").Id);
                                //三包开始时间
                                string new_policy_starttime = string.Empty;
                                if (productlineEntity.Contains("new_policy_starttime") && productlineEntity.GetAttributeValue<DateTime>("new_policy_starttime") != null)
                                    new_policy_starttime = productlineEntity.GetAttributeValue<DateTime>("new_policy_starttime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");
                                Guid new_repairrightsid = Guid.Empty;
                                string new_repairrightsname = string.Empty;
                                if (productlineEntity.Contains("new_repairrights_id"))
                                {
                                    new_repairrightsid = productlineEntity.GetAttributeValue<EntityReference>("new_repairrights_id").Id;
                                    new_repairrightsname = productlineEntity.GetAttributeValue<EntityReference>("new_repairrights_id").Name;
                                }
                                else
                                {
                                    new_repairrightsid = repairrightsruleEntity.GetAttributeValue<EntityReference>("new_repairrights_id").Id;
                                    new_repairrightsname = repairrightsruleEntity.GetAttributeValue<EntityReference>("new_repairrights_id").Name;
                                }
                                if (!workorderentity.Contains("new_salechannel"))
                                    throw new InvalidProgramException(GetResource("new_srv_workorder.Nosalechannel", "当前工单销售渠道为空"));
                                int new_salechannel = workorderentity.GetAttributeValue<OptionSetValue>("new_salechannel").Value;
                                //查询权益
                                SearchRepairrightsrule searchRepairrightsrule = new SearchRepairrightsrule();
                                var repairrightsEC = searchRepairrightsrule.Searchrepairrights(OrganizationService, new_repairrightsid, new_salechannel);
                                if (repairrightsEC == null || repairrightsEC.Count <= 0)
                                    throw new InvalidPluginExecutionException(string.Format(GetResource("Norepairrights", "根据权益：{0},渠道:{1}查询权益明细为空"), new_repairrightsname, new_salechannel));

                                #region 转换货
                                if (new_changetype == 2)
                                {
                                    var repairrightsruleList = repairrightsEC.Where(x => x.GetAliasAttributeValue<OptionSetValue>("repairrightsline.new_servicetype").Value == new_changetype).ToList();
                                    if (repairrightsruleList == null || repairrightsruleList.Count <= 0)
                                        throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.RepairrightsruleNotjiance", "当前受理物品关联权益不支持转此服务类型，无法转换类型"));
                                }
                                #endregion 转换货

                                #region 转退货

                                else if (new_changetype == 3)
                                {
                                    //变更退货类型
                                    int new_returntype = e.GetAttributeValue<OptionSetValue>("new_returntype").Value;
                                    //若 退货类型 = 【维修特批退货】，选择此类型不校验三包政策退货权益 edit by p-songyongxiang 2024-12-23
                                    if (new_returntype != (int)returntypeEnum.Specialapproval_return)
                                    {
                                        var repairrightsruleList = repairrightsEC.Where(x => x.GetAliasAttributeValue<OptionSetValue>("repairrightsline.new_servicetype").Value == new_changetype && x.GetAliasAttributeValue<OptionSetValue>("repairrightsline.new_returntype").Value == new_returntype).ToList();
                                        if (repairrightsruleList == null || repairrightsruleList.Count <= 0)
                                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.RepairrightsruleNotjiance", "当前受理物品关联权益不支持转此服务类型，无法转换类型"));
                                    }

                                }
                                #endregion 转退货
                            }
                        }
                        #endregion 服务类型转换

                        #endregion
                        // 提交时间
                        apply["new_submittime"] = DateTime.UtcNow;
                        break;

                    default: break;
                }

                #region 提交：5013-不予保修
                if (type == 2 && new_type == 8)
                {
                    if (e.Contains("new_sn"))
                    {
                        QueryExpression workorder = new QueryExpression("new_srv_workorder");
                        workorder.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        workorder.Criteria.AddCondition("new_sn", ConditionOperator.Equal, e.GetAttributeValue<string>("new_sn"));
                        //排除已取消或已关单 2025-6-25 by p-zhoulin10
                        workorder.Criteria.AddCondition("new_dealstatus", ConditionOperator.NotIn, 9, 10);
                        workorder.ColumnSet.AddColumns("new_name");
                        EntityCollection collection = OrganizationService.RetrieveMultiple(workorder);
                        if (collection != null && collection.Entities.Count > 0)
                        {
                            throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ErrorCheckType", "工单已创建，无需申请[5013-不予维保]特批"));
                        }
                    }
                }
                #endregion

                #region 判断盘点状态 -- 特批异型号申请通过 add by Hyacinthhuang 2021-12-8
                if (type == 3 && new_type == 5)
                {
                    if (e.Contains("new_workorder_id"))
                    {
                        RekTec.Crm.BizCommon.InventorytaskCommon cmd = new Crm.BizCommon.InventorytaskCommon();
                        cmd.CheckOrderInventoryStatus(OrganizationService, e.GetAttributeValue<EntityReference>("new_workorder_id").Id.ToString());
                    }

                }
                #endregion

                OrganizationService.Update(apply);
                #endregion
                #region 三包起始变更类型提交 2024-7-12
                if (type == 2 && new_type == 9)
                {
                    if (orderId != null)
                    {
                        //查询产品明细表
                        var que = new QueryExpression("new_srv_productline");
                        que.ColumnSet = new ColumnSet("new_policy_type");
                        que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        que.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                        var EC = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(que);
                        if (EC.Entities.Count > 0)
                        {
                            foreach (var item in EC.Entities)
                            {
                                if (item.Attributes.Contains("new_policy_type") && item.GetAttributeValue<OptionSetValue>("new_policy_type").Value != 5)
                                {
                                    throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NoException", "当前工单三包起始时间无异常，不允许创建三包起始时间变更特批单"));
                                }
                                else if (!item.Attributes.Contains("new_policy_type"))
                                {
                                    throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NotExist", "当前工单三包起始时间不存在，不允许创建三包起始时间变更特批单"));
                                }
                            }
                        }
                        else
                        {
                            throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NotExist", "当前工单三包起始时间不存在，不允许创建三包起始时间变更特批单"));
                        }
                    }
                }
                #endregion

                #region  三包起始变更类型特批通过 2024-7-12
                if (type == 3 && new_type == 9)
                {
                    if (orderId != null)
                    {
                        var workorderentity = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_srv_workorder", new Guid(orderId), new ColumnSet("new_salechannel", "new_userprofile_id", "new_goodsfiles_id",
                            "new_policy_type", "new_repairrights_id", "new_policy_starttime"));
                        int new_salechannel = workorderentity.GetAttributeValue<OptionSetValue>("new_salechannel").Value;
                        Guid new_repairrightsid = Guid.Empty;//三包权益id
                                                             //查询权益
                        SearchRepairrightsrule searchRepairrightsrule = new SearchRepairrightsrule();
                        int policyType = e.GetAttributeValue<OptionSetValue>("new_policy_type").Value;//0为发票时间
                        Entity userprofile = null;//产品档案
                        Entity productline = null;//受理物品
                        string new_packetsstarttime = e.GetAttributeValue<DateTime>("new_packetsstarttime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");//三包开始时间
                        string activationTime = string.Empty;//激活时间
                        string factoryTime = string.Empty;//出厂时间
                        string invoiceTime = e.GetAttributeValue<DateTime>("new_packetsstarttime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");//发票时间

                        if (orderId != null)
                        {
                            //查询产品明细表
                            var que = new QueryExpression("new_srv_productline");
                            que.ColumnSet = new ColumnSet("new_policy_type", "new_vote_time", "new_invoice_time", "new_policy_starttime",
                                "new_return_stoptime", "new_issuesreturn_stoptime", "new_exchange_stoptime", "new_repair_stoptime", "new_repairrights_id",
                                "new_delivery_time", "new_goodsfiles_id");
                            que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            que.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                            var EC = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(que);
                            if (EC != null && EC.Entities != null && EC.Entities.Count > 0)
                            {
                                foreach (var item in EC.Entities)
                                {
                                    if (item.Attributes.Contains("new_policy_type") && item.GetAttributeValue<OptionSetValue>("new_policy_type").Value != 5)
                                    {
                                        throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NoException", "当前工单三包起始时间无异常，不允许创建三包起始时间变更特批单"));
                                    }
                                    else if (!item.Attributes.Contains("new_policy_type"))
                                    {
                                        throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NotExist", "当前工单三包起始时间不存在，不允许创建三包起始时间变更特批单"));
                                    }
                                    productline = item;
                                    productline["new_policy_starttime"] = Cast.ConToDateTime(new_packetsstarttime);

                                    if (productline.Contains("new_delivery_time") && productline.GetAttributeValue<DateTime>("new_delivery_time") != null)
                                        factoryTime = productline.GetAttributeValue<DateTime>("new_delivery_time").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");

                                    if (1 == policyType)
                                    {
                                        productline["new_invoice_time"] = Cast.ConToDateTime(new_packetsstarttime);
                                        productline["new_policy_type"] = new OptionSetValue(3);
                                    }
                                    if (productline.Contains("new_goodsfiles_id") && productline.GetValue<Guid>("new_goodsfiles_id") != null)
                                    {
                                        var new_oldgoodsfiles = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_goodsfiles", productline.GetValue<Guid>("new_goodsfiles_id"), new ColumnSet("new_projectcode", "new_commoditycode", "new_sku", "new_brand_id", "new_new_category1_id", "new_category2_id", "new_category3_id", "new_model3_id", "new_model2_id", "new_model1_id", "new_isserialnum", "new_commodityname", "new_acceptancemode", "new_replacefee"));
                                        UpdatePacketsTime(new_salechannel, null, invoiceTime, activationTime, factoryTime, new_oldgoodsfiles, null, productline);
                                    }
                                }
                            }
                            //服务单更新三包信息
                            workorderentity["new_policy_starttime"] = Cast.ConToDateTime(new_packetsstarttime);
                            if (workorderentity.Attributes.Contains("new_policy_type") && workorderentity.GetAttributeValue<OptionSetValue>("new_policy_type").Value != 5)
                            {
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NoException", "当前工单三包起始时间无异常，不允许创建三包起始时间变更特批单"));
                            }
                            else if (!workorderentity.Attributes.Contains("new_policy_type"))
                            {
                                throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit.NotExist", "当前工单三包起始时间不存在，不允许创建三包起始时间变更特批单"));
                            }
                            if (1 == policyType)
                            {
                                workorderentity["new_invoice_time"] = Cast.ConToDateTime(new_packetsstarttime);
                                workorderentity["new_policy_type"] = new OptionSetValue(3);
                            }
                            if (workorderentity.Contains("new_goodsfiles_id") && workorderentity.GetValue<Guid>("new_goodsfiles_id") != null)
                            {
                                var new_oldgoodsfiles = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_goodsfiles", workorderentity.GetValue<Guid>("new_goodsfiles_id"), new ColumnSet("new_projectcode", "new_commoditycode", "new_sku", "new_brand_id", "new_new_category1_id", "new_category2_id", "new_category3_id", "new_model3_id", "new_model2_id", "new_model1_id", "new_isserialnum", "new_commodityname", "new_acceptancemode", "new_replacefee"));
                                UpdatePacketsTime(new_salechannel, workorderentity, invoiceTime, activationTime, factoryTime, new_oldgoodsfiles, null, null);
                            }
                        }

                        if (orderId != null)
                        {
                            //查询产品档案表
                            if (workorderentity.Contains("new_userprofile_id") && workorderentity.GetValue<Guid>("new_userprofile_id") != null)
                            {
                                userprofile = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_srv_userprofile", workorderentity.GetValue<Guid>("new_userprofile_id"), new ColumnSet("new_invoicetime",
                                    "new_packetsstarttime", "new_policy_type", "new_casttime", "new_returnendtime", "new_issuesreturn_stoptime",
                                    "new_exchangeendtime", "new_maintenanceendtime", "new_repairrights_id", "new_activate_date", "new_factorytime", "new_goodsfiles_id"));

                                userprofile["new_packetsstarttime"] = Cast.ConToDateTime(new_packetsstarttime);
                                if (userprofile.Contains("new_activate_date") && userprofile.GetAttributeValue<DateTime>("new_activate_date") != null)
                                    activationTime = userprofile.GetAttributeValue<DateTime>("new_activate_date").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");
                                if (userprofile.Contains("new_factorytime") && userprofile.GetAttributeValue<DateTime>("new_factorytime") != null)
                                    factoryTime = userprofile.GetAttributeValue<DateTime>("new_factorytime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd");

                                if (1 == policyType)
                                {
                                    userprofile["new_invoicetime"] = Cast.ConToDateTime(new_packetsstarttime);
                                    userprofile["new_policy_type"] = new OptionSetValue(3);
                                }

                                if (userprofile.Contains("new_goodsfiles_id") && userprofile.GetValue<Guid>("new_goodsfiles_id") != null)
                                {
                                    var new_oldgoodsfiles = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_goodsfiles", userprofile.GetValue<Guid>("new_goodsfiles_id"), new ColumnSet("new_projectcode", "new_commoditycode", "new_sku", "new_brand_id", "new_new_category1_id", "new_category2_id", "new_category3_id", "new_model3_id", "new_model2_id", "new_model1_id", "new_isserialnum", "new_commodityname", "new_acceptancemode", "new_replacefee"));
                                    UpdatePacketsTime(new_salechannel, null, invoiceTime, activationTime, factoryTime, new_oldgoodsfiles, userprofile, null);
                                }
                            }

                        }
                    }
                }
                #endregion

                #region  电池盖保内特批通过 add by panyizhang 2024-5-14
                if (type == 3 && new_type == 7)
                {
                    string imei = null, sn = null;
                    if (e.TryGetAttributeValue<EntityReference>("new_workorder_id", out _))
                    {
                        //判断物料是否均为电池盖特批
                        bool isAllBatteryCover = false;
                        //查询更换件明细
                        var que = new QueryExpression("new_srv_partline");
                        que.ColumnSet = new ColumnSet("new_materialcategory2_id");
                        que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        que.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                        var EC = OrganizationService.RetrieveMultiple(que);
                        if (EC?.Entities?.Count > 0)
                        {
                            isAllBatteryCover = true;
                            foreach (var item in EC.Entities)
                            {
                                var query = new QueryExpression("new_materialcategory2");
                                query.Criteria.AddCondition("new_code", ConditionOperator.Equal, 4);
                                query.Criteria.AddCondition("new_materialcategory2id", ConditionOperator.Equal, item.GetValue<Guid>("new_materialcategory2_id"));
                                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                var result = OrganizationService.RetrieveMultiple(query);
                                if (result?.Entities?.Count > 0)
                                {
                                    //更换件明细保内
                                    item["new_inrepairs"] = new OptionSetValue(1);
                                    //item.SetPropertyValue("new_inrepairs", new OptionSetValue(1));
                                    OrganizationService.Update(item);
                                }
                                else
                                {
                                    isAllBatteryCover = false;
                                }
                            }
                        }
                        if (isAllBatteryCover)
                        {
                            //服务单保内
                            var entity = new Entity("new_srv_workorder");
                            entity.Id = new Guid(orderId);
                            entity["new_warranty"] = new OptionSetValue(1);
                            OrganizationService.Update(entity);
                        }
                    }
                    //增加当特批申请单中imei或sn任一不为空时检验 added by Shengchang Pei 2024-9-6 (batteryCover_EH)
                    else if (e.TryGetAttributeValue<string>("new_imei", out imei) || e.TryGetAttributeValue<string>("new_sn", out sn))
                    {
                        //查询满足条件的工单
                        var queryWorkOrder = new QueryExpression("new_srv_workorder");
                        queryWorkOrder.ColumnSet = new ColumnSet("new_srv_workorderid");
                        var conditionGroup = new FilterExpression(LogicalOperator.Or);
                        if (imei != null)
                        {
                            conditionGroup.AddCondition("new_imei", ConditionOperator.Equal, imei);
                        }
                        if (sn != null)
                        {
                            conditionGroup.AddCondition("new_sn", ConditionOperator.Equal, sn);
                        }
                        if (conditionGroup.Conditions.Count > 0)
                        {
                            queryWorkOrder.Criteria.AddFilter(conditionGroup);
                        }
                        queryWorkOrder.Criteria.AddCondition("new_dealstatus", ConditionOperator.NotIn, 11, 8, 9, 13, 14);

                        var workOrderResult = OrganizationService.RetrieveMultipleWithBypassPlugin(queryWorkOrder);
                        if (workOrderResult?.Entities?.Count > 0)
                        {
                            var firstWorkOrder = workOrderResult.Entities.First();

                            //查询更换件明细
                            var query = new QueryExpression("new_srv_partline");
                            query.ColumnSet = new ColumnSet("new_materialcategory2_id", "new_inrepairs");
                            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, firstWorkOrder.Id);

                            //关联物料子类别
                            var materialLink = query.AddLink("new_materialcategory2", "new_materialcategory2_id", "new_materialcategory2id");
                            materialLink.Columns = new ColumnSet("new_code");
                            materialLink.EntityAlias = "material";

                            var partLinesWithMaterials = OrganizationService.RetrieveMultipleWithBypassPlugin(query);

                            //取出所有子物料类别为电池盖(new_code = 4)的更换件明细
                            var batteryCoverParts = partLinesWithMaterials?.Entities
                                                    .Where(p => !string.IsNullOrEmpty(p.GetAliasAttributeValue<string>("material.new_code")) &&
                                                        p.GetAliasAttributeValue<string>("material.new_code") == "4")
                                                            .ToList();

                            if (batteryCoverParts?.Count > 0)
                            {
                                //更新电池盖类更换件明细为保内
                                foreach (var part in batteryCoverParts)
                                {
                                    part["new_inrepairs"] = new OptionSetValue(1);
                                    OrganizationService.Update(part);
                                }
                                //若只有电池盖
                                if (batteryCoverParts.Count == partLinesWithMaterials?.Entities?.Count)
                                {
                                    // 更新工单为保内
                                    var workOrderToUpdate = new Entity("new_srv_workorder", firstWorkOrder.Id);
                                    workOrderToUpdate["new_warranty"] = new OptionSetValue(1);
                                    OrganizationService.Update(workOrderToUpdate);

                                    // 将工单号回写到特批单关联单号中
                                    var specialApplyToUpdate = new Entity("new_srv_specialapply", e.Id);
                                    specialApplyToUpdate["new_workorder_id"] = new EntityReference("new_srv_workorder", firstWorkOrder.Id);
                                    OrganizationService.Update(specialApplyToUpdate);
                                }
                            }
                        }
                    }
                }
                #endregion
                #region 签核同意后动作
                Log.InfoMsg($"【type】__【{type}】__【new_approvalstatus】__【{new_approvalstatus}】__【特批单号】__【{e.GetAttributeValue<string>("new_name")}】");
                if (type == 3 && order != null)
                {
                    int new_dealstatus = order.Contains("new_dealstatus") ? order.GetAttributeValue<OptionSetValue>("new_dealstatus").Value : 0;
                    if (new int[] { 8, 9, 10, 11, 12, 13 }.Contains(new_dealstatus))
                    {
                        throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.ApproveLimit", "特批同意失败，工单已服务完成！"));
                    }
                    SpecialApproveAction(e);
                }
                #endregion
                #region 保外特批通过更新服务单建议应付手工费和实际人工费
                if (type == 3 && e.Contains("new_type") && e.GetAttributeValue<OptionSetValue>("new_type").Value == 11)
                {
                    var workOrderToUpdate = new Entity("new_srv_workorder", new Guid(orderId));
                    workOrderToUpdate["new_reduce_labor_cost"] = e.GetAttributeValue<decimal>("new_deduction_amount");
                    decimal manualFee = order.GetAttributeValue<decimal>("new_manualfee");
                    decimal deductionAmount = e.GetAttributeValue<decimal>("new_deduction_amount");
                    workOrderToUpdate["new_pay_manualfee"] = manualFee - deductionAmount;
                    workOrderToUpdate["new_actualpay_manualfee"] = manualFee - deductionAmount;
                    OrganizationService.Update(workOrderToUpdate);
                }
                #endregion
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("【SpecialOrderSubmit】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 三包时间计算
        /// </summary>
        /// <param name="oldgoodsfiles">商品档案</param>
        public void UpdatePacketsTime(int new_salechannel, Entity workorder, string invoiceTime, string activationTime,
            string factoryTime, Entity oldgoodsfiles, Entity userprofile, Entity productline)
        {
            string new_returnendtime = string.Empty;//无理由退货截止日期
            string issuesreturn_stoptime = string.Empty;//质量问题退货截止日期
            string new_exchangeendtime = string.Empty;//换货截止日期
            string new_maintenanceendtime = string.Empty;//维修截止日期
            Guid new_repairrightsid = Guid.Empty;//三包权益id
            int days = 0;
            SearchRepairrightsrule searchRepairrightsrule = new SearchRepairrightsrule();//查询权益

            if (userprofile != null && userprofile.Contains("new_repairrights_id"))
            {
                new_repairrightsid = userprofile.GetAttributeValue<EntityReference>("new_repairrights_id").Id;
            }
            else if (productline != null && productline.Contains("new_repairrights_id"))
            {
                new_repairrightsid = productline.GetAttributeValue<EntityReference>("new_repairrights_id").Id;
            }
            else if (workorder != null && workorder.Contains("new_repairrights_id"))
            {
                new_repairrightsid = workorder.GetAttributeValue<EntityReference>("new_repairrights_id").Id;
            }
            if (new_repairrightsid == null || "".Equals(new_repairrightsid))
            {
                var EC = searchRepairrightsrule.SearchData(OrganizationServiceDisableMultLang, OrganizationServiceAdmin, UserId, oldgoodsfiles, "", new_salechannel, Cache, LangCode, Log);//查询设备三包数据
                if (EC != null && EC.Contains("new_newrepairrights_id"))
                {
                    new_repairrightsid = EC.GetAttributeValue<EntityReference>("new_newrepairrights_id").Id;
                }
            }
            if (new_repairrightsid != null)
            {
                var repairrightsEC = searchRepairrightsrule.Searchrepairrights(OrganizationService, new_repairrightsid, new_salechannel);
                #region  非自营三包时间计算 2024-7-12

                if (!string.IsNullOrEmpty(invoiceTime))
                {
                    //维修、检测
                    var packetsstarttime = Cast.ConToDateTime(invoiceTime);
                    //维修
                    if (string.IsNullOrWhiteSpace(new_maintenanceendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 1, 0);
                        if (days != 0)
                        {
                            new_maintenanceendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                        }
                    }
                    //换货
                    if (string.IsNullOrWhiteSpace(new_exchangeendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 2, 0);
                        if (days != 0)
                            new_exchangeendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                    }
                }
                else if (!string.IsNullOrEmpty(activationTime))
                {
                    var packetsstarttime = Cast.ConToDateTime(activationTime);
                    //维修
                    if (string.IsNullOrWhiteSpace(new_maintenanceendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 1, 0);
                        if (days != 0)
                        {
                            new_maintenanceendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                        }
                    }
                    //换货
                    if (string.IsNullOrWhiteSpace(new_exchangeendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 2, 0);
                        if (days != 0)
                            new_exchangeendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                    }

                }
                else if (!string.IsNullOrEmpty(factoryTime))
                {
                    var packetsstarttime = Cast.ConToDateTime(factoryTime).AddDays(90);
                    //维修
                    if (string.IsNullOrWhiteSpace(new_maintenanceendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 1, 0);
                        if (days != 0)
                        {
                            new_maintenanceendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                        }
                    }
                    //换货
                    if (string.IsNullOrWhiteSpace(new_exchangeendtime))
                    {
                        days = Command<CreateWorkorderCommand>().SearchrepairrightsDay(repairrightsEC, 2, 0);
                        if (days != 0)
                            new_exchangeendtime = packetsstarttime.AddDays(days).ToString("yyyy-MM-dd");
                    }

                }
                #endregion
                if (userprofile != null)
                {
                    /*if (string.IsNullOrWhiteSpace(new_returnendtime) )
                        userprofile["new_returnendtime"] = Cast.ConToDateTime(new_returnendtime);//无理由退货截止日期
                    if (string.IsNullOrWhiteSpace(issuesreturn_stoptime) )
                        userprofile["new_issuesreturn_stoptime"] = Cast.ConToDateTime(issuesreturn_stoptime);//质量问题退货截止日期*/
                    if (!string.IsNullOrWhiteSpace(new_exchangeendtime))
                        userprofile["new_exchangeendtime"] = Cast.ConToDateTime(new_exchangeendtime);//换货截止日期
                    if (!string.IsNullOrWhiteSpace(new_maintenanceendtime))
                        userprofile["new_maintenanceendtime"] = Cast.ConToDateTime(new_maintenanceendtime);//维修截止日期
                    OrganizationServiceAdmin.Update(userprofile);
                }
                if (productline != null)
                {
                    /*if (string.IsNullOrWhiteSpace(new_returnendtime) )
                        productline["new_return_stoptime"] = Cast.ConToDateTime(new_returnendtime);//无理由退货截止日期
                    if (string.IsNullOrWhiteSpace(issuesreturn_stoptime))
                        productline["new_issuesreturn_stoptime"] = Cast.ConToDateTime(issuesreturn_stoptime);//质量问题退货截止日期*/
                    if (!string.IsNullOrWhiteSpace(new_exchangeendtime))
                        productline["new_exchange_stoptime"] = Cast.ConToDateTime(new_exchangeendtime);//换货截止日期
                    if (!string.IsNullOrWhiteSpace(new_maintenanceendtime))
                        productline["new_repair_stoptime"] = Cast.ConToDateTime(new_maintenanceendtime);//维修截止日期
                    OrganizationServiceAdmin.Update(productline);
                }
                if (workorder != null)
                {
                    /*if (string.IsNullOrWhiteSpace(new_returnendtime) )
                        workorder["new_return_stoptime"] = Cast.ConToDateTime(new_returnendtime);//无理由退货截止日期
                    if (string.IsNullOrWhiteSpace(issuesreturn_stoptime) )
                        workorder["new_issuesreturn_stoptime"] = Cast.ConToDateTime(issuesreturn_stoptime);//质量问题退货截止日期*/
                    if (!string.IsNullOrWhiteSpace(new_exchangeendtime))
                        workorder["new_exchange_stoptime"] = Cast.ConToDateTime(new_exchangeendtime);//换货截止日期
                    if (!string.IsNullOrWhiteSpace(new_maintenanceendtime))
                        workorder["new_repair_stoptime"] = Cast.ConToDateTime(new_maintenanceendtime);//维修截止日期
                    OrganizationServiceAdmin.Update(workorder);
                }

            }
        }


        /// <summary>
        /// 特批单审核通过执行
        /// </summary>
        /// <param name="specialapply">特批单</param>
        public void SpecialApproveAction(Entity specialapply)
        {
            try
            {
                Log.InfoMsg("特批同意开始");
                if (!specialapply.Contains("new_type"))
                {
                    return;
                }

                #region  获取对应工作指示表ID
                Guid msdynWorkorderId = Guid.Empty;   // 关联工作指示单ID
                if (specialapply.Contains("new_msdyn_workorder_id"))
                {
                    msdynWorkorderId = specialapply.GetValue<Guid>("new_msdyn_workorder_id");
                }
                else if (specialapply.Contains("new_workorder_id"))
                {
                    var workOrderEnt = OrganizationService.Retrieve("new_srv_workorder", specialapply.GetValue<Guid>("new_workorder_id"), new ColumnSet("new_msdyn_workorderid"));
                    if (workOrderEnt != null)
                    {
                        msdynWorkorderId = workOrderEnt.GetValue<Guid>("new_msdyn_workorderid");
                    }
                }
                #endregion

                int type = specialapply.GetAttributeValue<OptionSetValue>("new_type").Value;
                Log.InfoMsg($"【{type}】__开始执行特批同意");
                switch (type)
                {
                    case 1:
                        #region 三包换期审核通过
                        //三包换期 保外，有工单，更新状态为保内
                        if (specialapply.Contains("new_servicetype") && specialapply.Contains("new_workorder_id"))
                        {
                            bool skipStockCheck = false;
                            var workOrder = OrganizationService.Retrieve("new_srv_workorder", specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id, new ColumnSet("new_station_id"));
                            if (workOrder.Contains("new_station_id"))
                            {
                                var new_station_id = workOrder.GetAttributeValue<EntityReference>("new_station_id").Id;
                                Entity station = OrganizationServiceDisableMultLang.Retrieve("new_srv_station", new_station_id, new ColumnSet("new_skipstockcheck"));
                                skipStockCheck = station.GetAttributeValue<bool>("new_skipstockcheck");//是否跳过库存校验
                            }
                            var new_servicetype = specialapply.GetAttributeValue<OptionSetValue>("new_servicetype").Value;
                            if (new_servicetype == 1 || new_servicetype == 2 || new_servicetype == 3)
                            {
                                Log.InfoMsg("更新三包超期");
                                var workorderEntity = new Entity("new_srv_workorder");
                                workorderEntity.Id = specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id;
                                //保内
                                workorderEntity["new_warranty"] = new OptionSetValue(1);
                                // 是否特批工单：是
                                workorderEntity["new_isspecialorder"] = true;                                                     //
                                OrganizationService.Update(workorderEntity);

                                #region 同步更新工作指示
                                if (msdynWorkorderId != Guid.Empty)
                                {
                                    var msdynWorkorderEntity = new Entity("msdyn_workorder");
                                    msdynWorkorderEntity.Id = msdynWorkorderId;
                                    //保内
                                    msdynWorkorderEntity["new_warranty"] = new OptionSetValue(1);
                                    // 是否特批工单：是
                                    msdynWorkorderEntity["new_isspecialorder"] = true;                                                     //
                                    OrganizationService.Update(msdynWorkorderEntity);
                                }
                                #endregion

                                Log.InfoMsg("工单更新结束");
                                #region 把已关联的更换件明细更新保内 add by Hyacinthhuang 2022-1-5
                                QueryExpression query = new QueryExpression("new_srv_partline");
                                query.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id);
                                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                query.ColumnSet = new ColumnSet("new_srv_partlineid", "new_productnew_id");
                                EntityCollection ec = OrganizationServiceAdmin.RetrieveMultiple(query);
                                if (ec != null && ec.Entities.Count > 0)
                                {
                                    foreach (var item in ec.Entities)
                                    {
                                        decimal resultPrice = 0M;
                                        Entity newline = new Entity("new_srv_partline");
                                        newline.Id = item.Id;
                                        newline["new_inrepairs"] = new OptionSetValue(1);//保内

                                        if (workOrder.Contains("new_station_id") && !skipStockCheck && item.Contains("new_productnew_id"))
                                        {
                                            //通过服务网点找配件价格模板，通过价格模板和物料找配件价格，赋值
                                            resultPrice = Command<WorkOrderCommand>().SearchProductPriceByStationId(workOrder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString(), 1, item.GetAttributeValue<EntityReference>("new_productnew_id").Id.ToString());
                                            newline["new_price"] = resultPrice;
                                        }
                                        if (msdynWorkorderId != Guid.Empty)
                                        {
                                            newline["new_srv_msdynworkorder_id"] = new EntityReference("msdyn_workorder", msdynWorkorderId);
                                        }
                                        OrganizationServiceAdmin.Update(newline);
                                    }
                                }
                                #endregion
                                Log.InfoMsg("三包更新结束");
                            }
                        }
                        #endregion
                        break;
                    case 4:
                        #region MMI特批
                        // mmi特批更新服务单白名单
                        if (specialapply.Contains("new_workorder_id"))
                        {
                            Log.InfoMsg("MMi特批开始。。。");
                            var updateEntity = new Entity("new_srv_workorder");
                            updateEntity.Id = specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id;
                            // 是否特批：是
                            updateEntity["new_isspecialorder"] = true;
                            updateEntity["new_mmiwhitelist"] = true; //白名单
                            OrganizationService.Update(updateEntity);

                            #region 同步更新工作指示
                            if (msdynWorkorderId != Guid.Empty)
                            {
                                var msdynWorkorderEntity = new Entity("msdyn_workorder");
                                msdynWorkorderEntity.Id = msdynWorkorderId;
                                //保内
                                msdynWorkorderEntity["new_isspecialorder"] = true;
                                msdynWorkorderEntity["new_mmiwhitelist"] = true; //白名单                                                   //
                                OrganizationService.Update(msdynWorkorderEntity);
                            }
                            #endregion
                        }

                        #endregion
                        break;
                    case 5:
                        #region 异型换机审核通过
                        if (!specialapply.Contains("new_workorder_id"))
                            throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.Workorderid_ISEmpty", "特批申请服务单为空"));
                        Log.InfoMsg("异型号换货特批开始。。");
                        // 查询工单：网点，服务商，负责人(分派给配件更换明细)
                        Entity order = OrganizationService.Retrieve("new_srv_workorder", specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id,
                            new ColumnSet("new_station_id", "new_servicestation_id", "ownerid",
                                            "new_sn", "new_imei", "new_goodsfiles_id",
                                            "new_type", "new_warranty", "new_exchange_way",
                                            "new_dealstatus", "new_partcost", "new_pay_materialcost", "new_materialcost"));

                        if (order == null)
                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                        // 保内、保外
                        int new_warranty = order.Contains("new_warranty") ? order.GetAttributeValue<OptionSetValue>("new_warranty").Value : 0;
                        // 工单类型
                        int new_type = order.Contains("new_type") ? order.GetAttributeValue<OptionSetValue>("new_type").Value : 0;
                        // 换货方式
                        int new_exchange_way = order.Contains("new_exchange_way") ? order.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;

                        int new_dealstatus = order.Contains("new_dealstatus") ? order.GetAttributeValue<OptionSetValue>("new_dealstatus").Value : 0;
                        if (new int[] { 8, 9, 10 }.Contains(new_dealstatus))
                            throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.OrderStatus", "当前关联工单状态不允许特批"));
                        #region 校验明细数量
                        QueryExpression expression = new QueryExpression("new_srv_specialapplyline");
                        expression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        expression.Criteria.AddCondition("new_specialapply_id", ConditionOperator.Equal, specialapply.Id);
                        //expression.ColumnSet.AllColumns = true;
                        expression.ColumnSet = new ColumnSet("new_warechangeway", "new_product_id", "new_warehousestock");
                        EntityCollection collection = OrganizationService.RetrieveMultiple(expression);
                        if (collection == null || collection.Entities.Count <= 0)
                            throw new InvalidPluginExecutionException(GetResource("new_srv_specialapply.DetailsIsEmpty", "特批类型【异形换机】必须要有特批申请明细"));
                        #endregion

                        Entity detail = collection[0];// 明细只能有一条

                        #region 创建配件更换明细
                        Entity partLine = new Entity("new_srv_partline");
                        partLine["new_status"] = new OptionSetValue(1);// 未领料
                        partLine["new_isuser"] = true;//已使用
                        partLine["new_qty"] = 1M;//数量
                        partLine["new_origin"] = new OptionSetValue(2);//来源：异型换机
                        partLine["new_srv_workorder_id"] = new EntityReference("new_srv_workorder", order.Id);//服务单
                        if (msdynWorkorderId != Guid.Empty)
                        {
                            partLine["new_srv_msdynworkorder_id"] = new EntityReference("msdyn_workorder", msdynWorkorderId);//工作指示
                        }
                        partLine["new_oldsn"] = order.GetAttributeValue<string>("new_sn");// 旧sn
                        partLine["new_oldimei"] = order.GetAttributeValue<string>("new_imei");// 旧ime
                        int wareChangeWay = 0;
                        if (detail.Contains("new_warechangeway"))
                        {
                            // 以换代修发货方式
                            wareChangeWay = detail.GetAttributeValue<OptionSetValue>("new_warechangeway").Value;
                            partLine["new_exchangeway"] = detail.GetAttributeValue<OptionSetValue>("new_warechangeway");
                        }
                        // 更换件保内保外跟工单保持一致
                        if (new_warranty > 0)
                        {
                            partLine["new_inrepairs"] = new OptionSetValue(new_warranty);
                        }
                        if (order.Contains("new_goodsfiles_id"))
                        {
                            // 旧物料取服务单商品档案物料
                            string oldproductid = GetGoodsProduct(order.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id.ToString());
                            if (!string.IsNullOrWhiteSpace(oldproductid))
                            {
                                partLine["new_product_id"] = new EntityReference("product", new Guid(oldproductid));
                                partLine["new_mainproduct_id"] = new EntityReference("product", new Guid(oldproductid));
                            }
                            partLine["new_oldgoodsfiles_id"] = new EntityReference("new_goodsfiles", order.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id);
                        }
                        // 新物料
                        Entity product = null;
                        string productId = null;
                        string category2Id = string.Empty;
                        string category1Id = string.Empty;//物料类别
                        string goods_id = string.Empty;//商品档案id
                        List<decimal> list = new List<decimal>();
                        bool isEnough = false;
                        // 新物料取特批明细的物料
                        // 查询价格，先用工单网点匹配价格模板，后续用物料匹配价格查询零售价
                        if (detail.Contains("new_product_id"))
                        {
                            partLine["new_productnew_id"] = detail.GetAttributeValue<EntityReference>("new_product_id");
                            productId = detail.GetAttributeValue<EntityReference>("new_product_id").Id.ToString();
                            // 物料
                            product = OrganizationService.Retrieve("product", new Guid(productId), new ColumnSet("new_materialcategory2_id", "new_materialcategory1_id", "new_goods_id", "productnumber", "new_isserial"));
                            if (product != null)
                            {
                                if (product.Contains("new_materialcategory2_id"))
                                {
                                    partLine["new_materialcategory2_id"] = product.GetAttributeValue<EntityReference>("new_materialcategory2_id");
                                    category2Id = product.GetAttributeValue<EntityReference>("new_materialcategory2_id").Id.ToString();
                                }
                                if (product.Contains("new_materialcategory1_id"))
                                {
                                    partLine["new_materialcategory1_id"] = product.GetAttributeValue<EntityReference>("new_materialcategory1_id");
                                    category1Id = product.GetAttributeValue<EntityReference>("new_materialcategory1_id").Id.ToString();
                                }
                                //新商品档案
                                if (product.Contains("new_goods_id"))
                                {
                                    partLine["new_goodsfiles_id"] = new EntityReference("new_goodsfiles", product.GetAttributeValue<EntityReference>("new_goods_id").Id);
                                    goods_id = product.GetAttributeValue<EntityReference>("new_goods_id").Id.ToString();
                                }
                                // 是否串号管理
                                if (product.Contains("new_isserial"))
                                {
                                    partLine["new_isserial"] = product.GetAttributeValue<bool>("new_isserial");
                                }
                            }
                        }

                        #region 查询价格
                        Entity stock = null;
                        string stationId = "";
                        if (order.Contains("new_station_id"))
                        {
                            stationId = order.GetAttributeValue<EntityReference>("new_station_id").Id.ToString();
                            list = GetProductPrice(stationId, productId);
                            stock = GetGoodStocksite(stationId);
                            if (stock != null)
                            {
                                // 仓库 = 网点良品库
                                partLine["new_stocksiteid"] = new EntityReference("new_srv_stocksite", stock.Id);
                                // 本地仓库才要查询库存
                                if (wareChangeWay == 1)
                                {
                                    #region add by Hyacinthhuang 2021-12-9  根据网点良品库数量查询库存，更新更换件缺料信息
                                    decimal num = GetProductStockNum(stock.Id.ToString(), productId);
                                    if (num == decimal.Zero)
                                    {
                                        partLine["new_lackmaterialnumber"] = new decimal(1);// 缺料数量：1
                                        partLine["new_iflackmaterial"] = true;// 已缺料：是
                                    }
                                    else if (num > 0)
                                    {
                                        isEnough = true;
                                    }
                                    #endregion
                                }
                            }
                        }
                        // 大仓缺料更新缺料状态 add by Hyacinthhuang 2022-4-6
                        if (wareChangeWay == 2 && detail.Contains("new_warehousestock") && detail.GetAttributeValue<OptionSetValue>("new_warehousestock").Value == 2)
                        {
                            partLine["new_lackmaterialnumber"] = new decimal(1);// 缺料数量：1
                            partLine["new_iflackmaterial"] = true;// 已缺料：是
                        }
                        if ((list == null || list.Count <= 0) && order.Contains("new_servicestation_id"))
                        {
                            list = GetProductPrice(order.GetAttributeValue<EntityReference>("new_servicestation_id").Id.ToString(), productId);
                        }
                        decimal price = decimal.Zero;
                        if (list != null && list.Count > 0)
                        {
                            // 价格 工单保内取保内价，保外取零售价
                            price = new_warranty == 1 ? list[0] : list[2];
                            partLine["new_price"] = price;
                        }
                        #endregion
                        //维修以换代修大仓发货、换货大仓换货场景不赋值更换件明细的【配件库存】字段
                        if (!((new_type == 1 && wareChangeWay == 2) || (new_type == 2 && new_exchange_way == 2)))
                        {
                            partLine = Command<new_srv_partlinebll>().CombinationParLine(partLine, stock.Id.ToString(), detail.Contains("new_product_id") ? detail.GetAttributeValue<EntityReference>("new_product_id").Id.ToString() : string.Empty);
                        }
                        Guid partLineId = OrganizationService.Create(partLine);

                        #region 更新工单是特批 add by Hyacinthhuang 2022-1-2
                        Entity neworder = new Entity("new_srv_workorder");
                        neworder.Id = order.Id;
                        neworder["new_isspecialorder"] = true;
                        neworder["new_isdifferent"] = true;
                        #region 计算工单费用 add by Hyacinthhuang 2022-3-22
                        EntityCollection ActivityEc = Command<WorkOrderCommand>().JudgeIsUseSerByOrderId(order.Id.ToString(), category2Id, true);
                        if (ActivityEc != null && ActivityEc.Entities.Count > 0 && ActivityEc[0].Contains("new_discount"))
                        {
                            price = price * ActivityEc[0].GetAttributeValue<decimal>("new_discount") / 100;
                        }
                        // 保内
                        if (new_warranty == 1)
                        {
                            neworder["new_partcost"] = price + order.GetAttributeValue<decimal>("new_partcost");
                        }
                        // 保外
                        else if (new_warranty == 2)
                        {
                            neworder["new_pay_materialcost"] = price + order.GetAttributeValue<decimal>("new_pay_materialcost");
                            neworder["new_actualpay_materialcost"] = price + order.GetAttributeValue<decimal>("new_pay_materialcost");
                            neworder["new_materialcost"] = price + order.GetAttributeValue<decimal>("new_materialcost");
                        }
                        OrganizationService.Update(neworder);
                        #endregion

                        #region 更新工作指示
                        if (msdynWorkorderId != Guid.Empty)
                        {
                            var msdynWorkorderEntity = new Entity("msdyn_workorder");
                            msdynWorkorderEntity.Id = msdynWorkorderId;
                            msdynWorkorderEntity["new_isspecialorder"] = true;
                            msdynWorkorderEntity["new_isdifferent"] = true;
                            // 保内 
                            if (new_warranty == 1)
                            {
                                msdynWorkorderEntity["new_partcost"] = price + order.GetAttributeValue<decimal>("new_partcost");
                            }
                            // 保外
                            else if (new_warranty == 2)
                            {
                                msdynWorkorderEntity["new_pay_materialcost"] = price + order.GetAttributeValue<decimal>("new_pay_materialcost");
                            }
                            OrganizationService.Update(msdynWorkorderEntity);
                        }
                        #endregion

                        #endregion

                        #region 分派配件更换明细给工单的负责人
                        RekTec.Crm.BizCommon.CommonHelper.Assign(OrganizationService, "new_srv_partline", partLineId, order.GetAttributeValue<EntityReference>("ownerid").Id);
                        #endregion

                        #region 生成对应的处理方法 add by 2021-12-14 Hyacinthhuang
                        int orderType = order.Contains("new_type") ? order.GetAttributeValue<OptionSetValue>("new_type").Value : 0;
                        Command.ProductErroupModel model = new Command.ProductErroupModel();
                        model.new_workorder_id = specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id.ToString();
                        model.productid = productId;
                        model.new_exchangeway = wareChangeWay;
                        //取物料类别，物料子类别，商品档案id edit by p-songyongxiang 2023-12-14
                        model.new_materialcategory1_id = category1Id;
                        model.new_materialcategory2_id = category2Id;
                        model.new_oldgoodesid = goods_id;
                        Command<Bll.new_srv_partlinebll>().CreateOrderApproach(model, orderType, partLineId.ToString() + ",");
                        #endregion

                        #region 大仓 && 库存充足，生成库存事务 add by Hyacinthhuang 2022-1-2
                        if (((new_type == 1 && wareChangeWay == 1) || (new_type == 2 && new_exchange_way == 1)) && isEnough)
                        {
                            Helper.Parttransaction cmd = new Helper.Parttransaction();
                            List<string> sn = new List<string>();
                            this.Command<Parttransaction>().srv_stocksalebuyout(stock, product.GetAttributeValue<string>("productnumber"), "1", stationId, sn, "new_srv_workorder", order.Id.ToString(), Helper.transactiontypeEnum.Inventoryfreeze);
                        }
                        #endregion

                        #endregion
                        #endregion
                        break;
                    case 6:
                        #region 服务类型转换
                        if (!specialapply.Contains("new_workorder_id"))
                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                        Guid orderid = specialapply.GetAttributeValue<EntityReference>("new_workorder_id").Id;
                        var orderentity = OrganizationService.Retrieve("new_srv_workorder", orderid, new ColumnSet("new_type"));
                        if (orderentity == null)
                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));

                        TranTypeModel tranTypeModel = new TranTypeModel();
                        tranTypeModel.entityid = orderid.ToString();
                        tranTypeModel.new_type = orderentity.GetAttributeValue<OptionSetValue>("new_type").Value;
                        tranTypeModel.new_turntype = specialapply.GetAttributeValue<OptionSetValue>("new_changetype").Value;
                        //退货类型
                        if (specialapply.Contains("new_returntype"))
                            tranTypeModel.new_returntype = specialapply.GetAttributeValue<OptionSetValue>("new_returntype").Value;
                        //退货方式
                        if (specialapply.Contains("new_refund_way"))
                            tranTypeModel.new_refund_way = specialapply.GetAttributeValue<OptionSetValue>("new_refund_way").Value;
                        //换货方式
                        if (specialapply.Contains("new_exchange_way"))
                            tranTypeModel.new_exchange_way = specialapply.GetAttributeValue<OptionSetValue>("new_exchange_way").Value;
                        tranTypeModel.new_turntypereason = specialapply.GetAttributeValue<string>("new_submitmemo");
                        if (msdynWorkorderId != Guid.Empty)
                        {
                            tranTypeModel.workorderid = msdynWorkorderId.ToString();
                        }
                        Command<TranTypeCommand>().TranTypeAction(tranTypeModel);
                        #endregion 服务类型转换
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("【SpecialApproveAction】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 查询商品物料 add by Hyacinthhuang 2021-12-3
        /// </summary>
        /// <param name="goodsId">商品档案id</param>
        /// <returns></returns>
        public string GetGoodsProduct(string goodsId)
        {
            if (string.IsNullOrWhiteSpace(goodsId))
                return null;

            try
            {
                QueryExpression query = new QueryExpression("product");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_isgoods", ConditionOperator.Equal, true);
                query.Criteria.AddCondition("new_goods_id", ConditionOperator.Equal, goodsId);
                query.ColumnSet.AddColumns("productid");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    return ec[0].Id.ToString();
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.InfoMsg($"【GetGoodsProduct】错误：" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 查询网点良品库 add by Hyacinthhuang 2021-12-3
        /// </summary>
        /// <param name="stationId">网点id</param>
        /// <returns>良品库id</returns>
        public Entity GetGoodStocksite(string stationId)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return null;

            try
            {
                QueryExpression query = new QueryExpression("new_srv_stocksite");
                query.Criteria.AddCondition("new_station_id", ConditionOperator.Equal, stationId);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                // 仓库类型：良品库
                query.Criteria.AddCondition("new_stocktype", ConditionOperator.Equal, 1);
                query.ColumnSet.AddColumns("new_srv_stocksiteid", "new_stocksitecode");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    return ec.Entities[0];
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("【GetGoodStocksite】错误：" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 查询物料良品库可用数量 add by Hyacinthhuang 2021-12-9
        /// </summary>
        /// <param name="stockId">仓库id</param>
        /// <param name="productId">物料id</param>
        /// <returns></returns>
        public decimal GetProductStockNum(string stockId, string productId)
        {
            if (string.IsNullOrWhiteSpace(stockId) || string.IsNullOrWhiteSpace(productId))
                return decimal.Zero;

            try
            {
                QueryExpression query = new QueryExpression("new_srv_siteinv");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_stocksite_id", ConditionOperator.Equal, stockId);
                query.Criteria.AddCondition("new_product_id", ConditionOperator.Equal, productId);
                query.ColumnSet.AddColumns("new_ablenum");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                    return decimal.Zero;

                Entity e = ec.Entities[0];
                if (e.Contains("new_ablenum"))
                    return e.GetAttributeValue<decimal>("new_ablenum");

                return decimal.Zero;
            }
            catch (Exception ex)
            {
                Log.InfoMsg($"【GetProductStockNum】错误：" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 查询物料价格
        /// </summary>
        /// <param name="stationId">网点id</param>
        /// <param name="productId">物料id</param>
        /// <returns>保内价，保外价，零售价</returns>
        public List<decimal> GetProductPrice(string stationId, string productId)
        {
            if (string.IsNullOrWhiteSpace(stationId))
                return null;

            try
            {
                List<decimal> list = new List<decimal>();
                var station = OrganizationService.Retrieve("new_srv_station", new Guid(stationId), new ColumnSet("new_pricetemplate", "new_srv_stationid"));
                if (station == null)
                {
                    return null;
                }
                if (!station.Contains("new_pricetemplate"))
                {
                    return null;
                }
                var partprice = new QueryExpression("new_srv_partprice");
                partprice.ColumnSet = new ColumnSet("new_insideprice", "new_outsideprice", "new_srv_partpriceid", "new_price");
                partprice.Criteria.AddCondition("new_partpricetemplet_id", ConditionOperator.Equal, station.GetAttributeValue<EntityReference>("new_pricetemplate").Id);
                partprice.Criteria.AddCondition("new_partsid", ConditionOperator.Equal, productId);
                partprice.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var partpriceList = OrganizationService.RetrieveMultiple(partprice);
                if (partpriceList == null || partpriceList.Entities.Count <= 0)
                    return null;

                Entity price = partpriceList.Entities[0];
                if (price.Contains("new_insideprice"))
                {
                    list.Add(price.GetAttributeValue<decimal>("new_insideprice"));
                }
                else
                {
                    list.Add(0M);
                }
                if (price.Contains("new_outsideprice"))
                {
                    list.Add(price.GetAttributeValue<decimal>("new_outsideprice"));
                }
                else
                {
                    list.Add(0M);
                }
                if (price.Contains("new_price"))
                {
                    list.Add(price.GetAttributeValue<decimal>("new_price"));
                }
                else
                {
                    list.Add(0M);
                }
                return list;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("【GetProductPrice】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }


        }

        public bool ProductCustomerViewFiter(string new_srv_specialapplylineId)
        {
            try
            {
                string stationId = GetStationIdFormSpecialApply(new_srv_specialapplylineId);
                if (string.IsNullOrWhiteSpace(stationId) || stationId == null)
                {
                    return false;
                }
                var sys = CrmHelper.GetSystemParameterValue(OrganizationService, "new_srv_specialapplyline.new_product_id.Country");
                if (sys == null || string.IsNullOrEmpty(sys))
                {
                    return false;
                }
                var sysList = sys.Split(';');
                if (sysList.Length == 0)
                {
                    return false;
                }
                bool checkStationCountry = CheckStationCountry(stationId, sysList);
                return checkStationCountry;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[ProductCustomerViewFiter]方法报错：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 检查网点国家是否满足大仓发货
        /// </summary>
        /// <param name="stationId">网点id</param>GetStockCountByStationIdAndProductIdNew
        /// <param name="newIds">国家id</param>
        /// <returns></returns>
        public bool CheckStationCountry(string stationId, string[] newIds)
        {
            bool result = false;
            if (string.IsNullOrWhiteSpace(stationId) || newIds == null || newIds.Length <= 0)
                return result;
            try
            {
                QueryExpression query = new QueryExpression("new_srv_station");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, stationId);
                query.ColumnSet.AddColumns("new_srv_stationid");
                LinkEntity link = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                link.LinkCriteria.AddCondition("new_id", ConditionOperator.In, newIds.ToArray());
                query.LinkEntities.Add(link);
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec != null && ec.Entities.Count > 0)
                {
                    result = true;
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("【CheckStationCountry】错误" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 获取特批申请单的网点id
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public string GetStationIdFormSpecialApply(string entityId)
        {
            try
            {
                if (string.IsNullOrEmpty(entityId) || entityId == null)
                {
                    return string.Empty;
                }
                QueryExpression qExpress = new QueryExpression("new_srv_specialapply");
                qExpress.ColumnSet.AddColumn("new_station_id");
                qExpress.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qExpress.Criteria.AddCondition("new_srv_specialapplyid", ConditionOperator.Equal, entityId);
                var qExpressLists = OrganizationService.RetrieveMultiple(qExpress);
                if (qExpressLists == null || qExpressLists.Entities == null || qExpressLists.Entities.Count == 0)
                {
                    return string.Empty;
                }
                if (!qExpressLists.Entities.FirstOrDefault().Contains("new_station_id"))
                {
                    return string.Empty;
                }
                return qExpressLists.Entities.FirstOrDefault().GetAttributeValue<EntityReference>("new_station_id").Id.ToString();
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[ProductCustomerViewFiter]方法报错：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }



    }

    public static class CommonFun
    {
        public static T GetValue<T>(this Entity ent, string key, int valType = 0)
        {
            object result = null;
            if (ent.Contains(key))
            {
                result = ent[key];
                if (valType == 1 && ent.FormattedValues.Contains(key))
                {
                    result = ent.FormattedValues[key];
                }
                if (result.GetType().Name == "AliasedValue")
                {
                    result = ((AliasedValue)result).Value;
                }

                switch (result.GetType().Name)
                {
                    case "OptionSetValue":
                        result = ((OptionSetValue)result).Value;
                        break;
                    case "EntityReference":
                        result = ((EntityReference)result).Id;
                        break;
                    case "Money":
                        result = ((Money)result).Value;
                        break;
                    default:
                        break;
                }
            }
            return result == null ? default(T) : (T)ChangeType(result, typeof(T));
        }

        public static object ChangeType(object value, Type type)
        {
            if (value == null && type.IsGenericType) return Activator.CreateInstance(type);
            if (value == null) return null;
            if (type == value.GetType()) return value;
            if (type.IsEnum)
            {
                if (value is string)
                    return Enum.Parse(type, (string)value);
                else
                    return Enum.ToObject(type, value);
            }
            if (!type.IsInterface && type.IsGenericType)
            {
                Type innerType = type.GetGenericArguments()[0];
                object innerValue = ChangeType(value, innerType);
                return Activator.CreateInstance(type, new object[] { innerValue });
            }
            if (value is string && type == typeof(Guid)) return Guid.Parse(value.ToString());
            if (value is Guid && type == typeof(string)) return value.ToString();
            if (value is string && type == typeof(Version)) return Version.Parse(value.ToString());
            if (!(value is IConvertible)) return value;
            return Convert.ChangeType(value, type);
        }
    }
}
