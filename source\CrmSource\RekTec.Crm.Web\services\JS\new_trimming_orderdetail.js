﻿//#region 文件描述
/******************************************************************
** 创建人   :   p-songyongxiang
** 创建时间 :   2025-04-30
** 说明     :   XMS高维修整单明细 脚本
******************************************************************/
//#endregion
function form_load() {
    //锁定所有字段
    rtcrm.disableAll(true);
    //Fieldvisibility();
}
function Rematch(itemids) {
    var idlist = JSON.stringify(itemids);
    XM.ActionAsync('new_TrimorderdetailRematch', { trimmingorderdetailids: idlist }).then(res => {
        var returnresult = JSON.parse(res.outputresult);
        if (returnresult.code == "200") {
            rtcrm.alertDialog($t("new_trimming_orderdetail.rematchSuccess", "重新匹配成功"), function () {
                rtcrm.refresh();
            });
        } else {
            rtcrm.openAlertDialog(returnresult.msg);
        }
    }).catch(error => {
        rtcrm.openAlertDialog(error);
        return;
    });
}