﻿using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Newtonsoft.Json;
using RekTec.Crm.Common.Helper;
using RekTec.ServiceSettlement.Function.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace RekTec.ServiceSettlement.Function.Common
{
    public class SettlementAdjustmentLineCommand
    {
        public static ServiceClient OrganizationService = null;
        /// <summary>
        /// 结算单调差逻辑
        /// </summary>
        public static void Run(string myQueueItem, ILogger log)
        {
            List<AdjustModel> amlist = new List<AdjustModel>();
            List<AdjustModelInstall> installlist = new List<AdjustModelInstall>();
            List<AdjustModelHighdimensionalfactory> highdimensionalfactorylist = new List<AdjustModelHighdimensionalfactory>();
            string settlementType = "-1";
            string settlementasyncjoblogid = "";//结算异步作业日志id
            string new_srv_expense_claimid = "";//结算单id
            //组织服务
            try
            {
                #region 参数校验
                try
                {
                    log.LogInformation($"SettlementAdjustmentLine入参：【{myQueueItem}】");
                    if (string.IsNullOrWhiteSpace(myQueueItem))
                    {
                        throw new Exception("myQueueItem cannot be empty");
                    }
                    var inputJson = CommonHelper.ReadJToken(myQueueItem);
                    settlementType = CommonHelper.JSON_SeleteNode(inputJson, "settlementType");
                    if (int.Parse(settlementType) == 9)
                    {
                        //结算单类型 = 安装
                        AdjustModelInstallRequest request = JsonConvert.DeserializeObject<AdjustModelInstallRequest>(myQueueItem);
                        settlementasyncjoblogid = request.new_settlementasyncjoblogId;
                        new_srv_expense_claimid = request.new_srv_expense_claimid;
                        installlist = request.amlist;
                    }
                    else if (int.Parse(settlementType) == 1)
                    {
                        //结算单类型 = B2X，非B2X
                        AdjustModelRequest request = JsonConvert.DeserializeObject<AdjustModelRequest>(myQueueItem);
                        settlementasyncjoblogid = request.new_settlementasyncjoblogId;
                        new_srv_expense_claimid = request.new_srv_expense_claimid;
                        amlist = request.amlist;
                    } else if (int.Parse(settlementType) == 10) 
                    {
                        //结算单类型 = 高维工厂
                        AdjustModelHighdimensionalfactoryRequest request = JsonConvert.DeserializeObject<AdjustModelHighdimensionalfactoryRequest>(myQueueItem);
                        settlementasyncjoblogid = request.new_settlementasyncjoblogId;
                        new_srv_expense_claimid = request.new_srv_expense_claimid;
                        highdimensionalfactorylist = request.amlist;
                    }
                    OrganizationService = CommonHelper.GetService(log);
                }
                catch (Exception ex)
                {
                    log.LogError($"crm_settlementadjustmentline初始化参数异常：【{ex}】,myQueueItem：【{myQueueItem}】");
                    return;
                }
                if (OrganizationService == null)
                {
                    throw new Exception($"OrganizationService cannot be empty");
                }
                #endregion
                #region 服务单赋值
                EntityCollection updatecols = new EntityCollection();
                if (int.Parse(settlementType) == 1)
                {
                    foreach (var am in amlist)
                    {
                        Entity e = new Entity("new_srv_workorder", new Guid(am.new_srv_workorderid));
                        e["new_adjuststatus"] = new OptionSetValue(2);//已调差
                        e["new_specialfeeshare"] = am.new_specialfeeshare;//特殊费用
                        e["new_localbuycostbuysell"] = am.new_localbuyfeebuysell;//换机local buy费（Buysell）
                        e["new_localbuymarkupcostbuysell"] = am.new_localbuyfeemarkupbuysell;//换机local buy markup费（Buysell）
                        e["new_repairfeekpi"] = am.new_repairfeekpi;//维修劳务费（KPI）
                        e["new_detectionapi"] = am.new_detectionapi;//检测费（KPI）
                        e["new_settlementmoney"] = am.new_settlementmoney;//费用合计
                        e["new_changemoney"] = am.new_changemoney;//调差金额
                                                                  //更新工单费用表
                        if (!string.IsNullOrWhiteSpace(am.new_workorder_costtable_id))
                        {
                            Entity costen = new Entity("new_workorder_costtable");
                            costen.Id = new Guid(am.new_workorder_costtable_id);
                            costen["new_withholdingfeerecoilrepair"] = am.new_withholdingfeerecoilrepair;
                            updatecols.Entities.Add(costen);
                        }
                        e["new_adjustretrycount"] = e.Contains("new_adjustretrycount") ? e.GetAttributeValue<int>("new_adjustretrycount") + 1 : 1;//重试次数+1
                        updatecols.Entities.Add(e);
                    }
                }
                else if (int.Parse(settlementType) == 9)
                {
                    foreach (var am in installlist)
                    {
                        if (!string.IsNullOrWhiteSpace(am.new_workorder_costtable_id))
                        {
                            decimal settlementmoneyinstall = am.new_specialfee + am.new_installfeekpi + am.new_fixedmonthfeeinstall;
                            Entity e = new Entity("new_workorder_costtable", new Guid(am.new_workorder_costtable_id));
                            e["new_othermiscellaneouschargesinstall"] = am.new_specialfee;
                            e["new_installfeekpi"] = am.new_installfeekpi;
                            e["new_withholdingfeerecoilinstall"] = am.new_withholdingfeeinstall * -1;
                            e["new_settlementmoneyinstall"] = settlementmoneyinstall;//费用合计
                            e["new_changemoneyinstall"] = settlementmoneyinstall - am.new_withholdingmoneyinstall;//调差金额
                            updatecols.Entities.Add(e);
                        }
                    }
                } else if (int.Parse(settlementType) == 10) 
                {
                    //更新修整单明细
                    foreach (var am in highdimensionalfactorylist) 
                    {
                        var new_changemoney = am.new_settlementmoney - am.new_withholdingmoney;
                        Entity e = new Entity("new_trimming_orderdetail", new Guid(am.new_trimming_orderdetailid));
                        e["new_withholdingfeerecoilhigh"] = am.new_withholdingfeerecoilHighdimensionalfactory;//预提反冲费
                        e["new_high_dimensionalfeekpi"] = am.new_high_dimensionalfeekpi;
                        e["new_changemoney"] = new_changemoney;//调差金额
                        e["new_specialfee"] = am.new_specialfeeshare;//特殊费用分摊
                        e["new_settlementmoney"] = am.new_settlementmoney;//费用合计
                        updatecols.Entities.Add(e);
                    }
                }
                CommonHelper.MultUpdateRequest(OrganizationService, log, updatecols);
                log.LogInformation($"crm_settlementadjustmentline更新完成，结算单id:{new_srv_expense_claimid}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(20);//执行状态 = 执行成功
                settlementasyncjoblog.Id = new Guid(settlementasyncjoblogid);
                OrganizationService.Update(settlementasyncjoblog);
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError($"crm_settlementadjustmentline异常：{ex}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(30);//执行状态 = 执行失败
                settlementasyncjoblog["new_remark"] = ex.Message;
                settlementasyncjoblog.Id = new Guid(settlementasyncjoblogid);
                OrganizationService.Update(settlementasyncjoblog);
                //抛出异常，触发死信
                throw new Exception($"crm_settlementadjustmentline异常：{ex}");
            }
        }
        /// <summary>
        /// 迈创结算单调差逻辑
        /// </summary>
        public static void MaitroxExceuteAdjust(string myQueueItem, ILogger log)
        {
            AdjustModelMaitroxRequest request = new AdjustModelMaitroxRequest();
            List<AdjustModelMaitrox> amlist = new List<AdjustModelMaitrox>();
            try
            {
                #region 参数校验
                try
                {
                    log.LogInformation($"SettlementAdjustmentLine入参：【{myQueueItem}】");
                    if (string.IsNullOrWhiteSpace(myQueueItem))
                    {
                        throw new Exception("myQueueItem cannot be empty");
                    }
                    request = JsonConvert.DeserializeObject<AdjustModelMaitroxRequest>(myQueueItem);
                    amlist = request.amlist;
                    OrganizationService = CommonHelper.GetService(log);
                }
                catch (Exception ex)
                {
                    log.LogError($"crm_settlementadjustmentline初始化参数异常：【{ex}】,myQueueItem：【{myQueueItem}】");
                    return;
                }
                if (string.IsNullOrWhiteSpace(request.new_srv_expense_claimid))
                {
                    log.LogError($"new_srv_expense_claimid cannot be empty");
                    return;
                }
                if (OrganizationService == null)
                {
                    throw new Exception($"OrganizationService cannot be empty");
                }
                #endregion
                #region 服务单赋值
                EntityCollection updatecols = new EntityCollection();
                foreach (var am in amlist)
                {
                    decimal settlementmoneymaitrox = am.new_partservicecostkpi + am.new_localbuyreplacementcost
                    + am.new_markupreplacementcost + am.new_ecosystemcategorybuybackfee + am.new_customerrefund
                    + am.new_markuplogisticsfee + am.new_warehousingfee + am.new_othermiscellaneouscharges
                    + am.new_fixedservicefee + am.new_capitalinterestexpense + am.new_directshippingcost
                    + am.new_minimumworkorderfee + am.new_sparepartscost + am.new_withholdingfeemaitrox
                    + am.new_withholdingfeerecoil + am.new_refurbishfee;
                    Entity e = new Entity("new_srv_workorder", new Guid(am.new_srv_workorderid));
                    e["new_adjuststatusmaitrox"] = new OptionSetValue(2);//已调差
                    e["new_partservicecost"] = am.new_partservicecost;//备件服务费
                    e["new_partservicecostkpi"] = am.new_partservicecostkpi;//备件服务费KPI
                    e["new_localbuyreplacementcost"] = am.new_localbuyreplacementcost;//local buy 换机费
                    e["new_markupreplacementcost"] = am.new_markupreplacementcost;//local buy 换机markup费
                    e["new_ecosystemcategorybuybackfee"] = am.new_ecosystemcategorybuybackfee;//生态链产品回购费
                    e["new_customerrefund"] = am.new_customerrefund;//客户退款
                    e["new_markuplogisticsfee"] = am.new_markuplogisticsfee;//物流费
                    e["new_warehousingfee"] = am.new_warehousingfee;//仓储费
                    e["new_othermiscellaneouscharges"] = am.new_othermiscellaneouscharges;//迈创其他费用
                    e["new_changemoneymaitrox"] = settlementmoneymaitrox - am.new_withholdingmoney;//调差金额
                    e["new_settlementmoneymaitrox"] = settlementmoneymaitrox;//费用合计
                    e["new_withholdingfeemaitrox"] = am.new_withholdingfeemaitrox;//预提费
                    e["new_withholdingfeerecoil"] = am.new_withholdingfeerecoil;//预提反冲费
                    e["new_refurbishfee"] = am.new_refurbishfee;//高维费用
                    updatecols.Entities.Add(e);
                }
                CommonHelper.MultUpdateRequest(OrganizationService, log, updatecols);
                log.LogInformation($"crm_maitroxsettlementadjustmentline更新完成，结算单id:{request.new_srv_expense_claimid}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(20);//执行状态 = 执行成功
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError($"crm_maitroxsettlementadjustmentline异常：{ex.Message}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(30);//执行状态 = 执行失败
                settlementasyncjoblog["new_remark"] = ex.Message;
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                //抛出异常，触发死信
                throw new Exception($"crm_maitroxsettlementadjustmentline异常：{ex}");
            }
        }
        /// <summary>
        /// 激活结算单调差逻辑
        /// </summary>
        public static void ActivateExceuteAdjust(string myQueueItem, ILogger log)
        {
            AdjustModelActivateRequest request = new AdjustModelActivateRequest();
            List<AdjustModelActivate> amlist = new List<AdjustModelActivate>();
            try
            {
                #region 参数校验
                try
                {
                    log.LogInformation($"ActivateSettlementAdjustmentLine：【{myQueueItem}】");
                    if (string.IsNullOrWhiteSpace(myQueueItem))
                    {
                        throw new Exception("myQueueItem cannot be empty");
                    }
                    request = JsonConvert.DeserializeObject<AdjustModelActivateRequest>(myQueueItem);
                    amlist = request.amlist;
                    OrganizationService = CommonHelper.GetService(log);
                }
                catch (Exception ex)
                {
                    log.LogError($"crm_activatesettlementadjustmentline初始化参数异常：【{ex}】,myQueueItem：【{myQueueItem}】");
                    return;
                }
                if (string.IsNullOrWhiteSpace(request.new_srv_expense_claimid))
                {
                    log.LogError($"new_expense_claim_id cannot be empty");
                    return;
                }
                if (OrganizationService == null)
                {
                    throw new Exception($"OrganizationService cannot be empty");
                }
                #endregion
                #region 激活结算单明细赋值
                EntityCollection updatecols = new EntityCollection();
                foreach (var am in amlist)
                {
                    var new_changemoney = am.new_settlementmoney - am.new_withholdingmoney;
                    Entity e = new Entity("new_srv_expense_activationline", new Guid(am.new_srv_expense_activationlineid));
                    e["new_withholdingfeerecoilactivate"] = am.new_withholdingfeerecoilactivate;
                    e["new_changemoney"] = new_changemoney;//调差金额
                    e["new_specialfeeshare"] = am.new_specialfeeshare;//特殊费用
                    e["new_settlementmoney"] = am.new_settlementmoney;//费用合计
                    updatecols.Entities.Add(e);
                }
                CommonHelper.MultUpdateRequest(OrganizationService, log, updatecols);
                log.LogInformation($"crm_activatesettlementadjustmentline更新完成，结算单id:{request.new_srv_expense_claimid}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(20);//执行状态 = 执行成功
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError($"crm_activatesettlementadjustmentline异常：{ex.Message}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(30);//执行状态 = 执行失败
                settlementasyncjoblog["new_remark"] = ex.Message;
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                //抛出异常，触发死信
                throw new Exception($"crm_maitroxsettlementadjustmentline异常：{ex}");
            }
        }
        /// <summary>
        /// handling 结算单调差逻辑
        /// </summary>
        /// <param name="myQueueItem"></param>
        /// <param name="log"></param>
        public static void HandlingExceuteAdjust(string myQueueItem, ILogger log)
        {
            AdjustModelHandlingRequest request = new AdjustModelHandlingRequest();
            List<AdjustModelHandling> amlist = new List<AdjustModelHandling>();
            try
            {
                #region 参数校验
                try
                {
                    log.LogInformation($"handlingSettlementAdjustmentLine：【{myQueueItem}】");
                    if (string.IsNullOrWhiteSpace(myQueueItem))
                    {
                        throw new Exception("myQueueItem cannot be empty");
                    }
                    request = JsonConvert.DeserializeObject<AdjustModelHandlingRequest>(myQueueItem);
                    amlist = request.amlist;
                    OrganizationService = CommonHelper.GetService(log);
                }
                catch (Exception ex)
                {
                    log.LogError($"crm_handlingsettlementadjustmentline初始化参数异常：【{ex}】,myQueueItem：【{myQueueItem}】");
                    return;
                }
                if (string.IsNullOrWhiteSpace(request.new_srv_expense_claimid))
                {
                    log.LogError($"new_srv_expense_claimid cannot be empty");
                    return;
                }
                if (OrganizationService == null)
                {
                    throw new Exception($"OrganizationService cannot be empty");
                }
                #endregion
                #region Handling结算单明细赋值
                EntityCollection updatecols = new EntityCollection();
                foreach (var am in amlist)
                {
                    Entity e = new Entity("new_srv_workorder", new Guid(am.new_srv_workorderid));
                    e["new_changemoneyhandling"] = am.new_changemoneyhandling;//调差金额
                    e["new_othermiscellaneouschargeshandling"] = am.new_othermiscellaneouschargeshandling;//特殊费用
                    e["new_settlementmoneyhandling"] = am.new_settlementmoneyhandling;//费用合计
                    e["new_callfeehandling"] = am.new_callfeehandling;//呼叫费
                    e["new_saleamounthandling"] = am.new_saleamounthandling;//销售额
                    e["new_handlingfeekpi"] = am.new_handlingfeekpi;//收集费KPI
                    if (!string.IsNullOrWhiteSpace(am.new_workorder_costtable_id))
                    {
                        Entity costen = new Entity("new_workorder_costtable");
                        costen.Id = new Guid(am.new_workorder_costtable_id);
                        costen["new_withholdingfeerecoilhandling"] = am.new_withholdingfeerecoilhandling;
                        updatecols.Entities.Add(costen);
                    }
                    updatecols.Entities.Add(e);
                }
                CommonHelper.MultUpdateRequest(OrganizationService, log, updatecols);
                log.LogInformation($"crm_handlingsettlementadjustmentline更新完成，结算单id:{request.new_srv_expense_claimid}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(20);//执行状态 = 执行成功
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                #endregion
            }
            catch (Exception ex)
            {
                log.LogError($"crm_handlingsettlementadjustmentline异常：{ex.Message}");
                Entity settlementasyncjoblog = new Entity("new_settlementasyncjoblog");
                settlementasyncjoblog["new_executionstatus"] = new OptionSetValue(30);//执行状态 = 执行失败
                settlementasyncjoblog["new_remark"] = ex.Message;
                settlementasyncjoblog.Id = new Guid(request.new_settlementasyncjoblogId);
                OrganizationService.Update(settlementasyncjoblog);
                //抛出异常，触发死信
                throw new Exception($"crm_handlingsettlementadjustmentline异常：{ex}");
            }
        }
    }
}
