﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Crm.HiddenApi;
using RekTec.Crm.Plugin.Helper;
using RekTec.Service1CC.Service.Helper;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using RekTec.Service1.Parts.Helper;
using XiaoMi.Crm.BaseCommon.ApplicationInsights;
using XiaoMi.Crm.BaseCommon.Model;
using RekTec.Crm.OrganizationService.Common.Helper;
using System.Threading.Tasks;
using System.Text;
using static XiaoMi.Crm.CC.Common.Core.Helper.EnumHelper;
using XiaoMi.Crm.CC.Common.Core.Helper;
using RekTec.Service1CC.Service.Model;
using Newtonsoft.Json;
using RekTec.Crm.Model.Email;
using RekTec.Crm.Entity;
using RekTec.Crm.Plugin.Common.Email;
using Microsoft.Xrm.Sdk.Workflow.Activities;
using Microsoft.Xrm.Sdk.Metadata;
using Microsoft.Crm.Sdk.Messages;
using System.Text.Json.Nodes;
using System.Collections;

namespace RekTec.Service1CC.Service.Command
{
    public class IncidentCommand : HiddenCommand
    {

        /// <summary>
        /// 案例升级（用于一级案例升级二级案例）
        /// </summary>
        /// <param name="incidentId">案例id</param>
        /// <returns></returns>
        public Guid Upgrade(Guid incidentId)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();

            StringBuilder sbadd = new StringBuilder();
            //Stopwatch swadd = new Stopwatch();

            Log.InfoMsg("进入案例Upgrade方法");
            try
            {
                if (incidentId == Guid.Empty)
                    throw new InvalidPluginExecutionException(GetResource("incident.idEmpty", "案例id丢失"));
                //案例维护字段
                string[] fields = new string[] {
                    "new_nickname",    //昵称/称呼前缀
                    "new_publicname",    //媒体/机构名称
                    "new_sex",    //性别
                    "new_contactnum2",    //联系电话2
                    "new_email2",    //电子邮件地址2
                    "new_miid",    //米聊号
                    "new_l1casetype_id",    //一级案例类型
                    "new_l2casetype_id",    //二级案例类型
                    "new_l3casetype_id",    //三级案例类型
                    "new_prodpara",    //产品参数
                    "new_as_staenquire",    //售后网点咨询
                    "new_as_infenquire",    //售后进度查询
                    "new_webfuncomp",    //投诉网站功能
                    "new_discountintcomp",    //投诉优惠券与积分
                    "new_expcomp",    //投诉物流
                    "new_salecomp",    //投诉米店/第三方
                    "new_as_comp",    //售后服务投诉
                    "new_as_attitcomp",    //投诉售后服务态度
                    "new_as_policomp",    //投诉售后政策
                    "new_as_storecomp",    //投诉售后网点覆盖
                    "new_as_timecomp",    //投诉售后服务时效
                    "new_as_pricecomp",    //投诉售后价格
                    "new_as_servcomp",    //投诉售后服务质量
                    "new_as_machcomp",    //投诉官翻机
                    "new_as_micarecomp",    //投诉Mi Care
                    "new_requestfollowupon", //用户要求回访时间 20240409 客诉重构新增
                    "new_asconplain",//投诉售后分类
                    "new_comcheck",//不认可检测结果
                    "new_long_tat",//Long-TAT
                    "new_comprice",//不认可保外报价
                    "new_comsrvqua",//不满服务质量
                    "new_srvpers",//不满服务人员
                    "new_comsrvlaw",//不满服务政策
                    "new_ascomplain_product_id",//客诉产品分类

                    "new_cs_comp",    //客服服务投诉
                    "new_cs_sopcomp",    //投诉客服服务覆盖
                    "new_cs_attitcomp",    //投诉客服服务态度
                    "new_cs_profcomp",    //投诉客服专业度
                    "new_cs_timecomp",    //投诉客服响应及时性
                    "new_cs_promecomp",    //投诉客服服务履约
                    "new_softcomp",    //投诉软件
                    "new_hardcomp",    //投诉硬件
                    "new_miuicomp",    //投诉MIUI
                    "new_mihomecomp",    //投诉Mi home
                    "new_mesenstivie",    //媒体/政府敏感来访
                    "new_lawsenstivie",    //律师敏感来访
                    "new_isthank",    //是否为感谢来访
                    "new_purchasename",    //购买渠道
                    "new_purchasetime",    //购买时间
                    "new_ismi",    //是否自营
                    "new_miordercode",    //自营订单号
                    "new_ordercode",    //非自营订单号
                    "new_srv_workorder_id",    //售后服务单号
                    "new_imei",    //IMEI号
                    "new_sn",    //SN号
                    "new_warrantyornot",    //保内/保外
                    "new_miui",    //MIUI版本
                    "new_saleactivity_id",    //销售活动
                    "new_channel",    //来访渠道
                    "new_source",    //来访入口
                    "new_probdescrib",    //问题描述
                    "new_customerequire",    //客户需求
                    "new_l1agremark",    //一线备注
                    //"new_l2agremark",    //二线备注
                    "new_iscallback",    //是否需要回拨
                    "new_callbacktype",    //指定回拨方式
                    "new_callbackemail",    //回拨邮件地址
                    "new_callbacknum",    //回拨联系电话
                    //"new_resolvetime",    //解决时间
                    //"new_countime",    //解决时长
                    //"new_solution",    //最终解决方案
                    //"new_resolveby",    //解决人
                    //"new_isuptodepartment",    //是否升级至其他部门
                    //"new_departmentremark",    //其他部门回复意见
                    //"new_checktime",    //核实时间
                    //"new_checkremerk",    //核实结果
                    //"new_iscustaccept",    //客户是否接受
                    //"new_casecode",    //案例编码   根据规则生产
                    "new_caselevel",    //案例级别   
                    //"new_uptime",    //升级时间  升级时间为一级案例专有字段
                    "new_srv_workorder_id",    //转为售后服务单
                    "new_transfworkordertime",    //转售后服务单时间
                    //"new_assigntime",    //分派时间
                    //"new_handlingtime",    //处理时长
                    //"new_supervisenum",    //督办次数
                    //"new_rejectinf",    //驳回信息
                    //"new_transfcrisistime",    //转危机事件时间
                    //"new_rejectime",    //驳回时间
                    //"new_rejectreason",    //驳回原因
                    //"new_dealstatus",    //处理状态
                    "new_casestatus",    //案例状态    案例状态有默认值
                    "customerid",       //客户id
                    //new_contactid",    //联系人
                    "new_phone",    //联系电话1
                    "emailaddress",  //电子邮件1

                    "new_appname",  //APP名称
                    "new_appversion",   //App版本
                    //"new_resolvecal",   //解决时长（计算）
                    //"new_resolvetype",  //解决类型
                    "new_country_id",  //国家
                    "new_region_id",    //区域
                    "new_province_id",   //省份
                    "new_city_id",  //城市
                    "new_county_id",    //区县
                    "new_custaddress",    //详细地址
                    "new_ismediarisk",    //是否有媒体风险
                    "new_isinjury",    //是否有人员伤害
                    "new_occurtime",    //发生时间
                    "new_language_id",    //语言
                    "new_productline_id",    //产品线
                    //"new_productmodel2_id",    //产品型号
                    "new_injurydescribe",    //伤情简要描述
                    //"new_transfcrisis_id",    //转危机事件
                    "new_isinvoice",    //是否有发票
                    "new_invoicenum",    //发票号码
                    "new_invoicetime",    //发票时间
                    //"new_qastatus",    //质检抽样状态
                    //"new_acceptime",    //受理时间
                    "new_somchannel",    //社媒来访渠道
                    "new_somsource",    //社媒来访入口
                    "new_somaccount",    //社媒账号
                    "new_isbuyprod",    //是否已购买产品
                    //"new_srv_design_id",    //回访设计
                    //"new_rvstatus",    //回访抽样状态
                    "new_crisisplace",//危机事件发生国家
                    "new_casecode",//国家编码
                    "new_lv1casetypecode",//一级案例类型编码
                    "prioritycode",//Priority
                    "new_goodsfiles_id",//商品档案
                    "new_productline",//新产品线
                    "new_productmod_id",//一级机型
                    "new_productmodel2_id",//二级机型
                    "new_ordertype", //订单类型
                    "new_casesource",//来源
                    "new_issensitiveuser", //是否敏感用户

                    "new_servicemode",//【服务方式】
                    "new_postal_code"//【邮政编码】
                };

                //swadd.Restart();
                //获取一级案例
                var incidentInfo = this.OrganizationService.RetrieveWithBypassPlugin(CommonEntityName.Case, incidentId, new ColumnSet(fields));
                if (incidentInfo == null)
                    throw new InvalidPluginExecutionException(GetResource("incident.noData", "无案例数据"));
                //sbadd?.AppendLine($"获取一级案例，耗时：{swadd.ElapsedMilliseconds}。");

                //案例等级
                var new_caselevel = incidentInfo.ToOpIntDefalut("new_caselevel");
                //判断案例是否是一级案例
                if (new_caselevel != null && new_caselevel != (int)caseLevelEnum.lv1Case)
                    throw new InvalidPluginExecutionException(GetResource("incident.caselevelNoMatch", "案例类型不匹配"));

                //swadd.Restart();
                //判断是否存在活跃的二级案例
                CheckL2Case(incidentId);
                //sbadd?.AppendLine($"判断是否存在活跃的二级案例，耗时：{swadd.ElapsedMilliseconds}。");

                var key = incidentId + "upgrade";
                if (RedisHelper.IdempotentCheck(OrganizationService, key, "UploadWorkOrderCache", Log, 1))
                {
                    throw new InvalidPluginExecutionException(GetResource("incident.repeatUpgrade", "已存在二级案例，请勿重复升级"));
                }

                //swadd.Restart();
                //一级案例更新
                incidentInfo = UpdateL1Case(incidentInfo, incidentId);
                //sbadd?.AppendLine($"一级案例更新，耗时：{swadd.ElapsedMilliseconds}。");

                //swadd.Restart();
                //创建二级案例
                var secondLineId = CreateL2Case(incidentInfo, incidentId);
                //sbadd?.AppendLine($"创建二级案例，耗时：{swadd.ElapsedMilliseconds}。");

                //一级案例如果是offline案例则需要把一线按时上的附件信息带入到二线案例上 20230815 一线案例升级带入附件
                this.Log.InfoMsg($"记录日志：一线案例ID:{incidentId} ；二线案例ID:{secondLineId} 案例来源：{incidentInfo.GetAttributeValue<OptionSetValue>("new_casesource").Value.ToString()}");

                //swadd.Restart();
                CreateAnnotation(incidentId, secondLineId);
                //sbadd?.AppendLine($"创建附件注释数据，耗时：{swadd.ElapsedMilliseconds}。");

                sbadd?.AppendLine($"执行Incident/Upgrade，耗时：{sw.ElapsedMilliseconds}。");
                Log.InfoMsg(sbadd?.ToString());
                //swadd.Stop();
                sw.Stop();

                return secondLineId;
            }
            catch (Exception ex)
            {
                //Log.InfoMsg($"执行Incident/Reject 接口链路：{sbadd?.ToString()}");
                //swadd.Stop();
                sw.Stop();
                this.Log.InfoMsg("案例升级失败");
                this.Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }

        }
        /// <summary>
        /// 判断是否存在活跃的二级案例
        /// </summary>
        /// <param name="incidentId"></param>
        /// <exception cref="Exception"></exception>
        private void CheckL2Case(Guid incidentId)
        {
            try
            {
                QueryExpression queryIncident = new QueryExpression();
                queryIncident.ColumnSet.AddColumns("new_casestatus", "new_caselevel");
                queryIncident.EntityName = "incident";
                queryIncident.Criteria.AddCondition("parentcaseid", ConditionOperator.Equal, incidentId);
                var secondCaseList = this.OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryIncident);

                foreach (var secondCaseInDB in secondCaseList?.Entities)
                {
                    int curCaseStatus = secondCaseInDB.ToOpIntDefalut("new_casestatus");
                    int curCaseLevel = secondCaseInDB.ToOpIntDefalut("new_caselevel");
                    // 一级案例存在案例状态为待处理、处理中、已升级的二级案例不允许再次升级   
                    if (curCaseStatus != null && curCaseLevel != null && curCaseStatus != (int)caseStatus.rejected && curCaseLevel == (int)caseLevelEnum.lv2Case)
                        throw new Exception(GetResource("incident.repeatUpgrade", "已存在二级案例，请勿重复升级"));
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
        }
        /// <summary>
        /// 一级案例更新
        /// </summary>
        /// <param name="incidentId"></param>
        /// <exception cref="Exception"></exception>
        private Entity UpdateL1Case(Entity incidentInfo, Guid incidentId)
        {
            try
            {
                //升级时间
                var caseInfoNew = new Entity(CommonEntityName.Case, incidentId);
                //案例状态 已升级
                caseInfoNew["new_casestatus"] = new OptionSetValue((int)caseStatus.upgraded);
                caseInfoNew["new_uptime"] = DateTime.UtcNow;
                caseInfoNew["new_requestname"] = "Incident/Upgrade";
                caseInfoNew["new_isupgrade"] = true;
                OrganizationService.Update(caseInfoNew, true);
                incidentInfo["new_casestatus"] = new OptionSetValue((int)caseStatus.upgraded);
                incidentInfo["new_uptime"] = DateTime.UtcNow;

                #region 根据系统参数配置表Incident.IsCritical，Incident.IsComplaint更新案例上危机事件和客诉单控制显隐参数
                if (incidentInfo.ToEr("new_l3casetype_id") == null)
                {
                    return incidentInfo;
                }
                var l3Detail = OrganizationServiceAdmin.Retrieve("new_l3casetype", incidentInfo.ToEr("new_l3casetype_id").Id, new ColumnSet("new_code"), true);
                if (!l3Detail.Contains("new_code"))
                {
                    return incidentInfo;
                }
                var code = l3Detail.ToStr("new_code");
                var critical = OrganizationServiceAdmin.GetSystemParamValue("Incident.AllowCritical", true);
                var complaint = OrganizationServiceAdmin.GetSystemParamValue("Incident.AllowComplaint", true);
                if (!string.IsNullOrEmpty(critical))
                {
                    var criticalList = critical.Split(';').ToList();
                    if (criticalList.Contains(code))
                    {
                        incidentInfo["new_iscriticaltype"] = true;
                    }
                    else
                    {
                        incidentInfo["new_iscriticaltype"] = false;
                    }
                }
                if (!string.IsNullOrEmpty(complaint))
                {
                    var complaintList = complaint.Split(';').ToList();
                    if (complaintList.Contains(code))
                    {
                        incidentInfo["new_iscomplainttype"] = true;
                    }
                    else
                    {
                        incidentInfo["new_iscomplainttype"] = false;
                    }
                }
                #endregion 根据系统参数配置表Incident.IsCritical，Incident.IsComplaint更新案例上危机事件和客诉单控制显隐参数

                return incidentInfo;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }

        }
        /// <summary>
        /// 创建2级案例数据
        /// </summary>
        /// <param name="incidentInfo"></param>
        /// <param name="incidentId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private Guid CreateL2Case(Entity incidentInfo, Guid incidentId)
        {
            try
            {
                //需要解密的字段
                string[] fields = { "new_miid", "new_phone", "emailaddress", "new_contactnum2", "new_email2", "new_custaddress" };

                var secondCase = new Entity(CommonEntityName.Case);
                //不需要复制的字段
                //升级时间、案例编号、案例等级、案例状态
                string[] noCopyFiled = new string[] { "incidentid", "new_uptime", "new_caselevel", "new_casestatus" };
                //删除不需复制的字段
                foreach (var field in incidentInfo.Attributes)
                {
                    if (noCopyFiled.Contains(field.Key))
                        continue;
                    if (fields.Contains(field.Key) && incidentInfo[field.Key].GetType() == typeof(string))
                    {
                        //解密
                        string val = incidentInfo.ToStr(field.Key);
                        if (!string.IsNullOrWhiteSpace(val))
                        {
                            secondCase[field.Key] = EncryptHelper.AESDecrypt(val);
                        }
                    }
                    else
                    {
                        secondCase[field.Key] = incidentInfo[field.Key];
                    }
                }
                secondCase["new_caselevel"] = new OptionSetValue((int)caseLevelEnum.lv2Case);
                secondCase["new_casestatus"] = new OptionSetValue((int)caseStatus.pending);
                secondCase["parentcaseid"] = new EntityReference(CommonEntityName.Case, incidentId);
                secondCase["ownerid"] = new EntityReference(CommonEntityName.User, UserId);
                secondCase["new_caselevelcode"] = "WO";
                secondCase["new_requestname"] = "incidentUpgrade";
                secondCase = OrganizationService.AutoNumber(this.Context, secondCase, Log);
                var secondCaseId = OrganizationService.Create(secondCase);

                string objectid = secondCase.GetAttributeValue<string>("new_emailid");
                if (!string.IsNullOrEmpty(objectid))
                {
                    secondCase.Id = secondCaseId;
                    OptionSetValue new_channel = secondCase.GetAttributeValue<OptionSetValue>("new_channel");
                    if (new_channel.Value == 1)
                    {
                        Entity entity = new Entity("phonecall", new Guid(objectid));
                        entity["regardingobjectid"] = secondCase.ToEntityReference();
                        //更新电话实体
                        OrganizationServiceAdmin.Update(entity);
                    }
                    else if (new_channel.Value == 3)
                    {
                        Entity entity = new Entity("email", new Guid(objectid));
                        entity["regardingobjectid"] = secondCase.ToEntityReference();
                        //更新邮件实体
                        OrganizationServiceAdmin.Update(entity);
                    }
                }

                return secondCaseId;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        /// 创建件附件注释数据
        /// </summary>
        /// <param name="incidentId"></param>
        /// <param name="secondLineId"></param>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        private void CreateAnnotation(Guid incidentId, Guid secondLineId)
        {
            try
            {
                //1.获取一线案例的附件注释
                QueryExpression query = new QueryExpression();
                query.EntityName = "annotation";
                FilterExpression filter = new FilterExpression(LogicalOperator.And);
                filter.AddCondition("objectid", ConditionOperator.Equal, incidentId);//一线案例ID
                query.Criteria.AddFilter(filter);
                query.ColumnSet.AddColumns("subject", "filename", "mimetype", "notetext", "documentbody", "isdocument");
                var res = OrganizationService.RetrieveMultiple(query, true);

                if (res?.Entities?.Count > 0)
                {   //有附件, 添加到二线案例附件注释中。
                    foreach (var item in res.Entities)
                    {
                        try
                        {
                            Entity annotation = new Entity("annotation");
                            annotation.Attributes.Add("objectid", new EntityReference("incident", secondLineId));// 关联case;
                            annotation.Attributes.Add("subject", item.GetAttributeValue<string>("subject")); //标题
                            annotation.Attributes.Add("filename", item.GetAttributeValue<string>("filename"));//名称
                            annotation.Attributes.Add("mimetype", item.GetAttributeValue<string>("mimetype"));
                            annotation.Attributes.Add("notetext", item.GetAttributeValue<string>("notetext")); //说明
                            annotation.Attributes.Add("documentbody", item.GetAttributeValue<string>("documentbody"));//文件base64内容
                            OrganizationServiceAdmin.Create(annotation);
                        }
                        catch (Exception ex)
                        {
                            throw new InvalidPluginExecutionException(GetResource("incident.docmentnoteerrotip", "案例升级时，附件添加出现异常"));
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 案例驳回
        /// </summary>
        /// <param name="incidentId">案例id</param>
        /// <param name="rejectReason">驳回原因</param>
        /// <param name="operationTime">操作时间</param>
        public void Reject(Guid incidentId, string rejectReason, DateTime operationTime)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();

            StringBuilder sbadd = new StringBuilder();
            //Stopwatch swadd = new Stopwatch();
            try
            {
                Log.InfoMsg($"案例驳回 incidentId：{incidentId} rejectReason：{rejectReason} operationTime：{operationTime.ToString("yyyy-MM-dd HH:mm:ss")}");
                if (incidentId == Guid.Empty)
                    throw new InvalidPluginExecutionException(GetResource("incident.idEmpty", "案例id丢失"));
                // 二级案例信息
                //swadd.Restart();
                var incidentTwoInfo = this.OrganizationService.Retrieve(CommonEntityName.Case, incidentId, new ColumnSet("parentcaseid", "new_caselevel", "title"), true);
                if (incidentTwoInfo == null)
                    throw new InvalidPluginExecutionException(GetResource("incident.noData", "无案例数据"));
                //sbadd?.AppendLine($"二级案例信息查询，耗时：{swadd.ElapsedMilliseconds}。");

                //案例等级
                var new_caselevel = incidentTwoInfo.ToOpIntDefalut("new_caselevel");
                //判断案例是否是一级案例
                if (new_caselevel != null && new_caselevel != (int)caseLevelEnum.lv2Case)
                    throw new InvalidPluginExecutionException(GetResource("incident.caselevelNoMatch", "案例类型不匹配"));
                //父级案例
                var parentcaseid = incidentTwoInfo.ToEr("parentcaseid");
                if (parentcaseid is null)
                    throw new InvalidPluginExecutionException(GetResource("incident.noParentCase", "无父级案例信息"));
                //二级案例更新
                var incidentTwoUp = new Entity(CommonEntityName.Case, incidentId);
                incidentTwoUp["new_rejectime"] = DateTime.UtcNow;
                incidentTwoUp["new_rejectreason"] = rejectReason;
                incidentTwoUp["new_casestatus"] = new OptionSetValue((int)caseStatus.rejected);
                OrganizationService.Update(incidentTwoUp, true);

                //一级案例信息
                //swadd.Restart();
                var incidentOneInfo = this.OrganizationService.RetrieveWithBypassPlugin(CommonEntityName.Case, parentcaseid.Id, new ColumnSet("new_rejectinf", "new_rejectnum"));
                //sbadd?.AppendLine($"一级案例信息查询，耗时：{swadd.ElapsedMilliseconds}。");
                //驳回信息记录
                string new_rejectinf = incidentOneInfo.GetAttributeValue<string>("new_rejectinf") ?? "";
                //驳回次数
                int new_rejectnum = incidentOneInfo.Contains("new_rejectnum") ? incidentOneInfo.GetAttributeValue<int>("new_rejectnum") : 0;
                //用户信息
                //swadd.Restart();
                var userInfo = this.OrganizationService.RetrieveWithBypassPlugin(CommonEntityName.User, this.UserId, new ColumnSet("fullname"));
                //sbadd?.AppendLine($"用户信息查询，耗时：{swadd.ElapsedMilliseconds}。");
                string fullname = userInfo.GetAttributeValue<string>("fullname");
                string title = incidentTwoInfo.Contains("title") ? incidentTwoInfo["title"].ToString() : "--";
                //本次驳回记录
                string record = $"{title};{operationTime.ToString("yyyy-MM-dd HH:mm:ss")};{fullname};{rejectReason}\n";
                //累计驳回记录
                new_rejectinf += record;
                //更新一级案例信息
                var incidentOneUp = new Entity(CommonEntityName.Case, parentcaseid.Id);
                incidentOneUp["new_rejectinf"] = new_rejectinf;
                incidentOneUp["new_rejectnum"] = new_rejectnum + 1;
                incidentOneUp["new_casestatus"] = new OptionSetValue(7);
                OrganizationService.Update(incidentOneUp, true);
                //sbadd?.AppendLine($"更新一级案例信息，耗时：{swadd.ElapsedMilliseconds}。");

                sbadd?.AppendLine($"执行Incident/Reject，耗时：{sw.ElapsedMilliseconds}。");
                Log.InfoMsg(sbadd?.ToString());
                //swadd.Stop();
                sw.Stop();
            }
            catch (Exception ex)
            {
                //Log.InfoMsg($"执行Incident/Reject 接口链路：{sbadd?.ToString()}");
                //swadd.Stop();
                sw.Stop();
                this.Log.InfoMsg("案例驳回失败");
                this.Log.LogException(ex);
                throw;
            }

        }

        /// <summary>
        /// 转为服务单
        /// </summary>
        /// <param name="incidentId">案例Id</param>
        public void ConvertToWorkOrder(Guid incidentId)
        {
            try
            {
                if (incidentId == Guid.Empty)
                    throw new InvalidPluginExecutionException(GetResource("incident.idEmpty", "案例id丢失"));
                // 案例实体名称
                string caseEntityName = "incident";
                //售后工单需要案例的字段
                string[] fields = new string[] { "", "", "" };
                //获取一级案例
                var incidentInfo = this.OrganizationService.Retrieve(caseEntityName, incidentId, new Microsoft.Xrm.Sdk.Query.ColumnSet(fields));
                if (incidentInfo == null)
                    throw new InvalidPluginExecutionException(GetResource("incident.noData", "无案例数据"));

                string wordOrderEntityName = "";
                var workOrder = new Entity(wordOrderEntityName);
                //？？
                //创建售后服务单
                Guid wordOrderId = this.OrganizationService.Create(workOrder);

                //更新案例的售后服务单号
                var caseEntity = new Entity("incidentid", incidentId);
                caseEntity["new_srv_workorder_id"] = new EntityReference(wordOrderEntityName, wordOrderId);
                this.OrganizationService.Update(caseEntity);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 转为危机事件
        /// </summary>
        /// <param name="incidentId">案例Id</param>
        public string ConvertToCrisisEvent(string incidentId)
        {
            Stopwatch sw = new Stopwatch();

            //StringBuilder sbadd = new StringBuilder();
            //Stopwatch swadd = new Stopwatch();

            sw.Start();
            Guid new_criticalId = new Guid();
            var createTag = false;
            try
            {
                //swadd.Restart();
                ColumnSet columnList = new ColumnSet("new_srv_workorder_id", "new_region_id",
                    "new_country_id", "new_crisisplace", "new_province_id", "new_city_id",
                    "new_county_id", "new_occurtime", "new_customerequire", "customerid",
                    "new_phone", "emailaddress", "new_custaddress", "new_goodsfiles_id",
                    "new_imei", "new_sn", "new_purchasename", "new_purchasetime",
                    "new_ismediarisk", "new_isinjury", "createdon", "new_probdescrib",
                    "new_injurydescribe", "new_l2agremark", "new_iscriticaltype", "new_l3casetype_id");
                Entity incidentEntity = OrganizationService.RetrieveWithBypassPlugin("incident", new Guid(incidentId), columnList);
                //sbadd?.AppendLine($"incident 查询，耗时：{swadd.ElapsedMilliseconds}。");
                //20230515 CC_5-1更新,后台再次验证是否可转危机事件
                //swadd.Restart();
                var criticalType = GetParameter("Incident.AllowCritical", OrganizationServiceAdmin);
                //sbadd?.AppendLine($"Incident.AllowCritical 系统参数查询，耗时：{swadd.ElapsedMilliseconds}。");
                if (string.IsNullOrWhiteSpace(criticalType) || !incidentEntity.Contains("new_l3casetype_id"))
                {
                    throw new InvalidPluginExecutionException(GetResource("incident.caselevelNoMatch", "案例类型不匹配"));
                }
                var criticalList = criticalType.Split(';').ToList();
                //swadd.Restart();
                Entity category3 = OrganizationService.RetrieveWithBypassPlugin("new_l3casetype", incidentEntity.GetAttributeValue<EntityReference>("new_l3casetype_id").Id, new ColumnSet("new_code"));
                //sbadd?.AppendLine($"new_l3casetype 查询，耗时：{swadd.ElapsedMilliseconds}。");
                var categorycode = "";
                if (category3.Contains("new_code"))
                {
                    categorycode = category3.GetAttributeValue<string>("new_code");
                }
                if (!criticalList.Contains(categorycode))
                {
                    //swadd.Restart();
                    var updateIncident = new Entity("incident", incidentEntity.Id);
                    updateIncident["new_iscriticaltype"] = false;
                    OrganizationService.Update(updateIncident, true);
                    //sbadd?.AppendLine($"incident 更新，耗时：{swadd.ElapsedMilliseconds}。");
                    throw new InvalidPluginExecutionException(GetResource("incident.caselevelNoMatch", "案例类型不匹配"));
                }
                //swadd.Restart();

                //组装实体数据
                Entity new_criticalEntity = AssembleCriticalEntity(incidentEntity, new_criticalId);
                //自动编号
                //new_criticalEntity = OrganizationService.AutoNumber(this.Context, new_criticalEntity, Log);//edit by p-songyongxiang 2024-12-12 危机事件代码自动编码注释，采用CRM自带编码功能
                //创建数据
                new_criticalId = OrganizationService.Create(new_criticalEntity, true);

                //创建成功后调用E-learning系统，同步账号过去
                SyncELearningSystem(new_criticalEntity);
                //sbadd?.AppendLine($"new_critical 创建，耗时：{swadd.ElapsedMilliseconds}。");

                createTag = true;

                //swadd.Restart();
                Entity ectypeIncidentEntity = new Entity("incident", incidentEntity.Id);
                ectypeIncidentEntity["new_transfcrisis_id"] = new EntityReference("incident", new_criticalId);
                ectypeIncidentEntity["new_casestatus"] = new OptionSetValue((int)caseStatus.transferCritical);
                ectypeIncidentEntity["new_transfcrisistime"] = DateTime.UtcNow;
                OrganizationService.Update(ectypeIncidentEntity, true);
                //sbadd?.AppendLine($"incident 更新，耗时：{swadd.ElapsedMilliseconds}。");

                Log.InfoMsg($"执行 Incident/ConvertToCrisisEvent 耗时：{sw.ElapsedMilliseconds}");
                //swadd.Restart();
                sw.Stop();
                return new_criticalId.ToString();
            }
            catch (Exception ex)
            {
                //Log.InfoMsg($"执行 Incident/ConvertToCrisisEvent 接口链路：{sbadd?.ToString()}");
                //swadd.Restart();
                sw.Stop();
                Log.LogException(ex);
                //Log.DebugMsg(ex.Message);
                if (createTag)
                {
                    OrganizationService.Delete("new_critical", new_criticalId);
                }
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 组装数据
        /// </summary>
        /// <param name="incidentEntity"></param>
        /// <param name="new_criticalId"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        private Entity AssembleCriticalEntity(Entity incidentEntity, Guid new_criticalId)
        {
            try
            {
                Entity new_criticalEntity = new Entity("new_critical");
                new_criticalEntity.Id = new_criticalId;
                //审批状态
                new_criticalEntity["new_approvestatus"] = new OptionSetValue(1);
                //案例	lookup	case
                new_criticalEntity["new_incident_id"] = new EntityReference("incident", incidentEntity.Id);
                //售后单号	lookup	售后服务单号
                EntityReference new_srv_workorder_id = incidentEntity.GetAttributeValue<EntityReference>("new_srv_workorder_id");
                if (new_srv_workorder_id != null)
                {
                    new_criticalEntity["new_srv_workorder_id"] = new_srv_workorder_id;
                }
                //区域	lookup	区域
                EntityReference new_region_id = incidentEntity.GetAttributeValue<EntityReference>("new_region_id");
                if (new_region_id != null)
                {
                    new_criticalEntity["new_region_id"] = new_region_id;
                }
                //国家	lookup	国家
                EntityReference new_country_id = incidentEntity.GetAttributeValue<EntityReference>("new_country_id");
                if (new_country_id != null)
                {
                    new_criticalEntity["new_country_id"] = new_country_id;
                }
                //危机事件发生地点
                EntityReference new_crisisplace = incidentEntity.GetAttributeValue<EntityReference>("new_crisisplace");
                if (new_crisisplace != null)
                {
                    new_criticalEntity["new_occurcountry"] = new_crisisplace;
                    Entity new_countryEntity = OrganizationService.RetrieveWithBypassPlugin("new_country", incidentEntity.GetAttributeValue<EntityReference>("new_crisisplace").Id, new ColumnSet("crcce_servicemanager"));
                    if (new_countryEntity.Contains("crcce_servicemanager"))
                    {
                        //负责人为国家服务经理
                        new_criticalEntity["ownerid"] = new_countryEntity.GetAttributeValue<EntityReference>("crcce_servicemanager");
                    }
                    else
                    {
                        throw new InvalidPluginExecutionException(GetResource("incident.CrisisEventOccurrence", "当前危机事件发生地在服务范围外，请重新选择地点！"));
                    }
                }
                //省份	lookup	Provinces
                EntityReference new_province_id = incidentEntity.GetAttributeValue<EntityReference>("new_province_id");
                if (new_province_id != null)
                {
                    new_criticalEntity["new_province_id"] = new_province_id;
                }
                //城市	lookup	City of origin
                EntityReference new_city_id = incidentEntity.GetAttributeValue<EntityReference>("new_city_id");
                if (new_city_id != null)
                {
                    new_criticalEntity["new_city_id"] = new_city_id;
                }
                //县区	lookup	Counties
                EntityReference new_county_id = incidentEntity.GetAttributeValue<EntityReference>("new_county_id");
                if (new_county_id != null)
                {
                    new_criticalEntity["new_county_id"] = new_county_id;
                }
                //事件状态	picklist	
                new_criticalEntity["new_eventstatus"] = new OptionSetValue(1);
                //信息来源	picklist	
                new_criticalEntity["new_informationsources"] = new OptionSetValue(1);
                //创建方式	picklist	
                new_criticalEntity["new_creationmethod"] = new OptionSetValue(1);
                //发生时间	datetime	发生时间
                DateTime new_occurtime = incidentEntity.GetAttributeValue<DateTime>("new_occurtime");
                if (new_occurtime != DateTime.MinValue)
                {
                    new_criticalEntity["new_occurtime"] = new_occurtime;
                }
                //用户诉求	memo	客户需求
                string new_customerequire = incidentEntity.GetAttributeValue<string>("new_customerequire");
                if (new_customerequire != null)
                {
                    new_criticalEntity["new_userdemands"] = new_customerequire;
                }
                //客户姓名	lookup	客户姓名
                EntityReference customerid = incidentEntity.GetAttributeValue<EntityReference>("customerid");
                if (customerid != null)
                {
                    new_criticalEntity["new_account_id"] = customerid;
                    new_criticalEntity["new_feedback"] = customerid.Name;
                }
                //客户电话	string	contact number
                string new_phone = incidentEntity.GetAttributeValue<string>("new_phone");
                if (new_phone != null)
                {
                    new_criticalEntity["new_accountnumber"] = new_phone;
                }
                //客户邮箱	string	Email Address
                string emailaddress = incidentEntity.GetAttributeValue<string>("emailaddress");
                if (emailaddress != null)
                {
                    new_criticalEntity["new_accountemail"] = emailaddress;
                }
                //客户地址	string	详细地址
                string new_custaddress = incidentEntity.GetAttributeValue<string>("new_custaddress");
                if (new_custaddress != null)
                {
                    new_criticalEntity["new_accountaddress"] = new_custaddress;
                }
                EntityReference new_goodsfiles_id = incidentEntity.GetAttributeValue<EntityReference>("new_goodsfiles_id");
                if (new_goodsfiles_id != null)
                {
                    //商品档案
                    new_criticalEntity["new_goods_id"] = new_goodsfiles_id;
                    Entity new_goodsfiles = OrganizationService.RetrieveWithBypassPlugin("new_goodsfiles", new_goodsfiles_id.Id, new ColumnSet("new_commoditycode", "new_category3_id", "new_model3_id"));
                    //产品编码
                    new_criticalEntity["new_userprofile_code"] = new_goodsfiles.GetAttributeValue<string>("new_commoditycode");
                    //三级品类
                    EntityReference new_category3_id = new_goodsfiles.GetAttributeValue<EntityReference>("new_category3_id");
                    if (new_category3_id != null)
                    {
                        new_criticalEntity["new_category3_id"] = new_category3_id;
                    }
                    //三级机型
                    EntityReference new_model3_id = new_goodsfiles.GetAttributeValue<EntityReference>("new_model3_id");
                    if (new_model3_id != null)
                    {
                        new_criticalEntity["new_model3_id"] = new_model3_id;
                        Entity new_model3 = OrganizationService.RetrieveWithBypassPlugin("new_model3", new_model3_id.Id, new ColumnSet("new_model1_id", "new_model2_id"));
                        //一级机型
                        EntityReference new_model1_id = new_model3.GetAttributeValue<EntityReference>("new_model1_id");
                        if (new_model1_id != null)
                        {
                            new_criticalEntity["new_model1_id"] = new_model1_id;
                        }
                        //二级机型
                        EntityReference new_model2_id = new_model3.GetAttributeValue<EntityReference>("new_model2_id");
                        if (new_model2_id != null)
                        {
                            new_criticalEntity["new_model2_id"] = new_model2_id;
                        }
                    }
                }
                //IMEI	string	IMEI号
                string new_imei = incidentEntity.GetAttributeValue<string>("new_imei");
                if (new_imei != null)
                {
                    new_criticalEntity["new_imei"] = new_imei;
                }
                //SN	string	SN号
                string new_sn = incidentEntity.GetAttributeValue<string>("new_sn");
                if (new_sn != null)
                {
                    new_criticalEntity["new_sn"] = new_sn;
                }
                //购买渠道	picklist	购买渠道
                OptionSetValue new_purchasename = incidentEntity.GetAttributeValue<OptionSetValue>("new_purchasename");
                if (new_purchasename != null)
                {
                    new_criticalEntity["new_buyingchannels"] = new_purchasename;
                }
                //购买时间	datetime	购买时间
                DateTime new_purchasetime = incidentEntity.GetAttributeValue<DateTime>("new_purchasetime");
                if (new_purchasetime != DateTime.MinValue)
                {
                    new_criticalEntity["new_purchasetime"] = new_purchasetime;
                }
                //是否有媒体风险	boolean	是否有媒体风险
                new_criticalEntity["new_ismediarisk"] = incidentEntity.GetAttributeValue<bool>("new_ismediarisk");
                //是否有人员伤害	boolean	是否有人员伤害
                new_criticalEntity["new_ispersoninjury"] = incidentEntity.GetAttributeValue<bool>("new_isinjury");
                //用户反馈时间	datetime	Created On
                DateTime createdon = incidentEntity.GetAttributeValue<DateTime>("createdon");
                if (createdon != DateTime.MinValue)
                {
                    new_criticalEntity["new_feedbacktime"] = createdon;
                }
                //危机事件描述	memo	问题描述
                string new_probdescrib = incidentEntity.GetAttributeValue<string>("new_probdescrib");
                if (new_probdescrib != null)
                {
                    new_criticalEntity["new_crisis_describe"] = new_probdescrib;
                }
                //伤情简要描述	memo	伤情简要描述
                string new_injurydescribe = incidentEntity.GetAttributeValue<string>("new_injurydescribe");
                if (new_injurydescribe != null)
                {
                    new_criticalEntity["new_injury_describe"] = new_injurydescribe;
                }
                //客服建议	memo	二线备注
                string new_l2agremark = incidentEntity.GetAttributeValue<string>("new_l2agremark");
                if (new_l2agremark != null)
                {
                    new_criticalEntity["new_suggest"] = new_l2agremark;
                }

                //把内容更新到new_record里作为变更记录
                var opUser = OrganizationServiceAdmin.RetrieveWithBypassPlugin("systemuser", Context.UserId, new ColumnSet("domainname")).GetAttributeValue<string>("domainname");
                var opTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
                var opString = "Create record.";
                new_criticalEntity["new_record"] = "Operator: " + opUser + " Operation time(UTC): " + opTime + " Operations: " + opString + "\n"; //更新操作动作

                //创建危机事件赋值敏感用户为是，优先级为高
                new_criticalEntity["new_issensitiveuser"] = true;
                new_criticalEntity["new_prioritycode"] = new OptionSetValue((int)urgency.high); //优先级 高

                return new_criticalEntity;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        /// 创建成功后调用E-learning系统，同步账号过去
        /// </summary>
        /// <param name="criticalentity"></param>
        /// <exception cref="Exception"></exception>
        private void SyncELearningSystem(Entity criticalentity)
        {
            try
            {
                string key = OrganizationServiceAdmin.GetSystemParamValue("Aeskey256");
                string iv = OrganizationServiceAdmin.GetSystemParamValue("AesIv256");
                if (!criticalentity.Contains("new_account_id"))
                {
                    if (criticalentity.Contains("new_accountnumber"))
                    {
                        var accountnumber = AesEncrypt(criticalentity.GetAttributeValue<string>("new_accountnumber"), key);//客户电话

                        QueryExpression query = new QueryExpression("account");
                        query.ColumnSet = new ColumnSet(true);
                        query.Criteria.AddCondition("new_account_tel", ConditionOperator.Equal, accountnumber);
                        var account = this.OrganizationService.RetrieveMultiple(query);
                        if (account == null || account.Entities == null)
                        {
                            throw new InvalidPluginExecutionException(GetResource("", "获取人员信息失败"));
                        }
                        if (account.Entities.Count == 0)
                        {
                            Log.InfoMsg("开始创建");
                            var accountentity = new Entity("account");
                            var accountid = Guid.NewGuid();
                            accountentity.Id = accountid;//用户主键
                            if (criticalentity.Contains("new_feedback"))
                            {
                                accountentity["name"] = criticalentity.GetAttributeValue<string>("new_feedback");//反馈人
                                if (criticalentity.Contains("new_country_id"))
                                {
                                    accountentity["new_country"] = criticalentity.GetAttributeValue<EntityReference>("new_country_id");//国家
                                }
                                if (criticalentity.Contains("new_province_id"))
                                {
                                    accountentity["new_province_id"] = criticalentity.GetAttributeValue<EntityReference>("new_province_id");//省份
                                }
                                if (criticalentity.Contains("new_region_id"))
                                {
                                    accountentity["new_region_id"] = criticalentity.GetAttributeValue<EntityReference>("new_region_id");//区域
                                }
                                if (criticalentity.Contains("new_city_id"))
                                {
                                    accountentity["new_city"] = criticalentity.GetAttributeValue<EntityReference>("new_city_id");//城市
                                }
                                if (criticalentity.Contains("new_county_id"))
                                {
                                    accountentity["new_county"] = criticalentity.GetAttributeValue<EntityReference>("new_county_id");//县区
                                }
                                if (criticalentity.Contains("new_accountaddress"))
                                {
                                    accountentity["new_address"] = criticalentity.GetAttributeValue<string>("new_accountaddress"); //地址
                                }
                                if (criticalentity.Contains("new_customer_type"))
                                {
                                    accountentity["new_accounttype"] = criticalentity.GetAttributeValue<OptionSetValue>("new_customer_type");//客户类型
                                }
                                if (criticalentity.Contains("new_accountnumber"))
                                {
                                    accountentity["new_account_tel"] = criticalentity.GetAttributeValue<string>("new_accountnumber");//客户电话
                                }
                                if (criticalentity.Contains("new_accountemail"))
                                {
                                    accountentity["new_email"] = criticalentity.GetAttributeValue<string>("new_accountemail");//客户邮箱
                                }
                                this.OrganizationService.Create(accountentity);
                                Log.InfoMsg("添加成功");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        ///  AES 加密
        /// </summary>
        /// <param name="str">明文（待加密）</param>
        /// <param name="key">加密的key</param>
        /// <returns></returns>
        public static string AesEncrypt(string str, string key)
        {
            if (string.IsNullOrEmpty(str)) return null;
            Byte[] toEncryptArray = Encoding.UTF8.GetBytes(str);

            System.Security.Cryptography.RijndaelManaged rm = new System.Security.Cryptography.RijndaelManaged
            {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = System.Security.Cryptography.CipherMode.ECB,
                Padding = System.Security.Cryptography.PaddingMode.PKCS7
            };
            try
            {
                System.Security.Cryptography.ICryptoTransform cTransform = rm.CreateEncryptor();
                Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

                return Convert.ToBase64String(resultArray, 0, resultArray.Length);
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                rm.Clear();
            }
        }

        /// <summary>
        /// 创建记录
        /// </summary>
        /// <param name="zj"></param>
        /// <param name="cj"></param>
        /// <param name="fj"></param>
        /// <param name="bc"></param>
        /// <param name="new_qadate"></param>
        public void CreateRecord(bool zj, bool cj, bool fj, bool bc, DateTime new_qadate)
        {
            try
            {
                Entity new_qarecordinfos = new Entity("new_qarecord");
                if (zj)
                {
                    new_qarecordinfos["new_qastatus"] = new OptionSetValue(1);
                }
                if (cj)
                {
                    new_qarecordinfos["new_spotqastatus"] = new OptionSetValue(1);
                }
                if (fj)
                {
                    new_qarecordinfos["new_reqastatus"] = new OptionSetValue(1);
                }
                if (bc)
                {
                    new_qarecordinfos["new_dialstatus"] = new OptionSetValue(1);
                }
                new_qarecordinfos["new_qadate"] = new_qadate;
                OrganizationService.Create(new_qarecordinfos);
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.DebugMsg(ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 案例编码数据清洗
        /// </summary>
        /// <param name="incidentId"></param>
        public void CleanCase(string incidentId)
        {
            try
            {
                #region 1、校验数据
                Asserts.IsTrue(string.IsNullOrWhiteSpace(incidentId), GetResource("incident.incidentId", "案例id不能为空!"));
                #endregion 1、校验数据

                #region 2、获取数据
                var incident = OrganizationService.Retrieve("incident", new Guid(incidentId), new ColumnSet("new_caselevelcode", "new_casecode", "new_lv1casetypecode", "title", "new_caselevel", "new_country_id", "new_l1casetype_id"));

                if (incident == null)
                {
                    throw new InvalidPluginExecutionException("未根据ID获取到案例数据！");
                }

                var new_caselevelcode = incident.GetAttributeValue<string>("new_caselevelcode");
                var new_casecode = incident.GetAttributeValue<string>("new_casecode");
                var new_lv1casetypecode = incident.GetAttributeValue<string>("new_lv1casetypecode");
                var title = incident.GetAttributeValue<string>("title");
                var new_caselevel = incident.GetAttributeValue<OptionSetValue>("new_caselevel");
                var countryId = incident.GetAttributeValue<EntityReference>("new_country_id");
                var l1casetypeId = incident.GetAttributeValue<EntityReference>("new_l1casetype_id");

                Asserts.IsTrue(countryId == null || countryId.Id == Guid.Empty, "案例国家ID为空！");
                Asserts.IsTrue(l1casetypeId == null || l1casetypeId.Id == Guid.Empty, "案例一级案例类型ID为空！");

                if (new_caselevel == null || new_caselevel.Value <= 0)
                {
                    throw new InvalidPluginExecutionException("案例等级为空！ID为：" + incidentId);
                }
                var caselevelbValue = new_caselevel.Value;
                #endregion 2、获取数据

                #region 3、校验Code值
                if (string.IsNullOrWhiteSpace(new_casecode))
                {
                    var country = OrganizationService.Retrieve("new_country", countryId.Id, new ColumnSet("new_code"));
                    Asserts.IsTrue(country == null, "未根据国家ID获取到国家数据！1");
                    new_casecode = country.GetAttributeValue<string>("new_code");
                }
                if (string.IsNullOrWhiteSpace(new_lv1casetypecode))
                {
                    var l1casetype = OrganizationService.Retrieve("new_l1casetype", l1casetypeId.Id, new ColumnSet("new_typename"));
                    Asserts.IsTrue(l1casetype == null, "未根据一级案例类型ID获取到数据！");
                    new_lv1casetypecode = l1casetype.GetAttributeValue<string>("new_typename");
                }
                if (caselevelbValue == 2 && string.IsNullOrWhiteSpace(new_caselevelcode))
                {
                    new_caselevelcode = "WO";
                }
                #endregion 3、校验Code值

                #region 4、校验数据是否需要清洗
                bool isClean = false;
                if (string.IsNullOrWhiteSpace(title))
                {
                    throw new InvalidPluginExecutionException("案例编号为空！ID为：" + incidentId);
                }

                if ((caselevelbValue == 2 && title.Length != 16) || (caselevelbValue == 1 && title.Length != 14))
                {
                    isClean = true;
                }
                #endregion 4、校验数据是否需要清洗

                #region 5、更新数据
                Entity entity = new Entity("incident");
                entity.Id = new Guid(incidentId);
                if (isClean)
                {
                    var newCode = title.Substring(title.Length - 10);
                    var newTicketnumber = new_caselevelcode + new_casecode + new_lv1casetypecode + newCode;
                    entity["title"] = newTicketnumber;
                    entity["new_casecode"] = new_casecode;
                    entity["new_caselevelcode"] = new_caselevelcode;
                    entity["new_lv1casetypecode"] = new_lv1casetypecode;
                }
                entity["new_iscleaned"] = true;
                OrganizationServiceAdmin.Update(entity);
                #endregion 5、更新数据
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 无缓存取系统参数
        /// </summary>
        /// <param name="key"></param>
        ///         /// <param name="organizationService"></param>
        public string GetParameter(string key, IOrganizationService organizationService)
        {
            try
            {
                string para = "";
                QueryExpression qe = new QueryExpression("new_systemparameter");
                qe.NoLock = true;
                qe.ColumnSet = new ColumnSet("new_systemparameterid", "new_value");
                qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, key);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var paraList = organizationService.RetrieveMultiple(qe);
                if (paraList != null && paraList.Entities != null && paraList.Entities.Count > 0)
                {
                    para = paraList.Entities[0].GetAttributeValue<string>("new_value");
                }
                return para;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 获取投诉提醒列表数据
        /// </summary>
        /// <param name="organizationService"></param>
        /// <returns></returns>
        public List<LogisticsComplaintRemindModel> GetComplaintEmailConfigList(string countryId, int type)
        {
            //查询投诉邮箱配置表
            try
            {
                var fetchXml = $@"<fetch>
                                  <entity name='new_complaintemailconfig'>
                                    <attribute name='new_country' />
                                    <attribute name='new_complaintemailconfigid' />
                                    <attribute name='new_emailtmpl' />
                                    <attribute name='new_operation' />
                                    <attribute name='new_cc' />
                                    <attribute name='new_prodcuttype' />
                                    <attribute name='new_logisticsprovider' />
                                    <attribute name='new_serviceprovider' />
                                    <attribute name='new_type' />
                                    <filter>
                                      <condition attribute='new_country' operator='eq' value='{countryId}' />
                                      <condition attribute='new_type' operator='eq' value='{type}' />
                                    </filter>
                                  </entity>
                                </fetch>";
                EntityCollection ec = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(new FetchExpression(fetchXml));
                List<LogisticsComplaintRemindModel> list = new List<LogisticsComplaintRemindModel>();
                if (ec != null && ec.Entities != null && ec.Entities.Count > 0)
                {
                    //获取类型标签列表
                    var typeLabelList = GetLabels("new_complaintemailconfig", "new_type");
                    //获取产品类型标签列表
                    var productTypeLabelList = GetLabels("new_complaintemailconfig", "new_prodcuttype");
                    //获取当前用户的语言环境
                    var LanguageCode = GetUserUILanguageCode(OrganizationServiceAdmin, this.UserId);

                    foreach (var item in ec.Entities)
                    {
                        LogisticsComplaintRemindModel model = new LogisticsComplaintRemindModel();
                        model.id = item.Id.ToString();
                        model.new_country = item.ToEr("new_country").Name;
                        model.new_type = typeLabelList.OptionSet.Options.Where(p => p.Value == item.GetAttributeValue<OptionSetValue>("new_type").Value)
                            .FirstOrDefault()?.Label.LocalizedLabels.Where(p => p.LanguageCode == LanguageCode).FirstOrDefault()?.Label;
                        var prodcutType = item.GetAttributeValue<OptionSetValue>("new_prodcuttype").Value;
                        if (prodcutType == 3)
                        {
                            model.new_prodcuttype = "All Product";
                        }
                        else
                        {
                            model.new_prodcuttype = productTypeLabelList.OptionSet.Options.Where(p => p.Value == prodcutType)
                            .FirstOrDefault()?.Label.LocalizedLabels.Where(p => p.LanguageCode == LanguageCode).FirstOrDefault()?.Label;
                        }

                        model.new_serviceprovider = item.ToStr("new_serviceprovider");
                        model.new_logisticsprovider = item.ToStr("new_logisticsprovider");
                        model.new_emailtmpl = item.ToEr("new_emailtmpl").Name;
                        model.new_emailtmplid = item.ToEr("new_emailtmpl").Id.ToString();
                        if (item.Contains("new_operation"))//联系人
                        {
                            List<ContactModel> operation = JsonConvert.DeserializeObject<List<ContactModel>>(item.ToStr("new_operation"));
                            model.new_operationList = operation;
                            if (string.IsNullOrWhiteSpace(operation[0]._etn))
                            {
                                model.new_operation = string.Join("，", operation.Select(p => p.name).ToArray());
                            }
                            else
                            {
                                model.new_operation = string.Join("，", operation.Select(p => p._name).ToArray());
                            }
                        }
                        if (item.Contains("new_cc"))//抄送人
                        {
                            List<ContactModel> cc = JsonConvert.DeserializeObject<List<ContactModel>>(item.ToStr("new_cc"));
                            model.new_ccList = cc;
                            if (cc.Count > 0 && string.IsNullOrWhiteSpace(cc[0]._etn))
                            {
                                model.new_cc = string.Join("，", cc.Select(p => p.name).ToArray());
                            }
                            else
                            {
                                model.new_cc = string.Join("，", cc.Select(p => p._name).ToArray());
                            }

                        }
                        list.Add(model);
                    }
                }
                return list;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }

        }
        /// <summary>
        /// 获取当前用户的当前语言环境
        /// </summary>
        /// <param name="service"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static int GetUserUILanguageCode(IOrganizationService service, Guid userId)
        {
            QueryExpression userSettingsQuery = new QueryExpression("usersettings");
            userSettingsQuery.ColumnSet.AddColumns("uilanguageid", "systemuserid");
            userSettingsQuery.Criteria.AddCondition("systemuserid", ConditionOperator.Equal, userId);
            EntityCollection userSettings = service.RetrieveMultiple(userSettingsQuery);
            if (userSettings.Entities.Count > 0)
            {
                return (int)userSettings.Entities[0]["uilanguageid"];
            }
            return 2052;
        }
        public PicklistAttributeMetadata GetLabels(string entityName, string colName)
        {
            //获取类型标签值
            RetrieveAttributeRequest attributeRequest = new RetrieveAttributeRequest
            {
                EntityLogicalName = entityName, // 替换为你的实体名
                LogicalName = colName // 替换为你的字段名
            };
            RetrieveAttributeResponse attributeResponse = (RetrieveAttributeResponse)OrganizationService.Execute(attributeRequest);
            // 获取 OptionSet 元数据
            PicklistAttributeMetadata optionSetMetadata = (PicklistAttributeMetadata)attributeResponse.AttributeMetadata;

            return optionSetMetadata;
        }
        /// <summary>
        /// 发送物流投诉提醒邮件
        /// </summary>
        /// <param name="reqStr"></param>
        /// <returns></returns>
        public LogisticsComplaintRemindResponseModel SendLogisticsComplaintRemindEmail(LogisticsComplaintRemindRequestModel requestModel)
        {
            LogisticsComplaintRemindResponseModel responseModel = new LogisticsComplaintRemindResponseModel();
            try
            {
                responseModel.message = GetResource("incident.logistics_complaint_emailreceiving", "邮件正在发送中，发送结果请留意消息提醒或邮件状态！");
                responseModel.success = true;
                #region 1.参数校验
                var checkResult = CheckReqParams(requestModel.remindList);
                if (!string.IsNullOrWhiteSpace(checkResult))
                {
                    responseModel.message = checkResult;
                    responseModel.success = false;
                    return responseModel;
                }
                #endregion

                #region 2.查询案例信息
                Entity incidentEntity = OrganizationService.Retrieve("incident", Guid.Parse(requestModel.incidentId), new ColumnSet("new_progresslogisticscomplaint", "customerid", "new_l3casetype_id", "new_country_id", "new_probdescrib", "new_ismi", "new_miordercode", "new_ordercode"));
                if (incidentEntity == null)
                {
                    responseModel.message = GetResource("incident.logistics_complaint_email_fail", "发送邮件失败！");
                    responseModel.success = false;
                    return responseModel;
                }
                #endregion

                #region 3.从获取发件人邮箱
                var sendEmailNo = OrganizationServiceAdmin.GetSystemParamValue("Incident.RemindEmailNo", true);
                //校验发件人邮箱
                if (string.IsNullOrWhiteSpace(sendEmailNo))
                {
                    responseModel.message = GetResource("incident.logistics_complaint_sendemailno_error", "未配置发件箱，请联系米小助！");
                    responseModel.success = false;
                    return responseModel;
                }
                #endregion

                #region 4.获取发送邮件相关信息
                List<string> contactIds = new List<string>();//联系人id
                List<string> toIds = new List<string>();//收件id
                List<string> ccIds = new List<string>();//抄送人id
                //获取所有联系人的Id
                foreach (var item in requestModel.remindList)
                {
                    if (item != null && item.new_operationList != null && item.new_operationList.Count > 0)
                    {
                        List<string> recipientIds = new List<string>();
                        if (string.IsNullOrWhiteSpace(item.new_operationList[0]._id))
                        {
                            recipientIds = item.new_operationList.Select(p => p.id.Replace("{", "").Replace("}", "").ToLower()).ToList();
                        }
                        else
                        {
                            recipientIds = item.new_operationList.Select(p => p._id?.Replace("{", "").Replace("}", "").ToLower()).ToList();
                        }
                        toIds.AddRange(recipientIds);
                        contactIds.AddRange(recipientIds);
                    }
                    if (item != null && item.new_ccList != null && item.new_ccList.Count > 0)
                    {
                        List<string> ccIdList = new List<string>();
                        if (string.IsNullOrWhiteSpace(item.new_ccList[0]._id))
                        {
                            ccIdList = item.new_ccList.Select(p => p.id.Replace("{", "").Replace("}", "").ToLower()).ToList();
                        }
                        else
                        {
                            ccIdList = item.new_ccList.Select(p => p._id.Replace("{", "").Replace("}", "").ToLower()).ToList();
                        }
                        ccIds.AddRange(ccIdList);
                        contactIds.AddRange(ccIdList);
                    }
                }
                var allEmailList = GetRecipientEmailList(contactIds);
                //获取历史邮件主题
                var hisSubject = GetHisEmailSub(incidentEntity.Id);
                //查询案例附件
                EntityCollection attEc = GetIncidentAtt(incidentEntity.Id);
                //获取类型标签列表
                var typeLabelList = GetLabels("new_complaintemailconfig", "new_type");

                //获取邮件模板
                var emailTmp = requestModel.remindList.Where(p => !string.IsNullOrWhiteSpace(p.new_emailtmplid)).FirstOrDefault();
                var emailTmpId = emailTmp != null ? emailTmp.new_emailtmplid : "";
                if (string.IsNullOrEmpty(emailTmpId))
                {
                    responseModel.message = "邮件模板错误";
                    responseModel.success = false;
                }
                EmailRecordModel tmpModel = GetEmailTmpl(emailTmpId);
                //获取收件人邮箱
                var recipientEmailList = allEmailList.Where(p => toIds.Contains(p.Id.ToString().ToLower())).Select(p => p.ToStr("emailaddress1")).ToList();
                if (recipientEmailList == null || recipientEmailList.Count() == 0)
                {
                    responseModel.message = GetResource("incident.logistics_complaint_receiver_error", "收件人邮箱错误，请联系米小助检查投诉邮箱配置！");
                    responseModel.success = false;
                }
                //获取抄送人邮箱
                var ccEmailList = allEmailList.Where(p => ccIds.Contains(p.Id.ToString().ToLower())).Select(p => p.ToStr("emailaddress1")).ToList();
                #endregion

                #region 5.发送邮件
                var success_send = SendEmail(attEc, incidentEntity, tmpModel, recipientEmailList.ToList(), ccEmailList, typeLabelList, requestModel.type, sendEmailNo, hisSubject);
                if (success_send == false)
                {
                    responseModel.message = GetResource("incident.logistics_complaint_email_fail", "发送邮件失败！");
                    responseModel.success = true;
                }
                #endregion
            }
            catch (Exception ex)
            {
                responseModel.message = $"{GetResource("incident.logistics_complaint_email_fail", "发送邮件失败！")}{ex}";
                responseModel.success = false;
            }
            return responseModel;
        }
        /// <summary>
        /// 获取历史邮件主题
        /// </summary>
        /// <param name="incidentId"></param>
        /// <returns></returns>
        private string GetHisEmailSub(Guid incidentId)
        {
            //查询历史邮件
            QueryExpression query_hisEmail = new QueryExpression("email");
            query_hisEmail.ColumnSet.AddColumns("subject", "description", "to", "from");
            query_hisEmail.Criteria.AddCondition("regardingobjectid", ConditionOperator.Equal, incidentId);
            query_hisEmail.Criteria.AddCondition("new_emailresource", ConditionOperator.Equal, 1);
            query_hisEmail.AddOrder("createdon", OrderType.Descending);
            EntityCollection ec_hisEmail = OrganizationService.RetrieveMultiple(query_hisEmail);
            Entity entity_hisEmail = ec_hisEmail.Entities.FirstOrDefault();
            var hisSubject = entity_hisEmail != null ? entity_hisEmail.GetAttributeValue<string>("subject") : "";
            return hisSubject;
        }
        //查询案例附件
        private EntityCollection GetIncidentAtt(Guid incidentId)
        {
            QueryExpression query = new QueryExpression();
            query.EntityName = "annotation";
            FilterExpression filter = new FilterExpression(LogicalOperator.And);
            filter.AddCondition("objectid", ConditionOperator.Equal, incidentId);
            query.Criteria.AddFilter(filter);
            query.ColumnSet.AddColumns("subject", "filename", "mimetype", "notetext", "documentbody", "isdocument");
            EntityCollection attEc = OrganizationService.RetrieveMultiple(query);
            return attEc;
        }

        /// <summary>
        /// 获取邮件模板信息
        /// </summary>
        /// <param name="new_emailtmplid"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public EmailRecordModel GetEmailTmpl(string new_emailtmplid)
        {
            try
            {
                Entity entity = OrganizationServiceAdmin.Retrieve("new_tmpl_mail", Guid.Parse(new_emailtmplid), new ColumnSet("new_code", "new_description", "new_type", "new_topic", "new_memo", "new_content"));
                if (entity != null)
                {
                    #region 组装邮件信息
                    EmailRecordModel emailTemp = new EmailRecordModel();
                    //邮件正文
                    if (string.IsNullOrEmpty(entity.GetAttributeValue<string>("new_memo")))
                    {
                        emailTemp.new_mainbody = entity.GetAttributeValue<string>("new_content");
                    }
                    else
                    {
                        emailTemp.new_mainbody = entity.GetAttributeValue<string>("new_memo");
                    }
                    emailTemp.new_mainbody = GetResource("incident.logistics_complaint_emailtpl", emailTemp.new_mainbody);
                    //邮件主题
                    emailTemp.new_theme = GetResource("incident.logistics_complaint_emailsubject", entity.GetAttributeValue<string>("new_topic"));
                    //邮件记录id
                    emailTemp.new_emailid = entity.Id;
                    #endregion
                    return emailTemp;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(GetResource("incident.logistics_complaint_emailtpl_error", "邮件模板错误，请联系米小助检查投诉邮件模板！") + ex.Message);
            }

        }
        /// <summary>
        /// 获取收件人/抄送人邮箱
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public List<Entity> GetRecipientEmailList(List<string> contactIds)
        {
            QueryExpression query_recipient = new QueryExpression("contact");
            query_recipient.ColumnSet.AddColumns("emailaddress1");
            query_recipient.Criteria.AddCondition("contactid", ConditionOperator.In, contactIds.ToArray());
            EntityCollection recipientEntity = OrganizationServiceAdmin.RetrieveMultiple(query_recipient);
            if (recipientEntity == null || recipientEntity.Entities == null || recipientEntity.Entities.Count <= 0)
            {
                return new List<Entity>();
            }
            return recipientEntity.Entities.Where(p => p.Contains("emailaddress1")).ToList();
        }
        /// <summary>
        /// 参数校验
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public string CheckReqParams(List<LogisticsComplaintRemindModel> param)
        {
            var result = "";
            if (param.Count <= 0)
            {
                result = GetResource("incident.logistics_complaint_select", "请至少选择一条记录！");
            }
            foreach (var item in param)
            {
                //校验邮箱配置
                if (string.IsNullOrWhiteSpace(item.id))
                {
                    result = GetResource("incident.logistics_complaint_notfoundemail", "未找到投诉邮箱id，请联系米小助检查投诉邮箱配置！");
                    continue;
                }
                //校验邮件模板
                if (string.IsNullOrWhiteSpace(item.new_emailtmplid))
                {
                    result = GetResource("incident.logistics_complaint_emailtpl_error", "邮件模板错误，请联系米小助检查投诉邮件模板！");
                    continue;
                }
                //校验收件人
                if (item.new_operationList.Count <= 0)
                {
                    result = GetResource("incident.logistics_complaint_receiver_error", "收件人邮箱错误，请联系米小助检查投诉邮箱配置！");
                    continue;
                }
            }
            return result;
        }

        public bool SendEmail(EntityCollection attEc, Entity incidentEntity, EmailRecordModel emailTmp, List<string> recipientEmailList, List<string> ccEmailList, PicklistAttributeMetadata typeLabelList, int type, string sendEmailNo, string hisSubject)
        {
            try
            {
                #region 创建邮件
                //获取类型标签列表
                #region 组装邮件
                //收件人
                var receiver = string.Join(";", recipientEmailList);
                emailTmp.new_recipients = receiver;
                //邮件主题
                var typeLabel = typeLabelList.OptionSet.Options.Where(p => p.Value == type)
                            .FirstOrDefault()?.Label.LocalizedLabels.Where(p => p.LanguageCode == System.Threading.Thread.CurrentThread.CurrentCulture.LCID).FirstOrDefault()?.Label;
                var subject = string.IsNullOrWhiteSpace(hisSubject) ? string.Format(emailTmp.new_theme, typeLabel, incidentEntity.ToEr("new_country_id").Name) : hisSubject;
                //邮件内容
                var l3CaseType = incidentEntity.ToEr("new_l3casetype_id").Name;//案例三级分类类型
                var customer = incidentEntity.ToEr("customerid").Name;//客户名称
                var country = incidentEntity.ToEr("new_country_id").Name;//国家
                var probdescrib = incidentEntity.ToStr("new_probdescrib");//问题描述
                var orderNo = incidentEntity.GetAttributeValue<bool>("new_ismi") ? incidentEntity.ToStr("new_miordercode") : incidentEntity.ToStr("new_ordercode");//订单号
                var body = string.Format(emailTmp.new_mainbody, l3CaseType, customer, country, l3CaseType, probdescrib, orderNo);
                Log.InfoMsg($"收件人：{receiver}；主题：{subject}；正文：{body}。");
                #endregion

                Entity emailEntity = new Entity("email");

                #region 发件人
                Entity fromEntity = new Entity("activityparty");
                fromEntity["partyid"] = new EntityReference("queue", Guid.Parse(sendEmailNo));
                emailEntity["from"] = new Entity[] { fromEntity };
                #endregion

                #region 收件人
                List<Entity> newtoPartyList = new List<Entity>();
                foreach (var item in recipientEmailList)
                {
                    Entity toEntity = new Entity("activityparty");
                    toEntity["participationtypemask"] = new OptionSetValue(2);
                    toEntity["addressused"] = item;
                    newtoPartyList.Add(toEntity);
                }
                emailEntity["to"] = newtoPartyList.ToArray();
                #endregion

                #region 抄送人
                List<Entity> ccPartyList = new List<Entity>();
                foreach (var item in ccEmailList)
                {
                    Entity toEntity = new Entity("activityparty");
                    toEntity["participationtypemask"] = new OptionSetValue(2);
                    toEntity["addressused"] = item;
                    ccPartyList.Add(toEntity);
                }
                emailEntity["cc"] = ccPartyList.ToArray();
                #endregion

                emailEntity["new_emailresource"] = new OptionSetValue(1);//邮件来源为物流投诉
                emailEntity["subject"] = subject;
                emailEntity["description"] = body;
                emailEntity["regardingobjectid"] = new EntityReference("incident", incidentEntity.Id);
                Guid emailid = OrganizationService.Create(emailEntity);
                #endregion

                #region 添加附件
                if (attEc != null && attEc.Entities != null && attEc.Entities.Count > 0)
                {
                    foreach (var item in attEc.Entities)
                    {
                        //创建邮件附件
                        Entity _EmailAttachment = new Entity("activitymimeattachment");
                        if (item.Contains("subject"))
                            _EmailAttachment["subject"] = item.GetAttributeValue<string>("subject");
                        _EmailAttachment["objectid"] = new EntityReference("email", emailid);
                        _EmailAttachment["objecttypecode"] = "email";
                        if (item.Contains("filename"))
                            _EmailAttachment["filename"] = item.GetAttributeValue<string>("filename");
                        if (item.Contains("documentbody"))
                            _EmailAttachment["body"] = item.GetAttributeValue<string>("documentbody");
                        if (item.Contains("mimetype"))
                            _EmailAttachment["mimetype"] = item.GetAttributeValue<string>("mimetype");

                        OrganizationService.Create(_EmailAttachment);
                    }
                }
                #endregion

                #region 发送邮件
                SendEmailRequest sendEmailRequest = new SendEmailRequest
                {
                    EmailId = emailid,
                    TrackingToken = string.Empty,
                    IssueSend = true
                };
                OrganizationService.Execute(sendEmailRequest);
                #endregion

                #region 修改案例的物流投诉进度
                //获取案例的物流投诉进度
                OptionSetValueCollection optionSetValues = new OptionSetValueCollection();
                if (incidentEntity.Contains("new_progresslogisticscomplaint"))
                {
                    optionSetValues = (OptionSetValueCollection)incidentEntity["new_progresslogisticscomplaint"];
                }
                if (optionSetValues.Count == 0 || optionSetValues.Where(p => p.Value == type).Count() == 0)
                {
                    optionSetValues.Add(new OptionSetValue(type));  // 选项1
                }

                Entity entity_incident = new Entity("incident");
                entity_incident.Id = incidentEntity.Id;
                entity_incident["new_progresslogisticscomplaint"] = optionSetValues;
                OrganizationService.Update(entity_incident, true);
                #endregion

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(GetResource("incident.logistics_complaint_email_fail", "发送邮件失败！") + ex);
            }
        }
        /// <summary>
        /// 获取相同订单号的案例数据
        /// </summary>
        /// <param name="orderNo"></param>
        /// <param name="type"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public IncidentAssignResponseModel GetIncidentByOrderNo(string orderNo, int type, int pageIndex, int pageSize)
        {
            QueryExpression query = new QueryExpression("incident");
            query.ColumnSet.AddColumns("title", "new_casestatus", "ownerid");
            query.Criteria.AddCondition("new_miordercode", ConditionOperator.Equal, orderNo);
            query.Criteria.AddCondition(new ConditionExpression("new_channel", ConditionOperator.Equal, 5));//来访渠道为订单
            //type=1为批量分派，type=2为受理并保存。批量分派查询“待处理”和“处理中”的案例，受理并保存只查询“待处理”的案例
            if (type == 1)
            {
                query.Criteria.AddCondition(new ConditionExpression("new_casestatus", ConditionOperator.In, 1, 2));
            }
            else
            {
                query.Criteria.AddCondition(new ConditionExpression("new_casestatus", ConditionOperator.In, 1));
            }
            query.AddOrder("createdon", OrderType.Descending);
            query.PageInfo = new PagingInfo()
            {
                Count = pageSize,
                PageNumber = pageIndex
            };
            EntityCollection ec_incident = OrganizationService.RetrieveMultiple(query, true);

            //获取案例状态列表
            var typeLabelList = GetLabels("incident", "new_casestatus");
            //获取当前用户的语言环境
            var LanguageCode = GetUserUILanguageCode(OrganizationServiceAdmin, this.UserId);

            List<IncidentAssignDataModel> incidentAssignDatas = new List<IncidentAssignDataModel>();
            foreach (Entity entity in ec_incident.Entities)
            {
                IncidentAssignDataModel incidentAssignData = new IncidentAssignDataModel();
                incidentAssignData.title = entity.ToStr("title");
                incidentAssignData.caseStatus = typeLabelList.OptionSet.Options.Where(p => p.Value == entity.ToOpInt("new_casestatus"))
                            .FirstOrDefault()?.Label.LocalizedLabels.Where(p => p.LanguageCode == LanguageCode).FirstOrDefault()?.Label; ;
                incidentAssignData.ownerName = entity.ToEr("ownerid").Name;
                incidentAssignDatas.Add(incidentAssignData);
            }
            IncidentAssignResponseModel incidentAssignResponse = new IncidentAssignResponseModel();
            incidentAssignResponse.incidentData = incidentAssignDatas;
            incidentAssignResponse.moreRecords = ec_incident.MoreRecords;

            return incidentAssignResponse;
        }

        /// <summary>
        /// 批量分派案例
        /// </summary>
        /// <param name="curIncidentId">当前案例</param>
        /// <param name="orderNo">其它案例</param>
        /// <param name="userId">分派人</param>
        /// <param name="type">类型 1.批量分派，2.受理并保存</param>
        /// <returns></returns>
        public string BatchAssignIncident(string curIncidentId, string orderNo, string userId)
        {
            try
            {
                Log.InfoMsg($"curIncidentId:{curIncidentId};orderNo:{orderNo};userId:{userId}");
                #region 数据校验
                if (string.IsNullOrWhiteSpace(curIncidentId))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "curIncidentId"));
                }
                if (string.IsNullOrWhiteSpace(orderNo))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "orderNo"));
                }
                if (string.IsNullOrWhiteSpace(userId))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "userId"));
                }
                #endregion 数据校验

                #region 分派当前案例
                AssignRequest assign = new AssignRequest()
                {
                    Target = new EntityReference("incident", Guid.Parse(curIncidentId)),
                    Assignee = new EntityReference("systemuser", Guid.Parse(userId))
                };
                OrganizationService.Execute(assign);
                Log.InfoMsg($"分派当前案例成功，当前案例id：{curIncidentId}");
                #endregion 分派当前案例

                #region 分派与订单相关的其它案例
                QueryExpression query_incident = new QueryExpression("incident");
                query_incident.ColumnSet.AddColumns("title", "new_assignnum");
                query_incident.Criteria.AddCondition("new_miordercode", ConditionOperator.Equal, orderNo);
                query_incident.Criteria.AddCondition(new ConditionExpression("new_casestatus", ConditionOperator.In, 1, 2));//订单状态为1.待处理，2.处理中
                query_incident.Criteria.AddCondition(new ConditionExpression("new_caselevel", ConditionOperator.Equal, 2));//案例等级为二线案例
                query_incident.Criteria.AddCondition(new ConditionExpression("new_channel", ConditionOperator.Equal, 5));//来访渠道为订单
                query_incident.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                EntityCollection ec_incident = Helper.QueryPageHelper.QueryExpressionPage(OrganizationServiceAdmin, query_incident);

                IncidentServiceBusReqModel incidentServiceBusReq = new IncidentServiceBusReqModel();
                foreach (Entity entity in ec_incident.Entities)
                {
                    int new_assignnum = entity.GetAttributeValue<int?>("new_assignnum") ?? 0;

                    //如果是当前案例，则修改当前案例数据
                    if (entity.Id.ToString().ToUpper() == curIncidentId.ToUpper())
                    {
                        entity["new_assigninfo"] = "FirstAssign";//分派信息
                        entity["new_assigntime"] = DateTime.UtcNow;//分派时间
                        entity["new_assignnum"] = new_assignnum + 1;//分派次数
                        OrganizationService.Update(entity);
                    }
                    else
                    {
                        incidentServiceBusReq.incidentId = entity.Id.ToString();
                        incidentServiceBusReq.userId = userId;
                        incidentServiceBusReq.type = 1;//操作类型为批量分派
                        incidentServiceBusReq.assignNum = new_assignnum + 1;//分派次数
                        //推送消息到servicebus
                        string message = JsonConvert.SerializeObject(incidentServiceBusReq, Formatting.Indented);
                        CrmHelper.SendToServiceBus(null, OrganizationServiceAdmin, message, "XM_BatchAssign_ServiceBus");
                    }
                }
                Log.InfoMsg($"共{ec_incident.Entities?.Count()}个案例发送servicebus成功,当前案例id：{curIncidentId}");
                #endregion
                return "ok";
            }
            catch (Exception ex)
            {
                throw new Exception("分派案例失败:" + ex);
            }
        }

        //批量受理并保存案例
        public string BatchAcceptIncident(string curIncidentId, string orderNo, string userId)
        {
            try
            {
                Log.InfoMsg($"curIncidentId:{curIncidentId};orderNo:{orderNo};userId:{userId}");
                #region 数据校验
                if (string.IsNullOrWhiteSpace(curIncidentId))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "curIncidentId"));
                }
                if (string.IsNullOrWhiteSpace(orderNo))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "orderNo"));
                }
                if (string.IsNullOrWhiteSpace(userId))
                {
                    throw new Exception(string.Format(GetResource("incident.param.IsNullOrWhiteSpace", "参数：{0}不能为空"), "userId"));
                }
                #endregion 数据校验
                //查询当前案例信息
                Entity curIncident = OrganizationServiceAdmin.Retrieve("incident", Guid.Parse(curIncidentId), new ColumnSet("createdon", "new_assignnum"));
                curIncident["new_casestatus"] = new OptionSetValue((int)caseStatus.processing);
                curIncident["new_assigninfo"] = "FirstAssign";//分派信息
                curIncident["new_assigntime"] = DateTime.UtcNow;//分派时间
                curIncident["ownerid"] = new EntityReference("systemuser", Guid.Parse(userId));//负责人

                int new_assignnum = curIncident.GetAttributeValue<int?>("new_assignnum") ?? 0;
                curIncident["new_assignnum"] = new_assignnum + 1;//分派次数
                var curDateTime = DateTime.UtcNow;
                curIncident["new_acceptime"] = curDateTime;
                //若当前时间-创建时间>24小时，则“是否响应超时”为是
                if (curDateTime.Subtract(curIncident.ToDateTime("createdon")).TotalHours > 24)
                {
                    curIncident["new_isrespovertime"] = true;
                }
                else
                {
                    curIncident["new_isrespovertime"] = false;
                }
                OrganizationService.Update(curIncident);

                Log.InfoMsg($"受理并保存当前案例成功，当前案例id：{curIncidentId}");

                //查询与订单相关的案例
                QueryExpression query_incident = new QueryExpression("incident");
                query_incident.ColumnSet.AddColumns("createdon", "new_assignnum");
                query_incident.Criteria.AddCondition("new_miordercode", ConditionOperator.Equal, orderNo);
                query_incident.Criteria.AddCondition("incidentid", ConditionOperator.NotEqual, Guid.Parse(curIncidentId));
                query_incident.Criteria.AddCondition(new ConditionExpression("new_casestatus", ConditionOperator.In, 1));//案例状态为待处理
                query_incident.Criteria.AddCondition(new ConditionExpression("new_caselevel", ConditionOperator.Equal, 2));//案例级别为二线案例
                query_incident.Criteria.AddCondition(new ConditionExpression("new_channel", ConditionOperator.Equal, 5));//来访渠道为订单
                query_incident.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                EntityCollection ec_incident = Helper.QueryPageHelper.QueryExpressionPage(OrganizationServiceAdmin, query_incident);

                foreach (Entity entity in ec_incident.Entities)
                {
                    new_assignnum = entity.GetAttributeValue<int?>("new_assignnum") ?? 0;
                    IncidentServiceBusReqModel incidentServiceBusReq = new IncidentServiceBusReqModel();
                    incidentServiceBusReq.incidentId = entity.Id.ToString();
                    incidentServiceBusReq.userId = userId;
                    incidentServiceBusReq.assignNum = new_assignnum + 1;
                    incidentServiceBusReq.type = 2;//操作类型为批量受理并保存
                    incidentServiceBusReq.createdon = entity.GetAttributeValue<DateTime>("createdon");
                    //推送消息到servicebus
                    string message = JsonConvert.SerializeObject(incidentServiceBusReq, Formatting.Indented);
                    CrmHelper.SendToServiceBus(null, OrganizationServiceAdmin, message, "XM_BatchAssign_ServiceBus");
                }
                Log.InfoMsg($"共{ec_incident.Entities?.Count()}个案例发送servicebus成功，当前案例id：{curIncidentId}");
                return "ok";
            }
            catch (Exception ex)
            {
                throw new Exception("受理并保存案例失败:" + ex);
            }

        }
    }
}
