﻿using System;
using System.Activities.Statements;
using System.Net.Http;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

namespace Rektec.ServiceOne.CC.Plugins
{
    public class appointment_update_post : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            var context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            var serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            var service = serviceFactory.CreateOrganizationService(context.UserId);
            var tracing = (ITracingService)serviceProvider.GetService(typeof(ITracingService));

            if (context.InputParameters.Contains("Target") &&
                context.InputParameters["Target"] is Entity target &&
                target.LogicalName == "appointment" &&
                target.Attributes.Contains("ownerid"))
            {
                // 查询数据库中旧数据（原始状态+owner）
                var existing = service.Retrieve("appointment", target.Id, new ColumnSet("statecode", "ownerid"));

                if (existing.Contains("statecode") &&
                    ((OptionSetValue)existing["statecode"]).Value == 1) // 1 = 已完成
                {
                    if (existing.Attributes.Contains("ownerid"))
                    {
                        // 还原原值
                        target["ownerid"] = existing["ownerid"];
                    }
                }
            }
        }
    }
}