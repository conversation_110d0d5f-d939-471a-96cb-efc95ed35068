﻿using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using New_srv_userprofile_rightslinePlugins.Model;
using XiaoMi.Crm.CS.ApiCommon.Helper;
using Microsoft.Xrm.Sdk.Query;
namespace New_srv_userprofile_rightslinePlugins.Service
{
    internal static class UserprofileRightsLineService
    {

        public static void SendToSAPNotify(Entity entity, ITracingService tracingService, IOrganizationService serviceAdmin)
        {
            try
            {
                DataModel dataModel = new DataModel();
                if (entity.TryGetAttributeValue<EntityReference>("new_userprofile_id", out EntityReference userprofileIdRef))
                {
                    var userProfileEntity = serviceAdmin.Retrieve("new_srv_userprofile", userprofileIdRef.Id,new ColumnSet("new_name"));
                    if(userProfileEntity.TryGetAttributeValue<string>("new_name",out string sn))
                    {
                        dataModel.sn = sn;
                    }
                }
                #region 通知SAP
                string queuename = "isp_notify_xms_warranty";
                string index = $"WarrantyData_{dataModel.sn}"; 
                SendNotifyHelper.SendToNotifyWarrantyData(dataModel, queuename, index, serviceAdmin);
                #endregion

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
