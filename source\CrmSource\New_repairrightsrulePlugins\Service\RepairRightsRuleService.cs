using System;
using System.Linq;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using New_repairrightsrule.Model;
using Newtonsoft.Json;
using RekTec.Crm.Common.Helper;
using XiaoMi.Crm.CS.ApiCommon.Helper;
using XiaoMi.Crm.CS.ApiCommon.Model;

namespace New_repairrightsrule.Service
{
    public class RepairRightsRuleService
    {
        private IOrganizationService _organizationService;
        
        private const string QUEUE_NAME = "isp_notify_xms_warranty_config";
        
        public RepairRightsRuleService(IOrganizationService iOrganizationService)
        {
            _organizationService = iOrganizationService;
        }
        
        public void SendRepairRightsRuleToNotify(Entity entity)
        {
            SendNotifyModel notifyModel = new SendNotifyModel();
            notifyModel.MessageType = (int)WarrantyConfigTypeEnum.RepairrightsRule;
            RepairRightsRuleModel model = new RepairRightsRuleModel();
            if (entity.Contains("new_name"))
            {
                model.policyId = int.Parse(entity.GetAttributeValue<string>("new_name"));
            }
            if (entity.TryGetAttributeValue<EntityReference>("new_repairrights_id", out EntityReference repairRightsRef))
            {
                Entity repairRightsEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_repairrights", repairRightsRef.Id, new ColumnSet("new_code"));
                if (repairRightsEntity != null && repairRightsEntity.Contains("new_code"))
                {
                    model.rightId = int.Parse(repairRightsEntity.GetAttributeValue<string>("new_code"));
                }
 
                QueryExpression query = new QueryExpression("new_repairrightsline");
                query.Criteria.AddCondition("new_repairrights_id", ConditionOperator.Equal, repairRightsRef.Id);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                EntityCollection repairRightsLineCollection = _organizationService.RetrieveMultiple(query);
                if (!repairRightsLineCollection.Entities.Any())
                {
                    return; // 没有权益明细不推
                }
            }
            else
            {
                return; // 没关联权益不推
            }
            if (entity.Contains("new_brand_id"))
            {
                Entity brandEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_brand", entity.GetAttributeValue<EntityReference>("new_brand_id").Id, new ColumnSet("new_code"));
                if (brandEntity != null && brandEntity.Contains("new_code"))
                {
                    model.brandId = int.Parse(brandEntity.GetAttributeValue<string>("new_code"));
                }
            }
            if (entity.Contains("new_region_id"))
            {
                Entity regionEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_region", entity.GetAttributeValue<EntityReference>("new_region_id").Id, new ColumnSet("new_code"));
                if (regionEntity != null && regionEntity.Contains("new_code"))
                {
                    model.regionId = regionEntity.GetAttributeValue<string>("new_code");
                }
            }
            if (entity.Contains("new_country_id"))
            {
                Entity countryEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_country", entity.GetAttributeValue<EntityReference>("new_country_id").Id, new ColumnSet("new_id"));
                if (countryEntity != null && countryEntity.Contains("new_id"))
                {
                    model.countryId = int.Parse(countryEntity.GetAttributeValue<string>("new_id"));
                }
            }
            if (entity.Contains("new_province_id"))
            {
                Entity provinceEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_province", entity.GetAttributeValue<EntityReference>("new_province_id").Id, new ColumnSet("new_id"));
                if (provinceEntity != null && provinceEntity.Contains("new_id"))
                {
                    model.provinceId = int.Parse(provinceEntity.GetAttributeValue<string>("new_id"));
                }
            }
            if (entity.Contains("new_goodsfiles_id"))
            {
                Entity category1Entity = _organizationService.RetrieveWithBypassPluginAndFlow("new_goodsfiles", entity.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id, new ColumnSet("new_commoditycode"));
                if (category1Entity != null && category1Entity.Contains("new_commoditycode"))
                {
                    model.goodsId = category1Entity.GetAttributeValue<string>("new_commoditycode");
                }
            }
            if (entity.Contains("new_category3_id"))
            {
                Entity category3Entity = _organizationService.RetrieveWithBypassPluginAndFlow("new_category3", entity.GetAttributeValue<EntityReference>("new_category3_id").Id, new ColumnSet("new_code"));
                if (category3Entity != null && category3Entity.Contains("new_code"))
                {
                    model.category3Id = category3Entity.GetAttributeValue<string>("new_code");
                }
            }
            if (entity.Contains("new_model1_id"))
            {
                Entity model1Entity = _organizationService.RetrieveWithBypassPluginAndFlow("new_model1", entity.GetAttributeValue<EntityReference>("new_model1_id").Id, new ColumnSet("new_spm_id"));
                if (model1Entity != null && model1Entity.Contains("new_spm_id"))
                {
                    model.model1Id = model1Entity.GetAttributeValue<string>("new_spm_id");
                }
            }
            if (entity.Contains("new_model2_id"))
            {
                Entity model2Entity = _organizationService.RetrieveWithBypassPluginAndFlow("new_model2", entity.GetAttributeValue<EntityReference>("new_model2_id").Id, new ColumnSet("new_spm_id"));
                if (model2Entity != null && model2Entity.Contains("new_spm_id"))
                {
                    model.model2Id = model2Entity.GetAttributeValue<string>("new_spm_id");
                }
            }
            if (entity.Contains("new_model3_id"))
            {
                Entity model3Entity = _organizationService.RetrieveWithBypassPluginAndFlow("new_model3", entity.GetAttributeValue<EntityReference>("new_model3_id").Id, new ColumnSet("new_spm_id"));
                if (model3Entity != null && model3Entity.Contains("new_spm_id"))
                {
                    model.model3Id = model3Entity.GetAttributeValue<string>("new_spm_id");
                }
            }
            if (entity.Contains("statecode"))
            {
                OptionSetValue statecode = entity.GetAttributeValue<OptionSetValue>("statecode");
                if (statecode != null)
                {
                    model.enable = statecode.Value == 0 ? "Y" : "N";
                }
            }
            if (!entity.TryGetAttributeValue<OptionSetValue>("new_approvestatus", out OptionSetValue approveStatus) || approveStatus.Value != 3)
            {
                return;
            }
            if (entity.Contains("new_equitybydate"))
            {
                model.isSwitch = entity.GetAttributeValue<Boolean>("new_equitybydate") ? "Y" : "N";
            }
            if(entity.TryGetAttributeValue<DateTime>("new_toggledate", out DateTime dateTime))
            {
                model.switchDate = ((DateTimeOffset)dateTime).ToUnixTimeSeconds();
            }
            if (entity.Contains("new_newrepairrights_id"))
            {
                Entity switchRightEntity = _organizationService.RetrieveWithBypassPluginAndFlow("new_repairrights", entity.GetAttributeValue<EntityReference>("new_newrepairrights_id").Id, new ColumnSet("new_code"));
                if (switchRightEntity != null && switchRightEntity.Contains("new_code")&& int.TryParse(switchRightEntity.GetAttributeValue<string>("new_code"), out int rightId))
                {
                    model.switchRightId = rightId;
                }
            }
            notifyModel.MessageData = model;
            try
            {
                SendNotifyHelper.SendToNotify(notifyModel, QUEUE_NAME, model.policyId.ToString(), _organizationService);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}