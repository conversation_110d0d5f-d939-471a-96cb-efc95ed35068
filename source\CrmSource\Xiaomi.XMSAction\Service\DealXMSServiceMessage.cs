using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using Xiaomi.XMSAction.Model;
using Xiaomi.XMSAction.Helper;
using System.Text.RegularExpressions;
using System.IdentityModel.Metadata;
using System.Linq;
using static System.Collections.Specialized.BitVector32;
using System.Text.Json.Nodes;
using System.Text.Json;

namespace Xiaomi.XMSAction.Service
{
    class DealXMSServiceMessage
    {
        IOrganizationService SystemUserService;
        ITracingService log;
        XMSDataModel input;
        private const string SYSTEMPARAM = "ServiceOperate";

        public DealXMSServiceMessage(XMSDataModel input, IOrganizationService SystemUserService, ITracingService log)
        {
            this.SystemUserService = SystemUserService;
            this.log = log;
            this.input = input;
        }
        public void DealServiceStation()
        {
            try
            {
                XMSServiceStation xmsServiceStation = this.input?.xmsServiceStation ?? new XMSServiceStation();
                //pk对应的是服务网点编码
                if (!string.IsNullOrEmpty(xmsServiceStation.pk))
                {

                    #region 参数解析
                    //查询服务网点，服务网点存在则为更新消息、否则为创建消息
                    Entity station = new Entity("new_srv_station");
                    var entityQuery = new QueryExpression("new_srv_station");
                    entityQuery.ColumnSet = new ColumnSet("ownerid", "new_department");
                    entityQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    entityQuery.Criteria.AddCondition("new_code", ConditionOperator.Equal, xmsServiceStation.pk);
                    var entity = this.SystemUserService.RetrieveMultiple(entityQuery);
                    if (entity?.Entities?.Count > 0)
                    {
                        station = entity.Entities[0];
                        //判断是否为删除消息
                        if (!string.IsNullOrEmpty(xmsServiceStation.operate) && xmsServiceStation.operate == "DELETE")
                        {
                            //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                            if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, "new_srv_station", "delete"))
                            {
                                return;
                            }
                            station["statecode"] = new OptionSetValue(1);
                            SystemUserService.Update(station);
                            return;
                        }
                    }
                    if (xmsServiceStation.t_base_org == null || xmsServiceStation.t_base_org.Count == 0)
                    {
                        throw new InvalidPluginExecutionException("t_base_org数据为空");
                    }
                    TBaseOrg tBaseOrg = xmsServiceStation.t_base_org[0];

                    TBaseOrgAddress tBaseOrgAddress = xmsServiceStation.t_base_org_address.Where(x => x.type == 1).FirstOrDefault();

                    List<TBaseOrgServiceType> tBaseOrgServiceType = xmsServiceStation.t_base_org_service_type;

                    TBaseOrgCredential tBaseOrgCredential = xmsServiceStation.t_base_org_credential != null && xmsServiceStation.t_base_org_credential.Count > 0 ? xmsServiceStation.t_base_org_credential[0] : null;

                    TBaseOrgSettlement tBaseOrgSettlement = xmsServiceStation.t_base_org_settlement != null && xmsServiceStation.t_base_org_settlement.Count > 0 ? xmsServiceStation.t_base_org_settlement[0] : null;

                    TBaseOrgFinance tBaseOrgFinance = xmsServiceStation.t_base_org_finance != null && xmsServiceStation.t_base_org_finance.Count > 0 ? xmsServiceStation.t_base_org_finance[0] : null;

                    TBaseOrgExtra tBaseOrgExtra = xmsServiceStation.t_base_org_extra != null && xmsServiceStation.t_base_org_extra.Count > 0 ? xmsServiceStation.t_base_org_extra[0] : null;

                    List<BaseOrgExtend> baseOrgExtendList = xmsServiceStation.base_org_extend != null && xmsServiceStation.base_org_extend.Count > 0 ? xmsServiceStation.base_org_extend : null;
                    #endregion

                    #region 数据赋值
                    //服务网点类型
                    station["new_servicetype"] = new OptionSetValue(2);
                    if (!station.TryGetAttributeValue<OptionSetValue>("new_department", out OptionSetValue new_department))
                    {
                        //服务部门
                        station["new_department"] = new OptionSetValue(2);
                    }
                    //遍历tBaseOrgServiceType，获取对应的服务方式与服务类型值
                    if (tBaseOrgServiceType != null && tBaseOrgServiceType.Count > 0)
                    {
                        OptionSetValueCollection serviceType = new OptionSetValueCollection();
                        OptionSetValueCollection serviceMode = new OptionSetValueCollection();
                        foreach (var item in tBaseOrgServiceType)
                        {
                            if (item.type == 1)
                            {
                                if (item.service_type_key == 1 || item.service_type_key == 2 || item.service_type_key == 3)
                                {
                                    serviceType.Add(new OptionSetValue(CommonHelper.ConToInt(item.service_type_key)));
                                }
                                else if (item.service_type_key == 4 || item.service_type_key == 11)
                                {
                                    serviceType.Add(new OptionSetValue(5));//安装
                                }
                                else if (item.service_type_key == 6)
                                {
                                    serviceType.Add(new OptionSetValue(4));//检测
                                }
                                else if (item.service_type_key == 9)
                                {
                                    serviceType.Add(new OptionSetValue(6));//保养
                                }
                            }
                            else if (item.type == 2)
                            {
                                if (item.service_type_key == 1)
                                {
                                    serviceMode.Add(new OptionSetValue(2));//上门
                                }
                                else if (item.service_type_key == 3)
                                {
                                    serviceMode.Add(new OptionSetValue(3));//寄修
                                }
                                else if (item.service_type_key == 30 || item.service_type_key == 2)
                                {
                                    serviceMode.Add(new OptionSetValue(1));//到店
                                }
                                else if (item.service_type_key == 31)
                                {
                                    serviceMode.Add(new OptionSetValue(4));//代收
                                }
                            }
                        }
                        if (serviceType != null && serviceType.Count > 0)
                        {
                            //服务类型
                            station["new_srctype"] = serviceType;
                        }
                        if (serviceMode != null && serviceMode.Count > 0)
                        {
                            //服务方式
                            station["new_way"] = serviceMode;
                        }
                    }

                    string countryNmae = "";
                    string countryCode = "";
                    if (tBaseOrgAddress != null)
                    {
                        if (!string.IsNullOrEmpty(tBaseOrgAddress.manager_tel))
                        {
                            //电话
                            station["new_telephone"] = tBaseOrgAddress.manager_tel;
                            station["new_mailtel"] = tBaseOrgAddress.manager_tel;
                        }

                        if (!string.IsNullOrEmpty(tBaseOrgAddress.country.ToString()))
                        {
                            var countryQuery = new QueryExpression("new_country");
                            countryQuery.ColumnSet = new ColumnSet("new_name", "new_code");
                            countryQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            countryQuery.Criteria.AddCondition("new_id", ConditionOperator.Equal, tBaseOrgAddress.country.ToString());
                            var country = this.SystemUserService.RetrieveMultiple(countryQuery);
                            if (country?.Entities?.Count > 0)
                            {
                                //国家
                                station["new_country_id"] = new EntityReference("new_country", country.Entities[0].Id);
                                countryNmae = country.Entities[0].GetAttributeValue<string>("new_name");
                                countryCode = country.Entities[0].GetAttributeValue<string>("new_country");
                            }
                        }

                        //省份
                        var province = getEntity("new_province", "new_id", tBaseOrgAddress.province.ToString());
                        if (province != null)
                        {
                            station["new_province_id"] = province;
                        }
                        //城市
                        var city = getEntity("new_city", "new_id", tBaseOrgAddress.city.ToString());
                        if (city != null)
                        {
                            station["new_city_id"] = city;
                        }
                        //区县
                        var county = getEntity("new_county", "new_id", tBaseOrgAddress.district.ToString());
                        if (county != null)
                        {
                            station["new_county_id"] = county;
                        }
                        //街道
                        var street = getEntity("new_street", "new_id", tBaseOrgAddress.street.ToString());
                        if (street != null)
                        {
                            station["new_street_id"] = street;
                        }
                    }
                    //税率
                    if (!string.IsNullOrEmpty(tBaseOrg.rate))
                    {
                        if (decimal.TryParse(tBaseOrg.rate, out decimal rate))
                        {
                            station["new_taxrate"] = rate;
                        }
                    }
                    //邮箱
                    if (tBaseOrgCredential != null && !string.IsNullOrEmpty(tBaseOrgCredential.leader_email))
                    {
                        station["new_email"] = tBaseOrgCredential.leader_email;
                        station["new_mailowner"] = tBaseOrgCredential.leader_email;
                    }
                    //网点电话
                    station["new_networktel"] = tBaseOrg.hotline;
                    //机构名称
                    station["new_name"] = tBaseOrg.name;
                    //station["new_postalcode"] = tBaseOrg.zip_code;
                    //Sap ID（供应商）
                    station["new_supplier"] = tBaseOrg.sap_code;
                    if (tBaseOrgSettlement != null)
                    {
                        //签约主体
                        station["new_contractingbody"] = tBaseOrgSettlement.company_main_id;
                    }
                    if (tBaseOrgFinance != null)
                    {
                        //税号
                        station["new_dutyparagraph"] = tBaseOrgFinance.tax_num;
                        var payWay = CommonHelper.ConToInt(tBaseOrgFinance.pay_method);
                        if (payWay != 0)
                        {
                            //付款方式
                            station["new_payway"] = new OptionSetValue(payWay);
                        }
                    }
                    string level = tBaseOrg.org_level;
                    //销售主体
                    station["new_salesbody"] = tBaseOrg.mi_home_sap_id;
                    //服务网点级别
                    if (level == "ONE")
                    {
                        station["new_outletlevel"] = new OptionSetValue(1);
                    }
                    else if (level == "TWO")
                    {
                        station["new_outletlevel"] = new OptionSetValue(2);
                    }
                    else if (level == "THREE")
                    {
                        station["new_outletlevel"] = new OptionSetValue(3);
                    }
                    var cooperationstatus = CommonHelper.ConToInt(tBaseOrg.status);
                    //合作状态
                    if (cooperationstatus == 1 || cooperationstatus == 2 || cooperationstatus == 3)
                    {
                        station["new_cooperationstatus"] = new OptionSetValue(cooperationstatus);
                    }
                    //备件价格模板
                    var partpricetemplet = getEntity("new_srv_partpricetemplet", "new_name", tBaseOrg.price_template_id);
                    if (partpricetemplet != null)
                    {
                        station["new_pricetemplate"] = partpricetemplet;
                    }

                    if (tBaseOrgExtra != null)
                    {
                        var canappoint = CommonHelper.ConToInt(tBaseOrgExtra.reservable_flag);
                        //是否可预约
                        if (canappoint == 2)
                        {
                            station["new_iscanappoint"] = false;
                        }
                        else if (canappoint == 1)
                        {
                            station["new_iscanappoint"] = true;
                        }
                        if (!string.IsNullOrEmpty(tBaseOrgExtra.show_title)) {
                            station["new_wingwname"] = tBaseOrgExtra.show_title; 
                        }
                        if (!string.IsNullOrEmpty(tBaseOrgExtra.contact_tel))
                        {
                            station["new_branchphone"] = tBaseOrgExtra.contact_tel;
                        }
                        //是否官网显示
                        station["new_isshowingw"] = tBaseOrgExtra.official_show;
                    }
                    //英文地址
                    station["new_detailedadress"] = tBaseOrg.address;
                    //Sap标识符
                    station["new_sapid"] = tBaseOrg.sap_location;
                    if (tBaseOrg.is_mi_org == 1)
                    {
                        station["new_operationmodecode"] = new OptionSetValue(1);
                    }else if (tBaseOrg.is_mi_org == 3 || tBaseOrg.is_mi_org == 4 || tBaseOrg.is_mi_org == 5)
                    {
                        station["new_operationmodecode"] = new OptionSetValue(CommonHelper.ConToInt(tBaseOrg.is_mi_org) - 1);
                    }

                    if (baseOrgExtendList != null)
                    {
                        string salechannel = getBaseOrgExtendData("salesChannelTypes", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(salechannel) && salechannel.Trim().StartsWith("[") && salechannel.Trim().EndsWith("]"))
                        {
                            // 去除方括号并按逗号分割字符串
                            string[] numberStrings = salechannel.Trim('[').Trim(']').Split(',');

                            OptionSetValueCollection optionSetValues = new OptionSetValueCollection();
                            foreach (string numberString in numberStrings)
                            {
                                if (int.TryParse(numberString.Trim(), out int number))
                                {
                                    // 将解析出的数字添加到OptionSetValueCollection中
                                    optionSetValues.Add(new OptionSetValue(number));
                                }
                            }
                            if (numberStrings?.Count() > 0)
                            {
                                //销售渠道
                                station["new_salechannel"] = optionSetValues;
                            }
                        }
                        string standard = getBaseOrgExtendData("repairableVersion", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(standard) && standard.Trim().StartsWith("[") && standard.Trim().EndsWith("]"))
                        {
                            // 去除方括号并按逗号分割字符串
                            string[] numberStrings = standard.Trim('[').Trim(']').Split(',');

                            OptionSetValueCollection optionSetValues = new OptionSetValueCollection();
                            foreach (string numberString in numberStrings)
                            {
                                if (int.TryParse(numberString.Trim(), out int number))
                                {
                                    // 将解析出的数字添加到OptionSetValueCollection中
                                    optionSetValues.Add(new OptionSetValue(number));
                                }
                            }
                            if (numberStrings?.Count() > 0)
                            {
                                //可维修版本
                                station["new_standard"] = optionSetValues;
                            }
                        }
                        string inEntity = getBaseOrgExtendData("indonesianSubject", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(inEntity))
                        {
                            if (int.TryParse(inEntity.Trim(), out int number))
                            {
                                if (number == 1 || number == 2 || number == 3)
                                {
                                    //印尼主体
                                    station["new_entity"] = new OptionSetValue(number);
                                }
                            }

                        }
                        //是否支持增值服务
                        station["new_supportservices"] = getBaseOrgExtendDataBool("supportAddedServiceFlag", baseOrgExtendList);
                        //自动占料
                        station["new_ammaterials"] = getBaseOrgExtendDataBool("autoMaterialAllocateFlag", baseOrgExtendList);
                        //是否备件中心直发
                        station["new_ifdirectsupply"] = getBaseOrgExtendDataBool("sparePartsDirectShipFlag", baseOrgExtendList);
                        //是否支持急单急料
                        station["new_issupporturgent"] = getBaseOrgExtendDataBool("urgentMaterialFlag", baseOrgExtendList);
                        //是否支持写号
                        station["new_iscode"] = getBaseOrgExtendDataBool("writeNumberFlag", baseOrgExtendList);
                        //是否B2X管理网点
                        station["new_b2xmanagesite"] = getBaseOrgExtendDataBool("b2xManageFlag", baseOrgExtendList);
                        //是否保外坏品特殊出库
                        station["new_isspecialoutbound"] = getBaseOrgExtendDataBool("extDefectSpclOutFlag", baseOrgExtendList);
                        //保外RUR结费
                        station["new_rurbillable"] = getBaseOrgExtendDataBool("extRurFeeFlag", baseOrgExtendList);
                        //是否跳过库存校验
                        station["new_skipstockcheck"] = getBaseOrgExtendDataBool("skipInventoryCheck", baseOrgExtendList);
                        //MMI/CIT检测结果校验
                        station["new_mmicitcheck"] = getBaseOrgExtendDataBool("mmiCitValid", baseOrgExtendList);
                        string supportAddedServiceFlag = getBaseOrgExtendData("supportAddedServiceFlag", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(supportAddedServiceFlag))
                        {
                            if (int.TryParse(supportAddedServiceFlag.Trim(), out int number))
                            {
                                if (number == 100000000 || number == 100000001)
                                {
                                    //增值服务类型
                                    station["new_valueadded"] = new OptionSetValueCollection { new OptionSetValue(number) };
                                }
                            }

                        }
                        string retailSpecialOrderLimit = RemoveOuterQuotesTrim(getBaseOrgExtendData("retailSpecialOrderLimit", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(retailSpecialOrderLimit))
                        {
                            if (int.TryParse(retailSpecialOrderLimit.Trim(), out int number))
                            {
                                //特批单限额（零售通）
                                station["new_special_limit"] = number;
                            }

                        }
                        string sparePartsAuthFlag = getBaseOrgExtendData("sparePartsAuthFlag", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(sparePartsAuthFlag))
                        {
                            if (int.TryParse(sparePartsAuthFlag.Trim(), out int number))
                            {
                                //是否授权备件销售
                                if (number == 0)
                                {
                                    station["new_partssales"] = new OptionSetValue(100000001);
                                }
                                else if (number == 1)
                                {
                                    station["new_partssales"] = new OptionSetValue(100000000);
                                }
                            }
                        }
                        string createWarehouseFlag = getBaseOrgExtendData("createWarehouseFlag", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(createWarehouseFlag))
                        {
                            if (int.TryParse(createWarehouseFlag.Trim(), out int number))
                            {
                                //是否创建仓库
                                if (number == 1)
                                {
                                    station["new_creatwarehouse"] = new OptionSetValue(number);
                                }
                                else
                                {
                                    station["new_creatwarehouse"] = new OptionSetValue(2);//不创建仓库
                                }
                            }

                        }

                        #region 营业开始时间赋值逻辑
                        string weeklyBusinessEndTime = RemoveOuterQuotesTrim(getBaseOrgExtendData("weeklyBusinessEndTime", baseOrgExtendList));
                        string endTime = "";
                        if (!string.IsNullOrEmpty(weeklyBusinessEndTime))
                        {
                            if (int.TryParse(weeklyBusinessEndTime.Trim(), out int number))
                            {
                                switch (number)
                                {
                                    case 1:
                                        endTime = "Monday";
                                        break;
                                    case 2:
                                        endTime = "Tuesday";
                                        break;
                                    case 3:
                                        endTime = "Wednesday";
                                        break;
                                    case 4:
                                        endTime = "Thursday";
                                        break;
                                    case 5:
                                        endTime = "Friday";
                                        break;
                                    case 6:
                                        endTime = "Saturday";
                                        break;
                                    case 7:
                                        endTime = "Sunday";
                                        break;
                                }
                            }

                        }


                        string weeklyBusinessStartTime = RemoveOuterQuotesTrim(getBaseOrgExtendData("weeklyBusinessStartTime", baseOrgExtendList));
                        string startTime = "";
                        if (!string.IsNullOrEmpty(weeklyBusinessStartTime))
                        {
                            if (int.TryParse(weeklyBusinessStartTime.Trim(), out int number))
                            {
                                switch (number)
                                {
                                    case 1:
                                        startTime = "Monday";
                                        break;
                                    case 2:
                                        startTime = "Tuesday";
                                        break;
                                    case 3:
                                        startTime = "Wednesday";
                                        break;
                                    case 4:
                                        startTime = "Thursday";
                                        break;
                                    case 5:
                                        startTime = "Friday";
                                        break;
                                    case 6:
                                        startTime = "Saturday";
                                        break;
                                    case 7:
                                        startTime = "Sunday";
                                        break;
                                }
                            }

                        }
                        if (!String.IsNullOrEmpty(startTime) && !String.IsNullOrEmpty(endTime))
                        {
                            station["new_starttime"] = startTime + " - " + endTime;
                            if (tBaseOrg != null)
                            {
                                string bssid = tBaseOrgExtra.bssid;
                                // 按冒号分割MAC地址
                                string[] parts = bssid.Split(':');
                                // 检查是否有足够的部分（标准MAC地址有6部分）
                                if (parts.Length >= 6)
                                {
                                    station["new_starttime"] = station["new_starttime"] + tBaseOrg.business_hour_from.ToString() + ":00 - " + tBaseOrg.business_hour_to.ToString() + ":00";
                                }
                            }
                        }
                        #endregion

                        string paymentMethod = RemoveOuterQuotesTrim(getBaseOrgExtendData("paymentMethod", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(paymentMethod))
                        {
                            if (int.TryParse(paymentMethod.Trim(), out int number))
                            {
                                if (number == 1 || number == 2 || number == 3 || number == 4)
                                {
                                    //结算方式
                                    station["new_settlementway"] = new OptionSetValue(number);
                                }
                            }

                        }


                        string subOrgType = RemoveOuterQuotesTrim(getBaseOrgExtendData("subOrgType", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(subOrgType))
                        {
                            if (subOrgType == "MSC")
                            {
                                //网点类型
                                station["new_stietype"] = new OptionSetValue(1);
                            }
                            else if (subOrgType == "ESC")
                            {
                                station["new_stietype"] = new OptionSetValue(2);
                            }
                            else if (subOrgType == "CP")
                            {
                                station["new_stietype"] = new OptionSetValue(3);
                            }
                            else if (subOrgType == "SIS")
                            {
                                station["new_stietype"] = new OptionSetValue(4);
                            }
                            else if (subOrgType == "Mail-in Center")
                            {
                                station["new_stietype"] = new OptionSetValue(5);
                            }
                            else if (subOrgType == "Mi-Home")
                            {
                                station["new_stietype"] = new OptionSetValue(6);
                            }
                        }
                        string additionalServiceType = RemoveOuterQuotesTrim(getBaseOrgExtendData("additionalServiceType", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(additionalServiceType))
                        {
                            OptionSetValueCollection optionSetValues = new OptionSetValueCollection();
                            if (additionalServiceType == "insurance")
                            {
                                optionSetValues.Add(new OptionSetValue(100000000));
                            }
                            else if (additionalServiceType == "Micare Project")
                            {
                                optionSetValues.Add(new OptionSetValue(100000001));
                            }
                            //增值服务类型
                            station["new_valueadded"] = optionSetValues;
                        }

                        #region 语言赋值逻辑
                        string language = RemoveOuterQuotesTrim(getBaseOrgExtendData("language", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(language))
                        {
                            var languageQuery = new QueryExpression("new_language");
                            languageQuery.ColumnSet = new ColumnSet("new_langid");
                            languageQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            languageQuery.Criteria.AddCondition("new_code", ConditionOperator.Equal, language);
                            var languageEC = this.SystemUserService.RetrieveMultiple(languageQuery);
                            if (languageEC?.Entities?.Count > 0 && languageEC.Entities[0].Contains("new_langid"))
                            {
                                var languageQuery2 = new QueryExpression("msdyn_oclanguage");
                                languageQuery2.ColumnSet = new ColumnSet("msdyn_localeid");
                                languageQuery2.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                                languageQuery2.Criteria.AddCondition("msdyn_localeid", ConditionOperator.Equal, languageEC.Entities[0].GetAttributeValue<int>("new_langid"));
                                var languageEC2 = this.SystemUserService.RetrieveMultiple(languageQuery2);
                                if (languageEC2?.Entities?.Count > 0)
                                {
                                    station["new_language_id"] = new EntityReference("msdyn_oclanguage", languageEC2.Entities[0].Id);
                                }
                            }
                        }
                        #endregion

                        string productRange = RemoveOuterQuotesTrim(getBaseOrgExtendData("productRange", baseOrgExtendList));
                        if (!string.IsNullOrEmpty(productRange) && productRange.Trim().StartsWith("[") && productRange.Trim().EndsWith("]"))
                        {
                            // 1. 处理可能的转义字符
                            string processedJson = productRange
                                .Replace("\\\"", "\"")  // 替换转义的双引号
                                .Replace("\\\\", "\\"); // 替换转义的反斜杠

                            // 2. 检查并移除最外层的引号（如果存在）
                            if (processedJson.StartsWith("\"") && processedJson.EndsWith("\"") && processedJson.Length >= 2)
                            {
                                processedJson = processedJson.Substring(1, processedJson.Length - 2);
                            }

                            // 3. 解析JSON数组
                            var values = JsonSerializer.Deserialize<string[]>(processedJson);

                            OptionSetValueCollection optionSetValues = new OptionSetValueCollection();
                            foreach (var value in values)
                            {
                                if (int.TryParse(value, out int optionSetValue))
                                {
                                    optionSetValues.Add(new OptionSetValue(optionSetValue));
                                }
                            }

                            if (optionSetValues.Count > 0) // 直接检查optionSetValues的数量更安全
                            {
                                //产品范围
                                station["new_productrange"] = optionSetValues;
                            }
                        }

                        string organizationType = getBaseOrgExtendData("organizationType", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(organizationType))
                        {
                            if (int.TryParse(organizationType.Trim(), out int number))
                            {
                                //机构性质
                                if (number == 1)
                                {
                                    station["new_institutionalnature"] = new OptionSetValue(2);
                                }
                                else if (number == 2)
                                {
                                    station["new_institutionalnature"] = new OptionSetValue(1);
                                }
                            }

                        }
                        string sparePartsSupplyType = getBaseOrgExtendData("sparePartsSupplyType", baseOrgExtendList);
                        if (!string.IsNullOrEmpty(sparePartsSupplyType))
                        {
                            if (int.TryParse(sparePartsSupplyType.Trim(), out int number))
                            {
                                //备件供应类型
                                if (number == 1)
                                {
                                    //B2X交付
                                    station["new_sparepartssupplytype"] = new OptionSetValue(5);
                                }
                                else if (number == 2)
                                {
                                    //Maitrox交付
                                    station["new_sparepartssupplytype"] = new OptionSetValue(4);
                                }
                                else if (number == 3)
                                {
                                    //小米交付
                                    station["new_sparepartssupplytype"] = new OptionSetValue(2);
                                }
                                else if (number == 4)
                                {
                                    //买卖
                                    station["new_sparepartssupplytype"] = new OptionSetValue(1);
                                }
                            }

                        }
                    }
                    var businessServiceId = Guid.Empty;
                    if (!string.IsNullOrEmpty(tBaseOrg.service_supplier))
                    {
                        var stationQuery = new QueryExpression("new_srv_station");
                        stationQuery.ColumnSet = new ColumnSet("ownerid");
                        stationQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        stationQuery.Criteria.AddCondition("new_code", ConditionOperator.Equal, tBaseOrg.service_supplier);
                        LinkEntity link = new LinkEntity("new_srv_station", "systemuser", "ownerid", "systemuserid", JoinOperator.Inner);
                        link.EntityAlias = "systemuser";
                        link.Columns.AddColumns("businessunitid");
                        stationQuery.LinkEntities.Add(link);
                        var stations = this.SystemUserService.RetrieveMultiple(stationQuery);
                        if (stations?.Entities?.Count > 0)
                        {
                            //服务商
                            station["new_srv_station_id"] = new EntityReference("new_srv_station", stations.Entities[0].Id);
                            businessServiceId = stations.Entities[0].GetAliasAttributeValue<EntityReference>("systemuser.businessunitid").Id;
                        }
                    }
                    //经度维度
                    if (decimal.TryParse(tBaseOrg.gpsx, out decimal gpsx))
                    {
                        station["new_longitude"] = gpsx;
                    }
                    if (decimal.TryParse(tBaseOrg.gpsy, out decimal gpsy))
                    {
                        station["new_dimension"] = gpsy;
                    }
                    #endregion

                    #region 落库操作
                    if (station.Id == Guid.Empty)
                    {
                        //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                        if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, "new_srv_station", "create"))
                        {
                            return;
                        }
                        //服务网点编码
                        station["new_code"] = tBaseOrg.id;

                        //根据邮箱、米聊查询负责人用户
                        Entity systemuser = new Entity("systemuser");
                        if (tBaseOrgCredential != null && !string.IsNullOrEmpty(tBaseOrgCredential.leader_miliao))
                        {
                            var systemuserByMiliao = getSystemUser("new_miid", tBaseOrgCredential.leader_miliao);
                            if (systemuserByMiliao != null)
                            {
                                systemuser = systemuserByMiliao;
                            }
                        }
                        if (tBaseOrgCredential != null && !string.IsNullOrEmpty(tBaseOrgCredential.leader_email))
                        {
                            var systemuserByEmail = getSystemUser("domainname", tBaseOrgCredential.leader_email);
                            if (systemuser == null)
                            {
                                if (systemuserByEmail != null)
                                {
                                    systemuser = systemuserByEmail;
                                }
                            }
                        }
                        

                        if (systemuser.Id == Guid.Empty)
                        {
                            /*  systemuser = new Entity("systemuser");
                              systemuser["domainname"] = tBaseOrgCredential.leader_email;
                              systemuser["internalemailaddress"] = tBaseOrgCredential.leader_email;
                              systemuser["new_miid"] = tBaseOrgCredential.leader_miliao;

                              GetFirstNameAndLastName(tBaseOrgCredential.legal_person_name, out string firstName, out string lastName);
                              systemuser["firstname"] = firstName;
                              systemuser["lastname"] = lastName;

                              Entity businessunit = new Entity("businessunit");
                              businessunit["name"] = countryNmae + "-服务网点" + tBaseOrg.name + "-售后";
                              businessunit["parentbusinessunitid"] = new EntityReference("businessunit", businessServiceId);

                              var businessunitQuery = new QueryExpression("businessunit");
                              businessunitQuery.ColumnSet = new ColumnSet("new_code");
                              businessunitQuery.Criteria.AddCondition("parentbusinessunitid", ConditionOperator.Equal, businessServiceId.ToString());
                              var businessunitEC = this.SystemUserService.RetrieveMultiple(businessunitQuery);
                              if (businessunitEC?.Entities?.Count > 0)
                              {
                                  var codeList = businessunitEC.Entities.Where(a => a.Contains("new_code")).Select(x => x.GetAttributeValue<string>("new_code")).ToList();
                                  businessunit["new_code"] = getNewCode( businessunitEC.Entities[0].GetAttributeValue<string>("new_code"), getMaxNumber(codeList));
                              }
                              else
                              {
                                  businessunit["new_code"] = "xm" + countryCode + "0001";
                              }
                              var businessId = this.SystemUserService.Create(businessunit);

                              if (businessId != Guid.Empty && !string.IsNullOrEmpty(tBaseOrgCredential.leader_email))
                              {
                                  systemuser["businessunitid"] = new EntityReference("businessunit", businessId);
                                  systemuser["isdisabled"] = false;
                                  var userId = SystemUserService.Create(systemuser);
                                  station["ownerid"] = new EntityReference("systemuser", userId);
                              }*/
                            //当用户在ISP系统不存在时，负责人为管理员账号
                            string systemuserID = this.SystemUserService.GetOperableValue(SYSTEMPARAM, "new_srv_station", "systemuserid");
                            if (!string.IsNullOrEmpty(systemuserID) && Guid.TryParse(systemuserID, out Guid userGuid))
                            {
                                station["ownerid"] = new EntityReference("systemuser", userGuid);
                            }
                            else
                            {
                                throw new InvalidPluginExecutionException("无法获取有效的系统用户ID配置");
                            }

                        }
                        else
                        {
                            station["ownerid"] = new EntityReference("systemuser", systemuser.Id);
                        }
                        this.SystemUserService.Create(station);
                        
                    }
                    else
                    {
                        //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                        if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, "new_srv_station", "update"))
                        {
                            return;
                        }
                        this.SystemUserService.Update(station);
                    }
                    #endregion
                }

            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"服务网点同步失败：{ex.ToString()}");
            }
        }
        /// <summary>
        /// 将字符串末尾的数字替换为新数字并保持位数对齐
        /// </summary>
        /// <param name="code">包含末尾数字的原始编码</param>
        /// <param name="newNumber">要替换的新数字</param>
        /// <returns>数字替换后的完整编码</returns>
        static string getNewCode(string code, int newNumber)
        {
            Match match = Regex.Match(code, @"\d+$");
            if (match.Success)
            {
                int numberLength = match.Value.Length;
                string prefix = code.Substring(0, code.Length - numberLength);
                string newNumberStr = newNumber.ToString().PadLeft(numberLength, '0');
                return prefix + newNumberStr;
            }
            return code;
        }
        /// <summary>
        /// 获取字符串列表中最大数字值并返回递增1的结果
        /// </summary>
        /// <param name="codes">包含数字字符串的列表</param>
        /// <returns>最大数字+1的结果，若无有效数字则返回1</returns>
        public int getMaxNumber(List<string> codes)
        {
            List<int> numbers = codes
                .Select(code => ExtractNumber(code))
                .Where(num => num.HasValue)
                .Select(num => num.Value)
                .ToList();

            if (numbers.Any())
            {
                int maxNumber = numbers.Max();
                return maxNumber + 1;
            }
            else
            {
                return 1;
            }
        }
        /// <summary>
        /// 从字符串中提取第一个连续数字序列并转换为整数
        /// </summary>
        /// <param name="input">包含数字的输入字符串</param>
        /// <returns>提取到的整数值，若无数字匹配则返回null</returns>
        static int? ExtractNumber(string input)
        {
            Match match = Regex.Match(input, @"\d+");
            if (match.Success && int.TryParse(match.Value, out int number))
            {
                return number;
            }
            return null;
        }
        /// <summary>
        /// 将全名按最后一个空格分割为姓和名
        /// </summary>
        /// <param name="fullname">待分割的全名字符串</param>
        /// <param name="firstName">输出参数：分割后的名字</param>
        /// <param name="lastName">输出参数：分割后的姓氏</param>
        static void GetFirstNameAndLastName(string fullname, out string firstName, out string lastName)
        {
            // 查找最后一个空格的位置
            int lastSpaceIndex = fullname.LastIndexOf(' ');
            if (lastSpaceIndex != -1)
            {
                // 提取姓（从字符串开头到最后一个空格之前）
                lastName = fullname.Substring(0, lastSpaceIndex);
                // 提取名（从最后一个空格之后到字符串末尾）
                firstName = fullname.Substring(lastSpaceIndex + 1);
            }
            else
            {
                // 如果没有找到空格，假设整个字符串是名，姓为空
                lastName = "";
                firstName = fullname;
            }
        }

        /// <summary>
        /// 从组织扩展数据列表中根据键名获取对应的布尔值
        /// </summary>
        /// <param name="key">要查询的键名</param>
        /// <param name="baseOrgExtendList">组织扩展数据列表集合</param>
        /// <returns>若值为"1"返回true，否则返回false</returns>
        public bool getBaseOrgExtendDataBool(string key, List<BaseOrgExtend> baseOrgExtendList)
        {
            string data = getBaseOrgExtendData(key, baseOrgExtendList);
            if (int.TryParse(data.Trim(), out int number))
            {
                if (number == 1)
                {
                    return true;
                }
            }
            return false;

        }
        /// <summary>
        /// 从组织扩展数据列表中根据键名获取对应的字符串值
        /// </summary>
        /// <param name="key">要查询的键名</param>
        /// <param name="baseOrgExtendList">组织扩展数据列表集合</param>
        /// <returns>匹配的字符串值，若未找到或参数无效则返回空字符串</returns>
        public string getBaseOrgExtendData(string key, List<BaseOrgExtend> baseOrgExtendList)
        {
            if (string.IsNullOrEmpty(key))
            {
                return "";
            }
            BaseOrgExtend baseOrgExtend = baseOrgExtendList.Where(x => x.key_name == key).FirstOrDefault();
            if (baseOrgExtend != null)
            {
                return baseOrgExtend.data;
            }
            return "";
        }
        /// <summary>
        /// 去除字符串首尾的双引号并返回
        /// </summary>
        /// <param name="input">待处理的字符串</param>
        /// <returns>移除首尾引号后的字符串，若无引号则返回原字符串</returns>
        string RemoveOuterQuotesTrim(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            // 确保字符串以双引号开头和结尾
            if (input.StartsWith("\"") && input.EndsWith("\""))
            {
                return input.TrimStart('"').TrimEnd('"');
            }
            return input;
        }

        //服务人员同步暂时不开启
        public void DealServiceWorker()
        {
            try
            {

                XMSServiceWorker xmsServiceWorker = this.input?.xmsServiceWorker ?? new XMSServiceWorker();
                var query = new QueryExpression("new_srv_worker");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_code", ConditionOperator.Equal, xmsServiceWorker.pk);
                var entityCollection = this.SystemUserService.RetrieveMultiple(query);
                Entity worker = entityCollection?.Entities?.Count > 0 ? entityCollection.Entities[0] : new Entity("new_srv_worker");
                //判断是否为删除消息
                if (worker.Id != Guid.Empty && !string.IsNullOrEmpty(xmsServiceWorker.operate) && xmsServiceWorker.operate == "DELETE")
                {
                    worker["statecode"] = new OptionSetValue(1);
                    SystemUserService.UpdateWithBypassPluginAndFlow(worker);
                    return;
                }
                var engineer = xmsServiceWorker.base_engineer[0];
                var engineerOrg = xmsServiceWorker.base_engineer_org[0];
                var user = xmsServiceWorker.base_user[0];

                #region 数据赋值
                if (!string.IsNullOrEmpty(engineer.name))
                {
                    worker["new_name"] = engineer.name;
                }
                if (!string.IsNullOrEmpty(engineer.surname))
                {
                    worker["new_lastname"] = engineer.surname;
                }
                if (!string.IsNullOrEmpty(engineer.given_name))
                {
                    worker["new_firstname"] = engineer.given_name;
                }
                Guid businessId = Guid.Empty;
                if (!string.IsNullOrEmpty(engineerOrg.org_id))
                {
                    var entityQuery = new QueryExpression("new_srv_station");
                    entityQuery.ColumnSet = new ColumnSet("ownerid");
                    entityQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    entityQuery.Criteria.AddCondition("new_code", ConditionOperator.Equal, engineerOrg.org_id);
                    LinkEntity link = new LinkEntity("new_srv_station", "systemuser", "ownerid", "systemuserid", JoinOperator.Inner);
                    link.EntityAlias = "systemuser";
                    link.Columns.AddColumns("businessunitid");
                    entityQuery.LinkEntities.Add(link);
                    var entity = this.SystemUserService.RetrieveMultiple(entityQuery);
                    if (entity?.Entities?.Count > 0)
                    {
                        worker["new_srv_station_id"] = new EntityReference("new_srv_station", entity.Entities[0].Id);
                        businessId = entity.Entities[0].GetAliasAttributeValue<EntityReference>("systemuser.businessunitid").Id;
                        worker["new_businessdepartment"] = new EntityReference("businessunit", businessId);
                        worker["owningbusinessunit"] = new EntityReference("businessunit", businessId);
                    }
                }
                Entity systemuser = null;
                string miliao = user.miliao.ToString();
                if (!string.IsNullOrEmpty(miliao))
                {
                    worker["new_miid"] = miliao;
                    var systemuserByMiliao = getSystemUser("new_miid", miliao);
                    if (systemuserByMiliao != null)
                    {
                        systemuser = systemuserByMiliao;
                    }
                }
                var email = user.email;
                #region 创建系统用户
                if (!string.IsNullOrEmpty(email))
                {
                    worker["new_email"] = email;
                    var systemuserByEmail = getSystemUser("domainname", email);
                    if (systemuser == null)
                    {
                        if (systemuserByEmail != null)
                        {
                            systemuser = systemuserByEmail;
                        }
                    }
                    // 使用正则表达式匹配邮箱格式
                    string pattern = @"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$";
                    bool isEmail = Regex.IsMatch(email, pattern);
                    if (isEmail)
                    {
                        int atIndex = email.IndexOf('@');
                        worker["new_maildomain"] = email.Substring(atIndex + 1);
                    }
                }
                if (systemuser == null && worker.Id == Guid.Empty)
                {
                    systemuser = new Entity("systemuser");
                    systemuser["domainname"] = email;
                    systemuser["internalemailaddress"] = email;
                    systemuser["new_miid"] = miliao;
                    systemuser["firstname"] = engineer.given_name;
                    systemuser["lastname"] = engineer.surname;
                    if (businessId != Guid.Empty && !string.IsNullOrEmpty(email))
                    {
                        systemuser["businessunitid"] = new EntityReference("businessunit", businessId);
                        var userId = SystemUserService.Create(systemuser);
                        worker["new_systemuser_id"] = new EntityReference("systemuser", userId);
                    }

                }
                #endregion
                #endregion

                #region 落库
                else if (systemuser != null)
                {
                    worker["new_systemuser_id"] = new EntityReference("systemuser", systemuser.Id);
                }

                if (worker.Id == Guid.Empty)
                {
                    worker["new_code"] = engineer.engineer_id;
                    worker["new_isxms"] = true;
                    SystemUserService.Create(worker);
                }
                else
                {
                    SystemUserService.Update(worker);
                }
                #endregion
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"服务人员同步失败：{ex.Message}");
            }
        }



        public void DealServiceScope()
        {
            try
            {
                XMSServiceScopeReq xmsService = this.input?.xmsServiceScope ?? new XMSServiceScopeReq();
                //3：客服派单服务范围表，4：自助服务范围表，5：上门服务范围表，6：门店寄修服务范围表
                Dictionary<int, string> entityTypeMap = new Dictionary<int, string>
                {
                    { 3, "new_threecategories" },
                    { 4, "new_countrytosite_relation" },
                    { 5, "new_categoryarea" },
                    { 6, "new_repairrange" }
                };
                //进行删除操作-停用ISP实体
                if (!string.IsNullOrEmpty(xmsService.operate) && xmsService.operate == "DELETE")
                {
                    
                    int[] typeList = { 3, 4, 5, 6 };
                    Entity entity = null;
                    foreach (var item in typeList)
                    {
                        if (!entityTypeMap.TryGetValue(item, out string entityName))
                        {
                            return;
                        }
                        //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                        if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, entityName, "delete"))
                        {
                            return;
                        }

                        entity = GetEntityByxmsId(entityName, xmsService.pk);
                        if (entity.Id != Guid.Empty)
                        {
                            break;
                        }
                    }
                    //存在实体则停用
                    if (entity.Id != Guid.Empty)
                    {
                        entity["statecode"] = new OptionSetValue(1);
                        SystemUserService.Update(entity);
                        return;
                    }
                }
                else
                {

                    BaseServiceScopReq xmsServiceScope = xmsService.t_base_org_service_scope[0];
                    int type = xmsServiceScope.type;
                    if (!entityTypeMap.TryGetValue(type, out string entityName))
                    {
                        return;
                    }

                    Entity entity = GetEntityByxmsId(entityName, xmsService.pk);
                    SetCommonFields(entity, xmsServiceScope);

                    //根据具体类型调用具体方法
                    switch (type)
                    {
                        case 3:
                            SetThreecategories(entity, xmsServiceScope);
                            break;
                        case 4:
                            SetCountryToSiteFields(entity, xmsServiceScope);
                            break;
                        case 5:
                            SetCategoryAreaFields(entity, xmsServiceScope);
                            break;
                        case 6:
                            SetRepairRangeFields(entity, xmsServiceScope);
                            break;
                    }

                    if (entity.Id == Guid.Empty)
                    {
                        //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                        if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, entityName, "create"))
                        {
                            return;
                        }
                        entity["new_xmsid"] = xmsServiceScope.id;
                        SystemUserService.Create(entity);
                    }
                    else
                    {
                        //查找系统参数，若系统参数对应操作未开启，不操作对应实体
                        if (!this.SystemUserService.GetIsOperable(SYSTEMPARAM, entityName, "update"))
                        {
                            return;
                        }
                        SystemUserService.Update(entity);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"服务网格信息同步失败：{ex.Message}");
            }
        }
        /// <summary>
        /// 根据xmsId获取有效状态的实体对象
        /// </summary>
        /// <param name="entityName">目标实体名称</param>
        /// <param name="xmsId">外部系统ID（new_xmsid字段匹配值）</param>
        /// <returns>匹配到的第一个有效实体或新创建的实体对象</returns>
        /// <exception cref="InvalidPluginExecutionException">查询失败时抛出异常</exception>
        private Entity GetEntityByxmsId(string entityName, string xmsId)
        {
            try
            {
                var query = new QueryExpression(entityName);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_xmsid", ConditionOperator.Equal, xmsId);
                var entityCollection = this.SystemUserService.RetrieveMultiple(query);
                return entityCollection.Entities.Count > 0 ? entityCollection.Entities[0] : new Entity(entityName);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"服务信息查询失败：{ex.Message}");
            }
        }
        /// <summary>
        /// 设置基础服务范围实体的通用字段
        /// </summary>
        /// <param name="entity">目标实体对象</param>
        /// <param name="xmsServiceScope">服务范围请求数据</param>
        /// <remarks>包含站点、三级分类、国家字段的条件赋值</remarks>
        private void SetCommonFields(Entity entity, BaseServiceScopReq xmsServiceScope)
        {
            var station = getEntity("new_srv_station", "new_code", xmsServiceScope.org_id);
            if (station != null && xmsServiceScope.type != 4)
            {
                entity["new_srv_station_id"] = station;
            }
            var category3 = getEntity("new_category3", "new_code", xmsServiceScope.brand_class_id);
            if (category3 != null)
            {
                entity["new_category3_id"] = category3;
            }
            var country = getEntity("new_country", "new_id", xmsServiceScope.country.ToString());
            //type = 6 为门店寄修服务范围表
            if (country != null && xmsServiceScope.type != 6)
            {
                entity["new_country_id"] = country;
            }
        }
        /// <summary>
        /// 设置客服派单服务范围表实体字段
        /// </summary>
        /// <param name="entity">目标实体对象</param>
        /// <param name="xmsServiceScope">服务范围请求数据</param>
        private void SetThreecategories(Entity entity, BaseServiceScopReq xmsServiceScope)
        {
            var station = getEntity("new_srv_station", "new_code", xmsServiceScope.org_id);
            if (station != null)
            {
                entity["new_srv_station_id"] = station;
            }
            var category3 = getEntity("new_category3", "new_code", xmsServiceScope.brand_class_id);
            if (category3 != null)
            {
                entity["new_category3_id"] = category3;
            }
            var country = getEntity("new_country", "new_id", xmsServiceScope.country.ToString());
            if (country != null)
            {
                entity["new_country_id"] = country;
            }
        }
        /// <summary>
        /// 设置自助服务范围表实体字段
        /// </summary>
        /// <param name="entity">目标实体对象</param>
        /// <param name="xmsServiceScope">服务范围请求数据</param>
        private void SetCountryToSiteFields(Entity entity, BaseServiceScopReq xmsServiceScope)
        {
            var station = getEntity("new_srv_station", "new_code", xmsServiceScope.org_id);
            if (station != null)
            {
                entity["new_station_id"] = station;
            }
            var province = getEntity("new_province", "new_id", xmsServiceScope.province.ToString());
            if (province != null)
            {
                entity["new_province_id"] = province;
            }
            var city = getEntity("new_city", "new_id", xmsServiceScope.city.ToString());
            if (city != null)
            {
                entity["new_city_id"] = city;
            }
            var county = getEntity("new_county", "new_id", xmsServiceScope.district.ToString());
            if (county != null)
            {
                entity["new_county_id"] = county;
            }
            var goodsfiles = getEntity("new_goodsfiles", "new_commoditycode", xmsServiceScope.goods_id);
            if (goodsfiles != null)
            {
                entity["new_goodsfiles_id"] = goodsfiles;
            }
            int mailWay = CommonHelper.ConToInt(xmsServiceScope.mail_way);
            if (mailWay != 0)
            {
                entity["new_mailing_method"] = new OptionSetValue(mailWay);
            }
            int operateType = CommonHelper.ConToInt(xmsServiceScope.service_operate);
            if (operateType == 1)
            {
                entity["new_type"] = new OptionSetValue(1);
            }
            else if (operateType == 2 || operateType == 3)
            {
                entity["new_type"] = new OptionSetValue(2);
            }
        }
        /// <summary>
        /// 设置上门服务范围表实体字段
        /// </summary>
        /// <param name="entity">目标实体对象</param>
        /// <param name="xmsServiceScope">服务范围请求数据</param>
        private void SetCategoryAreaFields(Entity entity, BaseServiceScopReq xmsServiceScope)
        {
            var province = getEntity("new_province", "new_id", xmsServiceScope.province.ToString());
            if (province != null)
            {
                entity["new_province_id"] = province;
            }
            var city = getEntity("new_city", "new_id", xmsServiceScope.city.ToString());
            if (city != null)
            {
                entity["new_city_id"] = city;
            }
            var county = getEntity("new_county", "new_id", xmsServiceScope.district.ToString());
            if (county != null)
            {
                entity["new_county_id"] = county;
            }
            var goodsfiles = getEntity("new_goodsfiles", "new_commoditycode", xmsServiceScope.goods_id);
            if (goodsfiles != null)
            {
                entity["new_productprofileid"] = goodsfiles;
            }
            int serviceType = CommonHelper.ConToInt(xmsServiceScope.service_type);
            switch (serviceType)
            {
                case 6:
                    entity["new_servicemode"] = new OptionSetValue(1);
                    break;
                case 1:
                    entity["new_servicemode"] = new OptionSetValue(2);
                    break;
                case 3:
                    entity["new_servicemode"] = new OptionSetValue(3);
                    break;
                case 7:
                    entity["new_servicemode"] = new OptionSetValue(4);
                    break;
            }
            int operateType = CommonHelper.ConToInt(xmsServiceScope.service_operate);
            if (operateType != 0)
            {
                entity["new_servicetype"] = new OptionSetValueCollection() { new OptionSetValue(operateType) };
            }
        }
        /// <summary>
        /// 设置门店寄修服务范围表实体字段
        /// </summary>
        /// <param name="entity">目标实体对象</param>
        /// <param name="xmsServiceScope">服务范围请求数据</param>
        private void SetRepairRangeFields(Entity entity, BaseServiceScopReq xmsServiceScope)
        {
            int operateType = CommonHelper.ConToInt(xmsServiceScope.service_operate);
            if (operateType != 0 && operateType != 5 && operateType != 6)
            {
                entity["new_type"] = new OptionSetValue(operateType);
            }
            else if (operateType == 5)
            {
                //xms传值保养
                entity["new_type"] = new OptionSetValue(6);
            }
            else if (operateType == 6)
            {
                //xms传值安装
                entity["new_type"] = new OptionSetValue(5);
            }
            int serviceType = CommonHelper.ConToInt(xmsServiceScope.service_type);
            switch (serviceType)
            {
                case 6:
                    entity["new_servicemode"] = new OptionSetValue(1);
                    break;
                case 1:
                    entity["new_servicemode"] = new OptionSetValue(2);
                    break;
                case 3:
                    entity["new_servicemode"] = new OptionSetValue(3);
                    break;
                case 7:
                    entity["new_servicemode"] = new OptionSetValue(4);
                    break;
            }
        }
        /// <summary>
        /// 查询系统用户实体
        /// </summary>
        /// <param name="fieldName">查询字段名</param>
        /// <param name="new_code">字段匹配值</param>
        /// <returns>匹配到的第一个系统用户实体，若未找到返回null</returns>
        public Entity getSystemUser(string fieldName, string new_code)
        {
            if (string.IsNullOrEmpty(new_code))
            {
                return null;
            }
            var entityQuery = new QueryExpression("systemuser");
            entityQuery.Criteria.AddCondition(fieldName, ConditionOperator.Equal, new_code);
            var entity = this.SystemUserService.RetrieveMultiple(entityQuery);
            return entity.Entities.Count > 0 ? entity.Entities[0] : null;
        }
        /// <summary>
        /// 查询实体引用对象
        /// </summary>
        /// <param name="tableName">目标实体逻辑名称</param>
        /// <param name="fieldName">查询字段名</param>
        /// <param name="new_code">字段匹配值</param>
        /// <returns>匹配到的实体引用对象，若未找到或状态无效返回null</returns>
        public EntityReference getEntity(string tableName, string fieldName, string new_code)
        {
            if (string.IsNullOrEmpty(new_code))
            {
                return null;
            }
            var entityQuery = new QueryExpression(tableName);
            entityQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            entityQuery.Criteria.AddCondition(fieldName, ConditionOperator.Equal, new_code);
            var entity = this.SystemUserService.RetrieveMultiple(entityQuery);
            return entity.Entities.Count > 0 ? new EntityReference(tableName, entity.Entities[0].Id) : null;
        }
    }
}