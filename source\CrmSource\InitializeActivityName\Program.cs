﻿using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Linq;
using System.IO;
using RekTec.Crm.OrganizationService.Common.Helper;
using System.Configuration;
using System.Runtime.InteropServices;
using System.Collections.Generic;

namespace InitializeActivityName
{
    public class Program
    {
        // UAT
        public static string UatConnectionString = ConfigurationManager.AppSettings["UatConnectionString"];
        // 亚洲
        public static string AsiaConnectionString = ConfigurationManager.AppSettings["AsiaConnectionString"];
        // 欧洲
        public static string EuropeConnectionString = ConfigurationManager.AppSettings["EuropeConnectionString"];
        // 拉美
        public static string LatinConnectionString = ConfigurationManager.AppSettings["LatinConnectionString"];
        // 中东
        public static string UaeConnectionString = ConfigurationManager.AppSettings["UaeConnectionString"];
        // 汉语简体
        public static string SimplifiedChinese = "2052";
        // 英语
        public static string English = "1033";
        // 汉语繁体（中国香港）
        public static string TraditionalChineseHK = "3076";
        // 汉语繁体（中国台湾）
        public static string TraditionalChineseTW = "1028";

        static void Main(string[] args)
        {
            string logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Logs");

            // 如果Logs目录不存在则创建
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }


            #region UAT
            if (!string.IsNullOrEmpty(UatConnectionString))
            {
                try
                {
                    ServiceClient UatService = new ServiceClient(UatConnectionString);

                    // 分页查询
                    bool moreRecords = true;
                    int pageNum = 1;
                    int pageSize = 1000;
                    string pagingCookie = null;

                    while (moreRecords)
                    {
                        EntityCollection collection = GetActivityRuleData(pageSize, pageNum, pagingCookie, UatService);
                        if (collection.Entities.Count > 0)
                        {
                            foreach (Entity en in collection.Entities)
                            {
                                UpdateActivityAndCreateTranslation(UatService, en);
                            }
                        }

                        if (collection.MoreRecords)
                        {
                            pageNum++;
                            pagingCookie = collection.PagingCookie;
                        }
                        else
                        {
                            moreRecords = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(logDirectory, "UAT数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 亚洲
            if (!string.IsNullOrEmpty(AsiaConnectionString))
            {
                try
                {
                    ServiceClient AsiaService = new ServiceClient(AsiaConnectionString);

                    // 分页查询
                    bool moreRecords = true;
                    int pageNum = 1;
                    int pageSize = 1000;
                    string pagingCookie = null;

                    while (moreRecords)
                    {
                        EntityCollection collection = GetActivityRuleData(pageSize, pageNum, pagingCookie, AsiaService);
                        if (collection.Entities.Count > 0)
                        {
                            foreach (Entity en in collection.Entities)
                            {
                                UpdateActivityAndCreateTranslation(AsiaService, en);
                            }
                        }

                        if (collection.MoreRecords)
                        {
                            pageNum++;
                            pagingCookie = collection.PagingCookie;
                        }
                        else
                        {
                            moreRecords = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(logDirectory, "亚洲数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 欧洲
            if (!string.IsNullOrEmpty(EuropeConnectionString))
            {
                try
                {
                    ServiceClient EuropeService = new ServiceClient(EuropeConnectionString);

                    // 分页查询
                    bool moreRecords = true;
                    int pageNum = 1;
                    int pageSize = 1000;
                    string pagingCookie = null;

                    while (moreRecords)
                    {
                        EntityCollection collection = GetActivityRuleData(pageSize, pageNum, pagingCookie, EuropeService);
                        if (collection.Entities.Count > 0)
                        {
                            foreach (Entity en in collection.Entities)
                            {
                                UpdateActivityAndCreateTranslation(EuropeService, en);
                            }
                        }

                        if (collection.MoreRecords)
                        {
                            pageNum++;
                            pagingCookie = collection.PagingCookie;
                        }
                        else
                        {
                            moreRecords = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(logDirectory, "欧洲数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 拉美
            if (!string.IsNullOrEmpty(LatinConnectionString))
            {
                try
                {
                    ServiceClient LatinService = new ServiceClient(LatinConnectionString);

                    // 分页查询
                    bool moreRecords = true;
                    int pageNum = 1;
                    int pageSize = 1000;
                    string pagingCookie = null;

                    while (moreRecords)
                    {
                        EntityCollection collection = GetActivityRuleData(pageSize, pageNum, pagingCookie, LatinService);
                        if (collection.Entities.Count > 0)
                        {
                            foreach (Entity en in collection.Entities)
                            {
                                UpdateActivityAndCreateTranslation(LatinService, en);
                            }
                        }

                        if (collection.MoreRecords)
                        {
                            pageNum++;
                            pagingCookie = collection.PagingCookie;
                        }
                        else
                        {
                            moreRecords = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(logDirectory, "拉美数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 中东
            if (!string.IsNullOrEmpty(UaeConnectionString))
            {
                try
                {
                    ServiceClient UaeService = new ServiceClient(UaeConnectionString);

                    // 分页查询
                    bool moreRecords = true;
                    int pageNum = 1;
                    int pageSize = 1000;
                    string pagingCookie = null;

                    while (moreRecords)
                    {
                        EntityCollection collection = GetActivityRuleData(pageSize, pageNum, pagingCookie, UaeService);
                        if (collection.Entities.Count > 0)
                        {
                            foreach (Entity en in collection.Entities)
                            {
                                UpdateActivityAndCreateTranslation(UaeService, en);
                            }
                        }

                        if (collection.MoreRecords)
                        {
                            pageNum++;
                            pagingCookie = collection.PagingCookie;
                        }
                        else
                        {
                            moreRecords = false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(logDirectory, "中东数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion
        }

        /// <summary>
        /// 更新历史数据服务活动名称并创建翻译
        /// </summary>
        /// <param name="service"></param>
        /// <param name="activity"></param>
        public static void UpdateActivityAndCreateTranslation(ServiceClient service, Entity activity)
        {
            if (activity.Contains("new_projectinfo_first_id") && activity.Contains("new_projectinfo_second_id"))
            {
                // 一级项目名称，二级项目名称
                var name1 = ((EntityReference)activity["new_projectinfo_first_id"]).Name;
                var name2 = ((EntityReference)activity["new_projectinfo_second_id"]).Name;
                // 活动规则配置编码
                var new_name = activity.Contains("new_name") ? activity["new_name"].ToString() : string.Empty;

                // 一级项目ID，二级项目ID
                var id1 = ((EntityReference)activity["new_projectinfo_first_id"]).Id.ToString().ToLower();
                var id2 = ((EntityReference)activity["new_projectinfo_second_id"]).Id.ToString().ToLower();
                string[] idarr = new string[] { id1, id2 };

                // 查询原【一级项目】【二级项目】对应的所有翻译内容
                QueryExpression dataQuery = new QueryExpression("new_data_languageconfig");
                dataQuery.NoLock = true;
                dataQuery.ColumnSet = new ColumnSet("new_value", "new_data_id", "new_language_id");
                dataQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                dataQuery.Criteria.AddCondition("new_entity_name", ConditionOperator.Equal, "new_projectinfo");
                dataQuery.Criteria.AddCondition("new_attribute_name", ConditionOperator.Equal, "new_name");
                dataQuery.Criteria.AddCondition("new_value", ConditionOperator.NotNull);
                dataQuery.Criteria.AddCondition("new_data_id", ConditionOperator.In, idarr);
                LinkEntity link = new LinkEntity("new_data_languageconfig", "new_language", "new_language_id", "new_languageid", JoinOperator.Inner);
                link.EntityAlias = "l";
                link.Columns = new ColumnSet("new_langid");
                link.LinkCriteria.AddCondition("new_langid", ConditionOperator.NotNull);
                dataQuery.LinkEntities.Add(link);
                EntityCollection dataColl = service.RetrieveMultiple(dataQuery);
                if (dataColl.Entities.Count > 0)
                {
                    var list = dataColl.Entities.Select(x =>
                        new
                        {
                            dataId = x["new_data_id"].ToString().ToLower(),
                            value = x["new_value"].ToString(),
                            langId = ((EntityReference)x["new_language_id"]).Id,
                            langCode = ((AliasedValue)x["l.new_langid"]).Value.ToString()
                        }
                    );

                    // 处理中文简体翻译，汉语简体：2052
                    // 一级项目翻译，二级项目翻译
                    var project2052_1 = list.Where(x => x.langCode == SimplifiedChinese && x.dataId == id1).ToList();
                    var project2052_2 = list.Where(x => x.langCode == SimplifiedChinese && x.dataId == id2).ToList();

                    if (project2052_1.Count > 0 && project2052_2.Count > 0)
                    {
                        CreateLanguageConfig(project2052_1, project2052_2, new_name, activity, service);
                    }

                    // 处理英文翻译，英语：1033
                    // 一级项目翻译，二级项目翻译
                    var project1033_1 = list.Where(x => x.langCode == English && x.dataId == id1).ToList();
                    var project1033_2 = list.Where(x => x.langCode == English && x.dataId == id2).ToList();

                    if (project1033_1.Count > 0 && project1033_2.Count > 0)
                    {
                        CreateLanguageConfig(project1033_1, project1033_2, new_name, activity, service);
                    }

                    // 处理中文繁体-香港翻译，汉语繁体（中国香港）：3076
                    // 一级项目翻译，二级项目翻译
                    var project3076_1 = list.Where(x => x.langCode == TraditionalChineseHK && x.dataId == id1).ToList();
                    var project3076_2 = list.Where(x => x.langCode == TraditionalChineseHK && x.dataId == id2).ToList();

                    if (project3076_1.Count > 0 && project3076_2.Count > 0)
                    {
                        CreateLanguageConfig(project3076_1, project3076_2, new_name, activity, service);
                    }

                    // 处理中文繁体-台湾翻译，汉语繁体（中国台湾）：1028
                    // 一级项目翻译，二级项目翻译
                    var project1028_1 = list.Where(x => x.langCode == TraditionalChineseTW && x.dataId == id1).ToList();
                    var project1028_2 = list.Where(x => x.langCode == TraditionalChineseTW && x.dataId == id2).ToList();

                    if (project1028_1.Count > 0 && project1028_2.Count > 0)
                    {
                        CreateLanguageConfig(project1028_1, project1028_2, new_name, activity, service);
                    }
                }

                // 更新【服务活动名称】
                Entity newEnt = new Entity("new_arconfiguration");
                newEnt.Id = activity.Id;
                newEnt["new_servicename"] = name1 + "-" + name2;
                service.Update(newEnt);
            }
        }

        /// <summary>
        /// 创建多语言配置
        /// </summary>
        /// <param name="data1"></param>
        /// <param name="data2"></param>
        /// <param name="name"></param>
        /// <param name="activity"></param>
        /// <param name="service"></param>
        public static void CreateLanguageConfig(IEnumerable<dynamic> data1, IEnumerable<dynamic> data2, string name, Entity activity, ServiceClient service)
        {
            Entity ent = new Entity("new_data_languageconfig");
            ent["new_entity_name"] = "new_arconfiguration";
            ent["new_attribute_name"] = "new_servicename";
            ent["new_language_id"] = new EntityReference("new_language", data1.FirstOrDefault().langId);
            ent["new_value"] = data1.FirstOrDefault().value + "-" + data2.FirstOrDefault().value;
            ent["new_code"] = name;
            ent["new_data_id"] = activity.Id.ToString();
            ent["new_language_code"] = data1.FirstOrDefault().langCode;
            service.Create(ent);
        }

        /// <summary>
        /// 分页查询活动规则配置
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageNum"></param>
        /// <param name="pagingCookie"></param>
        /// <param name="service"></param>
        /// <returns></returns>
        public static EntityCollection GetActivityRuleData(int pageSize, int pageNum, string pagingCookie, ServiceClient service)
        {
            QueryExpression query = new QueryExpression("new_arconfiguration");
            query.NoLock = true;
            query.ColumnSet.AddColumns("new_projectinfo_first_id", "new_projectinfo_second_id", "new_name");
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_servicename", ConditionOperator.Null);
            query.Criteria.AddCondition("new_projectinfo_first_id", ConditionOperator.NotNull);
            query.Criteria.AddCondition("new_projectinfo_second_id", ConditionOperator.NotNull);
            query.PageInfo.Count = pageSize;
            query.PageInfo.PageNumber = pageNum;
            query.PageInfo.PagingCookie = pagingCookie;
            return service.RetrieveMultipleWithBypassPlugin(query);
        }


        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="strTxtFileHead"></param>
        /// <param name="strInfo"></param>
        public static void WriteLog(string dir, string strTxtFileHead, string strInfo)
        {
            dir = dir + "\\" + DateTime.Now.ToString("yyyy/MM/dd").Replace("/", "-");
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            var finfo = new FileInfo(dir + "\\" + strTxtFileHead + ".log");
            FileStream fs = !finfo.Exists ? finfo.Create() : finfo.OpenWrite();
            //加入时间
            string writeinfo = DateTime.Now.ToString() + ": " + strInfo;
            var w = new StreamWriter(fs);
            try
            {
                w.BaseStream.Seek(0, SeekOrigin.End);
                w.Write(writeinfo);
                w.Write("\r\n\r\n");
            }
            finally
            {
                w.Flush();
                w.Close();
                w.Dispose();
                fs.Close();
                fs.Dispose();
            }
        }
    }
}
