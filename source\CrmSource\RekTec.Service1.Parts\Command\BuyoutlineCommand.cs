﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using RekTec.Crm.BizCommon;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.HiddenApi;
using RekTec.Service1.Parts.Common;
using RekTec.Service1.Parts.Model;
using RekTec.Service1.Parts.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace RekTec.Service1.Parts.Command
{
    /// <summary>
    /// 备件买断单明细
    /// </summary>
    public class BuyoutlineCommand : HiddenCommand
    {
        /// <summary>
        ///  盘点锁库addby xiexiaoguang createdon 2021-12-10
        /// </summary>
        /// <param name="buyoutId"> 买断单id</param>
        public void isinventory(string buyoutId)
        {
            var e = this.OrganizationService.Retrieve("new_buyout", Guid.Parse(buyoutId), new ColumnSet("new_station_id"));
            InventorytaskCommon sd = new InventorytaskCommon();
            string new_servicestation_id = string.Empty;
            string new_station_id = string.Empty;
            if (e.Contains("new_station_id"))
                new_station_id = e.GetAttributeValue<EntityReference>("new_station_id").Id.ToString();
            var typesend = this.OrganizationService.Retrieve("new_srv_station", e.GetAttributeValue<EntityReference>("new_station_id").Id, new ColumnSet("new_servicetype"));
            if (typesend.GetAttributeValue<OptionSetValue>("new_servicetype").Value == 1)
            {
                if (sd.IsInventorytask(new_station_id, "", this.OrganizationService))
                    throw new InvalidPluginExecutionException(GetResource("", "盘点进行中无法进行该操作！"));
            }
            else
            {
                if (sd.IsInventorytask("", new_station_id, this.OrganizationService))
                    throw new InvalidPluginExecutionException(GetResource("", "盘点进行中无法进行该操作！"));
            }
        }


        /// <summary>
        /// 查询备件买断单明细
        /// </summary>
        /// <param name="buyouId">备件买断单id</param>
        /// <param name="queryId">查询单号</param>
        /// <returns></returns>
        public object GetBuyoutline(string buyouId, string queryId, int pageIndex, int pageSize, string startDate, string endDate)
        {
            try
            {
                var param = new
                {
                    buyouId = buyouId,
                    queryId = queryId,
                    pageIndex = pageIndex,
                    pageSize = pageSize,
                    startDate = startDate,
                    endDate = endDate
                };
                var appId = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationServiceAdmin, "sys_warrantyQueryAppId");
                var appKey = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationServiceAdmin, "sys_warrantyQueryAppKey");
                var url = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationServiceAdmin, "sys_getBuyoutLine");
                var tokenUrl = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationServiceAdmin, "sys_warrantyQueryTokenUrl");

                var result = CommonHelper.Post(param, appId, appKey, url, tokenUrl);

                BuyoutLineModel buyoutLineModel = JsonConvert.DeserializeObject<BuyoutLineModel>(result);

                if (buyoutLineModel.statusCode != 200)
                {
                    throw new Exception(buyoutLineModel.message);
                }

                return buyoutLineModel.body;

            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询备件买断明细失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 备件买断 赋值操作
        /// </summary>
        /// <param name="ec">数据集</param>
        /// <param name="new_type">买断类型</param>
        /// <returns></returns>
        public List<BuyoutlineModel> Assignment(EntityCollection ec, int new_type, Guid stationId)
        {
            List<BuyoutlineModel> buyoutlineModels = new List<BuyoutlineModel>();
            try
            {
                foreach (var item in ec.Entities)
                {
                    BuyoutlineModel buyoutlineModel = new BuyoutlineModel();

                    buyoutlineModel.AccessoryId = item.Contains("leProduct.productid") ? item.GetAttributeValue<AliasedValue>("leProduct.productid").Value.ToString() : string.Empty;
                    buyoutlineModel.AccessoryName = item.Contains("leProduct.name") ? item.GetAttributeValue<AliasedValue>("leProduct.name").Value.ToString() : string.Empty;
                    buyoutlineModel.AccessoryCode = item.Contains("leProduct.productnumber") ? item.GetAttributeValue<AliasedValue>("leProduct.productnumber").Value.ToString() : string.Empty;

                    var res = getprice(stationId, new Guid(buyoutlineModel.AccessoryId), buyoutlineModel.AccessoryCode);
                    buyoutlineModel.Price = res.Price;

                    if (res.Currency != null)
                    {
                        buyoutlineModel.CurrencyId = res.Currency.Id.ToString();
                        buyoutlineModel.CurrencyName = res.Currency.Name;
                    }

                    buyoutlineModel.Count = item.Contains("lePartline.new_qty") ? (decimal)item.GetAttributeValue<AliasedValue>("lePartline.new_qty").Value : 0;
                    buyoutlineModel.Amount = buyoutlineModel.Count * buyoutlineModel.Price;

                    if (new_type == 1)
                    {
                        buyoutlineModel.InvadjId = item.GetAttributeValue<Guid>("new_invadjid").ToString();
                        buyoutlineModel.InvadjIdName = item.GetAttributeValue<string>("new_name");
                        buyoutlineModel.InvadjlineId = item.Contains("leInvadjline.new_invadjlineid") ? item.GetAttributeValue<AliasedValue>("leInvadjline.new_invadjlineid").Value.ToString() : string.Empty;
                    }
                    if (new_type == 2)
                    {
                        buyoutlineModel.ServiceId = item.GetAttributeValue<Guid>("new_srv_workorderid").ToString();
                        buyoutlineModel.ServiceIdName = item.GetAttributeValue<string>("new_name");
                        buyoutlineModel.Partlineid = item.Contains("lePartline.new_srv_partlineid") ? item.GetAttributeValue<AliasedValue>("lePartline.new_srv_partlineid").Value.ToString() : string.Empty;

                        buyoutlineModel.IMEI = item.Contains("lePartline.new_oldimei") ? item.GetAttributeValue<AliasedValue>("lePartline.new_oldimei").Value.ToString() : string.Empty;

                    }
                    if (new_type == 3)
                    {
                        buyoutlineModel.WarehouseId = item.GetAttributeValue<Guid>("new_srv_stocksiteid").ToString();
                        buyoutlineModel.WarehouseName = item.GetAttributeValue<string>("new_name");
                        buyoutlineModel.Inventory = item.Contains("leSiteinv.new_ablenum") ? (decimal)item.GetAttributeValue<AliasedValue>("leSiteinv.new_ablenum").Value : 0;
                    }
                    buyoutlineModels.Add(buyoutlineModel);
                }
                return buyoutlineModels;
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 生成备件买断单明细
        /// </summary>
        /// <param name="buyoulines">买断单明细</param>
        /// <param name="buyouId">买断单Id</param>
        /// <param name="type">买断类型</param>
        public object CreateBuyoutline(string buyoulines, string buyouId)
        {
            isinventory(buyouId);
            BuyoutlineAddModel buyoutlineAddModel = new BuyoutlineAddModel();
            buyoutlineAddModel.ParentEntityId = buyouId;
            //buyoutlineAddModel.Rows = JsonHelper.Deserialize<List<BuyoutlineModel>>(buyoulines);
            var new_buyout = OrganizationService.Retrieve("new_buyout", new Guid(buyouId), new ColumnSet("new_name", "new_queuecount", "new_type"));

            var new_type = new_buyout.GetAttributeValue<OptionSetValue>("new_type");

            List<BuyoutlineModel> rows = JsonHelper.Deserialize<List<BuyoutlineModel>>(buyoulines);
            List<string> errorOrder = new List<string>();
            //零售通收款工单从保外买断中剔除
            if (new_type != null && new_type.Value == 2)
            {
                rows = rows.Where(r => !checkServiceOrderStatus(r.ServiceId, r.ServiceIdName, errorOrder)).ToList();
            }
            buyoutlineAddModel.Rows = rows.OrderBy(m => m.ServiceId).OrderBy(m => m.AccessoryId).ToList();

            //买断单明细数量校验
            checkMaxLines(buyouId, buyoutlineAddModel.Rows.Count);

            var new_name = new_buyout.GetAttributeValue<string>("new_name");
            var new_queuecount = new_buyout.GetAttributeValue<int>("new_queuecount");

            Entity entity = new Entity("new_buyout");
            entity.Id = new Guid(buyoutlineAddModel.ParentEntityId);
            entity["new_importstatus"] = new OptionSetValue(1);
            if (buyoutlineAddModel.Rows.Count < 50)
            {
                ServriceBusCommon.queuename = "crm_buyout_confirm1";
                ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);
                entity["new_queuecount"] = new_queuecount + 1;
            }
            else
            {
                var groupCount = buyoutlineAddModel.Rows.Count / 2;

                var list1 = buyoutlineAddModel.Rows.Skip(0).Take(groupCount).ToList();
                var list2 = buyoutlineAddModel.Rows.Skip(groupCount).Take(buyoutlineAddModel.Rows.Count).ToList();
                //var list3 = buyoutlineAddModel.Rows.Skip(groupCount * 2).Take(groupCount).ToList();
                //var list4 = buyoutlineAddModel.Rows.Skip(groupCount * 3).Take(buyoutlineAddModel.Rows.Count).ToList();

                buyoutlineAddModel.Rows = list1;
                ServriceBusCommon.queuename = "crm_buyout_confirm1";
                ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                buyoutlineAddModel.Rows = list2;
                ServriceBusCommon.queuename = "crm_buyout_confirm2";
                ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                //buyoutlineAddModel.Rows = list3;
                //ServriceBusCommon.queuename = "crm_buyout_confirm3";
                //ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                //buyoutlineAddModel.Rows = list4;
                //ServriceBusCommon.queuename = "crm_buyout_confirm4";
                //ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);
                entity["new_queuecount"] = new_queuecount + 2;
            }
            OrganizationService.Update(entity);
            CreateLineResult result = new CreateLineResult(0,"");
            if (errorOrder.Count > 0)
            {
                result.errorcount = errorOrder.Count;
                result.errormsg = $"【{string.Join("、", errorOrder.Distinct())}】工单小米已收款，无需买断！";
            }
            return result;
        }

        /// <summary>
        /// 校验买断单明细的最大数量
        /// </summary>
        /// <param name="buyoutId"></param>
        /// <param name="count"></param>
        public void checkMaxLines(string buyoutId, int count)
        {
            string maxlines = GetSystemParameterValue(OrganizationService, "sys_buyoutMaxLines");
            if(string.IsNullOrEmpty(maxlines))
            {
                throw new InvalidPluginExecutionException(string.Format(GetResource("Buyout.LockSysParam", "系统参数中未配置[{0}]的值！"), "sys_buyoutMaxLines"));
            }
            int maxnum = int.Parse(maxlines);
            QueryExpression query = new QueryExpression("new_buyoutline");
            query.ColumnSet = new ColumnSet("new_buyoutlineid");
            query.Criteria.AddCondition("new_buyout_id", ConditionOperator.Equal, buyoutId);
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            var ec = OrganizationService.RetrieveMultiple(query);
            if (ec == null || ec.Entities == null || ec.Entities.Count <= 0)
            {
                if(count > maxnum)
                {
                    throw new InvalidPluginExecutionException(string.Format(GetResource("Buyout.MaxLinesLimits", "买断单明细数量不能超过{0}条！"), maxnum));
                }
            }
            else
            {
                var currentnum = ec.Entities.Count + count;
                if (currentnum > maxnum)
                {
                    throw new InvalidPluginExecutionException(string.Format(GetResource("Buyout.MaxLinesLimits", "买断单明细数量不能超过{0}条！"),maxnum));
                }
            }
        }

        /// <summary>
        /// 根据价格模板获取价格 addby xiexiaoguang createdon 2021-11-23
        /// </summary>
        /// <param name="stationid">服务商id</param>
        /// <param name="productid">物料id</param>
        /// <param name="currency">币种</param>
        /// <returns></returns>
        public Replacement getprice(Guid stationid, Guid productid, string code)
        {
            try
            {
                QueryExpression qe = new QueryExpression("new_srv_partprice");
                qe.ColumnSet = new ColumnSet("new_outsideprice", "new_transactioncurrencyid");
                qe.Criteria.AddCondition("new_partsid", ConditionOperator.Equal, productid);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity le1 = new LinkEntity("new_srv_partprice", "new_srv_partpricetemplet", "new_partpricetemplet_id", "new_srv_partpricetempletid", JoinOperator.Inner);
                qe.LinkEntities.Add(le1);

                LinkEntity le2 = new LinkEntity("new_srv_partpricetemplet", "new_srv_station", "new_srv_partpricetempletid", "new_pricetemplate", JoinOperator.Inner);
                le2.LinkCriteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, stationid);
                le1.LinkEntities.Add(le2);

                var ec = OrganizationService.RetrieveMultiple(qe);//备件价格模板

                if (ec?.Entities?.Count > 0)
                {
                    Replacement replacement = new Replacement();
                    replacement.Price = ec.Entities.First().GetAttributeValue<decimal>("new_outsideprice");
                    if (ec.Entities.First().Contains("new_transactioncurrencyid"))
                        replacement.Currency = ec.Entities.First().GetAttributeValue<EntityReference>("new_transactioncurrencyid");//货币
                    return replacement;
                }
                throw new InvalidPluginExecutionException($"物料[{code}]未查询到对应价格模板！");
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("查询价格模板出错:" + ex.Message);
            }
        }

        /// <summary>
        /// 确认买断
        /// </summary>
        /// <param name="buyoutId">买断单id</param>
        /// <param name="type">买断类型</param>
        public void BuyoutConfirm(string buyoutId, int type)
        {
            try
            {
                isinventory(buyoutId);
                var new_name = GetName("new_buyout", new Guid(buyoutId));

                //ServriceBusCommon.queuename = "crm_buyout_buyoutconfirm";
                //ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(new { buyoutId, type, new_name }), this.OrganizationService, new_name);
                PartsHelper.CreatePartsLog(OrganizationServiceAdmin,"",new_name, "crm_buyout_buyoutconfirm", "crm_buyout_buyoutconfirm", JsonConvert.SerializeObject(new { buyoutId, type, new_name }),1,"",Log);
                #region 修改状态为买断确认中
                Entity entity = new Entity("new_buyout");
                entity.Id = new Guid(buyoutId);
                entity["new_status"] = new OptionSetValue(6);//确认买断修改买断状态 买断确认中
                entity["new_confirmbuyouttime"] = DateTime.UtcNow;
                OrganizationService.Update(entity);
                #endregion
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }

        }

        /// <summary>
        /// 确认付款，调用营收稽核接口
        /// </summary>
        /// <param name="buyoutId"></param>
        public void Audit(string buyoutId)
        {
            Entity new_application_apilog1 = new Entity("new_application_apilog");
            try
            {
                //string[] codes = new string[] { "CZ", "RO", "FR", "IT", "DE", "ES", "GB", "NL", "PL"};
                string countryCodes = PartsCommonHelper.GetSystemParamValue(OrganizationService, "Buyout_TaxingEngineCountries");
                if (string.IsNullOrEmpty(countryCodes))
                {
                    throw new InvalidPluginExecutionException($"RekTec.Service1.Parts.Command.Audit() 缺少系统参数Buyout_TaxingEngineCountries");
                }
                string[] codes = countryCodes.Split(';');
                //isinventory(buyoutId);
                QueryExpression qeBuyoutline = new QueryExpression("new_buyoutline");
                qeBuyoutline.ColumnSet = new ColumnSet("new_code", "new_qty", "new_amount", "new_taxamount", "new_workorder_id", "new_price");
                qeBuyoutline.Criteria.AddCondition("new_buyout_id", ConditionOperator.Equal, buyoutId);
                qeBuyoutline.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity leBuyout = new LinkEntity("new_buyoutline", "new_buyout", "new_buyout_id", "new_buyoutid", JoinOperator.Inner);
                leBuyout.Columns = new ColumnSet("new_name", "new_transactioncurrency_id");
                leBuyout.EntityAlias = "leBuyout";
                qeBuyoutline.LinkEntities.Add(leBuyout);

                LinkEntity leCurrency = new LinkEntity("new_buyout", "transactioncurrency", "new_transactioncurrency_id", "transactioncurrencyid", JoinOperator.Inner);
                leCurrency.Columns = new ColumnSet("isocurrencycode");
                leCurrency.EntityAlias = "leCurrency";
                leBuyout.LinkEntities.Add(leCurrency);

                LinkEntity leStation = new LinkEntity("new_buyout", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                leStation.EntityAlias = "leStation";
                leStation.Columns = new ColumnSet("new_sapid", "new_customersapid");
                leBuyout.LinkEntities.Add(leStation);

                LinkEntity leCountry = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                leCountry.EntityAlias = "leCountry";
                leCountry.Columns = new ColumnSet("new_code");
                leStation.LinkEntities.Add(leCountry);

                LinkEntity leWorkorder = new LinkEntity("new_buyoutline", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                leWorkorder.EntityAlias = "leWorkorder";
                qeBuyoutline.LinkEntities.Add(leWorkorder);

                LinkEntity leGoodsfiles = new LinkEntity("new_srv_workorder", "new_goodsfiles", "new_goodsfiles_id", "new_goodsfilesid", JoinOperator.Inner);
                leGoodsfiles.EntityAlias = "leGoodsfiles";
                leGoodsfiles.Columns = new ColumnSet("new_sku");
                leWorkorder.LinkEntities.Add(leGoodsfiles);

                LinkEntity linkEntity = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                linkEntity.EntityAlias = "linkEntity";
                linkEntity.Columns = new ColumnSet("new_sapid");
                leWorkorder.LinkEntities.Add(linkEntity);

                LinkEntity leProduct = new LinkEntity("new_buyoutline", "product", "new_newproduct_id", "productid", JoinOperator.Inner);
                leProduct.EntityAlias = "leProduct";
                leProduct.Columns = new ColumnSet("new_isgoods");
                qeBuyoutline.LinkEntities.Add(leProduct);

                LinkEntity leGoods = new LinkEntity("product", "new_goodsfiles", "new_goods_id", "new_goodsfilesid", JoinOperator.LeftOuter);
                leGoods.EntityAlias = "leGoods";
                leGoods.Columns = new ColumnSet("new_sku");
                leProduct.LinkEntities.Add(leGoods);

                var ecBuyoutline = OrganizationService.RetrieveMultiple(qeBuyoutline);

                #region 调用营收稽核接口
                if (ecBuyoutline?.Entities?.Count > 0)
                {
                    string url = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "sys_AuditUrl");
                    string appid = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "sys_AuditAppId");
                    string appkey = CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "sys_AuditAppKey");

                    List<AuditModel> paramDatas = new List<AuditModel>();

                    AuditModel paramData = new AuditModel();
                    paramData.billDetailList = new List<billDetail>();

                    paramData.billId = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();
                    paramData.billType = 720002;
                    paramData.businessDate = int.Parse(DateTime.Now.ToString("yyyyMMdd"));

                    var customer = ecBuyoutline.Entities.First().Contains("leStation.new_customersapid") ? ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leStation.new_customersapid").Value.ToString() : string.Empty;

                    //一次查询所有SAP渠道
                    var sapid_list = ecBuyoutline.Entities.Where(x=>x.Contains("linkEntity.new_sapid")).Select(x=> x.GetAttributeValue<AliasedValue>("linkEntity.new_sapid").Value.ToString()).Distinct().ToArray();
                    var region_list = GetRegion(sapid_list);

                    foreach (var item in ecBuyoutline.Entities)
                    {
                        billDetail billDetail = new billDetail();
                        billDetail.businessType = "BNWXR";
                        billDetail.oowOrder = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();

                        var sapid = item.Contains("linkEntity.new_sapid") ? item.GetAttributeValue<AliasedValue>("linkEntity.new_sapid").Value.ToString() : string.Empty;
                        var company = string.Empty;
                        try
                        {
                            company = sapid.Substring(0, sapid.Length - 1) + "0";
                        }
                        catch (ArgumentOutOfRangeException ex)
                        {
                            if (item.Contains("new_workorder_id"))
                            {
                                string msg = string.Format(GetResource("Buyout.NullSapid", "{0}工单所属网点未配置sap业务发生地！"), item.GetAttributeValue<EntityReference>("new_workorder_id").Name);
                                throw new InvalidPluginExecutionException(msg);
                            }
                            else
                            {
                                throw new InvalidPluginExecutionException(ex.Message);
                            }
                        }
                        var region = region_list.ContainsKey(sapid)? region_list[sapid] :string.Empty;
                        if (string.IsNullOrWhiteSpace(region))
                        {
                            region = item.GetAliasAttributeValue<string>("leCountry.new_code");
                        }
                        billDetail.location = sapid;
                        billDetail.company = company;
                        billDetail.region = region;

                        billDetail.sku1 = item.GetAttributeValue<string>("new_code");
                        if (item.Contains("leProduct.new_isgoods"))
                        {
                            bool new_isgoods = (bool)item.GetAttributeValue<AliasedValue>("leProduct.new_isgoods").Value;
                            if (new_isgoods)
                            {
                                if (item.Contains("leGoods.new_sku"))
                                {
                                    billDetail.sku1 = item.GetAttributeValue<AliasedValue>("leGoods.new_sku").Value.ToString();
                                }
                            }
                        }
                        billDetail.quantity1 = Convert.ToInt32(item.GetAttributeValue<decimal>("new_qty"));

                        if (item.Contains("leGoodsfiles.new_sku"))
                            billDetail.sku2 = item.GetAttributeValue<AliasedValue>("leGoodsfiles.new_sku").Value.ToString();

                        billDetail.quantity2 = 1;

                        if (codes.Contains(item.GetAliasAttributeValue<string>("leCountry.new_code")))
                        {
                            //这里将 new_amount 改为 new_price 是正确的修改。这样可以确保使用单价而不是总金额来计算税额。
                            billDetail.originPrice = Math.Round(item.GetAttributeValue<decimal>("new_price"), 2);
                            billDetail.receivables = Math.Round(item.GetAttributeValue<decimal>("new_taxamount"), 2);
                        }
                        else
                        {
                            billDetail.receivables = Math.Round(item.GetAttributeValue<decimal>("new_amount"), 2);
                        }

                        if (item.Contains("leBuyout.new_name"))
                            billDetail.serviceno = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();

                        if (item.Contains("leCurrency.isocurrencycode"))
                            billDetail.currency = item.GetAttributeValue<AliasedValue>("leCurrency.isocurrencycode").Value.ToString();

                        billDetail.outMonth = DateTime.Now.ToString("yyyyMM");

                        paramData.billDetailList.Add(billDetail);

                        billDetail billDetail1 = new billDetail();
                        billDetail1.businessType = "BWWX";
                        billDetail1.oowOrder = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();


                        billDetail1.location = sapid;
                        billDetail1.company = company;
                        billDetail1.region = region;

                        billDetail1.sku1 = item.GetAttributeValue<string>("new_code");
                        if (item.Contains("leProduct.new_isgoods"))
                        {
                            bool new_isgoods = (bool)item.GetAttributeValue<AliasedValue>("leProduct.new_isgoods").Value;
                            if (new_isgoods)
                            {
                                if (item.Contains("leGoods.new_sku"))
                                {
                                    billDetail1.sku1 = item.GetAttributeValue<AliasedValue>("leGoods.new_sku").Value.ToString();
                                }
                            }
                        }
                        billDetail1.quantity1 = Convert.ToInt32(item.GetAttributeValue<decimal>("new_qty"));

                        if (item.Contains("leGoodsfiles.new_sku"))
                            billDetail1.sku2 = item.GetAttributeValue<AliasedValue>("leGoodsfiles.new_sku").Value.ToString();
                        billDetail1.quantity2 = 1;

                        if (codes.Contains(item.GetAliasAttributeValue<string>("leCountry.new_code")))
                        {
                            //这里将 new_amount 改为 new_price 是正确的修改。这样可以确保使用单价而不是总金额来计算税额。
                            billDetail1.originPrice = Math.Round(item.GetAttributeValue<decimal>("new_price"), 2);
                            billDetail1.receivables = Math.Round(item.GetAttributeValue<decimal>("new_taxamount"), 2);
                            if (item.Contains("new_workorder_id"))
                            {
                                billDetail1.documentNo = item.GetAttributeValue<EntityReference>("new_workorder_id").Name;
                            }
                        }
                        else
                        {
                            billDetail1.receivables = Math.Round(item.GetAttributeValue<decimal>("new_amount"), 2);
                        }

                        if (item.Contains("leBuyout.new_name"))
                            billDetail1.serviceno = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();

                        if (item.Contains("leCurrency.isocurrencycode"))
                            billDetail1.currency = item.GetAttributeValue<AliasedValue>("leCurrency.isocurrencycode").Value.ToString();

                        billDetail1.outMonth = DateTime.Now.ToString("yyyyMM");

                        paramData.billDetailList.Add(billDetail1);
                    }

                    paramData.billDetailList.ForEach(m => m.customer = customer);

                    paramDatas.Add(paramData);

                    var requestData = CommonHelper.GetX5Data(paramDatas, appid, appkey, url, "commonBill.create");

                    string data = CommonHelper.EncodeBase64(JsonConvert.SerializeObject(requestData));

                    var result = CommonHelper.X5Post(data, url);

                    var strRequestData = JsonConvert.SerializeObject(requestData);
                    new_application_apilog1["new_requesttype"] = new OptionSetValue(2);
                    new_application_apilog1["new_code"] = ecBuyoutline.Entities.First().GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString();
                    new_application_apilog1["new_requesttime"] = DateTime.Now;
                    new_application_apilog1["new_issuccess"] = true;
                    new_application_apilog1["new_requestcontent"] = strRequestData.Length > 999999 ? strRequestData.Substring(0, 999999) : strRequestData;
                    new_application_apilog1["new_name"] = "营收稽核";
                    new_application_apilog1["new_requestmethod"] = "commonBill.create";
                    new_application_apilog1["new_response"] = result;
                    #endregion

                    #region 修改状态为已付款
                    Entity entity = new Entity("new_buyout");
                    entity.Id = new Guid(buyoutId);
                    entity["new_status"] = new OptionSetValue(3);
                    OrganizationService.Update(entity);
                    #endregion
                }
            }
            catch (Exception ex)
            {
                new_application_apilog1["new_issuccess"] = false;
                new_application_apilog1["new_exception"] = ex.Message;
                Log.ErrorMsg($"备件买断单明细推送营收稽核出错：{ex}" );
                throw new InvalidPluginExecutionException(ex.Message);
            }
            finally
            {
                OrganizationService.Create(new_application_apilog1);
            }
        }


        /// <summary>
        /// 报表导入 addby yuzhuoqun createdon 2022-02-18
        /// </summary>
        /// <param name="importLogId"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public ImportResultModel fileexceladd(string importLogId, string content)
        {
            ImportResultModel result = new ImportResultModel();
            if (string.IsNullOrWhiteSpace(content))
                return result;
            try
            {
                result.Messages = new List<ImportMessage>();
                BuyoutlineAddModel buyoutlineAddModel = RekTec.Crm.Common.Helper.JsonHelper.Deserialize<BuyoutlineAddModel>(content);

                var new_buyout = OrganizationService.Retrieve("new_buyout", new Guid(buyoutlineAddModel.ParentEntityId), new ColumnSet("new_name", "new_queuecount", "new_type"));
                var new_type = new_buyout.GetAttributeValue<OptionSetValue>("new_type");
                List<BuyoutlineModel> rows = buyoutlineAddModel.Rows;
                List<string> errorOrder = new List<string>();
                //零售通收款工单从保外买断中剔除
                if (new_type != null && new_type.Value == 2)
                {
                    rows = rows.Where(r => !checkServiceOrderStatus(r.ServiceId, r.ServiceIdName, errorOrder)).ToList();
                }

                buyoutlineAddModel.Rows = rows.OrderBy(m => m.ServiceId).OrderBy(m => m.AccessoryId).ToList();

                //买断单明细数量校验
                checkMaxLines(buyoutlineAddModel.ParentEntityId, buyoutlineAddModel.Rows.Count);
                var new_name = new_buyout.GetAttributeValue<string>("new_name");
                var new_queuecount = new_buyout.GetAttributeValue<int>("new_queuecount");
                Entity entity = new Entity("new_buyout");
                entity.Id = new Guid(buyoutlineAddModel.ParentEntityId);
                if (buyoutlineAddModel.Rows.Count < 50)
                {
                    ServriceBusCommon.queuename = "crm_buyout_confirm1";
                    ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);
                    entity["new_queuecount"] = new_queuecount + 1;
                }
                else
                {
                    var groupCount = buyoutlineAddModel.Rows.Count / 2;

                    var list1 = buyoutlineAddModel.Rows.Skip(0).Take(groupCount).ToList();
                    var list2 = buyoutlineAddModel.Rows.Skip(groupCount).Take(buyoutlineAddModel.Rows.Count).ToList();

                    buyoutlineAddModel.Rows = list1;
                    ServriceBusCommon.queuename = "crm_buyout_confirm1";
                    ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                    buyoutlineAddModel.Rows = list2;
                    ServriceBusCommon.queuename = "crm_buyout_confirm2";
                    ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                    //buyoutlineAddModel.Rows = list3;
                    //ServriceBusCommon.queuename = "crm_buyout_confirm3";
                    //ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);

                    //buyoutlineAddModel.Rows = list4;
                    //ServriceBusCommon.queuename = "crm_buyout_confirm4";
                    //ServriceBusCommon.sendservricebus(JsonConvert.SerializeObject(buyoutlineAddModel), this.OrganizationService, new_name);
                    entity["new_queuecount"] = new_queuecount + 2;
                }
                OrganizationService.Update(entity);
                if (errorOrder.Count > 0)
                {
                    ImportMessage import = new ImportMessage()
                    {
                        Message = $"【{string.Join("、", errorOrder.Distinct())}】工单小米已收款，无需买断！",
                        MessageType = 4
                    };
                    result.Messages.Add(import);
                }
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.DebugMsg(ex.Message);
                ImportMessage import = new ImportMessage()
                {
                    Message = ex.Message,
                    MessageType = 4
                };
                result.Messages.Add(import);
                Log.ErrorMsg("添加备件买断单明细报表导入出错：" + ex);
                // throw new InvalidPluginExecutionException(GetResource("", "添加备件采购明细报表导入出错：" + ex));
            }

            return result;
        }
        /// <summary>
        /// 根据指定字段值获取id
        /// </summary>
        /// <param name="entityName"></param>
        /// <param name="fieldName"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        public Guid GetEntityId(string entityName, string fieldName, string fieldValue)
        {
            QueryExpression qe = new QueryExpression(entityName);
            qe.Criteria.AddCondition(fieldName, ConditionOperator.Equal, fieldValue);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var ec = OrganizationService.RetrieveMultiple(qe);

            if (ec?.Entities?.Count > 0)
                return ec.Entities.First().Id;
            return default;
        }
        /// <summary>
        /// 查询sap区域配置表
        /// </summary>
        /// <param name="location"></param>
        /// <returns></returns>
        public Dictionary<string, string> GetRegion(string[] location)
        {
            Dictionary<string, string> regions = new Dictionary<string, string>();
            if (location == null || location.Count() == 0) return regions;
            QueryExpression qe = new QueryExpression("new_regionconfig");
            qe.ColumnSet = new ColumnSet("new_region", "new_sap_location");
            qe.Criteria.AddCondition("new_sap_location", ConditionOperator.In, location);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var ec = OrganizationService.RetrieveMultiple(qe);
            if (ec?.Entities?.Count > 0)
            {
                foreach (var item in ec.Entities) {
                    regions.Add(item.GetAttributeValue<string>("new_sap_location"), item.GetAttributeValue<string>("new_region"));
                }
            }
                
            return regions;
        }
        /// <summary>
        /// 获取实体new_name字段
        /// </summary>
        /// <param name="entityName"></param>
        /// <param name="guid"></param>
        /// <returns></returns>
        public string GetName(string entityName, Guid guid)
        {
            var ec = OrganizationService.Retrieve(entityName, guid, new ColumnSet("new_name"));
            if (ec != null)
            {
                return ec.GetAttributeValue<string>("new_name");
            }
            return string.Empty;
        }
        /// <summary>
        /// 买断单明细禁用
        /// </summary>
        /// <param name="ids"></param>
        public void BuyoutLineDisable(string[] ids)
        {
            try
            {
                foreach (var id in ids)
                {
                    QueryExpression qe = new QueryExpression("new_buyoutline");
                    qe.ColumnSet = new ColumnSet("new_invadj_id", "new_invadjline_id", "new_partline_id", "new_buyout_id", "new_name", "new_qty", "new_stocksite_id", "new_product_id", "new_workorder_id", "new_ismanage");
                    qe.Criteria.AddCondition("new_buyoutlineid", ConditionOperator.Equal, id);

                    LinkEntity le = new LinkEntity("new_buyoutline", "new_buyout", "new_buyout_id", "new_buyoutid", JoinOperator.Inner);
                    le.EntityAlias = "le";
                    le.Columns = new ColumnSet("new_type", "new_station_id", "new_status", "new_buyoutid");
                    qe.LinkEntities.Add(le);

                    LinkEntity leProduct = new LinkEntity("new_buyoutline", "product", "new_product_id", "productid", JoinOperator.Inner);
                    leProduct.EntityAlias = "leProduct";
                    leProduct.Columns = new ColumnSet("new_isserial", "productid");
                    qe.LinkEntities.Add(leProduct);

                    var ec = OrganizationService.RetrieveMultiple(qe);

                    List<snmodel> sns = new List<snmodel>();

                    if (ec is null || ec.Entities is null || ec.Entities.Count == 0) return;

                    var new_buyoutline = ec.Entities.FirstOrDefault();

                    var new_status = ((OptionSetValue)new_buyoutline.GetAttributeValue<AliasedValue>("le.new_status").Value).Value;
                    if (new_status != 1)
                        throw new InvalidPluginExecutionException(PluginResourceHelper.GetResource(this.OrganizationServiceAdmin, LangCode, "new_buyoutimeiline.buyoutimeilinenotdelete", "当前明细所属的备件备品买断单是非制单状态，不允许删除当前明细！"));


                    var productid = new_buyoutline.GetAttributeValue<AliasedValue>("leProduct.productid").Value;

                    string stationId = ((EntityReference)new_buyoutline.GetAttributeValue<AliasedValue>("le.new_station_id").Value).Id.ToString();

                    var new_name = new_buyoutline.GetAttributeValue<string>("new_name");
                    var new_qty = new_buyoutline.GetAttributeValue<decimal>("new_qty");

                    var new_type = ((OptionSetValue)new_buyoutline.GetAttributeValue<AliasedValue>("le.new_type").Value).Value;

                    var new_isserial = Convert.ToBoolean(new_buyoutline.GetAttributeValue<AliasedValue>("leProduct.new_isserial").Value);

                    var new_buyoutid = new_buyoutline.GetAttributeValue<AliasedValue>("le.new_buyoutid").Value.ToString();

                    //仓库id
                    Guid stocksiteId = Guid.Empty;
                    Entity new_stocksite = new Entity();

                    if (new_type == 1) //丢件买断
                    {
                        if (new_buyoutline.Contains("new_invadj_id"))
                        {
                            var new_invadj_id = new_buyoutline.GetAttributeValue<EntityReference>("new_invadj_id").Id;
                            var new_invadjline_id = new_buyoutline.GetAttributeValue<EntityReference>("new_invadjline_id").Id;

                            if (new_isserial)  //是否串号管理为是
                            {
                                QueryExpression qeInvadjline = new QueryExpression("new_invadjcodeline");
                                qeInvadjline.ColumnSet = new ColumnSet("new_name", "new_imei", "new_imei2");
                                qeInvadjline.Criteria.AddCondition("new_invadjid", ConditionOperator.Equal, new_invadj_id);//库存调整单号
                                qeInvadjline.Criteria.AddCondition("new_invadjline_id", ConditionOperator.Equal, new_invadjline_id);//库存调整单明细
                                qeInvadjline.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                                var ecInvadjline = OrganizationService.RetrieveMultiple(qeInvadjline);

                                if (ecInvadjline?.Entities?.Count == 0)
                                    throw new Exception(GetResource("", "查询串号失败！"));

                                foreach (var invadjline in ecInvadjline.Entities)
                                {
                                    snmodel snmodel = new snmodel();
                                    snmodel.sn = invadjline.GetAttributeValue<string>("new_name");
                                    snmodel.imei1 = invadjline.GetAttributeValue<string>("new_imei");
                                    snmodel.imei2 = invadjline.GetAttributeValue<string>("new_imei2");
                                    sns.Add(snmodel);
                                }
                            }

                            Entity entity = new Entity("new_invadj");
                            entity.Id = new_invadj_id;
                            entity["new_buyoutstate"] = new OptionSetValue(1);
                            OrganizationService.Update(entity);

                            var new_invadj = OrganizationService.Retrieve("new_invadj", new_invadj_id, new ColumnSet("new_stocksite_id"));
                            if (new_invadj == null || !new_invadj.Contains("new_stocksite_id"))
                                throw new InvalidPluginExecutionException(GetResource("new_buyoutline.DeleteCheck", "删除失败：查询库存调整单失败！"));

                            stocksiteId = new_invadj.GetAttributeValue<EntityReference>("new_stocksite_id").Id;
                            new_stocksite = OrganizationService.Retrieve("new_stocksite", stocksiteId, new ColumnSet("new_stocksitecode"));
                            //SetInventory(stocksiteId, new_name, new_qty);

                        }

                    }

                    if (new_type == 2) //保外买断
                    {
                        //查询服务单更换件明细
                        QueryExpression qePartline = new QueryExpression("new_srv_partline");
                        qePartline.ColumnSet = new ColumnSet("new_oldimei", "new_oldsn", "new_stocksiteid", "new_isserial");
                        qePartline.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, new_buyoutline.GetAttributeValue<EntityReference>("new_workorder_id").Id);//服务单id
                        qePartline.Criteria.AddCondition("new_product_id", ConditionOperator.Equal, new_buyoutline.GetAttributeValue<EntityReference>("new_product_id").Id);//物料id
                                                                                                                                                                            //qePartline.Criteria.AddCondition("new_isserial", ConditionOperator.Equal, true);//是否串号管理
                        qePartline.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        var ecPartline = OrganizationService.RetrieveMultiple(qePartline);

                        if (ecPartline is null || ecPartline.Entities is null || ecPartline.Entities.Count == 0)
                        {
                            throw new Exception("未查询到服务单更换件明细！");
                        }

                        #region 修改服务单更换件明细 买断类型
                        Entity partline = new Entity("new_srv_partline");
                        partline.Id = ecPartline.Entities.First().Id;
                        partline["new_buyoutstate"] = new OptionSetValue(1);
                        partline["new_buyout_id"] = null;//买断单
                        OrganizationService.Update(partline);
                        #endregion


                        //服务仓库
                        var stocksiteid = ecPartline.Entities[0].GetAttributeValue<EntityReference>("new_stocksiteid").Id;
                        new_stocksite = OrganizationService.Retrieve("new_srv_stocksite", stocksiteid, new ColumnSet("new_stocksitecode"));

                        var screen = ecPartline.Entities.Where(m => m.GetAttributeValue<bool>("new_isserial") == true).FirstOrDefault();

                        if (screen != null)
                        {
                            snmodel snmodel = new snmodel();
                            snmodel.sn = screen.GetAttributeValue<string>("new_oldsn");
                            snmodel.imei1 = screen.GetAttributeValue<string>("new_oldimei");
                            sns.Add(snmodel);
                        }
                        //SetInventory(stocksiteId, new_name, new_qty);

                    }

                    if (new_type == 3) //销售买断
                    {
                        stocksiteId = new_buyoutline.GetAttributeValue<EntityReference>("new_stocksite_id").Id;
                        new_stocksite = OrganizationService.Retrieve("new_stocksite", stocksiteId, new ColumnSet("new_stocksitecode"));
                    }
                    this.Command<Parttransaction>().srv_stock(new_stocksite, new_name, new_qty.ToString(), stationId, sns, "new_buyout", new_buyoutid, transactiontypeEnum.Releasefreeze);

                    QueryExpression qeBuyouttimeline = new QueryExpression("new_buyoutimeiline");
                    qeBuyouttimeline.Criteria.AddCondition("new_buyoutline_id", ConditionOperator.Equal, new_buyoutline.Id);
                    var ecBuyouttimeline = OrganizationService.RetrieveMultiple(qeBuyouttimeline);

                    if (ecBuyouttimeline?.Entities?.Count > 0)
                        foreach (var item in ecBuyouttimeline.Entities)
                        {
                            OrganizationService.Delete("new_buyoutimeiline", item.Id);
                        }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }
        /// <summary>
        /// 买断明细报表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Report BuyoutLineReportQuery(BuyoutLineCondition query)
        {
            Log.InfoMsg("query:" + JsonHelper.Serialize(query));
            Report r = new Report();
            List<BuyoutLineReport> buyoutLines = new List<BuyoutLineReport>();
            int total = 0;
            try
            {
                if (query.pageIndex == 0 && query.pageSize == 0)
                {
                    bool isMore = true;
                    query.pageSize = 5000;
                    while (isMore)
                    {
                        query.pageIndex += 1;
                        Report re = GetBuyoutLine(query, true);
                        buyoutLines.AddRange(re.buyoutLineReports);
                        total += re.TotalRecordCount;
                        isMore = re.HasMoreRecords;
                    }
                    r.buyoutLineReports = buyoutLines;
                    r.TotalRecordCount = total;
                }
                else
                {
                    r = GetBuyoutLine(query, false);
                }
                return r;
            }
            catch (Exception e)
            {

                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        public Report GetBuyoutLine(BuyoutLineCondition query, bool isExport)
        {
            Log.InfoMsg("query:" + JsonHelper.Serialize(query));
            Report r = new Report();
            List<BuyoutLineReport> buyoutLines = new List<BuyoutLineReport>();
            try
            {
                #region 

                //Dictionary<int, string> picklist = CrmHelper.GetPicklistOptions(OrganizationService, "new_buyout", "new_status");

                //#region  买断主档

                //QueryExpression qe = new QueryExpression("new_buyout");
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 2);//买断类型为保外买断
                ////qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, "BJMD20220527102546");
                ////qe.Criteria.AddCondition("new_status",ConditionOperator.Equal,6);//买断状态为已买断
                ////qe.Criteria.AddCondition("new_importstatus", ConditionOperator.Equal, 2);//执行状态为执行完成
                ////FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                //if (!string.IsNullOrWhiteSpace(query.buyoutcode))
                //{
                //    //买断单号
                //    qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, query.buyoutcode);
                //}
                //if (!string.IsNullOrWhiteSpace(query.servicestationid))
                //{
                //    //买断服务商
                //    qe.Criteria.AddCondition("new_station_id", ConditionOperator.Equal, query.servicestationid);
                //}
                //if (!string.IsNullOrWhiteSpace(query.ownerid))
                //{
                //    //买断服务商
                //    qe.Criteria.AddCondition("ownerid", ConditionOperator.Equal, query.ownerid);
                //}
                //if (query.buyoutstates != 0)
                //{
                //    //买断状态
                //    qe.Criteria.AddCondition("new_status", ConditionOperator.Equal, query.buyoutstates);
                //}
                //if (query.applytime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.applytime_start))
                //{
                //    qe.Criteria.AddCondition("createdon", ConditionOperator.GreaterEqual, DateTime.Parse(query.applytime_start));
                //}
                //if (query.applytime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.applytime_end))
                //{
                //    qe.Criteria.AddCondition("createdon", ConditionOperator.LessThan, DateTime.Parse(query.applytime_end).AddDays(1));
                //}
                //if (query.applytime_start == "1991-01-01")
                //{
                //    //filter.AddCondition("createdon", ConditionOperator.DoesNotContainValues);
                //}
                //if (query.outtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.outtime_start))
                //{
                //    qe.Criteria.AddCondition("new_confirmbuyouttime", ConditionOperator.GreaterEqual, DateTime.Parse(query.outtime_start));
                //}
                //if (query.outtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.outtime_end))
                //{
                //    qe.Criteria.AddCondition("new_confirmbuyouttime", ConditionOperator.LessThan, DateTime.Parse(query.outtime_end).AddDays(1));
                //}
                //if (query.outtime_start == "1991-01-01")
                //{
                //    //filter.AddCondition("new_confirmbuyouttime", ConditionOperator.DoesNotContainValues);
                //}
                //if (query.uploadtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.uploadtime_start))
                //{
                //    qe.Criteria.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.GreaterEqual, DateTime.Parse(query.uploadtime_start));
                //}
                //if (query.uploadtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.uploadtime_end))
                //{
                //    qe.Criteria.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.LessThan, DateTime.Parse(query.uploadtime_end).AddDays(1));
                //}
                //if (query.uploadtime_start == "1991-01-01")
                //{
                //    //filter.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.DoesNotContainValues);
                //}
                //qe.ColumnSet.AddColumns("new_name", "new_status", "ownerid", "createdon", "new_confirmbuyouttime", "new_uploadpaymentvouchertime");
                ////qe.Criteria.AddFilter(filter);

                //#endregion

                //#region 买断明细

                //LinkEntity line = new LinkEntity("new_buyout", "new_buyoutline", "new_buyoutid", "new_buyout_id", JoinOperator.LeftOuter);
                //line.EntityAlias = "line";
                //line.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //if (!string.IsNullOrWhiteSpace(query.productid))
                //{
                //    line.LinkCriteria.AddCondition("new_product_id", ConditionOperator.Equal, query.productid);
                //}
                //line.Columns.AddColumns("new_amount", "ownerid", "new_qty", "new_price", "new_product_id", "new_name");

                //#endregion

                //#region 买断服务商信息

                //LinkEntity station = new LinkEntity("new_buyout", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                //station.EntityAlias = "station";
                //station.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //station.Columns.AddColumns("new_customersapid", "new_code", "new_name");
                //if (!string.IsNullOrWhiteSpace(query.sapcode))
                //{
                //    //sapcode
                //    station.LinkCriteria.AddCondition("new_customersapid", ConditionOperator.Equal, query.sapcode);
                //}

                //#endregion

                //#region 工单信息

                //LinkEntity order = new LinkEntity("new_buyoutline", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                //order.EntityAlias = "order";
                //order.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //order.Columns.AddColumns("new_name", "new_repairprovider_id", "new_endtime");

                //if (!string.IsNullOrWhiteSpace(query.workordercode))
                //{
                //    order.LinkCriteria.AddCondition("new_name", ConditionOperator.Equal, query.workordercode);
                //}
                //if (!string.IsNullOrWhiteSpace(query.repairstationid))
                //{
                //    order.LinkCriteria.AddCondition("new_repairstation_id", ConditionOperator.Equal, query.repairstationid);
                //}
                //if (!string.IsNullOrWhiteSpace(query.stationid))
                //{
                //    order.LinkCriteria.AddCondition("new_station_id", ConditionOperator.Equal, query.stationid);
                //}
                //if (query.closetime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.closetime_start))
                //{
                //    order.LinkCriteria.AddCondition("new_endtime", ConditionOperator.GreaterEqual, DateTime.Parse(query.closetime_start));
                //}
                //if (query.closetime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.closetime_end))
                //{
                //    order.LinkCriteria.AddCondition("new_endtime", ConditionOperator.LessThan, DateTime.Parse(query.closetime_end).AddDays(1));
                //}
                ////if (query.closetime_end == "1991-01-01")
                ////{
                ////    FilterExpression filterorder = new FilterExpression(LogicalOperator.Or);
                ////    filterorder.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.DoesNotContainValues);
                ////    order.LinkCriteria.AddFilter(filterorder);
                ////}

                //#endregion

                //#region 所属网点信息

                //LinkEntity sta = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                //sta.EntityAlias = "sta";
                //sta.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //if (!string.IsNullOrWhiteSpace(query.stationid))
                //{
                //    sta.LinkCriteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, query.stationid);
                //}
                //sta.Columns.AddColumns("new_name", "new_code");

                //#endregion

                //#region 网点信息

                //LinkEntity resta = new LinkEntity("new_srv_workorder", "new_srv_station", "new_repairstation_id", "new_srv_stationid", JoinOperator.LeftOuter);
                //resta.EntityAlias = "resta";
                //if (!string.IsNullOrWhiteSpace(query.repairstationid))
                //{
                //    resta.LinkCriteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, query.repairstationid);
                //}
                //resta.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //resta.Columns.AddColumns("new_name", "new_code");

                //#endregion

                //#region 附件信息

                ////LinkEntity file = new LinkEntity("new_buyout", "new_picturepath", "new_buyoutid", "new_relationid", JoinOperator.MatchFirstRowUsingCrossApply);
                ////file.EntityAlias = "file";
                ////file.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                ////file.LinkCriteria.AddCondition("new_formstatus", ConditionOperator.Equal, 1);
                ////file.LinkCriteria.AddCondition("new_relationentityname", ConditionOperator.Equal, "new_buyout");
                ////file.Columns.AddColumns("createdon");

                //#endregion

                //#region 库存事务

                ////LinkEntity trans = new LinkEntity("new_buyout", "new_part_stocktransaction", "new_name", "new_fromno", JoinOperator.Inner);
                ////trans.EntityAlias = "trans";
                ////trans.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                ////trans.LinkCriteria.AddCondition("new_transactiontype", ConditionOperator.Equal, 5);
                ////trans.Columns.AddColumns("createdon");

                //#endregion

                //order.LinkEntities.Add(sta);
                //order.LinkEntities.Add(resta);
                //line.LinkEntities.Add(order);
                //qe.LinkEntities.Add(line);
                //qe.LinkEntities.Add(station);
                //qe.PageInfo.PagingCookie = "";

                //if ((query.pageSize != 0 && query.pageIndex != 0))
                //{
                //    qe.PageInfo = new PagingInfo
                //    {
                //        Count = query.pageSize,
                //        PageNumber = query.pageIndex,
                //        ReturnTotalRecordCount = true
                //        //PagingCookie
                //    };
                //}

                //EntityCollection ec = OrganizationService.RetrieveMultiple(qe);

                #endregion

                var ec = this.GetQueryResult(query);

                if (ec == null || ec.Entities == null || ec.Entities.Count == 0)
                {
                    r.TotalRecordCount = 0;
                    return r;
                }
                foreach (Entity item in ec.Entities)
                {
                    BuyoutLineReport blr = new BuyoutLineReport();
                    //买断单号
                    if (item.Contains("new_name"))
                    {
                        blr.buyoutcode = item.GetAttributeValue<string>("new_name");
                    }
                    //服务商编码
                    if (item.Contains("station.new_code"))
                    {
                        blr.servicestationcode = (string)item.GetAttributeValue<AliasedValue>("station.new_code").Value;
                    }
                    //服务商名称
                    if (item.Contains("station.new_name"))
                    {
                        blr.servicestationname = (string)item.GetAttributeValue<AliasedValue>("station.new_name").Value;
                    }
                    //国家/地区
                    if (item.Contains("station.new_country_id"))
                    {
                        blr.countryid = ((EntityReference)item.GetAttributeValue<AliasedValue>("station.new_country_id").Value).Id.ToString();
                        blr.countryid = ((EntityReference)item.GetAttributeValue<AliasedValue>("station.new_country_id").Value).Name;
                    }
                    //申请人账号
                    if (item.Contains("ownerid"))
                    {
                        blr.ownerid = item.GetAttributeValue<EntityReference>("ownerid").Name;
                    }
                    //sapcode
                    if (item.Contains("station.new_customersapid"))
                    {
                        blr.sapcode = (string)item.GetAttributeValue<AliasedValue>("station.new_customersapid").Value;
                    }
                    //维修工单号
                    if (item.Contains("order.new_name"))
                    {
                        blr.workordercode = (string)item.GetAttributeValue<AliasedValue>("order.new_name").Value;
                    }
                    //所属网点代码
                    if (item.Contains("sta.new_code"))
                    {
                        blr.stationcode = (string)item.GetAttributeValue<AliasedValue>("sta.new_code").Value;
                    }
                    //所属网点名称
                    if (item.Contains("sta.new_name"))
                    {
                        blr.stationname = (string)item.GetAttributeValue<AliasedValue>("sta.new_name").Value;
                    }
                    //维修网点代码
                    if (item.Contains("resta.new_code"))
                    {
                        blr.repairstationcode = (string)item.GetAttributeValue<AliasedValue>("resta.new_code").Value;
                    }
                    //维修网点名称
                    if (item.Contains("resta.new_name"))
                    {
                        blr.repairstationname = (string)item.GetAttributeValue<AliasedValue>("resta.new_name").Value;
                    }
                    //仓库名称
                    blr.stockname = "保外坏品库";
                    //买断状态
                    if (item.Contains("new_status"))
                    {
                        blr.buyoutstatus = item.FormattedValues["new_status"];
                        //item.GetAttributeValue<OptionSetValue>("new_status").Value
                        //blr.buyoutstatus = item.GetAttributeValue<OptionSetValue>("new_status");
                    }
                    //工单关单时间
                    if (item.Contains("order.new_endtime"))
                    {
                        blr.closetime = ((DateTime)item.GetAttributeValue<AliasedValue>("order.new_endtime").Value).ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    //申请买断时间
                    if (item.Contains("createdon"))
                    {
                        blr.buyouttime = item.GetAttributeValue<DateTime>("createdon").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    //付款凭证上传时间
                    if (item.Contains("new_uploadpaymentvouchertime"))
                    {
                        blr.uploadtime = item.GetAttributeValue<DateTime>("new_uploadpaymentvouchertime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    //库存出库时间
                    if (item.Contains("new_confirmbuyouttime"))
                    {
                        blr.outtime = item.GetAttributeValue<DateTime>("new_confirmbuyouttime").ToLocalTime(TimeZoneInfo).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    //物料编码
                    if (item.Contains("line.new_name"))
                    {
                        blr.productnumber = (string)item.GetAttributeValue<AliasedValue>("line.new_name").Value;
                    }
                    //物料名称
                    if (item.Contains("line.new_product_id"))
                    {
                        blr.productname = ((EntityReference)item.GetAttributeValue<AliasedValue>("line.new_product_id").Value).Name;
                    }
                    //新物料编码
                    if (item.Contains("line.new_code"))
                    {
                        blr.newproductnumber = (string)item.GetAttributeValue<AliasedValue>("line.new_code").Value;
                    }
                    //新物料名称
                    if (item.Contains("line.new_newproduct_id"))
                    {
                        blr.newproductname = ((EntityReference)item.GetAttributeValue<AliasedValue>("line.new_newproduct_id").Value).Name;
                    }
                    //零售价格
                    if (item.Contains("line.new_price"))
                    {
                        blr.retailprice = (decimal)item.GetAttributeValue<AliasedValue>("line.new_price").Value;
                    }
                    //买断价格
                    if (item.Contains("line.new_price"))
                    {
                        blr.oowprice = (decimal)item.GetAttributeValue<AliasedValue>("line.new_price").Value;
                    }
                    //数量
                    if (item.Contains("line.new_qty"))
                    {
                        blr.qty = (decimal)item.GetAttributeValue<AliasedValue>("line.new_qty").Value;
                    }
                    //金额
                    if (item.Contains("line.new_amount"))
                    {
                        blr.amount = (decimal)item.GetAttributeValue<AliasedValue>("line.new_amount").Value;
                    }

                    buyoutLines.Add(blr);
                }

                if (isExport)
                {
                    r.TotalRecordCount = ec.Entities.Count;
                }
                else
                {
                    r.TotalRecordCount = ec.TotalRecordCount;
                }
                r.HasMoreRecords = ec.MoreRecords;
                r.buyoutLineReports = buyoutLines;
                Log.InfoMsg("buyoutLines:" + JsonHelper.Serialize(buyoutLines));
                return r;
            }
            catch (Exception e)
            {

                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        public EntityCollection GetQueryResult(BuyoutLineCondition query)
        {
            try
            {
                #region  买断主档

                QueryExpression qe = new QueryExpression("new_buyout");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 2);//买断类型为保外买断
                //qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, "BJMD20220527102546");
                //qe.Criteria.AddCondition("new_status",ConditionOperator.Equal,6);//买断状态为已买断
                //qe.Criteria.AddCondition("new_importstatus", ConditionOperator.Equal, 2);//执行状态为执行完成
                //FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                if (!string.IsNullOrWhiteSpace(query.buyoutcode))
                {
                    //买断单号
                    qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, query.buyoutcode);
                }
                if (!string.IsNullOrWhiteSpace(query.servicestationid))
                {
                    //买断服务商
                    qe.Criteria.AddCondition("new_station_id", ConditionOperator.Equal, query.servicestationid);
                }
                if (!string.IsNullOrWhiteSpace(query.ownerid))
                {
                    //买断服务商
                    qe.Criteria.AddCondition("ownerid", ConditionOperator.Equal, query.ownerid);
                }
                if (query.buyoutstates != 0)
                {
                    //买断状态
                    qe.Criteria.AddCondition("new_status", ConditionOperator.Equal, query.buyoutstates);
                }
                if (query.applytime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.applytime_start))
                {
                    qe.Criteria.AddCondition("createdon", ConditionOperator.GreaterEqual, DateTime.Parse(query.applytime_start));
                }
                if (query.applytime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.applytime_end))
                {
                    qe.Criteria.AddCondition("createdon", ConditionOperator.LessThan, DateTime.Parse(query.applytime_end).AddDays(1));
                }
                if (query.applytime_start == "1991-01-01")
                {
                    //filter.AddCondition("createdon", ConditionOperator.DoesNotContainValues);
                }
                if (query.outtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.outtime_start))
                {
                    qe.Criteria.AddCondition("new_confirmbuyouttime", ConditionOperator.GreaterEqual, DateTime.Parse(query.outtime_start));
                }
                if (query.outtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.outtime_end))
                {
                    qe.Criteria.AddCondition("new_confirmbuyouttime", ConditionOperator.LessThan, DateTime.Parse(query.outtime_end).AddDays(1));
                }
                if (query.outtime_start == "1991-01-01")
                {
                    //filter.AddCondition("new_confirmbuyouttime", ConditionOperator.DoesNotContainValues);
                }
                if (query.uploadtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.uploadtime_start))
                {
                    qe.Criteria.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.GreaterEqual, DateTime.Parse(query.uploadtime_start));
                }
                if (query.uploadtime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.uploadtime_end))
                {
                    qe.Criteria.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.LessThan, DateTime.Parse(query.uploadtime_end).AddDays(1));
                }
                if (query.uploadtime_start == "1991-01-01")
                {
                    //filter.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.DoesNotContainValues);
                }
                qe.ColumnSet.AddColumns("new_name", "new_status", "ownerid", "createdon", "new_confirmbuyouttime", "new_uploadpaymentvouchertime");
                //qe.Criteria.AddFilter(filter);

                #endregion

                #region 买断明细

                LinkEntity line = new LinkEntity("new_buyout", "new_buyoutline", "new_buyoutid", "new_buyout_id", JoinOperator.LeftOuter);
                line.EntityAlias = "line";
                line.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                if (!string.IsNullOrWhiteSpace(query.productid))
                {
                    line.LinkCriteria.AddCondition("new_product_id", ConditionOperator.Equal, query.productid);
                }
                line.Columns.AddColumns("new_amount", "ownerid", "new_qty", "new_price", "new_product_id", "new_name", "new_newproduct_id", "new_code");

                #endregion

                #region 买断服务商信息

                LinkEntity station = new LinkEntity("new_buyout", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                station.EntityAlias = "station";
                station.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                station.Columns.AddColumns("new_customersapid", "new_code", "new_name", "new_country_id");
                if (!string.IsNullOrWhiteSpace(query.sapcode))
                {
                    //sapcode
                    station.LinkCriteria.AddCondition("new_customersapid", ConditionOperator.Equal, query.sapcode);
                }
                if (!string.IsNullOrWhiteSpace(query.countryid))
                {
                    station.LinkCriteria.AddCondition("new_country_id", ConditionOperator.Equal, query.countryid);
                }

                #endregion

                #region 工单信息

                LinkEntity order = new LinkEntity("new_buyoutline", "new_srv_workorder", "new_workorder_id", "new_srv_workorderid", JoinOperator.Inner);
                order.EntityAlias = "order";
                order.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                order.Columns.AddColumns("new_name", "new_repairprovider_id", "new_endtime");

                if (!string.IsNullOrWhiteSpace(query.workordercode))
                {
                    order.LinkCriteria.AddCondition("new_name", ConditionOperator.Equal, query.workordercode);
                }
                if (!string.IsNullOrWhiteSpace(query.repairstationid))
                {
                    order.LinkCriteria.AddCondition("new_repairstation_id", ConditionOperator.Equal, query.repairstationid);
                }
                if (!string.IsNullOrWhiteSpace(query.stationid))
                {
                    order.LinkCriteria.AddCondition("new_station_id", ConditionOperator.Equal, query.stationid);
                }
                if (query.closetime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.closetime_start))
                {
                    order.LinkCriteria.AddCondition("new_endtime", ConditionOperator.GreaterEqual, DateTime.Parse(query.closetime_start));
                }
                if (query.closetime_start != "1991-01-01" && !string.IsNullOrWhiteSpace(query.closetime_end))
                {
                    order.LinkCriteria.AddCondition("new_endtime", ConditionOperator.LessThan, DateTime.Parse(query.closetime_end).AddDays(1));
                }
                //if (query.closetime_end == "1991-01-01")
                //{
                //    FilterExpression filterorder = new FilterExpression(LogicalOperator.Or);
                //    filterorder.AddCondition("new_uploadpaymentvouchertime", ConditionOperator.DoesNotContainValues);
                //    order.LinkCriteria.AddFilter(filterorder);
                //}

                #endregion

                #region 所属网点信息

                LinkEntity sta = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
                sta.EntityAlias = "sta";
                sta.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                if (!string.IsNullOrWhiteSpace(query.stationid))
                {
                    sta.LinkCriteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, query.stationid);
                }
                sta.Columns.AddColumns("new_name", "new_code");

                #endregion

                #region 网点信息

                LinkEntity resta = new LinkEntity("new_srv_workorder", "new_srv_station", "new_repairstation_id", "new_srv_stationid", JoinOperator.LeftOuter);
                resta.EntityAlias = "resta";
                if (!string.IsNullOrWhiteSpace(query.repairstationid))
                {
                    resta.LinkCriteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, query.repairstationid);
                }
                resta.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                resta.Columns.AddColumns("new_name", "new_code");

                #endregion

                #region 附件信息

                //LinkEntity file = new LinkEntity("new_buyout", "new_picturepath", "new_buyoutid", "new_relationid", JoinOperator.MatchFirstRowUsingCrossApply);
                //file.EntityAlias = "file";
                //file.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //file.LinkCriteria.AddCondition("new_formstatus", ConditionOperator.Equal, 1);
                //file.LinkCriteria.AddCondition("new_relationentityname", ConditionOperator.Equal, "new_buyout");
                //file.Columns.AddColumns("createdon");

                #endregion

                #region 库存事务

                //LinkEntity trans = new LinkEntity("new_buyout", "new_part_stocktransaction", "new_name", "new_fromno", JoinOperator.Inner);
                //trans.EntityAlias = "trans";
                //trans.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //trans.LinkCriteria.AddCondition("new_transactiontype", ConditionOperator.Equal, 5);
                //trans.Columns.AddColumns("createdon");

                #endregion

                order.LinkEntities.Add(sta);
                order.LinkEntities.Add(resta);
                line.LinkEntities.Add(order);
                qe.LinkEntities.Add(line);
                qe.LinkEntities.Add(station);

                if ((query.pageSize != 0 && query.pageIndex != 0))
                {
                    qe.PageInfo = new PagingInfo
                    {
                        Count = query.pageSize,
                        PageNumber = query.pageIndex,
                        ReturnTotalRecordCount = true
                    };
                }

                EntityCollection ec = OrganizationService.RetrieveMultiple(qe);

                return ec;
            }
            catch (Exception e)
            {

                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        public int GetTotal(BuyoutLineCondition query)
        {
            try
            {
                int total = 0;
                if (query.pageIndex == 0 && query.pageSize == 0)
                {
                    bool isMore = true;
                    query.pageSize = 5000;
                    while (isMore)
                    {
                        query.pageIndex += 1;
                        var ec = this.GetQueryResult(query);
                        total += ec.Entities.Count;
                        isMore = ec.MoreRecords;
                    }
                }
                return total;
            }
            catch (Exception e)
            {

                throw new InvalidPluginExecutionException(e.Message);
            }
        }
        /// <summary>
        /// 调用计税引擎接口
        /// </summary>
        /// <param name="buyoutId">买断单id</param>
        public string TaxSteting(string buyoutId)
        {
            try
            {
                #region 查询数据
                QueryExpression qe = new QueryExpression("new_buyoutline");
                qe.ColumnSet = new ColumnSet("new_code", "new_newproduct_id", "new_qty", "new_amount");
                qe.Criteria.AddCondition("new_buyout_id", ConditionOperator.Equal, buyoutId);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity leBuyout = new LinkEntity("new_buyoutline", "new_buyout", "new_buyout_id", "new_buyoutid", JoinOperator.Inner);
                leBuyout.EntityAlias = "leBuyout";
                leBuyout.Columns = new ColumnSet("new_name");
                qe.LinkEntities.Add(leBuyout);

                LinkEntity leTransactioncurrency = new LinkEntity("new_buyout", "transactioncurrency", "new_transactioncurrency_id", "transactioncurrencyid", JoinOperator.LeftOuter);
                leTransactioncurrency.EntityAlias = "leTransactioncurrency";
                leTransactioncurrency.Columns = new ColumnSet("isocurrencycode");
                leBuyout.LinkEntities.Add(leTransactioncurrency);

                LinkEntity leStation = new LinkEntity("new_buyout", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.LeftOuter);
                leStation.EntityAlias = "leStation";
                leStation.Columns = new ColumnSet("new_customersapid", "new_salesbody", "new_dutyparagraph");
                leBuyout.LinkEntities.Add(leStation);

                LinkEntity leCountry = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter);
                leCountry.EntityAlias = "leCountry";
                leCountry.Columns = new ColumnSet("new_code");
                leStation.LinkEntities.Add(leCountry);

                var ec = OrganizationService.RetrieveMultiple(qe);
                #endregion

                #region 数据校验
                Entity entity = ec?.Entities[0];
                string countryCode = entity.Contains("leCountry.new_code") ? entity.GetAttributeValue<AliasedValue>("leCountry.new_code").Value.ToString() : string.Empty;
                string company = entity.Contains("leStation.new_salesbody") ? entity.GetAttributeValue<AliasedValue>("leStation.new_salesbody").Value.ToString() : string.Empty; ;
                string customersapid = entity.Contains("leStation.new_customersapid") ? entity.GetAttributeValue<AliasedValue>("leStation.new_customersapid").Value.ToString() : string.Empty;
                string buyoutname = entity.Contains("leBuyout.new_name") ? entity.GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString() : string.Empty;

                string new_dutyparagraph = entity.Contains("leStation.new_dutyparagraph") ? entity.GetAttributeValue<AliasedValue>("leStation.new_dutyparagraph").Value.ToString() : string.Empty;
                #endregion
                string counts = GetSystemParameterValue(OrganizationService, "TaxCount");
                if (string.IsNullOrWhiteSpace(counts))
                {
                    throw new InvalidPluginExecutionException(GetResource("Common.LockSysParam", "系统参数中未配置[TaxCount]的值！"));
                }
                int num = int.Parse(counts);
                #region 调用计税引擎接口
                if (ec?.Entities?.Count > 0 && ec?.Entities?.Count <= num)
                {
                    #region 生成xml文档
                    XmlDocument doc = new XmlDocument();
                    XmlElement INDATA = doc.CreateElement("INDATA");
                    INDATA.SetAttribute("version", "G");
                    doc.AppendChild(INDATA);

                    XmlElement EXTERNAL_COMPANY_ID = doc.CreateElement("EXTERNAL_COMPANY_ID");
                    EXTERNAL_COMPANY_ID.InnerText = company;
                    INDATA.AppendChild(EXTERNAL_COMPANY_ID);

                    XmlElement CALLING_SYSTEM_NUMBER = doc.CreateElement("CALLING_SYSTEM_NUMBER");
                    CALLING_SYSTEM_NUMBER.InnerText = "ISPPRO";
                    INDATA.AppendChild(CALLING_SYSTEM_NUMBER);

                    XmlElement INVOICE = doc.CreateElement("INVOICE");
                    INDATA.AppendChild(INVOICE);

                    XmlElement EXTERNAL_COMPANY_ID1 = doc.CreateElement("EXTERNAL_COMPANY_ID");
                    EXTERNAL_COMPANY_ID1.InnerText = company;
                    INVOICE.AppendChild(EXTERNAL_COMPANY_ID1);

                    XmlElement COMPANY_ROLE = doc.CreateElement("COMPANY_ROLE");
                    COMPANY_ROLE.InnerXml = "S";
                    INVOICE.AppendChild(COMPANY_ROLE);

                    XmlElement CURRENCY_CODE = doc.CreateElement("CURRENCY_CODE");
                    CURRENCY_CODE.InnerText = entity.Contains("leTransactioncurrency.isocurrencycode") ? entity.GetAttributeValue<AliasedValue>("leTransactioncurrency.isocurrencycode").Value.ToString() : string.Empty;
                    INVOICE.AppendChild(CURRENCY_CODE);

                    XmlElement CUSTOMER_NAME = doc.CreateElement("CUSTOMER_NAME");
                    CUSTOMER_NAME.InnerText = "MAITROX POLAND Sp.z o.o.";
                    INVOICE.AppendChild(CUSTOMER_NAME);

                    XmlElement CUSTOMER_NUMBER = doc.CreateElement("CUSTOMER_NUMBER");
                    CUSTOMER_NUMBER.InnerText = customersapid;
                    INVOICE.AppendChild(CUSTOMER_NUMBER);

                    XmlElement CALCULATION_DIRECTION = doc.CreateElement("CALCULATION_DIRECTION");
                    CALCULATION_DIRECTION.InnerText = "F";
                    INVOICE.AppendChild(CALCULATION_DIRECTION);

                    XmlElement FISCAL_DATE = doc.CreateElement("FISCAL_DATE");
                    FISCAL_DATE.InnerText = DateTime.Now.ToString("MM/dd/yyyy");
                    INVOICE.AppendChild(FISCAL_DATE);

                    XmlElement INVOICE_DATE = doc.CreateElement("INVOICE_DATE");
                    INVOICE_DATE.InnerText = DateTime.Now.ToString("MM/dd/yyyy");
                    INVOICE.AppendChild(INVOICE_DATE);

                    XmlElement INVOICE_NUMBER = doc.CreateElement("INVOICE_NUMBER");
                    INVOICE_NUMBER.InnerText = entity.Contains("leBuyout.new_name") ? entity.GetAttributeValue<AliasedValue>("leBuyout.new_name").Value.ToString() : string.Empty;
                    INVOICE.AppendChild(INVOICE_NUMBER);

                    XmlElement IS_AUDITED = doc.CreateElement("IS_AUDITED");
                    IS_AUDITED.InnerText = "N";
                    INVOICE.AppendChild(IS_AUDITED);

                    XmlElement USER_ELEMENT1 = doc.CreateElement("USER_ELEMENT");
                    USER_ELEMENT1.InnerXml = "<NAME>ATTRIBUTE2</NAME><VALUE>0</VALUE>";
                    INVOICE.AppendChild(USER_ELEMENT1);

                    XmlElement USER_ELEMENT2 = doc.CreateElement("USER_ELEMENT");
                    USER_ELEMENT2.InnerXml = "<NAME>ATTRIBUTE10</NAME><VALUE>ZCXS</VALUE>";
                    INVOICE.AppendChild(USER_ELEMENT2);

                    int i = 1;
                    List<updateEntity> updateEntitys = new List<updateEntity>();
                    foreach (var item in ec.Entities)
                    {
                        string id = i.ToString();
                        Entity updateEntity = new Entity("new_buyoutline");
                        updateEntity.Id = item.Id;
                        updateEntitys.Add(new updateEntity { id = id, entity = updateEntity });
                        XmlElement LINE = doc.CreateElement("LINE");
                        LINE.SetAttribute("ID", id);

                        XmlElement BILL_TO = doc.CreateElement("BILL_TO");
                        BILL_TO.InnerXml = $"<COUNTRY>{countryCode}</COUNTRY>";
                        LINE.AppendChild(BILL_TO);

                        XmlElement DESCRIPTION = doc.CreateElement("DESCRIPTION");
                        DESCRIPTION.InnerText = item.Contains("new_newproduct_id") ? item.FormattedValues["new_newproduct_id"] : string.Empty;
                        LINE.AppendChild(DESCRIPTION);

                        XmlElement GROSS_AMOUNT = doc.CreateElement("GROSS_AMOUNT");
                        GROSS_AMOUNT.InnerText = item.GetAttributeValue<decimal>("new_amount").ToString();
                        LINE.AppendChild(GROSS_AMOUNT);

                        XmlElement LINE_NUMBER = doc.CreateElement("LINE_NUMBER");
                        LINE_NUMBER.InnerText = i.ToString();
                        LINE.AppendChild(LINE_NUMBER);

                        XmlElement PART_NUMBER1 = doc.CreateElement("PART_NUMBER");
                        PART_NUMBER1.InnerText = item.GetAttributeValue<string>("new_code");
                        LINE.AppendChild(PART_NUMBER1);

                        XmlElement QUANTITIES = doc.CreateElement("QUANTITIES");
                        LINE.AppendChild(QUANTITIES);

                        XmlElement QUANTITY = doc.CreateElement("QUANTITY");
                        QUANTITY.InnerXml = $"<AMOUNT>{item.GetAttributeValue<decimal>("new_qty")}</AMOUNT><UOM>PC</UOM>";
                        QUANTITIES.AppendChild(QUANTITY);

                        XmlElement REGISTRATIONS = doc.CreateElement("REGISTRATIONS");
                        REGISTRATIONS.InnerXml = $"<BUYER_ROLE>{new_dutyparagraph}</BUYER_ROLE>";
                        LINE.AppendChild(REGISTRATIONS);

                        XmlElement SHIP_FROM = doc.CreateElement("SHIP_FROM");
                        SHIP_FROM.InnerXml = $"<COUNTRY>{countryCode}</COUNTRY>";
                        LINE.AppendChild(SHIP_FROM);

                        XmlElement SHIP_TO = doc.CreateElement("SHIP_TO");
                        SHIP_TO.InnerXml = $"<COUNTRY>{countryCode}</COUNTRY>";
                        LINE.AppendChild(SHIP_TO);

                        XmlElement SUPPLY = doc.CreateElement("SUPPLY");
                        SUPPLY.InnerXml = $"<COUNTRY>{countryCode}</COUNTRY>";
                        LINE.AppendChild(SUPPLY);

                        XmlElement TRANSACTION_TYPE = doc.CreateElement("TRANSACTION_TYPE");
                        TRANSACTION_TYPE.InnerXml = "GS";
                        LINE.AppendChild(TRANSACTION_TYPE);

                        INVOICE.AppendChild(LINE);
                        i++;
                    }

                    doc.Save("Order.xml");
                    #endregion

                    #region 接口参数
                    string body = @"<?xml version=""1.0"" encoding=""utf-8""?>
" + "\n" +
        @"<soapenv:Envelope
" + "\n" +
        @"    xmlns:ns=""http://www.sabrix.com/services/taxcalculationservice/2011-09-01""
" + "\n" +
        @"    xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"">
" + "\n" +
        @"    <soapenv:Header>
" + "\n" +
        @"        <wsse:Security soapenv:mustUnderstand=""1""
" + "\n" +
        @"            xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd""
" + "\n" +
        @"            xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">
" + "\n" +
        @"        </wsse:Security>
" + "\n" +
        @"    </soapenv:Header>
" + "\n" +
        @"    <soapenv:Body>
" + "\n" +
        @"        <taxCalculationRequest
" + "\n" +
        @"            xmlns=""http://www.sabrix.com/services/taxcalculationservice/2011-09-01"">
" + "\n" + doc.OuterXml +
        @"           
" + "\n" +
        @"        </taxCalculationRequest>
" + "\n" +
        @"    </soapenv:Body>
" + "\n" +
        @"</soapenv:Envelope>";
                    #endregion

                    #region 调用接口
                    string url = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_taxUrl");
                    HttpClient client = new HttpClient();
                    HttpContent httpContent = new StringContent(body, Encoding.UTF8);
                    httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/xml");
                    string result = client.PostAsync(url, httpContent).Result.Content.ReadAsStringAsync().Result;//返回值

                    XmlDocument document = new XmlDocument();
                    document.LoadXml(result);

                    XmlNode OUTDATA = document.GetElementsByTagName("OUTDATA").Item(0);
                    string json = Newtonsoft.Json.JsonConvert.SerializeXmlNode(OUTDATA);
                    TaxReturnModel model = JsonConvert.DeserializeObject<TaxReturnModel>(json);
                    if (model?.OUTDATA?.REQUEST_STATUS?.IS_SUCCESS != "true")
                        throw new Exception(model?.OUTDATA?.REQUEST_STATUS?.ERROR[0]?.DESCRIPTION);

                    updateBuyoutlineTax(updateEntitys, model);

                    #endregion

                    #region 更新主档状态
                    Entity eUpdate = new Entity("new_buyout");
                    eUpdate.Id = new Guid(buyoutId);
                    eUpdate["new_taxstatus"] = new OptionSetValue(3);//已计税
                    OrganizationService.Update(eUpdate);
                    #endregion
                }
                if (ec?.Entities?.Count > 0 && ec?.Entities?.Count > num)
                {
                    //买断单明细数量超过num值，走ServiceBus处理计税操作
                    Entity eUpdate = new Entity("new_buyout");
                    eUpdate.Id = new Guid(buyoutId);
                    eUpdate["new_taxstatus"] = new OptionSetValue(2);//计税中
                    OrganizationService.Update(eUpdate);

                    //ServriceBusCommon.queuename = "crm_taxsetting";
                    //ServriceBusCommon.sendservricebus(buyoutId, OrganizationService, buyoutname);

                    PartsHelper.CreatePartsLog(OrganizationServiceAdmin, "", buyoutname, "crm_taxsetting", "crm_taxsetting", buyoutId,1,"", Log);

                    return "process";
                }
                return "success";
                #endregion
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        public string GetSystemParameterValue(IOrganizationService organizationService, string paramName)
        {
            var query = new QueryExpression("new_systemparameter");
            query.ColumnSet = new ColumnSet("new_name", "new_value");
            query.Criteria = new FilterExpression(LogicalOperator.And);
            query.Criteria.AddCondition("new_name", ConditionOperator.Equal, paramName);
            var ret = organizationService.RetrieveMultiple(query);

            if (ret == null || ret.Entities.Count == 0)
            {
                return string.Empty;
            }
            var systemparameter = ret.Entities.First();
            string value = systemparameter.GetAttributeValue<string>("new_value");
            return value;
        }

        /// <summary>
        /// 更新明细税额
        /// </summary>
        /// <param name="updateEntities"></param>
        /// <param name="model"></param>
        public void updateBuyoutlineTax(List<updateEntity> updateEntities, TaxReturnModel model)
        {
            List<Task> tasks = new List<Task>();
            for (int i = 0; i < model?.OUTDATA?.INVOICE?.LINE.Count; i++)
            {
                int index = i;
                var item = model.OUTDATA.INVOICE.LINE[index];
                Entity updateEntity = updateEntities.Where(m => m.id == item.LINE_NUMBER).Select(m => m.entity).FirstOrDefault();
                updateEntity["new_taxrate"] = item?.TAX?.TAX_RATE;
                updateEntity["new_tax"] = item.TOTAL_TAX_AMOUNT;
                updateEntity["new_taxamount"] = item.TOTAL_TAX_AMOUNT + item.GROSS_AMOUNT;
                OrganizationService.Update(updateEntity);
            }
        }
        /// <summary>
        /// 给团队追加权限
        /// </summary>
        /// <param name="stationId">网点id</param>
        /// <param name="entityName">实体名</param>
        /// <param name="entityId">实体id</param>
        public void AddTeamPermissions(string stationId, string entityName, string entityId)
        {
            try
            {
                QueryExpression qeStation = new QueryExpression("new_srv_station");
                qeStation.Criteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, stationId);

                LinkEntity leStation1 = new LinkEntity("new_srv_station", "new_srv_station", "new_srv_station_id", "new_srv_stationid", JoinOperator.Inner);
                qeStation.LinkEntities.Add(leStation1);

                LinkEntity leStation2 = new LinkEntity("new_srv_station", "new_srv_station", "new_srv_stationid", "new_srv_station_id", JoinOperator.Inner);
                leStation2.EntityAlias = "leStation2";
                leStation2.Columns = new ColumnSet("new_name");
                leStation1.LinkEntities.Add(leStation2);

                EntityCollection ec = OrganizationService.RetrieveMultiple(qeStation);

                foreach (var item in ec.Entities)
                {
                    Console.WriteLine($"网点名：{item.GetAttributeValue<AliasedValue>("leStation2.new_name").Value}");
                    QueryExpression qeTeam = new QueryExpression("team");
                    qeTeam.ColumnSet = new ColumnSet("name");
                    qeTeam.Criteria.AddCondition("name", ConditionOperator.Like, "%" + item.GetAttributeValue<AliasedValue>("leStation2.new_name").Value + "%");
                    qeTeam.Criteria.AddCondition("name", ConditionOperator.Like, "%服务网点%");
                    EntityCollection ecc = OrganizationService.RetrieveMultiple(qeTeam);

                    foreach (var obj in ecc.Entities)
                    {
                        Console.WriteLine($"团队：{obj.GetAttributeValue<string>("name")}");
                        CommonHelper.ShareToTeam(OrganizationServiceAdmin, obj.Id, entityName, new Guid(entityId), true);
                    }
                }
                Entity entity = new Entity(entityName);
                entity.Id = new Guid(entityId);
                entity["new_ispermissionsharing"] = true;
                OrganizationService.Update(entity);
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 零售通收款工单从保外买断中剔除
        /// </summary>
        /// /// <param name="serviceid">服务单号</param>
        /// /// <param name="errorOrder">需要剔除的工单号</param>
        /// <returns>true:符合条件不许添加，false：不符合条件可以添加</returns>
        public bool checkServiceOrderStatus(string serviceid, string servicename, List<string> errorOrder)
        {
            //工单判空
            if (string.IsNullOrWhiteSpace(serviceid) && string.IsNullOrWhiteSpace(servicename))
            {
                return false;
            }
            string workorder = string.Empty;
            //var serviceorder = OrganizationService.Retrieve("new_srv_workorder",Guid.Parse(serviceid),new ColumnSet("new_station_id", "new_type", "new_warranty", "new_dealstatus"));
            QueryExpression query = new QueryExpression("new_srv_workorder");
            query.ColumnSet = new ColumnSet("new_station_id", "new_type", "new_warranty", "new_dealstatus", "new_name");
            if (!string.IsNullOrWhiteSpace(serviceid))
            {
                query.Criteria.AddCondition("new_srv_workorderid", ConditionOperator.Equal, serviceid);
            }
            else if (!string.IsNullOrWhiteSpace(servicename))
            {
                query.Criteria.AddCondition("new_name", ConditionOperator.Equal, servicename);
            }
            else
            {
                return false;
            }
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            LinkEntity link1 = new LinkEntity("new_srv_workorder", "new_ow_receipt", "new_ow_receipt_id", "new_ow_receiptid", JoinOperator.Inner);
            link1.EntityAlias = "re";
            link1.Columns = new ColumnSet("new_payment_method", "new_collection_status");
            link1.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.LinkEntities.Add(link1);

            LinkEntity link = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.Inner);
            link.EntityAlias = "st";
            link.Columns = new ColumnSet("new_srv_stationid", "new_operationmodecode");
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            query.LinkEntities.Add(link);

            var ec = OrganizationService.RetrieveMultiple(query);

            if (ec != null && ec.Entities != null && ec.Entities.Count > 0)
            {
                //  - 工单【服务类型】=【维修】，【出保/在保】=【保外】，-工单状态 =【已关单】
                var item = ec.Entities.First();
                workorder = item.GetAttributeValue<string>("new_name");
                var new_type = item.GetAttributeValue<OptionSetValue>("new_type");
                var new_warranty = item.GetAttributeValue<OptionSetValue>("new_warranty");
                var new_dealstatus = item.GetAttributeValue<OptionSetValue>("new_dealstatus");
                if (new_type == null || new_type.Value != 1)
                    return false;
                if (new_warranty == null || new_warranty.Value != 2)
                    return false;
                if (new_dealstatus == null || new_dealstatus.Value != 9)
                    return false;
                var new_operationmodecode = item.GetAliasAttributeValue<OptionSetValue>("st.new_operationmodecode").Value;
                //机构【经营模式】=【新零售直营】
                if (new_operationmodecode != 4)
                    return false;
                // 工单【收款方式】=【零售通代收款】-工单【收款状态】=【已收款】
                var new_payment_method = item.GetAliasAttributeValue<OptionSetValue>("re.new_payment_method").Value;
                var new_collection_status = item.GetAliasAttributeValue<OptionSetValue>("re.new_collection_status").Value;
                if(new_payment_method != 2 || new_collection_status != 2)
                    return false;
            }
            else
            {
                return false;
            }
            errorOrder.Add(workorder);
            return true;
        }
    }
    public class Replacement
    {
        public decimal Price { get; set; }
        public EntityReference Currency { get; set; }
    }

    public class BuyoutLineModel
    {
        public int statusCode { get; set; }
        public string message { get; set; }
        public Body body { get; set; }
    }
    public class Body
    {
        public List<BuyoutlineModel> list { get; set; }
        public int count { get; set; }
    }

    public class Report
    {
        public List<BuyoutLineReport> buyoutLineReports { get; set; }

        public int TotalRecordCount { get; set; }

        public bool HasMoreRecords { get; set; }
    }

    public class BuyoutLineReport
    {
        /// <summary>
        /// 买断单号
        /// </summary>
        public string buyoutcode { get; set; }

        /// <summary>
        /// 买断服务商编码
        /// </summary>
        public string servicestationcode { get; set; }

        /// <summary>
        /// 买断服务商名称
        /// </summary>
        public string servicestationname { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        public string ownerid { get; set; }

        /// <summary>
        /// sapcode
        /// </summary>
        public string sapcode { get; set; }

        /// <summary>
        /// 维修工单号
        /// </summary>
        public string workordercode { get; set; }

        /// <summary>
        /// 国家/地区
        /// </summary>
        public string countryname { get; set; }

        /// <summary>
        /// 国家/地区id
        /// </summary>
        public string countryid { get; set; }

        /// <summary>
        /// 维修网点编码
        /// </summary>
        public string repairstationcode { get; set; }

        /// <summary>
        /// 维修网点名称
        /// </summary>
        public string repairstationname { get; set; }

        /// <summary>
        /// 所属网点编码
        /// </summary>
        public string stationcode { get; set; }

        /// <summary>
        /// 所属网点名称
        /// </summary>
        public string stationname { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string stockname { get; set; }

        /// <summary>
        /// 买断状态
        /// </summary>
        public string buyoutstatus { get; set; }

        /// <summary>
        /// 工单关单时间
        /// </summary>
        public string closetime { get; set; }

        /// <summary>
        /// 申请买断时间
        /// </summary>
        public string buyouttime { get; set; }

        /// <summary>
        /// 付款凭证上传时间
        /// </summary>
        public string uploadtime { get; set; }

        /// <summary>
        /// 库存出库时间
        /// </summary>
        public string outtime { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string productnumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string productname { get; set; }

        /// <summary>
        /// 新物料编码
        /// </summary>
        public string newproductnumber { get; set; }

        /// <summary>
        /// 新物料名称
        /// </summary>
        public string newproductname { get; set; }

        /// <summary>
        /// 零售单价
        /// </summary>
        public decimal retailprice { get; set; }

        /// <summary>
        /// 保外单价
        /// </summary>
        public decimal oowprice { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal qty { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal amount { get; set; }

    }

    public class BuyoutLineCondition
    {

        /// <summary>
        /// 买断单号
        /// </summary>
        public string buyoutcode { get; set; }
        /// <summary>
        /// 维修工单号
        /// </summary>
        public string workordercode { get; set; }
        /// <summary>
        /// sapcode
        /// </summary>
        public string sapcode { get; set; }

        /// <summary>
        /// 买断服务商ID
        /// </summary>
        public string servicestationid { get; set; }
        /// <summary>
        /// 申请人ID
        /// </summary>
        public string ownerid { get; set; }
        /// <summary>
        /// 网点ID
        /// </summary>
        public string stationid { get; set; }
        /// <summary>
        /// 维修网点ID
        /// </summary>
        public string repairstationid { get; set; }
        /// <summary>
        /// 物料ID
        /// </summary>
        public string productid { get; set; }

        /// <summary>
        /// 国家/地区id
        /// </summary>
        public string countryid { get; set; }
        /// <summary>
        /// 买断状态
        /// </summary>
        public int buyoutstates { get; set; }

        public int pageSize { get; set; }

        public int pageIndex { get; set; }
        /// <summary>
        /// 申请买断开始时间
        /// </summary>
        public string applytime_start { get; set; }
        /// <summary>
        /// 申请买断结束时间
        /// </summary>
        public string applytime_end { get; set; }
        /// <summary>
        /// 工单关单开始时间
        /// </summary>
        public string closetime_start { get; set; }
        /// <summary>
        /// 工单关单结束时间
        /// </summary>
        public string closetime_end { get; set; }
        /// <summary>
        /// 库存出库开始时间
        /// </summary>
        public string outtime_start { get; set; }
        /// <summary>
        /// 库存出库结束时间
        /// </summary>
        public string outtime_end { get; set; }
        /// <summary>
        /// 付款凭证上传开始时间
        /// </summary>
        public string uploadtime_start { get; set; }
        /// <summary>
        /// 付款凭证上传结束时间
        /// </summary>
        public string uploadtime_end { get; set; }
    }

    /// <summary>
    /// 手动添加明细的返回结果
    /// </summary>
    public class CreateLineResult
    {
        /// <summary>
        /// 需要剔除的工单数量
        /// </summary>
        public int errorcount { get; set; }
        /// <summary>
        /// 提示信息
        /// </summary>
        public string errormsg { get; set; }

        /// <summary>
        /// 返回结果的构造参数
        /// </summary>
        /// <param name="count"></param>
        /// <param name="msg"></param>
        public CreateLineResult(int count,string msg)
        {
            this.errorcount = count;
            this.errormsg = msg;
        }
    }

}

