﻿using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Metadata;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using RekTec.ServiceSettlement.Function.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
//using System.Text.Json;

namespace RekTec.ServiceSettlement.Function.Common
{
    public class SettlementHelp
    {
        ILogger log;
        private IOrganizationService OrganizationService;
        //当前时间
        private static DateTime dateTime = DateTime.Now.AddMonths(-1);
        //开始时间
        private static DateTime beginDate = new DateTime(dateTime.Year, dateTime.Month, 1);
        //截止时间
        private static DateTime endDate = beginDate.AddMonths(1).AddMilliseconds(-1);
        //电视三级品类id
        string tvcategoryid = "";
        public SettlementHelp(ILogger plog)
        {
            OrganizationService = CommonHelper.CreateConnection();
            Entity tvcategory = GetTVCategory();
            if (tvcategory != null)
                tvcategoryid = tvcategory.Id.ToString();
            log = plog;
        }
        /// <summary>
        /// 查询结算标准
        /// </summary>
        /// <returns></returns>
        public EntityCollection GetExpenseStandard()
        {
            EntityCollection cols = new EntityCollection();
            QueryExpression qe = new QueryExpression("new_srv_expense_standard");
            qe.ColumnSet = new ColumnSet("new_country_id", "new_workordernumber", "new_station_id", "new_amount", "new_transactioncurrency_id", "new_feetype",
                "new_category1_id", "new_category2_id", "new_category3_id", "new_costweight", "new_optionalcharges", "new_ratio", "new_return_channel", "new_direction",
                "new_workinghours", "new_stationservice_id", "new_expensetypeconfigtable_id", "new_stietype", "new_minimumfee");
            qe.NoLock = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition(new ConditionExpression("new_feetype", ConditionOperator.In, new int[] { 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 41 }));
            LinkEntity link = new LinkEntity("new_srv_expense_standard", "new_expensetypeconfigtable", "new_expensetypeconfigtable_id", "new_expensetypeconfigtableid", JoinOperator.LeftOuter);
            qe.LinkEntities.Add(link);
            cols = CommonHelper.QueryExpressionPage(OrganizationService, qe);
            return cols;
        }
        /// <summary>
        /// 获取仓储单价表
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getwarehousing_unitprice(int year, int month, string srv_station_id)
        {
            QueryExpression qe = new QueryExpression("new_warehousing_unitpricedetail");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.ColumnSet = new ColumnSet("new_warehousing_unitprice_id", "new_init_count", "new_final_count", "new_type", "new_srv_expense_standard_id");
            LinkEntity link = new LinkEntity("new_warehousing_unitpricedetail", "new_warehousing_unitprice", "new_warehousing_unitprice_id", "new_warehousing_unitpriceid", JoinOperator.Inner);
            link.EntityAlias = "warehousing_unitprice";
            link.Columns = new ColumnSet("new_srv_station_id");
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            link.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, year);
            link.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, month);
            link.LinkCriteria.AddCondition("new_srv_station_id", ConditionOperator.Equal, srv_station_id);
            qe.LinkEntities.Add(link);
            EntityCollection cols = OrganizationService.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 获取分摊比例配置表
        /// </summary>
        /// <returns></returns>
        public EntityCollection GetRatioConfiglist()
        {
            QueryExpression qe = new QueryExpression("new_amortization_ratio");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.ColumnSet = new ColumnSet("new_category1_id", "new_category2_id", "new_category3_id", "new_model2_id", "new_ratio");
            LinkEntity link1 = new LinkEntity("new_amortization_ratio", "new_category1", "new_category1_id", "new_category1id", JoinOperator.LeftOuter);
            link1.EntityAlias = "category1";
            link1.Columns = new ColumnSet("new_code");
            LinkEntity link2 = new LinkEntity("new_amortization_ratio", "new_category2", "new_category2_id", "new_category2id", JoinOperator.LeftOuter);
            link2.EntityAlias = "category2";
            link2.Columns = new ColumnSet("new_code");
            LinkEntity link3 = new LinkEntity("new_amortization_ratio", "new_category3", "new_category3_id", "new_category3id", JoinOperator.LeftOuter);
            link3.EntityAlias = "category3";
            link3.Columns = new ColumnSet("new_code");
            LinkEntity link4 = new LinkEntity("new_amortization_ratio", "new_model2", "new_model2_id", "new_model2id", JoinOperator.LeftOuter);
            link4.EntityAlias = "model2";
            link4.Columns = new ColumnSet("new_code");
            qe.LinkEntities.Add(link1);
            qe.LinkEntities.Add(link2);
            qe.LinkEntities.Add(link3);
            qe.LinkEntities.Add(link4);
            EntityCollection cols = OrganizationService.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 查询费用类型配置表
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getexpensetypeconfig()
        {
            QueryExpression qe = new QueryExpression("new_expensetypeconfigtable");
            qe.ColumnSet = new ColumnSet("new_scene", "new_feetype", "new_name");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            return OrganizationService.RetrieveMultiple(qe);
        }
        /// <summary>
        /// 获取分摊范围
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getallocationscope(int providertype)
        {
            QueryExpression qe = new QueryExpression("new_allocationscopetable");
            qe.ColumnSet.AddColumns("new_region_id", "new_country_id", "new_srv_station_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link = new LinkEntity("new_allocationscopetable", "new_srv_station", "new_srv_station_id", "new_srv_stationid", JoinOperator.Inner);
            link.LinkCriteria.AddCondition("new_providertype", ConditionOperator.Equal, providertype);
            qe.LinkEntities.Add(link);
            qe.NoLock = true;
            return OrganizationService.RetrieveMultiple(qe);
        }
        /// <summary>
        /// 根据国家范围，区域范围查询满足仓储结费范围工单
        /// </summary>
        /// <param name="countryscope"></param>
        /// <param name="regionscope"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderStorage(List<Guid> countryscope, List<Guid> regionscope)
        {
            QueryExpression qe = new QueryExpression("new_workorder_settlement");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet(true);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition(new ConditionExpression("new_issettlementstorage", ConditionOperator.In, new int[] { 1, 2 }));
            qe.Criteria.AddCondition("new_settlementtimestorage", ConditionOperator.OnOrAfter, beginDate);
            qe.Criteria.AddCondition("new_settlementtimestorage", ConditionOperator.OnOrBefore, endDate);
            if (countryscope.Count > 0)
            {
                qe.Criteria.AddCondition(new ConditionExpression("new_country_id", ConditionOperator.In, countryscope));
            }
            if (regionscope.Count > 0)
            {
                LinkEntity link = new LinkEntity("new_workorder_settlement", "new_country", "new_country_id", "new_countryid", JoinOperator.LeftOuter);
                link.LinkCriteria.AddCondition(new ConditionExpression("new_region_idstorage", ConditionOperator.In, regionscope));
            }
            EntityCollection cols = CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
            return cols;
        }
        /// <summary>
        /// 查询三级品类-电视
        /// </summary>
        /// <returns></returns>
        public Entity GetTVCategory()
        {
            QueryExpression qe = new QueryExpression("new_category3");
            qe.ColumnSet = new ColumnSet("new_code", "new_name");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("new_code", ConditionOperator.Equal, "30000008");
            return OrganizationService.RetrieveMultiple(qe).Entities.FirstOrDefault();
        }
        /// <summary>
        /// 根据仓储服务商计算人力变动费，运营变动费等仓储费用
        /// </summary>
        /// <param name="new_srv_station_id"></param>
        /// <returns></returns>
        public StoragefeeModel CalcStoragefee(Guid new_srv_station_id, EntityCollection scopecols, EntityCollection ratioconfig, EntityCollection expensetypeconfig, EntityCollection warehousing_unitprice, EntityCollection expensestandard, string fieldname)
        {
            StoragefeeModel storagefee = new StoragefeeModel();
            Dictionary<string, decimal> storagefeelist = new Dictionary<string, decimal>();//费用集合
            Dictionary<string, decimal> allocationstoragefeelist = new Dictionary<string, decimal>();//分摊到费用表上的费用集合
            var scopelist = scopecols.Entities.Where(a => a.Contains("new_srv_station_id") && a.ToDefault<Guid>("new_srv_station_id") == new_srv_station_id).ToList();
            var standardlist = expensestandard.Entities.ToList();
            if (standardlist.Count > 0)
            {
                List<Guid> transactioncurrency_idlist = new List<Guid>();
                #region 根据结算标准计算租金固定费，运营固定费，租金-货架单价，租金-扩仓单价，人力固定费，人力-变动部分费用，运营变动费，预提费（仓储）
                //租金固定费
                decimal fixedrendfee = 0m;
                var fixedrendfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 47 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id).FirstOrDefault();
                if (fixedrendfeestandard != null)
                {
                    fixedrendfee = fixedrendfeestandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedrendfeestandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedrendfeestandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_fixedrentalfee", fixedrendfee);
                allocationstoragefeelist.Add("new_fixedrentalfee", fixedrendfee);
                //租金-货架费
                decimal shelfrentalfee = 0m;
                var expensetype_shelfrentalfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 10).ToList();
                var shelfrentalstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 48 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
                && expensetype_shelfrentalfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in shelfrentalstandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                   && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        shelfrentalfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_shelfrentalfee", shelfrentalfee);
                allocationstoragefeelist.Add("new_shelfrentalfee", shelfrentalfee);
                //租金扩仓费
                decimal warehouseexpansionfee = 0m;
                var expensetype_warehouseexpansionfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 20).ToList();
                var warehouseexpansionstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 49 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
                && expensetype_warehouseexpansionfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in warehouseexpansionstandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                   && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        warehouseexpansionfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_warehouseexpansionfee", warehouseexpansionfee);
                allocationstoragefeelist.Add("new_warehouseexpansionfee", warehouseexpansionfee);
                //人力固定费
                decimal fixedlaborcost = 0m;
                var fixedlaborcoststandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 50 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id).FirstOrDefault();
                if (fixedlaborcoststandard != null)
                {
                    fixedlaborcost = fixedlaborcoststandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedlaborcoststandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedlaborcoststandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_fixedlaborcost", fixedlaborcost);
                allocationstoragefeelist.Add("new_fixedlaborcost", fixedlaborcost);
                var expensetype_laborcost = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 30).ToList();
                //人力变动费-基本工资
                decimal variablelaborcost_basic = 0m;
                var variablelaborcost_basicstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 51 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
                && expensetype_laborcost.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))
                && a.ToDefault<int>("new_workinghours") == 10).ToList();
                foreach (var standard in variablelaborcost_basicstandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        variablelaborcost_basic += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_variablelaborcost_basic", variablelaborcost_basic);
                //人力变动费-工作日加班
                decimal variablelaborcost_overtime = 0m;
                var variablelaborcost_overtimestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 51 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
                && expensetype_laborcost.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))
                && a.ToDefault<int>("new_workinghours") == 20).ToList();
                foreach (var standard in variablelaborcost_overtimestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        variablelaborcost_overtime += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_variablelaborcost_overtime", variablelaborcost_overtime);
                //人力变动费-节假日加班
                decimal variablelaborcost_overtime1 = 0m;
                var variablelaborcost_overtime1standard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 51 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
                && expensetype_laborcost.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))
                && a.ToDefault<int>("new_workinghours") == 30).ToList();
                foreach (var standard in variablelaborcost_overtime1standard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        variablelaborcost_overtime1 += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_variablelaborcost_overtime1", variablelaborcost_overtime1);
                storagefeelist.Add("new_variablelaborcost", variablelaborcost_basic + variablelaborcost_overtime + variablelaborcost_overtime1);
                //运营固定费-包材
                decimal fixedoperationcosts_packaging = 0m;
                var expensetype_packaging = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 100).ToList();
                var fixedoperationcosts_packagingstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 52 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_packaging.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).FirstOrDefault();
                if (fixedoperationcosts_packagingstandard != null)
                {
                    fixedoperationcosts_packaging = fixedoperationcosts_packagingstandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedoperationcosts_packagingstandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedoperationcosts_packagingstandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_fixedoperationcosts_packaging", fixedoperationcosts_packaging);
                //运营固定费-水电网
                decimal fixedoperationcosts_utilities = 0m;
                var expensetype_utilities = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 110).ToList();
                var fixedoperationcosts_utilitiesstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 52 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_utilities.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).FirstOrDefault();
                if (fixedoperationcosts_utilitiesstandard != null)
                {
                    fixedoperationcosts_utilities = fixedoperationcosts_utilitiesstandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedoperationcosts_utilitiesstandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedoperationcosts_utilitiesstandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_fixedoperationcosts_utilities", fixedoperationcosts_utilities);
                //运营固定费-管理费
                decimal fixedoperationcosts_management = 0m;
                var expensetype_management = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 120).ToList();
                var fixedoperationcosts_managementstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 52 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_management.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).FirstOrDefault();
                if (fixedoperationcosts_managementstandard != null)
                {
                    fixedoperationcosts_management = fixedoperationcosts_managementstandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedoperationcosts_managementstandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedoperationcosts_managementstandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_fixedoperationcosts_management", fixedoperationcosts_management);
                //运营固定费-托盘报废
                decimal fixedoperationcosts_scrapped = 0m;
                var expensetype_scrapped = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 130).ToList();
                var fixedoperationcosts_scrappedstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 52 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_scrapped.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).FirstOrDefault();
                if (fixedoperationcosts_scrappedstandard != null)
                {
                    fixedoperationcosts_scrapped = fixedoperationcosts_scrappedstandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedoperationcosts_scrappedstandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedoperationcosts_scrappedstandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_fixedoperationcosts_scrapped", fixedoperationcosts_scrapped);
                //运营固定费-其他
                decimal fixedoperationcosts_other = 0m;
                var expensetype_other = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 140).ToList();
                var fixedoperationcosts_otherstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 52 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_other.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).FirstOrDefault();
                if (fixedoperationcosts_otherstandard != null)
                {
                    fixedoperationcosts_other = fixedoperationcosts_otherstandard.GetAttributeValue<decimal>("new_amount");
                    if (fixedoperationcosts_otherstandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(fixedoperationcosts_otherstandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                allocationstoragefeelist.Add("new_fixedoperationcosts_other", fixedoperationcosts_other);
                storagefeelist.Add("new_fixedoperationalcost", fixedoperationcosts_packaging + fixedoperationcosts_utilities
                    + fixedoperationcosts_management + fixedoperationcosts_scrapped + fixedoperationcosts_other);
                //运营变动费-电视操作费
                decimal televisionoperationfee = 0m;
                var expensetype_tvoperationfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 40).ToList();
                var televisionoperationfeeotherstandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 53 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_tvoperationfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in televisionoperationfeeotherstandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        televisionoperationfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_televisionoperationfee", televisionoperationfee);
                allocationstoragefeelist.Add("new_televisionoperationfee", televisionoperationfee);
                //运营变动费-打包费
                decimal packingfee = 0m;
                var expensetype_packingfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 80).ToList();
                var packingfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 58 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_packingfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in packingfeestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        packingfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                var minimumEntity = packingfeestandard.FirstOrDefault(x => x.Contains("new_minimumfee"));
                if (minimumEntity != null)
                    packingfee = Math.Max(packingfee, minimumEntity.GetAttributeValue<Decimal>("new_minimumfee"));
                storagefeelist.Add("new_packingfee", packingfee);
                allocationstoragefeelist.Add("new_packingfee", packingfee);
                //运营变动费-出入库操作费
                decimal entryexithandlingfee = 0m;
                var expensetype_entryexithandlingfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 50).ToList();
                var entryexithandlingfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 55 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_entryexithandlingfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in entryexithandlingfeestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        entryexithandlingfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_entryexithandlingfee", entryexithandlingfee);
                allocationstoragefeelist.Add("new_entryexithandlingfee", entryexithandlingfee);
                //运营变动费-大小件操作费
                decimal itemsizehandlingfee = 0m;
                var expensetype_itemsizehandlingfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 60).ToList();
                var itemsizehandlingfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 56 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_itemsizehandlingfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in itemsizehandlingfeestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        itemsizehandlingfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_itemsizehandlingfee", itemsizehandlingfee);
                allocationstoragefeelist.Add("new_itemsizehandlingfee", itemsizehandlingfee);
                //运营变动费-入库上架费
                decimal inboundshelvingcharge = 0m;
                var expensetype_inboundshelvingcharge = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 70).ToList();
                var inboundshelvingchargestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 57 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_inboundshelvingcharge.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in inboundshelvingchargestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        inboundshelvingcharge += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_inboundshelvingcharge", inboundshelvingcharge);
                allocationstoragefeelist.Add("new_inboundshelvingcharge", inboundshelvingcharge);
                //运营变动费-质检费
                decimal qualityinspectionfee = 0m;
                var expensetype_qualityinspectionfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 90).ToList();
                var qualityinspectionfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 59 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && expensetype_qualityinspectionfee.Select(a => a.Id).Contains(a.ToDefault<Guid>("new_expensetypeconfigtable_id"))).ToList();
                foreach (var standard in qualityinspectionfeestandard)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                    && a.ToDefault<Guid>("new_srv_expense_standard_id") == standard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        qualityinspectionfee += standard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (standard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(standard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_qualityinspectionfee", qualityinspectionfee);
                allocationstoragefeelist.Add("new_qualityinspectionfee", qualityinspectionfee);
                //物流费
                decimal logisticsfeestorage = 0m;
                var expensetype_logisticsfee = expensetypeconfig.Entities.Where(a => a.ToDefault<int>("new_feetype") == 150).FirstOrDefault();
                var logisticsfeestoragestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 54 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id
               && a.ToDefault<Guid>("new_expensetypeconfigtable_id") == expensetype_logisticsfee?.Id).FirstOrDefault();
                if (logisticsfeestoragestandard != null)
                {
                    var unitpriceconfig = warehousing_unitprice.Entities.Where(a => a.GetAliasAttributeValue<EntityReference>("warehousing_unitprice.new_srv_station_id").Id == new_srv_station_id
                   && a.ToDefault<Guid>("new_srv_expense_standard_id") == logisticsfeestoragestandard.Id).FirstOrDefault();
                    if (unitpriceconfig != null)
                        logisticsfeestorage = logisticsfeestoragestandard.GetAttributeValue<decimal>("new_amount") * unitpriceconfig.GetAttributeValue<int>(fieldname);
                    if (logisticsfeestoragestandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(logisticsfeestoragestandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_storageandlogisticscost", logisticsfeestorage);
                allocationstoragefeelist.Add("new_storageandlogisticscost", logisticsfeestorage);
                //预提费（仓储）
                decimal withholdingfeestorage = 0m;
                var withholdingfeestandard = standardlist.Where(a => a.ToDefault<int>("new_feetype") == 41 && a.ToDefault<Guid>("new_stationservice_id") == new_srv_station_id && !a.Contains("new_stietype")).FirstOrDefault();
                if (withholdingfeestandard != null)
                {
                    withholdingfeestorage = withholdingfeestandard.GetAttributeValue<decimal>("new_amount");
                    if (withholdingfeestandard.Contains("new_transactioncurrency_id"))
                        transactioncurrency_idlist.Add(withholdingfeestandard.ToDefault<Guid>("new_transactioncurrency_id"));
                }
                storagefeelist.Add("new_withholdingfeestorage", withholdingfeestorage);
                allocationstoragefeelist.Add("new_withholdingfeestorage", withholdingfeestorage);
                //费用保留两位小数
                Dictionary<string, decimal> copystoragefeelist = new Dictionary<string, decimal>(storagefeelist);
                Dictionary<string, decimal> copyallocationstoragefeelist = new Dictionary<string, decimal>(allocationstoragefeelist);
                foreach (var kvp in copystoragefeelist)
                {
                    storagefeelist[kvp.Key] = Math.Round(kvp.Value, 2);
                }
                foreach (var kvp in copyallocationstoragefeelist)
                {
                    allocationstoragefeelist[kvp.Key] = Math.Round(kvp.Value, 2);
                }
                storagefee.storagefeelist = storagefeelist;
                storagefee.allocationstoragefeelist = allocationstoragefeelist;
                storagefee.transactioncurrency_idlist = transactioncurrency_idlist;
                #endregion
            }
            return storagefee;
        }
        /// <summary>
        /// 创建分摊权重数据表
        /// </summary>
        /// <param name="workorderlist"></param>
        /// <param name="stationid"></param>
        /// <returns></returns>
        public EntityCollection Createallocationweightdata(EntityCollection workordercols, Guid stationid, EntityCollection ratiolist, Dictionary<string, decimal> allocationstoragefeelist, int year, int month, int type)
        {
            EntityCollection cols = new EntityCollection();
            var category_ratiolist = ratiolist.Entities.Where(a => !a.Contains("new_model2_id") && a.Contains("new_category1_id")).ToList();
            decimal sparepartscost_multiply_ratio_sum = 0m;//备件费汇总 * 系数的结果汇总
            //金额汇总默认为0；逐行进行汇总
            Dictionary<string, decimal> fielddic1 = new Dictionary<string, decimal>();
            foreach (var field in allocationstoragefeelist.Keys.ToList())
            {
                fielddic1.Add(field, 0);
            }
            List<Entity> categoryweightlist = new List<Entity>();
            //匹配到的三级品类
            List<string> matchcategory3 = new List<string>();
            //匹配到的二级品类
            List<string> matchcategory2 = new List<string>();
            #region 根据三级品类，二级品类，一级品类优先集对分摊比例配置表进行排序
            List<Entity> category_ratiolist_sort = new List<Entity>();
            //三级品类有值的集合
            List<Entity> isnotnullcategory3list = category_ratiolist.Where(a => a.Contains("new_category3_id")).ToList();
            category_ratiolist_sort.AddRange(isnotnullcategory3list);
            //三级品类为空，二级品类有值的集合
            List<Entity> isnotnullcategory2list = category_ratiolist.Where(a => a.Contains("new_category2_id") && !a.Contains("new_category3_id")).ToList();
            category_ratiolist_sort.AddRange(isnotnullcategory2list);
            //三级品类为空，二级品类为空，一级品类有值的集合
            List<Entity> isnotnullcategory1list = category_ratiolist.Where(a => a.Contains("new_category1_id") && !a.Contains("new_category2_id") && !a.Contains("new_category3_id")).ToList();
            category_ratiolist_sort.AddRange(isnotnullcategory1list);
            category_ratiolist = category_ratiolist_sort;
            #endregion
            for (int i = 0; i < category_ratiolist.Count; i++)
            {
                Entity weightconfig = new Entity("new_allocationweightdatatable");
                weightconfig.Id = Guid.NewGuid();
                if (category_ratiolist[i].Contains("new_category3_id"))
                    weightconfig["new_category3_id"] = category_ratiolist[i]["new_category3_id"];
                if (category_ratiolist[i].Contains("new_category2_id"))
                    weightconfig["new_category2_id"] = category_ratiolist[i]["new_category2_id"];
                if (category_ratiolist[i].Contains("new_category1_id"))
                    weightconfig["new_category1_id"] = category_ratiolist[i]["new_category1_id"];
                List<Entity> matchedworkorder = new List<Entity>();
                if (category_ratiolist[i].Contains("new_category1_id")
                    && category_ratiolist[i].Contains("new_category2_id")
                    && category_ratiolist[i].Contains("new_category3_id"))
                {
                    matchedworkorder = workordercols.Entities.Where(a => a.ToDefault<string>("new_category1_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && a.ToDefault<string>("new_category2_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category2.new_code")
                    && a.ToDefault<string>("new_category3_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category3.new_code")).ToList();
                    if (matchedworkorder.Count > 0)
                        matchcategory3.Add(category_ratiolist[i].GetAliasAttributeValue<string>("category3.new_code"));
                }
                else if (category_ratiolist[i].Contains("new_category1_id")
                    && category_ratiolist[i].Contains("new_category2_id"))
                {
                    matchedworkorder = workordercols.Entities.Where(a => a.ToDefault<string>("new_category1_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && a.ToDefault<string>("new_category2_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category2.new_code")
                    && !matchcategory3.Contains(a.ToDefault<string>("new_category3_code"))).ToList();
                    if (matchedworkorder.Count > 0)
                        matchcategory2.Add(category_ratiolist[i].GetAliasAttributeValue<string>("category2.new_code"));
                }
                else if (category_ratiolist[i].Contains("new_category1_id"))
                {
                    matchedworkorder = workordercols.Entities.Where(a => a.ToDefault<string>("new_category1_code") == category_ratiolist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && !matchcategory3.Contains(a.ToDefault<string>("new_category3_code")) && !matchcategory2.Contains(a.ToDefault<string>("new_category2_code"))).ToList();
                }
                //工单数量
                var workordercount = matchedworkorder.Count;
                weightconfig["new_workordercount"] = workordercount;
                weightconfig["new_ratio"] = category_ratiolist[i].GetAttributeValue<decimal>("new_ratio");
                weightconfig["new_sparepartscostsum"] = matchedworkorder.Sum(a => a.GetAttributeValue<decimal>("new_sparepartscoststorage"));//备件费汇总
                weightconfig["new_srv_station_id"] = new EntityReference("new_srv_station", stationid);
                weightconfig["new_year"] = year;
                weightconfig["new_month"] = month;
                weightconfig["new_type"] = new OptionSetValue(type);
                categoryweightlist.Add(weightconfig);
            }
            sparepartscost_multiply_ratio_sum = categoryweightlist.Sum(a => a.GetAttributeValue<decimal>("new_sparepartscostsum") * a.GetAttributeValue<decimal>("new_ratio"));
            foreach (var item in categoryweightlist)
            {

                //备件费汇总 * 系数
                var sparepartscost_multiply_ratio = item.GetAttributeValue<decimal>("new_sparepartscostsum") * item.GetAttributeValue<decimal>("new_ratio");
                decimal weight = 0m;
                //分摊权重
                if (sparepartscost_multiply_ratio_sum != 0)
                    weight = (sparepartscost_multiply_ratio / sparepartscost_multiply_ratio_sum) * 100;
                weight = Math.Round(weight, 2);
                item["new_weight"] = weight;
            }
            //一二三级品类权重尾差处理
            decimal categoryweightsum = categoryweightlist.Sum(a => a.GetAttributeValue<decimal>("new_weight"));
            var categoryweighten = categoryweightlist.Where(a => a.GetAttributeValue<int>("new_workordercount") != 0).FirstOrDefault();
            if (categoryweighten != null)
                categoryweighten["new_weight"] = categoryweighten.GetAttributeValue<decimal>("new_weight") + (100 - categoryweightsum);
            //一二三级品类分摊金额
            foreach (var item in categoryweightlist)
            {
                int index = categoryweightlist.IndexOf(item);
                foreach (var item1 in allocationstoragefeelist)
                {
                    if (index == categoryweightlist.Count - 1)
                    {
                        //尾差处理
                        if (item.GetAttributeValue<int>("new_workordercount") > 0)
                        {
                            var lastallocationamount = item1.Value - fielddic1[item1.Key];
                            item[item1.Key] = lastallocationamount;
                        }
                        else
                        {
                            var maxItem = categoryweightlist.OrderByDescending(a => a.GetAttributeValue<decimal>(item1.Key)).FirstOrDefault();
                            var lastallocationamount = item1.Value - fielddic1[item1.Key];
                            maxItem[item1.Key] = maxItem.GetAttributeValue<decimal>(item1.Key) + lastallocationamount;
                        }
                    }
                    else
                    {
                        decimal weight = item.GetAttributeValue<decimal>("new_weight");
                        var allocationamount = item1.Value * weight / 100;
                        allocationamount = Math.Round(allocationamount, 2);
                        item[item1.Key] = allocationamount;
                        fielddic1[item1.Key] += allocationamount;
                    }
                }
            }
            cols.Entities.AddRange(categoryweightlist);
            #region 二次分摊，电视根据二级机型分摊金额
            //匹配三级品类是电视的工单
            List<Entity> workorderlist_tv = workordercols.Entities.Where(a => a.ToDefault<string>("new_category1_code") == "10000003"
            && a.ToDefault<string>("new_category2_code") == "20000008" && a.ToDefault<string>("new_category3_code") == "30000008").ToList();
            List<Entity> modelweightlist = new List<Entity>();
            var model2_ratiolist = ratiolist.Entities.Where(a => a.Contains("new_model2_id")).ToList();
            var tv_categoryconfig = cols.Entities.Where(a => a.Contains("new_category3_id") && a.ToDefault<Guid>("new_category3_id").ToString() == tvcategoryid).FirstOrDefault();
            Dictionary<string, decimal> model2allocationstoragefeedic = new Dictionary<string, decimal>();
            if (tv_categoryconfig != null)
            {
                foreach (var item in allocationstoragefeelist.Keys.ToList())
                {
                    if (tv_categoryconfig.Contains(item))
                        model2allocationstoragefeedic.Add(item, tv_categoryconfig.GetAttributeValue<decimal>(item));
                }
            }
            //金额汇总默认为0；逐行进行汇总
            Dictionary<string, decimal> fielddic2 = new Dictionary<string, decimal>();
            foreach (var field in allocationstoragefeelist.Keys.ToList())
            {
                fielddic2.Add(field, 0);
            }
            for (int i = 0; i < model2_ratiolist.Count; i++)
            {
                Entity weightconfig = new Entity("new_allocationweightdatatable");
                weightconfig.Id = Guid.NewGuid();
                weightconfig["new_model2_id"] = model2_ratiolist[i]["new_model2_id"];
                if (model2_ratiolist[i].Contains("new_category3_id"))
                    weightconfig["new_category3_id"] = model2_ratiolist[i]["new_category3_id"];
                if (model2_ratiolist[i].Contains("new_category2_id"))
                    weightconfig["new_category2_id"] = model2_ratiolist[i]["new_category2_id"];
                if (model2_ratiolist[i].Contains("new_category1_id"))
                    weightconfig["new_category1_id"] = model2_ratiolist[i]["new_category1_id"];
                var matchedworkorder = workorderlist_tv.Where(a => a.Contains("new_model2_code")
                && a.ToDefault<string>("new_model2_code") == model2_ratiolist[i].GetAliasAttributeValue<string>("model2.new_code")).ToList();
                //工单数量
                var workordercount = matchedworkorder.Count;
                weightconfig["new_workordercount"] = workordercount;
                weightconfig["new_ratio1"] = model2_ratiolist[i].GetAttributeValue<decimal>("new_ratio");
                weightconfig["new_srv_station_id"] = new EntityReference("new_srv_station", stationid);
                weightconfig["new_year"] = year;
                weightconfig["new_month"] = month;
                weightconfig["new_type"] = new OptionSetValue(type);
                modelweightlist.Add(weightconfig);
            }
            //工单数量 *系数 汇总
            decimal workordercount_multiply_ratio_sum = modelweightlist.Sum(a => a.GetAttributeValue<int>("new_workordercount") * a.GetAttributeValue<decimal>("new_ratio1"));
            foreach (var item in modelweightlist)
            {
                int index = modelweightlist.IndexOf(item);
                //工单数量 * 系数
                var workordercount_multiply_ratio = item.GetAttributeValue<int>("new_workordercount") * item.GetAttributeValue<decimal>("new_ratio1");
                decimal weight = 0m;
                //分摊权重
                if (workordercount_multiply_ratio_sum != 0)
                    weight = (workordercount_multiply_ratio / workordercount_multiply_ratio_sum) * 100;
                weight = Math.Round(weight, 2);
                item["new_weight"] = weight;
            }
            //二级机型尾差处理
            decimal modelweightsum = modelweightlist.Sum(a => a.GetAttributeValue<decimal>("new_weight"));
            var modelweighten = modelweightlist.Where(a => a.GetAttributeValue<int>("new_workordercount") != 0).FirstOrDefault();
            if (modelweighten != null)
                modelweighten["new_weight"] = modelweighten.GetAttributeValue<decimal>("new_weight") + (100 - modelweightsum);
            //二级机型分摊金额
            foreach (var item in modelweightlist)
            {
                int index = modelweightlist.IndexOf(item);
                foreach (var item1 in model2allocationstoragefeedic)
                {
                    if (index == modelweightlist.Count - 1)
                    {
                        //尾差处理
                        if (item.GetAttributeValue<int>("new_workordercount") > 0)
                        {
                            var lastallocationamount = item1.Value - fielddic2[item1.Key];
                            item[item1.Key] = lastallocationamount;
                        }
                        else
                        {
                            var maxItem = modelweightlist.OrderByDescending(a => a.GetAttributeValue<decimal>(item1.Key)).FirstOrDefault();
                            var lastallocationamount = item1.Value - fielddic2[item1.Key];
                            maxItem[item1.Key] = maxItem.GetAttributeValue<decimal>(item1.Key) + lastallocationamount;
                        }
                    }
                    else
                    {
                        decimal weight = item.GetAttributeValue<decimal>("new_weight");
                        var allocationamount = item1.Value * weight / 100;
                        allocationamount = Math.Round(allocationamount, 2);
                        item[item1.Key] = allocationamount;
                        fielddic2[item1.Key] += allocationamount;
                    }
                }
            }
            #endregion
            cols.Entities.AddRange(modelweightlist);
            CommonHelper.MultCreateRequest(OrganizationService, log, cols);
            return cols;
        }
        /// <summary>
        /// 根据分摊权重数据表主键guid查询
        /// </summary>
        /// <param name="idlist"></param>
        /// <returns></returns>
        public EntityCollection Getallocationweightdata(List<Guid> idlist)
        {
            EntityCollection cols = new EntityCollection();
            if (idlist.Count > 0)
            {
                QueryExpression qe = new QueryExpression("new_allocationweightdatatable");
                qe.NoLock = true;
                qe.ColumnSet = new ColumnSet(true);
                qe.Criteria.AddCondition(new ConditionExpression("new_allocationweightdatatableid", ConditionOperator.In, idlist));
                LinkEntity link1 = new LinkEntity("new_allocationweightdatatable", "new_category1", "new_category1_id", "new_category1id", JoinOperator.LeftOuter);
                link1.Columns = new ColumnSet("new_code");
                link1.EntityAlias = "category1";
                LinkEntity link2 = new LinkEntity("new_allocationweightdatatable", "new_category2", "new_category2_id", "new_category2id", JoinOperator.LeftOuter);
                link2.Columns = new ColumnSet("new_code");
                link2.EntityAlias = "category2";
                LinkEntity link3 = new LinkEntity("new_allocationweightdatatable", "new_category3", "new_category3_id", "new_category3id", JoinOperator.LeftOuter);
                link3.Columns = new ColumnSet("new_code");
                link3.EntityAlias = "category3";
                LinkEntity link4 = new LinkEntity("new_allocationweightdatatable", "new_model2", "new_model2_id", "new_model2id", JoinOperator.LeftOuter);
                link4.Columns = new ColumnSet("new_code");
                link4.EntityAlias = "model2";
                qe.LinkEntities.Add(link1);
                qe.LinkEntities.Add(link2);
                qe.LinkEntities.Add(link3);
                qe.LinkEntities.Add(link4);
                cols = OrganizationService.RetrieveMultiple(qe);
            }
            return cols;
        }
        /// <summary>
        /// 仓储费服务商工单费用信息表，工单费用表关联结算单，分摊费用 
        /// </summary>
        /// <param name="workordersettlementList"></param>
        /// <param name="expenseclaimid"></param>
        public EntityCollection AssociateSettlement(List<Entity> workordersettlementList, EntityCollection weightlist, Dictionary<string, decimal> allocationfeelist, int settlementType)
        {
            EntityCollection cols = new EntityCollection();
            var category_weightlist = weightlist.Entities.Where(a => !a.Contains("new_model2_id")).ToList();
            var model2_weightlist = weightlist.Entities.Where(a => a.Contains("new_model2_id")).ToList();
            //匹配到的三级品类
            List<string> matchcategory3 = new List<string>();
            //匹配到的二级品类
            List<string> matchcategory2 = new List<string>();
            #region 根据三级品类，二级品类，一级品类优先集对分摊比例配置表进行排序
            List<Entity> category_weightlist_sort = new List<Entity>();
            //三级品类有值的集合
            List<Entity> isnotnullcategory3list = category_weightlist.Where(a => a.Contains("new_category3_id")).ToList();
            category_weightlist_sort.AddRange(isnotnullcategory3list);
            //三级品类为空，二级品类有值的集合
            List<Entity> isnotnullcategory2list = category_weightlist.Where(a => a.Contains("new_category2_id") && !a.Contains("new_category3_id")).ToList();
            category_weightlist_sort.AddRange(isnotnullcategory2list);
            //三级品类为空，二级品类为空，一级品类有值的集合
            List<Entity> isnotnullcategory1list = category_weightlist.Where(a => a.Contains("new_category1_id") && !a.Contains("new_category2_id") && !a.Contains("new_category3_id")).ToList();
            category_weightlist_sort.AddRange(isnotnullcategory1list);
            category_weightlist = category_weightlist_sort;
            #endregion
            for (int i = 0; i < category_weightlist.Count; i++)
            {
                List<Entity> matchedworkorder = new List<Entity>();
                if (category_weightlist[i].Contains("category1.new_code")
                    && category_weightlist[i].Contains("category2.new_code")
                    && category_weightlist[i].Contains("category3.new_code"))
                {
                    matchedworkorder = workordersettlementList.Where(a => a.ToDefault<string>("new_category1_code") == category_weightlist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && a.ToDefault<string>("new_category2_code") == category_weightlist[i].GetAliasAttributeValue<string>("category2.new_code")
                    && a.ToDefault<string>("new_category3_code") == category_weightlist[i].GetAliasAttributeValue<string>("category3.new_code")).ToList();
                    if (matchedworkorder.Count > 0)
                        matchcategory3.Add(category_weightlist[i].GetAliasAttributeValue<string>("category3.new_code"));
                }
                else if (category_weightlist[i].Contains("category1.new_code")
                    && category_weightlist[i].Contains("category2.new_code"))
                {
                    matchedworkorder = workordersettlementList.Where(a => a.ToDefault<string>("new_category1_code") == category_weightlist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && a.ToDefault<string>("new_category2_code") == category_weightlist[i].GetAliasAttributeValue<string>("category2.new_code")
                    && !matchcategory3.Contains(a.ToDefault<string>("new_category3_code"))).ToList();
                    if (matchedworkorder.Count > 0)
                        matchcategory2.Add(category_weightlist[i].GetAliasAttributeValue<string>("category2.new_code"));
                }
                else if (category_weightlist[i].Contains("category1.new_code"))
                {
                    matchedworkorder = workordersettlementList.Where(a => a.ToDefault<string>("new_category1_code") == category_weightlist[i].GetAliasAttributeValue<string>("category1.new_code")
                    && !matchcategory3.Contains(a.ToDefault<string>("new_category3_code")) && !matchcategory2.Contains(a.ToDefault<string>("new_category2_code"))).ToList();
                }
                bool secondaryallocation = false;
                if (category_weightlist[i].ToDefault<Guid>("new_category3_id").ToString() == tvcategoryid)
                    secondaryallocation = true;
                if (matchedworkorder.Count > 0)
                {
                    EntityCollection collection = Allocationamount(matchedworkorder, secondaryallocation, model2_weightlist, category_weightlist[i], allocationfeelist.Keys.ToList(), settlementType);
                    cols.Entities.AddRange(collection.Entities);
                }
            }
            return cols;
        }
        /// <summary>
        /// 根据分摊权重数据标准分摊金额
        /// </summary>
        /// <param name="workorderlist"></param>
        /// <param name="secondaryallocation"></param>
        /// <param name="model2_weight"></param>
        /// <param name="category_weight"></param>
        /// <param name="expenseclaim"></param>
        /// <param name="fieldlist"></param>
        public EntityCollection Allocationamount(List<Entity> workorderlist, bool secondaryallocation, List<Entity> model2_weight, Entity category_weight, List<string> fieldlist, int settlementType)
        {
            EntityCollection cols = new EntityCollection();
            if (secondaryallocation)
            {
                foreach (var item in model2_weight)
                {
                    var secondmatchedworkorder = workorderlist.Where(a => a.Contains("new_model2_code") && a.ToDefault<string>("new_model2_code") == item.GetAliasAttributeValue<string>("model2.new_code")).ToList();
                    if (secondmatchedworkorder.Count > 0)
                    {
                        var model2workorderlist = InitWorkorderCosttable(secondmatchedworkorder, item, fieldlist, settlementType).Entities.ToList();
                        cols.Entities.AddRange(model2workorderlist);
                    }
                }
            }
            else
            {
                if (workorderlist.Count > 0)
                    cols = InitWorkorderCosttable(workorderlist, category_weight, fieldlist, settlementType);
            }
            return cols;
        }
        /// <summary>
        /// 创建工单费用表对象，未执行组织服务创建，创建动作放在消息队列中执行
        /// </summary>
        /// <param name="workorderlist">匹配的工单</param>
        /// <param name="category_weight">分摊权重数据</param>
        /// <param name="expenseclaim">结算单</param>
        /// <param name="fieldlist">金额字段集合</param>
        /// <returns></returns>
        public EntityCollection InitWorkorderCosttable(List<Entity> workorderlist, Entity category_weight, List<string> fieldlist, int settlementType)
        {
            EntityCollection collection = new EntityCollection();
            Dictionary<string, decimal> fielddic = new Dictionary<string, decimal>();
            foreach (var item in fieldlist)
            {
                if (category_weight.Contains(item))
                {
                    decimal averageamount = Math.Round(category_weight.GetAttributeValue<decimal>(item) / workorderlist.Count, 2);
                    fielddic.Add(item, averageamount);
                }
            }
            //金额汇总默认为0；逐行进行汇总
            Dictionary<string, decimal> fielddic1 = new Dictionary<string, decimal>();
            foreach (var item in fielddic)
            {
                fielddic1.Add(item.Key, 0);
            }
            for (int i = 0; i < workorderlist.Count; i++)
            {
                Entity workorder_costtable = new Entity("new_workorder_costtable");
                workorder_costtable["new_workorder_settlement_id"] = workorderlist[i].ToEntityReference();
                if (workorderlist[i].Contains("costtable.new_workorder_costtableid"))
                    workorder_costtable.Id = workorderlist[i].GetAliasAttributeValue<Guid>("costtable.new_workorder_costtableid");
                workorder_costtable["new_name"] = workorderlist[i].GetAttributeValue<string>("new_name");
                foreach (var field in fieldlist)
                {
                    if (category_weight.Contains(field))
                    {
                        if (i == workorderlist.Count - 1)
                        {
                            //最后一条数据需要处理尾差
                            decimal lastamount = category_weight.GetAttributeValue<decimal>(field) - fielddic1[field];
                            workorder_costtable[field] = lastamount;
                        }
                        else
                        {
                            if (fielddic.ContainsKey(field))
                            {
                                workorder_costtable[field] = fielddic[field];
                                fielddic1[field] += fielddic[field];
                            }
                        }
                    }
                }
                //new_sku要赋值成AliasedValue类型，new_sku字段是在new_workorder_settlement表上
                //增加国家new_country_id字段，用于成本中心取值
                workorder_costtable["settle.new_sku"] = new AliasedValue("new_workorder_settlement", "new_sku", workorderlist[i].GetAttributeValue<string>("new_sku"));
                workorder_costtable["settle.new_country_id"] = new AliasedValue("new_workorder_settlement", "new_country_id", workorderlist[i].GetAttributeValue<EntityReference>("new_country_id"));
                //预提金额
                decimal withholdingmoneystorage = 0;
                fieldlist.ForEach(field =>
                {
                    withholdingmoneystorage += workorder_costtable.GetAttributeValue<decimal>(field);
                });
                if (settlementType == 5)
                    workorder_costtable["new_withholdingmoneystorage"] = withholdingmoneystorage;
                if (settlementType == 7)
                    workorder_costtable["new_withholdingmoneylogistics"] = withholdingmoneystorage;
                collection.Entities.Add(workorder_costtable);
            }
            return collection;
        }
        public static T DeepCopy<T>(T obj)
        {
            string jsonString = System.Text.Json.JsonSerializer.Serialize(obj);
            return System.Text.Json.JsonSerializer.Deserialize<T>(jsonString);
        }
        /// <summary>
        /// 创建发票环节sap批次数据
        /// </summary>
        /// <param name="expense_claim_id"></param>
        /// <param name="sap_type"></param>
        public void CreateInvoiceSapPatch(string expense_claim_id, int sap_type)
        {
            SapCommand sap = new SapCommand(OrganizationService);
            SapCommand_new2 sap_Create = new SapCommand_new2(log, OrganizationService);
            var expenseClaim = OrganizationService.Retrieve("new_srv_expense_claim", new Guid(expense_claim_id), new ColumnSet(true));
            var new_srv_station_id = expenseClaim.GetAttributeValue<EntityReference>("new_srv_station_id");
            int businesstype = expenseClaim.ToDefault<int>("new_businesstype");
            var bystietype = expenseClaim.Contains("new_stietype") ? true : false;
            if (businesstype == 5 || businesstype == 7)
            {
                //仓储结费
                SettlementCommand_New settlement = new SettlementCommand_New();
                //查询工单费用表
                QueryExpression qe1 = new QueryExpression("new_workorder_costtable");
                qe1.ColumnSet = new ColumnSet("new_settlementmoneystorage", "new_withholdingmoneystorage", "new_settlementmoneylogistics", "new_withholdingmoneylogistics");
                qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                if (businesstype == 5)
                    qe1.Criteria.AddCondition("new_expenseclaimidstorage", ConditionOperator.Equal, expense_claim_id);
                if (businesstype == 7)
                    qe1.Criteria.AddCondition("new_expenseclaimidlogistics", ConditionOperator.Equal, expense_claim_id);
                LinkEntity link1 = new LinkEntity("new_workorder_costtable", "new_workorder_settlement", "new_workorder_settlement_id", "new_workorder_settlementid", JoinOperator.Inner);
                link1.Columns = new ColumnSet("new_sku", "new_country_id");
                link1.EntityAlias = "settle";
                qe1.LinkEntities.Add(link1);
                EntityCollection workordercostlist = CommonHelper.QueryExpressionPage(OrganizationService, qe1);
                var srv_station = OrganizationService.Retrieve("new_srv_station", new_srv_station_id.Id, new ColumnSet(true));
                DisabledSapbatch(sap_type, expenseClaim.Id.ToString(), OrganizationService);
                settlement.CreateSAPLogStorage(workordercostlist, srv_station, expenseClaim, sap_type, "发票", businesstype, log, false);
            }
            else if (businesstype == 10) 
            {
                //高维工厂
                //备件
                var new_stietype = expenseClaim.ToDefault<int>("new_stietype");
                var srv_station = OrganizationService.Retrieve("new_srv_station", new_srv_station_id.Id, new ColumnSet(true));
                SettlementHelp settlementHelp = new SettlementHelp(log);
                settlementHelp.CreateSAPLogCostCenter(srv_station, expenseClaim, 3, "发票", log);
            }
            else
            {
                //网点类型有值取服务网点，没值则取服务商
                if (!bystietype)
                {
                    //备件
                    var new_stietype = expenseClaim.ToDefault<int>("new_stietype");
                    var srv_station = OrganizationService.Retrieve("new_srv_station", new_srv_station_id.Id, new ColumnSet(true));
                    var orColl = sap_Create.CreateSAPLog(sap_type, expenseClaim, srv_station, "发票");
                    CommonHelper.BatchExecuteBatches(log, (ServiceClient)OrganizationService, orColl).Wait();
                }
                else if (bystietype)
                {
                    //工单
                    var new_stietype = expenseClaim.ToDefault<int>("new_stietype");
                    var srv_station = sap_Create.GetSrvStation(new_srv_station_id.Id, new_stietype, OrganizationService);
                    var orColl = sap_Create.CreateSAPLog(sap_type, expenseClaim, srv_station, "发票");
                    CommonHelper.BatchExecuteBatches(log, (ServiceClient)OrganizationService, orColl).Wait();
                }
            }
        }
        /// <summary>
        /// 创建付款环节sap批次数据
        /// </summary>
        /// <param name="expense_claim"></param>
        /// <param name="station"></param>
        /// <param name="typestr"></param>
        public void CreatePaymentSapPatch(Entity expense_claim, Entity station, string typestr)
        {
            try
            {
                DisabledSapbatch(4, expense_claim.Id.ToString(), OrganizationService);
                string contractingbody = expense_claim.GetAttributeValue<string>("new_contractingbody");
                string currency = expense_claim.ToDefault<string>("new_sapcurrency");//币种
                int businesstype = expense_claim.ToDefault<int>("new_businesstype");//结算单类型
                string costcenter = expense_claim.GetAttributeValue<string>("new_costcenter");//成本中心                                             
                string originalOrder = null; //内部订单，发票号
                if (expense_claim.Contains("new_country_id"))
                    originalOrder = GetInternalOrder(expense_claim.GetAttributeValue<EntityReference>("new_country_id").Id.ToString(), contractingbody);
                var new_remark = string.Empty;
                //SAP回传金额
                string sapamount = expense_claim.ToDefault<string>("new_sapamount");
                //供应商SAPID
                string refer1 = expense_claim.GetAttributeValue<string>("new_sapcode");
                //结算年
                int year = expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月
                int month = expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                int expenseclaimtype = expense_claim.GetAttributeValue<OptionSetValue>("new_businesstype").Value;
                //发票代码
                string new_invoicenumber = expense_claim.GetAttributeValue<string>("new_invoicenumber");
                //发票号
                string new_invoiceno = expense_claim.GetAttributeValue<string>("new_invoiceno");
                int isreversecharge = expense_claim.ToDefault<int>("new_isreversecharge");
                //服务商简称
                string name = station?.ToDefault<string>("new_stationabbreviation");
                //国家名称
                string countryname = expense_claim.Contains("new_country_id") ? expense_claim.GetAttributeValue<EntityReference>("new_country_id").Name : "";
                string new_stietypetext = expense_claim.Contains("new_stietype") ? expense_claim.FormattedValues["new_stietype"] : "";
                Dictionary<string, string> infoType = new Dictionary<string, string>() { { "预提", "Accrual" }, { "反冲", "Reversal" }, { "发票", "Invoice" }, { "付款", "Payment" } };
                typestr = typestr + infoType.Where(a => a.Key == typestr).FirstOrDefault().Value;
                new_remark = (typestr + year + month + name + countryname);
                new_remark = new_remark.Length > 25 ? new_remark[..25] : new_remark;
                DateTime businessDate = expense_claim.Contains("new_paymentdate") ? expense_claim.ToDefault<DateTime>("new_paymentdate") : DateTime.UtcNow;
                //查询SKU1配置表
                QueryExpression qe1 = new QueryExpression("new_config_sku1");
                qe1.ColumnSet = new ColumnSet(true);
                qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe1.Criteria.AddCondition("new_contractingbody", ConditionOperator.Equal, contractingbody);
                qe1.Criteria.AddCondition("new_infotype", ConditionOperator.Equal, 4);//类型 = 付款
                EntityCollection sku1_cols = OrganizationService.RetrieveMultiple(qe1);
                //创建new_sap_batch 
                Entity sap_batch = new Entity("new_sap_batch");
                sap_batch["new_infotype"] = new OptionSetValue(4);//类型 = 付款
                sap_batch["new_businessdate"] = businessDate;//营业日期
                sap_batch["new_expensetno"] = expense_claim.ToEntityReference();
                sap_batch["new_receivablessum"] = Convert.ToDecimal(sapamount);
                Guid batchid = OrganizationService.Create(sap_batch);
                //创建new_sap_main
                Entity sap_main = new Entity("new_sap_main");
                sap_main["new_businesstype"] = "HWSH";
                sap_main["new_businessdate"] = businessDate;//营业日期
                sap_main["new_company"] = contractingbody;
                sap_main["new_department"] = costcenter;
                sap_main["new_originalorder"] = originalOrder;
                sap_main["new_remark"] = new_remark;
                sap_main["new_sapbatch"] = new EntityReference("new_sap_batch", batchid);
                Guid sap_mainid = OrganizationService.Create(sap_main);
                //创建new_sap_detail
                var config_sku1list = sku1_cols.Entities.Where(a => a.ToDefault<int>("new_businesstype") == expenseclaimtype).ToList();
                if (config_sku1list.Count == 0)
                    config_sku1list = sku1_cols.Entities.Where(a => !a.Contains("new_businesstype") /*&& a.ToDefault<int>("new_isreversecharge") == isreversecharge*/).ToList();
                var skuconfig = new Entity();
                if (config_sku1list.Count > 0)
                    skuconfig = config_sku1list.First();
                var otherskuconfig = config_sku1list.Where(a => a.Id != skuconfig.Id).ToList();//维护了多条sku
                string sku1 = skuconfig.GetAttributeValue<string>("new_name");
                int rowId = 1;
                Entity sap_detail = new Entity("new_sap_detail");
                sap_detail["new_rowid"] = rowId;
                sap_detail["new_sku1"] = sku1;
                if (sku1 == "PGE4009MI")
                {
                    sap_detail["new_sku2"] = expense_claim.ToDefault<string>("new_sapsubject");
                }
                else
                {
                    sap_detail["new_sku2"] = skuconfig.GetAttributeValue<string>("new_acctsubjects");//会计科目
                }
                sap_detail["new_currency"] = currency;
                sap_detail["new_originalorder"] = originalOrder;
                sap_detail["new_remark"] = new_remark;
                sap_detail["new_refer1"] = refer1;
                sap_detail["new_refer2"] = new_invoicenumber;
                sap_detail["new_refer3"] = new_invoiceno;
                if (contractingbody == "1440")
                {
                    if (sku1 == "PGE4009MI")
                    {
                        sap_detail["new_receivables"] = sapamount;
                    }
                    else if (sku1 == "PGE4010MI")
                    {
                        sap_detail["new_receivables"] = (Convert.ToDecimal(sapamount) / 1.04m * 0.03m).ToString();
                    }
                    else if (sku1 == "PGE4008MI")
                    {
                        sap_detail["new_receivables"] = ((Convert.ToDecimal(sapamount) / 1.04m * 0.03m) + Convert.ToDecimal(sapamount)).ToString();
                    }
                }
                else
                {
                    sap_detail["new_receivables"] = sapamount;
                }
                sap_detail["new_sap_mainid"] = new EntityReference("new_sap_main", sap_mainid);
                OrganizationService.Create(sap_detail);
                foreach (var othersku in otherskuconfig)
                {
                    var sapDetail2 = new Entity("new_sap_detail");
                    for (int i = 0; i < sap_detail.Attributes.Count; i++)
                    {
                        var butesName = sap_detail.Attributes.Keys.ToList();
                        sapDetail2[butesName[i]] = sap_detail[butesName[i]];
                    }
                    sapDetail2["new_rowid"] = rowId++;
                    sapDetail2["new_sku1"] = othersku.GetAttributeValue<string>("new_name");//虚拟物料
                    var othersku1 = othersku.GetAttributeValue<string>("new_name");
                    if (othersku1 == "PGE4009MI")
                    {
                        sapDetail2["new_sku2"] = expense_claim.ToDefault<string>("new_sapsubject");
                    }
                    else
                    {
                        sapDetail2["new_sku2"] = skuconfig.GetAttributeValue<string>("new_acctsubjects");//会计科目
                    }
                    //sapDetail2["new_sku2"] = othersku.GetAttributeValue<string>("new_acctsubjects");//会计科目
                    if (contractingbody == "1440")
                    {
                        if (othersku1 == "PGE4009MI")
                        {
                            sapDetail2["new_receivables"] = sapamount;
                        }
                        else if (othersku1 == "PGE4010MI")
                        {
                            sapDetail2["new_receivables"] = (Convert.ToDecimal(sapamount) / 1.04m * 0.03m).ToString();
                        }
                        else if (othersku1 == "PGE4008MI")
                        {
                            sapDetail2["new_receivables"] = ((Convert.ToDecimal(sapamount) / 1.04m * 0.03m) + Convert.ToDecimal(sapamount)).ToString();
                        }
                    }
                    else
                    {
                        sapDetail2["new_receivables"] = sapamount;
                    }
                    OrganizationService.Create(sapDetail2);
                }
                //更新new_sap_batch
                Entity batch = new Entity("new_sap_batch", batchid);
                batch["new_maincount"] = 1;
                batch["new_detailcount"] = config_sku1list.Count;
                OrganizationService.Update(batch);
            }
            catch (Exception ex)
            {
                log.LogInformation($"{ex.Message}");
                throw new Exception($"{ex.Message}");
            }
        }
        /// <summary>
        /// 根据国家,签约主体获取内部订单号
        /// </summary>
        /// <param name="countryid"></param>
        /// <param name="contractingbody"></param>
        /// <returns></returns>
        public string GetInternalOrder(string countryid, string contractingbody)
        {
            var fetchXml = @"<fetch distinct='false' no-lock='true' mapping='logical'>
                              <entity name='new_internalorderconfigure'>
                                <attribute name='new_internalorder' />
                                <attribute name='new_name' />
                                <attribute name='new_internalorderconfigureid' />
                                <attribute name='new_contractingbody' />
                                <filter type='and'>
                                  <condition attribute='statecode' operator='eq' value='0' />
                                  <condition attribute='new_country_id' operator='eq' value='{0}' />
                                  <condition attribute='new_contractingbody' operator='eq' value='{1}' />
                                </filter>
                              </entity>
                            </fetch>";
            EntityCollection collection = OrganizationService.RetrieveMultiple(new FetchExpression(string.Format(fetchXml, countryid, contractingbody)));
            if (collection?.Entities?.Count <= 0)
            {
                return null;
            }
            else
            {
                return collection.Entities[0]?.GetAttributeValue<string>("new_internalorder");
            }
        }
        /// <summary>
        /// 停用new_sap_batch数据
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="expense_claimid"></param>
        /// <param name="service"></param>
        public void DisabledSapbatch(int type, string expense_claimid, IOrganizationService service)
        {
            EntityCollection cols = service.RetrieveMultiple(
                new QueryExpression("new_sap_batch")
                {
                    ColumnSet = new ColumnSet(false),
                    Criteria = new FilterExpression
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_expensetno", ConditionOperator.Equal, expense_claimid),
                            new ConditionExpression("new_infotype", ConditionOperator.Equal, type),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity(item.LogicalName, item.Id);
                en["statecode"] = new OptionSetValue(1);
                service.Update(en);
            }
        }
        /// <summary>
        /// 获取物流费明细
        /// </summary>
        /// <returns></returns>
        public EntityCollection Getlogisticsfeedetail()
        {
            QueryExpression qe = new QueryExpression("new_logisticsfeedetail_table");
            qe.ColumnSet = new ColumnSet(true);
            //qe.Criteria.AddCondition("new_year", ConditionOperator.Equal, dateTime.Year.ToString());
            //qe.Criteria.AddCondition("new_month", ConditionOperator.Equal, dateTime.Month.ToString());
            qe.Criteria.AddCondition("new_state", ConditionOperator.Equal, 2);//处理状态 = 处理成功
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link = new LinkEntity("new_logisticsfeedetail_table", "new_logisticsfee_table", "new_logisticsfee_table_id", "new_logisticsfee_tableid", JoinOperator.Inner);
            link.EntityAlias = "logisticsfee";
            link.Columns = new ColumnSet("new_srv_station_id");
            link.LinkCriteria.AddCondition("new_year", ConditionOperator.Equal, dateTime.Year.ToString());
            link.LinkCriteria.AddCondition("new_month", ConditionOperator.Equal, dateTime.Month.ToString());
            link.LinkCriteria.AddCondition("new_state", ConditionOperator.Equal, 3);//处理状态 = 处理成功
            qe.LinkEntities.Add(link);
            EntityCollection cols = OrganizationService.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 根据服务商汇总物流费明细各种费用
        /// </summary>
        /// <param name="new_srv_stationid"></param>
        /// <param name="logisticsfeedetailcols"></param>
        /// <returns></returns>
        public LogisticsfeeModel Getlogisticsfeedetailfee(string new_srv_stationid, EntityCollection logisticsfeedetailcols, EntityCollection expensestandardlist)
        {
            LogisticsfeeModel logisticsfee = new LogisticsfeeModel();
            Dictionary<string, decimal> logisticsfeedic = new Dictionary<string, decimal>();
            var logisticsfeedetails = logisticsfeedetailcols.Entities.Where(a => a.Contains("logisticsfee.new_srv_station_id") && a.GetAliasAttributeValue<EntityReference>("logisticsfee.new_srv_station_id").Id.ToString() == new_srv_stationid);
            logisticsfeedic.Add("new_apifee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_apifee")));
            logisticsfeedic.Add("new_doafee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_doafee")));
            logisticsfeedic.Add("new_adcfilefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_adcfilefee")));
            logisticsfeedic.Add("new_allocatefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_allocatefee")));
            logisticsfeedic.Add("new_storagefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_storagefee")));
            logisticsfeedic.Add("new_operatefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_operatefee")));
            logisticsfeedic.Add("new_warehousesmovingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_warehousesmovingfee")));
            logisticsfeedic.Add("new_insurancefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_insurancefee")));
            logisticsfeedic.Add("new_operateservicefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_operateservicefee")));
            logisticsfeedic.Add("new_packing2fee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_packing2fee")));
            logisticsfeedic.Add("new_magneticfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_magneticfee")));
            logisticsfeedic.Add("new_freightforwardingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_freightforwardingfee")));
            logisticsfeedic.Add("new_tarifffee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_tarifffee")));
            logisticsfeedic.Add("new_registrationfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_registrationfee")));
            logisticsfeedic.Add("new_servicechargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_servicechargefee")));
            logisticsfeedic.Add("new_portsurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_portsurchargefee")));
            logisticsfeedic.Add("new_customscommissionfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_customscommissionfee")));
            logisticsfeedic.Add("new_domesticfreightfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_domesticfreightfee")));
            logisticsfeedic.Add("new_foreigninspectionfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigninspectionfee")));
            logisticsfeedic.Add("new_foreigntelexfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigntelexfee")));
            logisticsfeedic.Add("new_foreigndeliveryfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigndeliveryfee")));
            logisticsfeedic.Add("new_foreigncustomsfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigncustomsfee")));
            logisticsfeedic.Add("new_foreigndeclarationfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigndeclarationfee")));
            logisticsfeedic.Add("new_foreignpickfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreignpickfee")));
            logisticsfeedic.Add("new_foreignmiscellaneousfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreignmiscellaneousfee")));
            logisticsfeedic.Add("new_foreignthcfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreignthcfee")));
            logisticsfeedic.Add("new_transitfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_transitfee")));
            logisticsfeedic.Add("new_oceanfreightfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_oceanfreightfee")));
            logisticsfeedic.Add("new_foreigndocumentmakingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigndocumentmakingfee")));
            logisticsfeedic.Add("new_documenttransferfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_documenttransferfee")));
            logisticsfeedic.Add("new_airfreightfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_airfreightfee")));
            logisticsfeedic.Add("new_landfreightfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_landfreightfee")));
            logisticsfeedic.Add("new_timberingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_timberingfee")));
            logisticsfeedic.Add("new_deliveryfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_deliveryfee")));
            logisticsfeedic.Add("new_containerloadfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_containerloadfee")));
            logisticsfeedic.Add("new_imofee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_imofee")));
            logisticsfeedic.Add("new_packagingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_packagingfee")));
            logisticsfeedic.Add("new_commissionfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_commissionfee")));
            logisticsfeedic.Add("new_agencyfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_agencyfee")));
            logisticsfeedic.Add("new_registeredparkingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_registeredparkingfee")));
            logisticsfeedic.Add("new_bookingservicefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_bookingservicefee")));
            logisticsfeedic.Add("new_correctionsurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_correctionsurchargefee")));
            logisticsfeedic.Add("new_factoryloadingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_factoryloadingfee")));
            logisticsfeedic.Add("new_internationaltransportfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_internationaltransportfee")));
            logisticsfeedic.Add("new_domestictransportfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_domestictransportfee")));
            logisticsfeedic.Add("new_domesticportmiscellaneousfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_domesticportmiscellaneousfee")));
            logisticsfeedic.Add("new_exportgroundfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_exportgroundfee")));
            logisticsfeedic.Add("new_otherincidentalfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_otherincidentalfee")));
            logisticsfeedic.Add("new_goodsdeclarationfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_goodsdeclarationfee")));
            logisticsfeedic.Add("new_dangerousgoodssurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_dangerousgoodssurchargefee")));
            logisticsfeedic.Add("new_hksurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_hksurchargefee")));
            logisticsfeedic.Add("new_indonesiantaxfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_indonesiantaxfee")));
            logisticsfeedic.Add("new_unstackablesurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_unstackablesurchargefee")));
            logisticsfeedic.Add("new_landingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_landingfee")));
            logisticsfeedic.Add("new_milkrunfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_milkrunfee")));
            logisticsfeedic.Add("new_freighttruckfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_freighttruckfee")));
            logisticsfeedic.Add("new_freightfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_freightfee")));
            logisticsfeedic.Add("new_lastmilefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_lastmilefee")));
            logisticsfeedic.Add("new_indonesiasurchargefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_indonesiasurchargefee")));
            logisticsfeedic.Add("new_capitalservicefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_capitalservicefee")));
            logisticsfeedic.Add("new_indonesiastampfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_indonesiastampfee")));
            logisticsfeedic.Add("new_foreignhandlingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreignhandlingfee")));
            logisticsfeedic.Add("new_foreigntradefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigntradefee")));
            logisticsfeedic.Add("new_foreigndangerousoperatefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_foreigndangerousoperatefee")));
            logisticsfeedic.Add("new_documentationfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_documentationfee")));
            logisticsfeedic.Add("new_fuchrgfuefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_fuchrgfuefee")));
            logisticsfeedic.Add("new_essfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_essfee")));
            logisticsfeedic.Add("new_handwritinglabelfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_handwritinglabelfee")));
            logisticsfeedic.Add("new_dgcheckingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_dgcheckingfee")));
            logisticsfeedic.Add("new_dglicencefee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_dglicencefee")));
            logisticsfeedic.Add("new_racfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_racfee")));
            logisticsfeedic.Add("new_tollfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_tollfee")));
            logisticsfeedic.Add("new_dghandlingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_dghandlingfee")));
            logisticsfeedic.Add("new_remotepickupfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_remotepickupfee")));
            logisticsfeedic.Add("new_9typeelectricfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_9typeelectricfee")));
            logisticsfeedic.Add("new_lssfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_lssfee")));
            logisticsfeedic.Add("new_surchargeoverfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_surchargeoverfee")));
            logisticsfeedic.Add("new_terminalfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_terminalfee")));
            logisticsfeedic.Add("new_vgmfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_vgmfee")));
            logisticsfeedic.Add("new_unboxfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_unboxfee")));
            logisticsfeedic.Add("new_exportspecialfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_exportspecialfee")));
            logisticsfeedic.Add("new_greenplusfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_greenplusfee")));
            logisticsfeedic.Add("new_returnshippingfee", logisticsfeedetails.Sum(a => a.GetAttributeValue<decimal>("new_returnshippingfee")));
            #region 预提费（物流）
            var standard = expensestandardlist.Entities.Where(a => a.ToDefault<Guid>("new_stationservice_id").ToString() == new_srv_stationid && !a.Contains("new_stietype")).FirstOrDefault();
            decimal withholdingfeelogistics = 0m;
            if (standard != null)
                withholdingfeelogistics = standard.GetAttributeValue<decimal>("new_amount");
            logisticsfeedic.Add("new_withholdingfeelogistics", withholdingfeelogistics);
            #endregion
            logisticsfee.logisticsfeelist = logisticsfeedic;
            var logisticsfeedetail = logisticsfeedetails.FirstOrDefault();
            if (logisticsfeedetail != null)
            {
                if (logisticsfeedetail.Contains("new_logisticsfee_table_id"))
                {
                    Entity logisticsfee_table = OrganizationService.Retrieve("new_logisticsfee_table", logisticsfeedetail.GetAttributeValue<EntityReference>("new_logisticsfee_table_id").Id, new ColumnSet("new_transactioncurrency_id"));
                    if (logisticsfee_table.Contains("new_transactioncurrency_id"))
                        logisticsfee.transactioncurrency_id = logisticsfee_table.GetAttributeValue<EntityReference>("new_transactioncurrency_id").Id;
                }
            }
            return logisticsfee;
        }
        /// <summary>
        /// 仓储结算单创建sap批次数据，不分摊到sku
        /// </summary>
        /// <param name="new_srv_station"></param>
        /// <param name="expenseclaim"></param>
        /// <param name="sap_type"></param>
        /// <param name="typestr"></param>
        /// <param name="log"></param>
        public void CreateSAPLogStorageNoSKU(Entity new_srv_station, Entity expenseclaim, int sap_type, string typestr, ILogger log)
        {
            //创建sapbatch数据之前，先停用原来相同类型的批次数据
            DisabledSapbatch(sap_type, expenseclaim.Id.ToString(), OrganizationService);
            expenseclaim = OrganizationService.Retrieve(expenseclaim.LogicalName, expenseclaim.Id, new ColumnSet(true));
            string js_tax = "";
            string js_withouttax = "";
            string js_integer = "";
            JS_WithoutTax_Model jswithouttax = null;
            if (sap_type == 3)
            {
                js_tax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_Tax");
                js_withouttax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_WithoutTax");
                js_integer = CommonHelper.GetSystemParamValue(OrganizationService, "SettlementSendSapDetailIntegerProcess");
            }
            if (sap_type == 3)
            {
                if (!string.IsNullOrWhiteSpace(js_withouttax))
                {
                    List<JS_WithoutTax_Model> jswithouttaxlist = Newtonsoft.Json.JsonConvert.DeserializeObject<List<JS_WithoutTax_Model>>(js_withouttax);
                    jswithouttax = jswithouttaxlist.Where(a => a.stationcode == new_srv_station.ToDefault<string>("new_code")
                    && a.contractbody == new_srv_station.ToDefault<string>("new_contractingbody")).FirstOrDefault();
                }
            }
            string currency = "";
            if (expenseclaim.Contains("new_transactioncurrency_id"))
            {
                Entity transactioncurrency = OrganizationService.Retrieve("transactioncurrency", expenseclaim.ToDefault<Guid>("new_transactioncurrency_id"), new ColumnSet("isocurrencycode"));
                currency = transactioncurrency.GetAttributeValue<string>("isocurrencycode");
            }
            //批次业务发生时间
            DateTime businessDate = DateTime.UtcNow.AddDays(0 - DateTime.Now.Day);
            if (sap_type != 1)
            {
                businessDate = DateTime.UtcNow;
            }
            //结算年
            int year = expenseclaim.GetAttributeValue<OptionSetValue>("new_year").Value;
            //结算月
            int month = expenseclaim.GetAttributeValue<OptionSetValue>("new_month").Value;
            int expenseclaimtype = expenseclaim.GetAttributeValue<OptionSetValue>("new_businesstype").Value;
            int isreversecharge = expenseclaim.ToDefault<int>("new_isreversecharge");
            string contractingbody = expenseclaim.GetAttributeValue<string>("new_contractingbody");
            string costcenter = expenseclaim.GetAttributeValue<string>("new_costcenter");
            string profitCenter = expenseclaim.GetAttributeValue<string>("new_profitcenter");
            //内部订单，发票号
            string originalOrder = null;
            if (!string.IsNullOrWhiteSpace(costcenter))
            {
                if (expenseclaim.Contains("new_country_id"))
                    originalOrder = GetInternalOrder(expenseclaim.GetAttributeValue<EntityReference>("new_country_id").Id.ToString(), contractingbody);
            }
            string new_supplier = new_srv_station.GetAttributeValue<string>("new_supplier");
            string new_invoicenumber = expenseclaim.GetAttributeValue<string>("new_invoicenumber");
            string new_invoiceno = expenseclaim.GetAttributeValue<string>("new_invoiceno");
            decimal totalamount = 0;
            if (sap_type == 1 || sap_type == 5)
            {
                //预提费用
                totalamount = expenseclaim.GetAttributeValue<decimal>("new_withholdingmoney");
            }
            else if (sap_type == 3)
            {
                //总费用合计
                totalamount = expenseclaim.GetAttributeValue<decimal>("new_totalcost");
            }
            //服务商简称
            string name = new_srv_station?.ToDefault<string>("new_stationabbreviation");
            //国家名称
            string countryname = expenseclaim.Contains("new_country_id") ? expenseclaim.GetAttributeValue<EntityReference>("new_country_id").Name : "";
            Dictionary<string, string> infoType = new Dictionary<string, string>() { { "预提", "Accrual" }, { "反冲", "Reversal" }, { "发票", "Invoice" }, { "付款", "Payment" } };
            typestr = typestr + infoType.Where(a => a.Key == typestr).FirstOrDefault().Value;
            // 结算单类型-显示名称
            string expenseclaimtype_name = GetOptionsSetText("new_srv_expense_claim", "new_businesstype", expenseclaimtype);
            string new_remark_main = string.Empty;
            string new_remark_detail = string.Empty;
            new_remark_main = typestr + year + month + expenseclaimtype_name;
            new_remark_detail = typestr + year + month + expenseclaimtype_name + countryname + name;
            new_remark_main = new_remark_main.Length > 25 ? new_remark_main[..25] : new_remark_main;
            new_remark_detail = new_remark_detail.Length > 50 ? new_remark_detail[..50] : new_remark_detail;

            EntityCollection sku1collection = GetSKU1Collection(OrganizationService);
            //EntityCollection sku2collection = GetSKU2Collection(OrganizationService);
            // 记录涉及金额明细计算的sap_detail
            HashSet<Guid> originalSapDetail = new HashSet<Guid>();
            decimal summoney = 0;//明细总额
                                 //创建sap 批次主档
            Entity sapBatch = new Entity("new_sap_batch");
            sapBatch.Id = Guid.NewGuid();
            sapBatch["new_businessdate"] = businessDate;
            //单据类型
            sapBatch["new_infotype"] = new OptionSetValue(sap_type);
            sapBatch["new_expensetno"] = new EntityReference("new_srv_expense_claim", expenseclaim.Id);
            sapBatch["new_maincount"] = 1;
            //该批次详情表数据量
            sapBatch["new_detailcount"] = 1;
            //批次明细的总金额
            sapBatch["new_receivablessum"] = totalamount;
            OrganizationService.Create(sapBatch);
            summoney = totalamount;
            //创建sap主表
            Entity sapMain = new Entity("new_sap_main");
            sapMain.Id = Guid.NewGuid();
            //批次业务类型
            if (sap_type == 5)
                sapMain["new_businesstype"] = "HWSHR";
            else
                sapMain["new_businesstype"] = "HWSH";
            //批次结构的批次业务发生时间
            sapMain["new_businessdate"] = businessDate;
            //公司代码(签约主体)
            sapMain["new_company"] = contractingbody;
            //成本中心
            sapMain["new_department"] = costcenter;
            //sapBatch
            sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
            //内部订单，发票号
            sapMain["new_originalorder"] = originalOrder;
            //备注
            sapMain["new_remark"] = new_remark_main;
            OrganizationService.Create(sapMain);
            //创建sap detail明细
            //SKU1增加结算单类型匹配，优先级：1、签约主体+类型+结算单类型，2、签约主体+类型
            string sku1 = "";
            string sku2 = "";
            var skuconfiglist = sku1collection.Entities.Where(a => a.ToDefault<string>("new_contractingbody") == contractingbody
                   && a.ToDefault<int>("new_infotype") == sap_type
                   /*&& a.ToDefault<int>("new_isreversecharge") == isreversecharge*/).ToList();
            if (sap_type == 3)
                skuconfiglist = skuconfiglist.Where(a => a.ToDefault<int>("new_isreversecharge") == isreversecharge).ToList();
            var config_sku1list = skuconfiglist.Where(a => a.ToDefault<int>("new_businesstype") == expenseclaimtype).ToList();
            if (config_sku1list.Count == 0)
                config_sku1list = skuconfiglist.Where(a => !a.Contains("new_businesstype")).ToList();
            var skuconfig = new Entity();
            if (config_sku1list.Count > 0)
                skuconfig = config_sku1list.First();
            sku1 = skuconfig.ToDefault<string>("new_name");//sku1 = 虚拟物料
            sku2 = skuconfig.ToDefault<string>("new_acctsubjects");//sku2 = 会计科目
            Entity sap_detail = new Entity("new_sap_detail");
            sap_detail["new_sku1"] = sku1;
            sap_detail["new_sku2"] = sku2;
            sap_detail["new_currency"] = currency;
            sap_detail["new_remark"] = new_remark_detail;
            sap_detail["new_refer1"] = new_supplier;
            sap_detail["new_refer2"] = new_invoicenumber;
            sap_detail["new_refer3"] = new_invoiceno;
            sap_detail["new_rowid"] = 1;
            sap_detail["new_profitcenter"] = profitCenter;
            //内部订单，发票号
            sap_detail["new_originalorder"] = originalOrder;
            //发票
            if (jswithouttax != null)
            {
                sap_detail["new_sku1"] = jswithouttax.sku1;
                sap_detail["new_receivables"] = Math.Round(totalamount / Convert.ToDecimal(jswithouttax.tax), 2).ToString();
                summoney = Math.Round(totalamount / Convert.ToDecimal(jswithouttax.tax), 2);
            }
            else
            {
                sap_detail["new_receivables"] = totalamount.ToString();
            }
            sap_detail["new_sap_mainid"] = new EntityReference("new_sap_main", sapMain.Id);
            Guid sapdetailid = OrganizationService.Create(sap_detail);
            int detailCount = 1;//上面创建了一条明细
            int mainCount = 1;//上面创建了一条sap_main
            originalSapDetail.Add(sapdetailid);
            var otherskuconfig = config_sku1list?.Where(a => a.Id != skuconfig.Id).ToList();//维护了多条sku
            foreach (var othersku in otherskuconfig)
            {
                var sapDetail2 = new Entity("new_sap_detail");
                for (int i = 0; i < sap_detail.Attributes.Count; i++)
                {
                    var butesName = sap_detail.Attributes.Keys.ToList();
                    sapDetail2[butesName[i]] = sap_detail[butesName[i]];
                }
                sapDetail2["new_sku1"] = othersku?.ToDefault<string>("new_name");
                sapDetail2["new_sku2"] = othersku?.ToDefault<string>("new_acctsubjects");
                OrganizationService.Create(sapDetail2);
                detailCount++;
            }
            #region 发票环节扣除税费
            if (jswithouttax != null)
            {
                var totalcost_withouttax = Math.Round(totalamount / Convert.ToDecimal(jswithouttax.tax), 2);
                if (totalcost_withouttax != summoney)
                {
                    //对比发票数据表头的金额是否和明细数据总额一致，如果不一致，则将表头金额-明细总额的差额，加到第一个行明细的金额上
                    Guid firstGuid = originalSapDetail.FirstOrDefault();
                    if (firstGuid != Guid.Empty)
                    {
                        Entity entity = new Entity("new_sap_detail");
                        entity.Id = firstGuid;
                        decimal receivables = totalamount;
                        entity["new_receivables"] = (receivables + (totalcost_withouttax - summoney)).ToString();
                        OrganizationService.Update(entity);
                        summoney = totalcost_withouttax;
                    }
                }
            }
            #endregion
            #region 发票环节推送增值税&代扣税
            List<Entity> js_taxentities = new List<Entity>();
            if (!string.IsNullOrWhiteSpace(js_tax))
            {
                if (IsJson(js_tax))
                {
                    List<Js_Tax_Model> jstaxlist = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Js_Tax_Model>>(js_tax);
                    var taxlist = jstaxlist.Where(a => a.contractbody == contractingbody).ToList();
                    Func<Js_Tax_Model, Guid, int, Entity> createtaxdetail = (item, mainid, rowid) =>
                    {
                        Entity tax_detail = new Entity("new_sap_detail");
                        tax_detail["new_sku1"] = item.sku1;
                        tax_detail["new_sku2"] = item.sku2;
                        tax_detail["new_currency"] = currency;
                        tax_detail["new_remark"] = new_remark_detail;
                        tax_detail["new_refer1"] = new_supplier;
                        tax_detail["new_refer2"] = new_invoicenumber;
                        tax_detail["new_refer3"] = new_invoiceno;
                        tax_detail["new_rowid"] = rowid;
                        tax_detail["new_profitcenter"] = profitCenter;
                        //内部订单，发票号
                        tax_detail["new_originalorder"] = originalOrder;
                        tax_detail["new_receivables"] = ((int)Math.Round(totalamount * Convert.ToDecimal(item.tax), MidpointRounding.AwayFromZero)).ToString();
                        tax_detail["new_sap_mainid"] = new EntityReference("new_sap_main", mainid);
                        return tax_detail;
                    };
                    Func<Entity> createsapmain = () =>
                    {
                        //创建sap主表
                        Entity sapMain = new Entity("new_sap_main");
                        sapMain.Id = Guid.NewGuid();
                        //批次业务类型
                        if (sap_type == 5)
                            sapMain["new_businesstype"] = "HWSHR";
                        else
                            sapMain["new_businesstype"] = "HWSH";
                        //批次结构的批次业务发生时间
                        sapMain["new_businessdate"] = businessDate;
                        //公司代码(签约主体)
                        sapMain["new_company"] = contractingbody;
                        //成本中心
                        sapMain["new_department"] = costcenter;
                        //sapBatch
                        sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
                        //内部订单，发票号
                        sapMain["new_originalorder"] = originalOrder;
                        //备注
                        sapMain["new_remark"] = new_remark_main;
                        return sapMain;
                    };
                    if (taxlist.Count > 0)
                    {
                        var sapmain = createsapmain();
                        js_taxentities.Add(sapmain);
                        foreach (var item in taxlist)
                        {
                            int index = taxlist.IndexOf(item) + 1;
                            js_taxentities.Add(createtaxdetail(item, sapmain.Id, detailCount + index));
                        }
                    }
                }
            }
            if (js_taxentities.Count > 0)
            {
                var mainen = js_taxentities.Where(a => a.LogicalName == "new_sap_main").FirstOrDefault();
                if (mainen != null)
                {
                    //创建sap主表
                    Entity taxsapMain = new Entity("new_sap_main");
                    taxsapMain.Id = Guid.NewGuid();
                    //批次业务类型
                    if (sap_type == 5)
                        taxsapMain["new_businesstype"] = "HWSHR";
                    else
                        taxsapMain["new_businesstype"] = "HWSH";
                    //批次结构的批次业务发生时间
                    taxsapMain["new_businessdate"] = businessDate;
                    //公司代码(签约主体)
                    taxsapMain["new_company"] = contractingbody;
                    //成本中心
                    taxsapMain["new_department"] = costcenter;
                    //sapBatch
                    taxsapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
                    //内部订单，发票号
                    taxsapMain["new_originalorder"] = originalOrder;
                    //备注
                    taxsapMain["new_remark"] = new_remark_main;
                    Guid taxsapMainid = OrganizationService.Create(taxsapMain);
                    mainCount += 1;
                    var detailens = js_taxentities.Where(a => a.LogicalName == "new_sap_detail").ToList();
                    foreach (var item in detailens)
                    {
                        Entity tax_detail = new Entity("new_sap_detail");
                        tax_detail["new_sku1"] = item.GetAttributeValue<string>("new_sku1");
                        tax_detail["new_sku2"] = item.GetAttributeValue<string>("new_sku2");
                        tax_detail["new_currency"] = item.GetAttributeValue<string>("new_currency");
                        tax_detail["new_remark"] = item.GetAttributeValue<string>("new_remark");
                        tax_detail["new_refer1"] = item.GetAttributeValue<string>("new_refer1");
                        tax_detail["new_refer2"] = item.GetAttributeValue<string>("new_refer2");
                        tax_detail["new_refer3"] = item.GetAttributeValue<string>("new_refer3");
                        tax_detail["new_rowid"] = item.GetAttributeValue<int>("new_rowid");
                        tax_detail["new_profitcenter"] = profitCenter;
                        //内部订单，发票号
                        tax_detail["new_originalorder"] = originalOrder;
                        tax_detail["new_receivables"] = item.GetAttributeValue<string>("new_receivables");
                        tax_detail["new_sap_mainid"] = new EntityReference("new_sap_main", taxsapMainid);
                        OrganizationService.Create(tax_detail);
                        detailCount += 1;
                    }
                }
            }
            #endregion
            #region 判断SAP账单是否需要整数处理
            if (!string.IsNullOrWhiteSpace(js_integer) && IsJson(js_integer))
            {
                List<string> IntegerProcess = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(js_integer);
                if (IntegerProcess.Contains(contractingbody))
                {
                    var firstdetailid = originalSapDetail.FirstOrDefault();
                    if (firstdetailid != null)
                    {
                        Entity entity = new Entity("new_sap_detail");
                        entity.Id = firstdetailid;
                        decimal receivables = Convert.ToDecimal(entity["new_receivables"]);
                        receivables = Math.Round(receivables, 0, MidpointRounding.AwayFromZero);
                        entity["new_receivables"] = receivables.ToString();
                        OrganizationService.Update(entity);
                        summoney = receivables;
                    }
                }
            }
            #endregion
            //更新sap批次数据
            Entity sappatch = new Entity(sapBatch.LogicalName, sapBatch.Id);
            sapBatch["new_receivablessum"] = summoney;
            sapBatch["new_detailcount"] = detailCount;
            sapBatch["new_maincount"] = mainCount;
            OrganizationService.Update(sappatch);
        }
        /// <summary>
        /// 查询sku1配置表
        /// </summary>
        /// <param name="serviceAdmin"></param>
        /// <returns></returns>
        public EntityCollection GetSKU1Collection(IOrganizationService serviceAdmin)
        {
            return serviceAdmin.RetrieveMultiple(
                new QueryExpression("new_config_sku1")
                {
                    ColumnSet = new ColumnSet("new_contractingbody", "new_name", "new_infotype", "new_businesstype", "new_isreversecharge", "new_acctsubjects"),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
        }
        /// <summary>
        /// 查询sku2配置表
        /// </summary>
        /// <param name="serviceAdmin"></param>
        /// <returns></returns>
        public EntityCollection GetSKU2Collection(IOrganizationService serviceAdmin)
        {
            return serviceAdmin.RetrieveMultiple(
                new QueryExpression("new_config_sku2")
                {
                    ColumnSet = new ColumnSet("new_contractingbody", "new_name", "new_sku1", "new_infotype"),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
        }
        /// <summary>
        /// 判断字符串是否是JSON格式
        /// </summary>
        /// <param name="jsonString"></param>
        /// <returns></returns>
        public static bool IsJson(string jsonString)
        {
            return jsonString.Trim().StartsWith("{") && jsonString.Trim().EndsWith("}")
                || jsonString.Trim().StartsWith("[") && jsonString.Trim().EndsWith("]");
        }

        /// <summary>
        /// 取选项集
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        string GetOptionsSetText(string entityName, string attributeName, int selectedValue)
        {

            RetrieveAttributeRequest retrieveAttributeRequest = new RetrieveAttributeRequest
            {
                EntityLogicalName = entityName,
                LogicalName = attributeName,
                RetrieveAsIfPublished = true
            };

            RetrieveAttributeResponse retrieveAttributeResponse = (RetrieveAttributeResponse)OrganizationService.Execute(retrieveAttributeRequest);

            Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata retrievedPicklistAttributeMetadata = (Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata)

            retrieveAttributeResponse.AttributeMetadata;
            OptionMetadata[] optionList = retrievedPicklistAttributeMetadata.OptionSet.Options.ToArray();
            string selectedOptionLabel = string.Empty;
            foreach (OptionMetadata oMD in optionList)
            {
                if (oMD.Value == selectedValue)
                {
                    foreach (var LocalizedLabel in oMD.Label.LocalizedLabels)
                    { // 优先使用中文标签
                        if (LocalizedLabel.LanguageCode == 2052)
                            selectedOptionLabel = LocalizedLabel.Label;
                    }
                    if (selectedOptionLabel == string.Empty)
                    { // 否则使用用户默认语言标签
                        selectedOptionLabel = oMD.Label.UserLocalizedLabel.Label;
                    }
                    break;
                }
            }
            return selectedOptionLabel;
        }
        public static new_srv_workorderMaitrox WorkOrderToModelsMaitrox(Entity item)
        {
            try
            {
                //工单信息
                new_srv_workorderMaitrox order = new new_srv_workorderMaitrox
                {
                    new_srv_workorderid = item.Id,
                    //维修网点
                    new_repairstation_id = item.ToDefault<Guid>("new_repairstation_id"),
                };
                order.new_type = item.ToDefault<int>("new_type");
                //所属网点id
                order.new_srv_stationid = item.ToDefault<Guid>("new_station_id");
                order.new_station_idmaitrox = order.new_repairstation_id != Guid.Empty ? order.new_repairstation_id : order.new_srv_stationid;
                order.new_category1_id = item.ToDefault<Guid>("new_category1_id").ToString();
                order.new_category2_id = item.ToDefault<Guid>("new_category2_id").ToString();
                order.new_category3_id = item.ToDefault<Guid>("new_category3_id").ToString();
                if (item.Contains("az.new_country_id"))
                {
                    order.new_country_id = item.GetAliasAttributeValue<EntityReference>("az.new_country_id").Id.ToString();
                }
                // 服务单币种
                order.new_transactioncurrency_maitroxservice_id = item.ToDefault<Guid>("new_transactioncurrency_maitroxservice_id");
                //备件费
                order.new_sparepartscost = item.ToDefault<decimal>("new_sparepartscost");
                //备件服务费
                order.new_partservicecost = item.ToDefault<decimal>("new_partservicecost");
                if (item.Contains("new_expenseclaimidmaitrox"))
                {
                    order.new_expenseclaimid_old = item.GetAttributeValue<EntityReference>("new_expenseclaimidmaitrox").Id;
                }
                else
                {
                    order.new_expenseclaimid_old = Guid.Empty;
                }

                return order;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        /// <summary>
        /// 迈创结算单根据分摊权重结算标准获取权重配置表
        /// </summary>
        /// <param name="type"></param>
        /// <param name="expensestandard"></param>
        /// <param name="new_country_id"></param>
        /// <param name="category_r3List"></param>
        /// <returns></returns>
        public static List<CostWeightConfig> GetCostWeight(int type, decimal fee, EntityCollection expensestandard, Guid new_country_id, Dictionary<string, List<new_srv_workorderMaitrox>> category_r3List) 
        {
            List<CostWeightConfig> weightconfiglist = new List<CostWeightConfig>();
            foreach (var item in category_r3List)
            {
                CostWeightConfig weightconfig = new CostWeightConfig();
                weightconfig.new_category3_id = item.Key;
                weightconfig.workordercount = item.Value.Count;
                Entity weightstandard = null;
                //获取工单上的一级品类id
                var new_category1_id = !string.IsNullOrWhiteSpace(item.Value.FirstOrDefault().new_category1_id) ? item.Value.FirstOrDefault().new_category1_id : Guid.Empty.ToString();
                //根据三级品类，一级品类，国家，可选费用匹配
                weightstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && a.Contains("new_category3id") && a.GetAttributeValue<string>("new_category3id").Contains(item.Key)
                && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                && a.ToDefault<Guid>("new_country_id") == new_country_id
                && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))).FirstOrDefault();
                if (weightstandard == null)
                    weightstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && a.Contains("new_category3id") && a.GetAttributeValue<string>("new_category3id").Contains(item.Key)
                    && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                    && !a.Contains("new_country_id")
                    && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))).FirstOrDefault();
                if (weightstandard != null)
                {
                    if (weightstandard.Contains("new_optionalcharges"))
                    {
                        OptionSetValueCollection options = weightstandard.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges");
                        // 处理多选选项集字段值
                        var option = options.Where(a => a.Value == type).FirstOrDefault();
                        if (option != null)
                        {
                            decimal weight = weightstandard.ToDefault<decimal>("new_costweight") / 100;
                            weightconfig.weight = weight;
                            weightconfig.result = Math.Round(weight * weightconfig.workordercount, 2);
                        }
                    }
                }
                else
                {
                    //根据工单上的三级品类匹配不到结算标准，则将一级品类结算标准的权重 - SUM(三级品类结算标准权重)
                    weightstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && !a.Contains("new_category3id")
                        && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                        && a.ToDefault<Guid>("new_country_id") == new_country_id
                        && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))).FirstOrDefault();
                    if (weightstandard == null)
                        weightstandard = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && !a.Contains("new_category3id")
                        && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                        && !a.Contains("new_country_id")
                        && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))).FirstOrDefault();
                    if (weightstandard != null)
                    {
                        if (weightstandard.Contains("new_country_id"))
                        {
                            decimal weight = weightstandard.ToDefault<decimal>("new_costweight") / 100;
                            var sumweight = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && a.Contains("new_category3id")
                                && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                                && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))
                                && a.ToDefault<Guid>("new_country_id") == new_country_id).Sum(a => a.GetAttributeValue<decimal>("new_costweight")) / 100;
                            weightconfig.weight = weight - sumweight;
                            weightconfig.result = Math.Round((weight - sumweight) * weightconfig.workordercount, 2);
                        }
                        else 
                        {
                            decimal weight = weightstandard.ToDefault<decimal>("new_costweight") / 100;
                            var sumweight = expensestandard.Entities.Where(a => a.ToDefault<int>("new_feetype") == 28 && a.Contains("new_category3id")
                                && a.ToDefault<Guid>("new_category1_id").ToString() == new_category1_id.ToString()
                                && a.Contains("new_optionalcharges") && a.GetAttributeValue<OptionSetValueCollection>("new_optionalcharges").Contains(new OptionSetValue(type))
                                && !a.Contains("new_country_id")).Sum(a => a.GetAttributeValue<decimal>("new_costweight")) / 100;
                            weightconfig.weight = weight - sumweight;
                            weightconfig.result = Math.Round((weight - sumweight) * weightconfig.workordercount, 2);
                        }
                    }
                }
                weightconfiglist.Add(weightconfig);
            }
            decimal sumresult = weightconfiglist.Sum(a => a.result);
            //工单量乘以权重的结果的总和如果等于0，则无需按照权重分摊
            if (sumresult != 0)
            {
                foreach (var item in weightconfiglist)
                {
                    item.apportionfee = Math.Round((fee * item.result / sumresult), 2);
                }
                decimal sumapportionfee = weightconfiglist.Sum(a => a.apportionfee);
                var maxapportionfee = weightconfiglist.OrderByDescending(a => a.apportionfee).FirstOrDefault();
                //将尾差加到最大的那个元素上
                if (fee != sumapportionfee)
                {
                    maxapportionfee.apportionfee = maxapportionfee.apportionfee + (fee - sumapportionfee);
                }
            }
            return weightconfiglist;
        }
        /// <summary>
        /// 仓储结费创建sap 批次数据
        /// </summary>
        /// <param name="workordersettlecols"></param>
        /// <param name="station"></param>
        /// <param name="expense_Claim"></param>
        public void CreateSAPLogCostCenter(Entity station, Entity expense_Claim, int sap_type, string typestr, ILogger log)
        {
            //创建sapbatch数据之前，先停用原来相同类型的批次数据
            DisabledSapbatch(sap_type, expense_Claim.Id.ToString(), OrganizationService);
            string js_tax = "";
            string js_withouttax = "";
            string js_integer = "";
            JS_WithoutTax_Model jswithouttax = null;
            if (sap_type == 3)
            {
                js_tax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_Tax");
                js_withouttax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_WithoutTax");
                js_integer = CommonHelper.GetSystemParamValue(OrganizationService, "SettlementSendSapDetailIntegerProcess");
            }
            string contractingbody = expense_Claim.GetAttributeValue<string>("new_contractingbody");
            var new_srv_station_id = expense_Claim.GetAttributeValue<EntityReference>("new_srv_station_id");
            var new_srv_station = OrganizationService.Retrieve(new_srv_station_id.LogicalName, new_srv_station_id.Id, new ColumnSet("new_code", "new_contractingbody", "new_providertype"));
            int providertype = new_srv_station.ToDefault<int>("new_providertype");
            if (sap_type == 3)
            {
                if (!string.IsNullOrWhiteSpace(js_withouttax))
                {
                    List<JS_WithoutTax_Model> jswithouttaxlist = JsonConvert.DeserializeObject<List<JS_WithoutTax_Model>>(js_withouttax);
                    jswithouttax = jswithouttaxlist.Where(a => a.stationcode == new_srv_station.ToDefault<string>("new_code")
                    && a.contractbody == new_srv_station.ToDefault<string>("new_contractingbody")).FirstOrDefault();
                }
            }
            //批次业务类型
            string businesstype = "";
            if (sap_type == 5)
                businesstype = "HWSHR";
            else
                businesstype = "HWSH";
            decimal summoney = 0m;
            int detailCount = 0;
            decimal new_totalcost = expense_Claim.ToDefault<decimal>("new_totalcost");//结算单上total cost
            EntityCollection sku1collection = GetSKU1Collection(OrganizationService);
            //根据签约主体查询成本中心配置表
            QueryExpression qe = new QueryExpression("new_costcenterconfigure");
            qe.ColumnSet = new ColumnSet("new_costcenter", "new_contractingbody", "new_country_id", "new_code");
            qe.Criteria.AddCondition("new_contractingbody", ConditionOperator.Equal, contractingbody);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var costcentercols = OrganizationService.RetrieveMultiple(qe);
            //根据签约主体查询内部订单配置表
            QueryExpression qe1 = new QueryExpression("new_internalorderconfigure");
            qe1.ColumnSet = new ColumnSet("new_internalorder", "new_name", "new_internalorderconfigureid", "new_contractingbody", "new_country_id");
            qe1.Criteria.AddCondition("new_contractingbody", ConditionOperator.Equal, contractingbody);
            qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var internalordercols = OrganizationService.RetrieveMultiple(qe1);
            List<Highdimensionalsapmodel> highdimensionaldetaillist = new List<Highdimensionalsapmodel>();
            log.LogInformation("highdimensionaldetaillist集合开始整理" + DateTime.Now);
            EntityCollection trimmordercols = GetTrimmorderdetail(expense_Claim.Id.ToString());
            //根据服务商查询高维工厂劳务费分摊范围
            QueryExpression qeft = new QueryExpression("new_allocationscopetable");
            qeft.ColumnSet = new ColumnSet("new_srv_station_id", "new_country_id", "new_region_id", "new_category1_id", "new_category2_id", "new_category3_id");
            qeft.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qeft.Criteria.AddCondition("new_srv_station_id", ConditionOperator.Equal, new_srv_station_id.Id);
            LinkEntity link1 = new LinkEntity("new_allocationscopetable", "new_category1", "new_category1_id", "new_category1id", JoinOperator.LeftOuter);
            link1.Columns = new ColumnSet("new_code");
            link1.EntityAlias = "link1";
            LinkEntity link2 = new LinkEntity("new_allocationscopetable", "new_category2", "new_category2_id", "new_category2id", JoinOperator.LeftOuter);
            link2.Columns = new ColumnSet("new_code");
            link2.EntityAlias = "link2";
            LinkEntity link3 = new LinkEntity("new_allocationscopetable", "new_category3", "new_category3_id", "new_category3id", JoinOperator.LeftOuter);
            link3.Columns = new ColumnSet("new_code");
            link3.EntityAlias = "link3";
            qeft.LinkEntities.Add(link1);
            qeft.LinkEntities.Add(link2);
            qeft.LinkEntities.Add(link3);
            var ftscopecols = OrganizationService.RetrieveMultiple(qeft);
            if (ftscopecols.Entities.Count > 0) 
            {
                List<Guid> countryscope = new List<Guid>();
                List<Guid> regionscope = new List<Guid>();
                foreach (var item in ftscopecols.Entities)
                {
                    if (item.Contains("new_region_id"))
                        regionscope.Add(item.GetAttributeValue<EntityReference>("new_region_id").Id);
                    if (item.Contains("new_country_id"))
                        countryscope.Add(item.GetAttributeValue<EntityReference>("new_country_id").Id);
                }
                var workorderlist = GetWorkorderSettlement(countryscope, regionscope);
                //工单范围按照国家分组
                var workordergroup = workorderlist.Entities.Where(a => a.Contains("new_country_id")).GroupBy(b => b.GetAttributeValue<EntityReference>("new_country_id").Id);
                //分摊国家集合id
                var countrylist = workordergroup.Select(a => a.Key).ToList();
                //国家，（品类）工单数量集合
                Dictionary<string, int> countryworkordercount = new Dictionary<string, int>();
                //查询国家实体
                QueryExpression qecountry = new QueryExpression("new_country");
                qecountry.ColumnSet = new ColumnSet("new_region_idstorage");
                qecountry.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qecountry.Criteria.AddCondition(new ConditionExpression("new_countryid", ConditionOperator.In, countrylist));
                var countrycols = OrganizationService.RetrieveMultiple(qecountry);
                foreach (var country in countrycols.Entities)
                {
                    var workordercount = 0;
                    var _countryscope = ftscopecols.Entities.Where(a => a.ToDefault<Guid>("new_country_id") == country.Id).ToList();
                    if (_countryscope.Count > 0)
                    {
                        //一级品类集合
                        List<string> category1codelist = _countryscope.Select(a => a.GetAliasAttributeValue<string>("link1.new_code")).ToList();
                        //二级品类集合
                        List<string> category2codelist = _countryscope.Select(a => a.GetAliasAttributeValue<string>("link2.new_code")).ToList();
                        //三级品类集合
                        List<string> category3codelist = _countryscope.Select(a => a.GetAliasAttributeValue<string>("link3.new_code")).ToList();
                        //var workordercount = 0;
                        if (category1codelist.Count > 0 || category2codelist.Count > 0 || category3codelist.Count > 0) 
                        {
                            workordercount = workorderlist.Entities.Where(a => a.ToDefault<Guid>("new_country_id") == country.Id && (category1codelist.Contains(a.GetAttributeValue<string>("new_category1_code"))
                            || category2codelist.Contains(a.GetAttributeValue<string>("new_category2_code"))
                            || category3codelist.Contains(a.GetAttributeValue<string>("new_category3_code")))).Count();
                        }
                    }
                    else 
                    {
                        var _regionscope = ftscopecols.Entities.Where(a => a.ToDefault<Guid>("new_region_id") == country.ToDefault<Guid>("new_region_idstorage")).ToList();
                        //一级品类集合
                        List<string> category1codelist = _regionscope.Select(a => a.GetAliasAttributeValue<string>("link1.new_code")).ToList();
                        //二级品类集合
                        List<string> category2codelist = _regionscope.Select(a => a.GetAliasAttributeValue<string>("link2.new_code")).ToList();
                        //三级品类集合
                        List<string> category3codelist = _regionscope.Select(a => a.GetAliasAttributeValue<string>("link3.new_code")).ToList();
                        //var workordercount = 0;
                        if (category1codelist.Count > 0 || category2codelist.Count > 0 || category3codelist.Count > 0) 
                        {
                            workordercount = workorderlist.Entities.Where(a => a.ToDefault<Guid>("new_country_id") == country.Id && (category1codelist.Contains(a.GetAttributeValue<string>("new_category1_code"))
                            || category2codelist.Contains(a.GetAttributeValue<string>("new_category2_code"))
                            || category3codelist.Contains(a.GetAttributeValue<string>("new_category3_code")))).Count();
                        }
                    }
                    countryworkordercount.Add(country.Id.ToString(), workordercount);
                }
                //国家工单总量
                decimal workordertotalcount = countryworkordercount.Values.Sum();
                decimal sumratio = 0m;//工单量占比汇总
                List<CountryAllocationratio> countryAllocationratioslist = new List<CountryAllocationratio>();
                if (workordertotalcount != 0) 
                {
                    foreach (var key in countryworkordercount.Keys)
                    {
                        int index = countryworkordercount.Keys.ToList().IndexOf(key);
                        CountryAllocationratio allocationratio = new CountryAllocationratio();
                        allocationratio.countryid = key;
                        allocationratio.workordercount = countryworkordercount[key];
                        if (index == countryworkordercount.Keys.Count - 1)
                        {
                            decimal lastratio = 1 - sumratio;
                            allocationratio.ratio = lastratio;
                        }
                        else
                        {
                            var radio = Math.Round(allocationratio.workordercount / workordertotalcount, 2);
                            allocationratio.ratio = radio;
                            sumratio += radio;
                        }
                        countryAllocationratioslist.Add(allocationratio);
                    }
                }
                //明细金额根据国家占比分摊
                foreach (var grouptrimorder in trimmordercols.Entities)
                {
                    decimal withholdingmoney = grouptrimorder.GetAttributeValue<decimal>("new_withholdingmoney2");
                    decimal settlementmoney = grouptrimorder.GetAttributeValue<decimal>("new_settlementmoney2");
                    decimal sumwithholdingmoney = 0m;
                    decimal sumsettlementmoney = 0m;
                    foreach (var proportion in countryAllocationratioslist)
                    {
                        decimal allocatedamount_yt = 0m;//分摊的金额-预提
                        decimal allocatedamount_total = 0m;//分摊的金额-结算
                        int index = countryAllocationratioslist.IndexOf(proportion);
                        if (index == countryAllocationratioslist.Count -1) 
                        {
                            decimal lastytfee = withholdingmoney - sumwithholdingmoney;
                            decimal lasttotalfee = settlementmoney - sumsettlementmoney;
                            allocatedamount_yt = lastytfee;
                            allocatedamount_total = lasttotalfee;
                        }
                        else 
                        {
                            allocatedamount_yt = Math.Round(grouptrimorder.GetAttributeValue<decimal>("new_withholdingmoney2") * proportion.ratio, 2);
                            allocatedamount_total = Math.Round(grouptrimorder.GetAttributeValue<decimal>("new_settlementmoney2") * proportion.ratio, 2);
                            sumwithholdingmoney += allocatedamount_yt;
                            sumsettlementmoney += allocatedamount_total;
                        }
                        Highdimensionalsapmodel workordersettle = new Highdimensionalsapmodel();
                        workordersettle.new_sku = grouptrimorder.GetAliasAttributeValue<string>("sku");
                        var contcenterconfig = costcentercols.Entities.Where(a => a.ToDefault<Guid>("new_country_id").ToString() == proportion.countryid).FirstOrDefault();
                        workordersettle.new_costcenter = contcenterconfig != null ? contcenterconfig.ToDefault<string>("new_costcenter") : "";
                        var internalorderconfig = internalordercols.Entities.Where(a => a.ToDefault<Guid>("new_country_id").ToString() == proportion.countryid).FirstOrDefault();
                        workordersettle.new_internalorder = internalorderconfig != null ? internalorderconfig.ToDefault<string>("new_internalorder") : "";
                        workordersettle.new_workordercount = grouptrimorder.GetAttributeValue<int>("workordercount2");
                        workordersettle.new_withholdingmoney = allocatedamount_yt;
                        workordersettle.new_settlementmoney = allocatedamount_total;
                        highdimensionaldetaillist.Add(workordersettle);
                    }
                }
                log.LogInformation("highdimensionaldetaillist集合整理完成" + DateTime.Now);
                //批次业务发生时间
                DateTime businessDate = DateTime.UtcNow.AddDays(0 - DateTime.Now.Day);
                if (sap_type != 1)
                {
                    if (sap_type == 4)
                    {
                        //付款环节 业务日期取 付款申请接口回传的付款日期
                        businessDate = expense_Claim.Contains("new_paymentdate") ? expense_Claim.ToDefault<DateTime>("new_paymentdate") : DateTime.UtcNow;
                    }
                    else
                    {
                        businessDate = DateTime.UtcNow;
                    }
                }
                //币种
                string currency = "";
                if (sap_type == 4)
                {
                    currency = expense_Claim.ToDefault<string>("new_sapcurrency");//sap回传币种
                }
                else
                {
                    if (expense_Claim.Contains("new_transactioncurrency_id"))
                    {
                        Entity transactioncurrency = OrganizationService.Retrieve("transactioncurrency", expense_Claim.ToDefault<Guid>("new_transactioncurrency_id"), new ColumnSet("isocurrencycode"));
                        currency = transactioncurrency.GetAttributeValue<string>("isocurrencycode");
                    }
                }
                //结算单类型
                int expenseclaimtype = expense_Claim.ToDefault<int>("new_businesstype");
                //结算年
                int year = expense_Claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月
                int month = expense_Claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //string contractingbody = expense_Claim.GetAttributeValue<string>("new_contractingbody");
                string costcenter = expense_Claim.GetAttributeValue<string>("new_costcenter");
                string profitCenter = expense_Claim.GetAttributeValue<string>("new_profitcenter");
                int isreversecharge = expense_Claim.ToDefault<int>("new_isreversecharge");//是否reverse charge
                                                                                          //服务商简称
                string name = station?.ToDefault<string>("new_stationabbreviation");
                //国家名称
                string countryname = expense_Claim.Contains("new_country_id") ? expense_Claim.GetAttributeValue<EntityReference>("new_country_id").Name : "";
                //网点类型
                string new_stietypetext = expense_Claim.Contains("new_stietype") ? expense_Claim.FormattedValues["new_stietype"] : "";
                Dictionary<string, string> infoType = new Dictionary<string, string>() { { "预提", "Accrual" }, { "反冲", "Reversal" }, { "发票", "Invoice" }, { "付款", "Payment" } };
                typestr = typestr + infoType.Where(a => a.Key == typestr).FirstOrDefault().Value;
                // 结算单类型-显示名称
                string expenseclaimtype_name = GetOptionsSetText("new_srv_expense_claim", "new_businesstype", expenseclaimtype);
                string new_remark_main = string.Empty;
                string new_remark_detail = string.Empty;
                new_remark_main = typestr + year + month + expenseclaimtype_name;
                new_remark_detail = typestr + year + month + expenseclaimtype_name + countryname + name;
                new_remark_main = new_remark_main.Length > 25 ? new_remark_main[..25] : new_remark_main;
                new_remark_detail = new_remark_detail.Length > 50 ? new_remark_detail[..50] : new_remark_detail;
                //内部订单，发票号
                string originalOrder = null;
                if (!string.IsNullOrWhiteSpace(costcenter))
                {
                    if (expense_Claim.Contains("new_country_id"))
                        originalOrder = GetInternalOrder(expense_Claim.GetAttributeValue<EntityReference>("new_country_id").Id.ToString(), contractingbody);
                }
                string new_supplier = station.GetAttributeValue<string>("new_supplier");
                string new_invoicenumber = expense_Claim.GetAttributeValue<string>("new_invoicenumber");
                string new_invoiceno = expense_Claim.GetAttributeValue<string>("new_invoiceno");
                //创建sap 批次主档
                Entity sapBatch = new Entity("new_sap_batch");
                sapBatch.Id = Guid.NewGuid();
                //批次结构的批次业务发生时间  
                sapBatch["new_businessdate"] = businessDate;
                //单据类型
                sapBatch["new_infotype"] = new OptionSetValue(sap_type);
                sapBatch["new_expensetno"] = new EntityReference("new_srv_expense_claim", expense_Claim.Id);
                sapBatch["new_maincount"] = highdimensionaldetaillist.Count / 200;
                //该批次详情表数据量
                sapBatch["new_detailcount"] = highdimensionaldetaillist.Count;
                //批次明细的总金额
                if (sap_type == 1 || sap_type == 5)
                {
                    sapBatch["new_receivablessum"] = highdimensionaldetaillist.Sum(a => a.new_withholdingmoney);
                }
                else
                {
                    sapBatch["new_receivablessum"] = highdimensionaldetaillist.Sum(a => a.new_settlementmoney);
                }
                OrganizationService.Create(sapBatch);
                List<sap_main> sapmainlist = new List<sap_main>();
                List<sap_detail> originalsapdetail = new List<sap_detail>();
                var skuconfiglist = sku1collection.Entities.Where(a => a.ToDefault<string>("new_contractingbody") == contractingbody
                        && a.ToDefault<int>("new_infotype") == sap_type
                        /*&& a.ToDefault<int>("new_isreversecharge") == isreversecharge*/).ToList();
                if (sap_type == 3)
                    skuconfiglist = skuconfiglist.Where(a => a.ToDefault<int>("new_isreversecharge") == isreversecharge).ToList();
                var config_sku1list = skuconfiglist.Where(a => a.ToDefault<int>("new_businesstype") == expenseclaimtype).ToList();
                if (config_sku1list.Count == 0)
                    config_sku1list = skuconfiglist.Where(a => !a.Contains("new_businesstype")).ToList();
                List<sap_detail> sap_detaillist = new List<sap_detail>();
                int detailcount = 0;//sap_detail明细数量
                foreach (var item in highdimensionaldetaillist)
                {
                    int rowId = ++detailcount;
                    var skuconfig = new Entity();
                    if (config_sku1list.Count > 0)
                        skuconfig = config_sku1list.First();
                    var otherskuconfig = config_sku1list.Where(a => a.Id != skuconfig.Id).ToList();//维护了多条sku
                    int i = config_sku1list.IndexOf(skuconfig) + 1;
                    sap_detail detail = new sap_detail();
                    detail.quantity1 = item.new_workordercount.ToString();
                    detail.refer1 = station.GetAttributeValue<string>("new_supplier");
                    detail.refer2 = expense_Claim.GetAttributeValue<string>("new_invoicenumber");
                    detail.refer3 = expense_Claim.GetAttributeValue<string>("new_invoiceno");
                    detail.currency = currency;
                    //detail.originalOrder = originalOrder;
                    detail.originalOrder = item.new_internalorder;
                    detail.rowId = rowId;
                    detail.profitCenter = profitCenter;
                    detail.costcenter = item.new_costcenter;
                    if (sap_type == 1 || sap_type == 5)
                    {
                        //预提、反冲
                        detail.receivables = item.new_withholdingmoney.ToString();
                    }
                    else
                    {
                        detail.receivables = item.new_settlementmoney.ToString();
                    }
                    summoney += Convert.ToDecimal(detail.receivables);
                    detail.remark = new_remark_detail;
                    detail.sku3 = item.new_sku;
                    detail.sku1 = skuconfig.ToDefault<string>("new_name");//sku1 = 虚拟物料
                    detail.sku2 = skuconfig.ToDefault<string>("new_acctsubjects");//sku2 = 会计科目
                    if (sap_type == 3)
                    {
                        //发票
                        if (jswithouttax != null)
                        {
                            detail.sku1 = jswithouttax.sku1;
                            detail.receivables = Math.Round(Convert.ToDecimal(detail.receivables) / Convert.ToDecimal(jswithouttax.tax), 2).ToString();
                        }
                    }
                    sap_detaillist.Add(detail);
                    originalsapdetail.Add(detail);
                    foreach (var othersku in otherskuconfig)
                    {
                        sap_detail detail2 = SettlementHelp.DeepCopy(detail);
                        detail2.rowId = detail.rowId + 1;
                        detail2.sku1 = othersku.ToDefault<string>("new_name");//sku1 = 虚拟物料
                                                                              //sku2
                        detail2.sku2 = othersku?.ToDefault<string>("new_acctsubjects");
                        sap_detaillist.Add(detail2);
                    }
                }
                var sapdetailgroup = sap_detaillist.GroupBy(a => new { costcenter = a.costcenter, originalOrder = a.originalOrder });
                foreach (var item in sapdetailgroup)
                {
                    var sapdetialbatch = item.ToList();
                    int batchcount = 200;
                    for (int i = 0; i < sapdetialbatch.Count; i += batchcount)
                    {
                        var batchdata = sapdetialbatch.Skip(i).Take(batchcount).ToList();
                        sap_main main = new sap_main();
                        main.batchguid = sapBatch.Id.ToString();
                        main.remark = new_remark_main;
                        main.supplier = new_supplier;
                        //main.originalOrder = originalOrder;
                        //main.department = costcenter;
                        main.originalOrder = item.Key.originalOrder;
                        main.department = item.Key.costcenter;
                        main.company = contractingbody;
                        main.businessDate = businessDate.ToString();
                        main.businessType = businesstype;
                        main.details = batchdata;
                        sapmainlist.Add(main);
                    }
                }
                var sapdetaillist = sapmainlist.Select(a => a.details).ToList();
                detailCount = sapdetaillist.Select(a => a.Count).Sum();
                if (jswithouttax != null)
                {
                    var totalcost_withouttax = Math.Round(new_totalcost / Convert.ToDecimal(jswithouttax.tax), 2);
                    if (totalcost_withouttax != summoney)
                    {
                        //对比发票数据表头的金额是否和明细数据总额一致，如果不一致，则将表头金额-明细总额的差额，加到第一个行明细的金额上
                        var firstdetail = sapmainlist.FirstOrDefault().details.FirstOrDefault();
                        if (firstdetail != null)
                            firstdetail.receivables = (Convert.ToDecimal(firstdetail.receivables) + (totalcost_withouttax - summoney)).ToString();
                        summoney = totalcost_withouttax;
                    }
                }
                List<Entity> js_taxentities = new List<Entity>();
                if (!string.IsNullOrWhiteSpace(js_tax))
                {
                    if (IsJson(js_tax))
                    {
                        List<Js_Tax_Model> jstaxlist = JsonConvert.DeserializeObject<List<Js_Tax_Model>>(js_tax);
                        var taxlist = jstaxlist.Where(a => a.contractbody == contractingbody).ToList();
                        Func<Js_Tax_Model, Guid, int, Entity> createtaxdetail = (item, mainid, rowid) =>
                        {
                            Entity tax_detail = new Entity("new_sap_detail");
                            tax_detail["new_sku1"] = item.sku1;
                            tax_detail["new_sku2"] = item.sku2;
                            tax_detail["new_currency"] = currency;
                            tax_detail["new_remark"] = new_remark_detail;
                            tax_detail["new_refer1"] = new_supplier;
                            tax_detail["new_refer2"] = new_invoicenumber;
                            tax_detail["new_refer3"] = new_invoiceno;
                            tax_detail["new_rowid"] = rowid;
                            tax_detail["new_profitcenter"] = profitCenter;
                            //内部订单，发票号
                            tax_detail["new_originalorder"] = originalOrder;
                            tax_detail["new_receivables"] = ((int)Math.Round(new_totalcost * Convert.ToDecimal(item.tax), MidpointRounding.AwayFromZero)).ToString();
                            tax_detail["new_sap_mainid"] = new EntityReference("new_sap_main", mainid);
                            return tax_detail;
                        };
                        Func<Entity> createsapmain = () =>
                        {
                            //创建sap主表
                            Entity sapMain = new Entity("new_sap_main");
                            sapMain.Id = Guid.NewGuid();
                            //批次业务类型
                            if (sap_type == 5)
                                sapMain["new_businesstype"] = "HWSHR";
                            else
                                sapMain["new_businesstype"] = "HWSH";
                            //批次结构的批次业务发生时间
                            sapMain["new_businessdate"] = businessDate;
                            //公司代码(签约主体)
                            sapMain["new_company"] = contractingbody;
                            //成本中心
                            sapMain["new_department"] = costcenter;
                            //sapBatch
                            sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
                            //内部订单，发票号
                            sapMain["new_originalorder"] = originalOrder;
                            //备注
                            sapMain["new_remark"] = new_remark_main;
                            return sapMain;
                        };
                        if (taxlist.Count > 0)
                        {
                            var sapmain = createsapmain();
                            js_taxentities.Add(sapmain);
                            foreach (var item in taxlist)
                            {
                                int index = taxlist.IndexOf(item) + 1;
                                js_taxentities.Add(createtaxdetail(item, sapmain.Id, detailCount + index));
                            }
                            detailCount = detailCount + taxlist.Count;
                        }
                    }
                }
                if (js_taxentities.Count > 0)
                {
                    var mainen = js_taxentities.Where(a => a.LogicalName == "new_sap_main").FirstOrDefault();
                    if (mainen != null)
                    {
                        sap_main main = new sap_main();
                        main.batchguid = sapBatch.Id.ToString();
                        main.remark = new_remark_main;
                        main.supplier = new_supplier;
                        main.company = contractingbody;
                        main.originalOrder = originalOrder;
                        main.department = costcenter;
                        main.businessDate = businessDate.ToString();
                        main.businessType = businesstype;
                        main.details = new List<sap_detail>();
                        var detailens = js_taxentities.Where(a => a.LogicalName == "new_sap_detail").ToList();
                        foreach (var item in detailens)
                        {
                            sap_detail detail = new sap_detail();
                            detail.rowId = item.GetAttributeValue<int>("new_rowid");
                            detail.refer1 = new_supplier;
                            detail.refer2 = new_invoicenumber;
                            detail.refer3 = new_invoiceno;
                            detail.currency = currency;
                            detail.originalOrder = originalOrder;
                            detail.profitCenter = profitCenter;
                            detail.receivables = item.GetAttributeValue<string>("new_receivables");
                            detail.sku1 = item.GetAttributeValue<string>("new_sku1");
                            detail.sku2 = item.GetAttributeValue<string>("new_sku2");
                            main.details.Add(detail);
                        }
                        sapmainlist.Add(main);
                    }
                }
                #region 判断SAP账单是否需要整数处理
                if (!string.IsNullOrWhiteSpace(js_integer) && IsJson(js_integer))
                {
                    List<string> IntegerProcess = JsonConvert.DeserializeObject<List<string>>(js_integer);
                    if (IntegerProcess.Contains(contractingbody))
                    { // 根据签约主体判断Sap批次及其明细是否需要整数处理
                        decimal editSummoney = 0M; // 修改后的总金额明细
                        var detaillist = originalsapdetail;
                        detaillist.ForEach(a =>
                        {
                            decimal receivables = Convert.ToDecimal(a.receivables);
                            receivables = Math.Round(receivables, 0, MidpointRounding.AwayFromZero);
                            a.receivables = receivables.ToString();
                            editSummoney += receivables;
                        });
                        summoney = Math.Round(summoney, 0, MidpointRounding.AwayFromZero); // 以原有summoney取整为准
                        if (summoney != editSummoney)
                        { // 修改后的总额与总额不一致，添加差值到第一个行明细金额上
                            var firstdetail = sapmainlist.FirstOrDefault().details.FirstOrDefault();
                            if (firstdetail != null)
                                firstdetail.receivables = (Convert.ToDecimal(firstdetail.receivables) + (editSummoney - summoney)).ToString();
                        }
                    }
                }
                #endregion
                //修改sap 批次主档
                //更新批次表
                Entity updatesapBatch = new Entity("new_sap_batch");
                updatesapBatch.Id = sapBatch.Id;
                //该批次主表数据量
                updatesapBatch["new_maincount"] = sapmainlist.Count;
                //该批次详情表数据量
                updatesapBatch["new_detailcount"] = detailCount;
                //批次明细的总金额
                updatesapBatch["new_receivablessum"] = summoney;
                OrganizationService.Update(updatesapBatch);
                //同步创建sap_main和sap_detail的数据
                foreach (var main in sapmainlist)
                {
                    EntityCollection executecols = new EntityCollection();
                    //创建sap主表
                    Entity sapMain = new Entity("new_sap_main");
                    sapMain.Id = Guid.NewGuid();
                    //批次业务类型
                    sapMain["new_businesstype"] = main.businessType;
                    //批次结构的批次业务发生时间
                    sapMain["new_businessdate"] = Convert.ToDateTime(main.businessDate);
                    //公司代码(签约主体)
                    sapMain["new_company"] = main.company;
                    //成本中心
                    sapMain["new_department"] = main.department;
                    //sapBatch
                    sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", new Guid(main.batchguid));
                    //内部订单，发票号
                    sapMain["new_originalorder"] = main.originalOrder;
                    //备注
                    sapMain["new_remark"] = main.remark;
                    executecols.Entities.Add(sapMain);
                    //创建sap detail
                    foreach (var item in main.details)
                    {
                        Entity sapdetail = new Entity("new_sap_detail");
                        sapdetail["new_sku1"] = item.sku1;
                        sapdetail["new_sku2"] = item.sku2;
                        sapdetail["new_sku3"] = item.sku3;
                        sapdetail["new_currency"] = item.currency;
                        sapdetail["new_remark"] = item.remark;
                        sapdetail["new_refer1"] = item.refer1;
                        sapdetail["new_refer2"] = item.refer2;
                        sapdetail["new_refer3"] = item.refer3;
                        sapdetail["new_rowid"] = item.rowId;
                        sapdetail["new_quantity1"] = item.quantity1;
                        sapdetail["new_profitcenter"] = item.profitCenter;
                        //内部订单，发票号
                        sapdetail["new_originalorder"] = item.originalOrder;
                        sapdetail["new_receivables"] = item.receivables;
                        sapdetail["new_sap_mainid"] = new EntityReference("new_sap_main", sapMain.Id);
                        executecols.Entities.Add(sapdetail);
                    }
                    CommonHelper.MultCreateRequest(OrganizationService, log, executecols);
                }
            }
        }
        /// <summary>
        /// 根据国家范围，区域范围查询工单信息
        /// </summary>
        /// <param name="countryscope"></param>
        /// <param name="regionscope"></param>
        /// <returns></returns>
        public EntityCollection GetWorkorderSettlement(List<Guid> countryscope, List<Guid> regionscope)
        {
            QueryExpression qe = new QueryExpression("new_workorder_settlement");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_country_id", "new_category1_code", "new_category2_code", "new_category3_code");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            //qe.Criteria.AddCondition(new ConditionExpression("new_issettlementstorage", ConditionOperator.In, new int[] { 1 }));
            qe.Criteria.AddCondition("new_settlementtimestorage", ConditionOperator.OnOrAfter, beginDate);
            qe.Criteria.AddCondition("new_settlementtimestorage", ConditionOperator.OnOrBefore, endDate);
            if (countryscope.Count > 0)
            {
                qe.Criteria.AddCondition(new ConditionExpression("new_country_id", ConditionOperator.In, countryscope));
            }
            if (regionscope.Count > 0)
            {
                LinkEntity link = new LinkEntity("new_workorder_settlement", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition(new ConditionExpression("new_region_idstorage", ConditionOperator.In, regionscope));
                qe.LinkEntities.Add(link);
            }
            EntityCollection cols = CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe);
            return cols;
        }
        /// <summary>
        /// 根据结算单查询高维工厂修整单明细
        /// </summary>
        /// <param name="expenseClaimId"></param>
        /// <returns></returns>
        public EntityCollection GetTrimmorderdetail(string expenseClaimId)
        {
            string fetchXml = @$"<fetch mapping='logical'  no-lock='true'  version='1.0' aggregate='true'>
                                  <entity name='new_trimming_orderdetail'>
                                    <attribute name='new_trimming_orderdetailid' alias='workordercount2' aggregate='count' />
                                    <attribute name='new_withholdingmoney' alias='new_withholdingmoney2' aggregate='sum' />
                                    <attribute name='new_changemoney' alias='new_changemoney2' aggregate='sum' />
                                    <attribute name='new_settlementmoney' alias='new_settlementmoney2' aggregate='sum' />
                                    <attribute name='new_transactioncurrency_service_id' alias='tra' groupby='true' />
                                    <attribute name='new_itemcode' alias='sku' groupby='true' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='new_expenseclaim_id' operator='eq' value='{expenseClaimId}'  uitype='new_srv_expense_claim' />
                                    </filter>
                                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_service_id' link-type='outer' alias='hb'>
                                      <attribute name='isocurrencycode' alias='isocurrencycode' groupby='true' />
                                    </link-entity>
                                  </entity>
                                </fetch>";
            EntityCollection cols = CommonHelper.QueryXmlPage(OrganizationService, fetchXml);
            EntityCollection newcols = new EntityCollection();
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity(item.LogicalName, item.Id);
                if (item.Contains("workordercount2"))
                    en["workordercount2"] = item.GetAliasAttributeValue<int>("workordercount2");
                if (item.Contains("new_withholdingmoney2"))
                    en["new_withholdingmoney2"] = item.GetAliasAttributeValue<decimal>("new_withholdingmoney2");
                if (item.Contains("new_changemoney2"))
                    en["new_changemoney2"] = item.GetAliasAttributeValue<decimal>("new_changemoney2");
                if (item.Contains("new_settlementmoney2"))
                    en["new_settlementmoney2"] = item.GetAliasAttributeValue<decimal>("new_settlementmoney2");
                if (item.Contains("sku"))
                    en["sku"] = item["sku"];
                if (item.Contains("isocurrencycode"))
                    en["isocurrencycode"] = item["isocurrencycode"];
                newcols.Entities.Add(en);
            }
            return newcols;
        }
    }
}
