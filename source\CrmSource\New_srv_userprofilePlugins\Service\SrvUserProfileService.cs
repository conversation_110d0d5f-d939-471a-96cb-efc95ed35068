﻿using System;
using System.Text.Json.Serialization;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using XiaoMi.Crm.CS.ApiCommon.Helper;
using New_srv_userprofilePlugins.Model;
using Newtonsoft.Json;

namespace New_srv_userprofilePlugins.Service
{
    public static class SrvUserProfileService
    {
        public static void SendToSAPNotify(Entity entity, ITracingService tracingService, IOrganizationService serviceAdmin)
        {
            try
            {
                DataModel dataModel = new DataModel();
                if (entity.TryGetAttributeValue<string>("new_sn", out string sn))
                {
                    dataModel.sn = sn;
                }
                if(entity.TryGetAttributeValue<string>("new_name",out string snCode))
                {
                    dataModel.sn = snCode;
                }
                #region 通知SAP
                string queuename = "isp_notify_xms_warranty";
                string index = $"WarrantyData_{dataModel.sn}";
                SendNotifyHelper.SendToNotifyWarrantyData(dataModel, queuename, index, serviceAdmin);
                #endregion

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}