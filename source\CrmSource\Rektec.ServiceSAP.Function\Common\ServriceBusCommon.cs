﻿using RestSharp;
using Newtonsoft.Json;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Security.Cryptography;

using System.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RekTec.Crm.Common.Helper;
using Microsoft.Extensions.Logging;
using Azure.Messaging.ServiceBus;

namespace Rektec.ServiceSAP.Function.Common
{
    public class ServriceBusCommon
    {
        //RootManageSharedAccessKey
        string signatureKeyName;
        //JSJYjzgXcjdDGDzEGyuE1PefuEk9yv7s2+ASbEjHk8s=
        string signatureKey;
        //servicebus-eastasia-isp-multivendorinteg.servicebus.windows.net
        string CountqueueOrTopicUrl;
        //crm_settlementadjustmentline
        string queuesname;
        public ServriceBusCommon(string signatureKeyName, string signatureKey, string CountqueueOrTopicUrl, string queuesname)
        {
            this.signatureKeyName = signatureKeyName;
            this.signatureKey = signatureKey;
            this.CountqueueOrTopicUrl = CountqueueOrTopicUrl;
            this.queuesname = queuesname;
        }

        public async Task sendservricebus(string body, string token, int index, ILogger log)
        {
            try
            {
                var TopicUrl = CountqueueOrTopicUrl + queuesname + "/messages";
                TimeSpan timeToLive = TimeSpan.FromDays(1);
                var client = new RestClient(TopicUrl);
                client.Timeout = -1;
                var request = new RestRequest(Method.POST);
                request.AddHeader("Authorization", token);
                request.AddHeader("Content-Type", "application/xml");
                request.AddParameter("application/xml", body, ParameterType.RequestBody);
                IRestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    log.LogInformation($"第{index}条推送成功");
                }
                else
                {
                    log.LogError($"第{index}条推送失败，Content：{response.Content}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task sendservicebus2(Queue<ServiceBusMessage> messages, string ServiceBusConn, string queuesname, ILogger log)
        {
            await using var client = new ServiceBusClient(ServiceBusConn);
            ServiceBusSender sender = client.CreateSender(queuesname);
            int messageCount = messages.Count;
            while (messages.Count > 0)
            {
                using ServiceBusMessageBatch messageBatch = await sender.CreateMessageBatchAsync();
                if (messageBatch.TryAddMessage(messages.Peek()))
                {
                    messages.Dequeue();
                }
                else
                {
                    throw new Exception($"Message {messageCount - messages.Count} is too large and cannot be sent.");
                }
                while (messages.Count > 0 && messageBatch.TryAddMessage(messages.Peek()))
                {
                    messages.Dequeue();
                }
                await sender.SendMessagesAsync(messageBatch);
                log.LogInformation($"推送{messageBatch.Count}完成，剩余{messages.Count}");
            }
        }
        /// <summary>
        /// 获取Token
        /// </summary>
        /// <param name="resourceUri"></param>
        /// <param name="keyName"></param>
        /// <param name="key"></param>
        /// <param name="ttl"></param>
        /// <returns></returns>
        public string GetSasToken()
        {
            var resourceUri = CountqueueOrTopicUrl + queuesname + "/messages";
            TimeSpan ttl = TimeSpan.FromDays(1);
            var expiry = GetExpiry(ttl);
            string stringToSign = HttpUtility.UrlEncode(resourceUri) + "\n" + expiry;
            HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(signatureKey));
            var signature = Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign)));
            var sasToken = string.Format(System.Globalization.CultureInfo.InvariantCulture, "SharedAccessSignature sr={0}&sig={1}&se={2}&skn={3}",
            HttpUtility.UrlEncode(resourceUri), HttpUtility.UrlEncode(signature), expiry, signatureKeyName);
            return sasToken;
        }

        private string GetExpiry(TimeSpan ttl)
        {
            TimeSpan expirySinceEpoch = DateTime.UtcNow - new DateTime(1970, 1, 1) + ttl;
            return Convert.ToString((int)expirySinceEpoch.TotalSeconds);
        }
    }
}
