﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XiaoMi.ServiceOrder.Plugins.ApplicationInsights;
using XiaoMi.ServiceOrder.Plugins.Model;
using static Pipelines.Sockets.Unofficial.SocketConnection;

namespace XiaoMi.ServiceOrder.Plugins.Command
{
    internal class WarrantyQuerySearchBll
    {
        public dynamic Log;
        private StringBuilder returnMsg = new StringBuilder();
        public CommonHelper commonHelper;
        IPluginExecutionContext context = null;
        IOrganizationService service = null;
        IOrganizationService serviceAdmin = null;
        RestTelemetryClient log = null;
        private static Stopwatch swTotal = new Stopwatch();
        IMEIInterfacebll iMEIInterfacebll = new IMEIInterfacebll();

        private string requestId = string.Empty;
        private string logModuleName = "维保查询";
        private string logClassInfo = "WarrantyQuerySearchBll";
        #region 构造函数
        public WarrantyQuerySearchBll(IPluginExecutionContext _context, IOrganizationService _service, IOrganizationService _serviceAdmin, RestTelemetryClient _log,string _requestId)
        {
            swTotal.Start();
            context = _context;
            service = _service;
            serviceAdmin = _serviceAdmin;
            requestId = _requestId;
            Log = new System.Dynamic.ExpandoObject();
            Log.InfoMsg = new DelegatePrint(Print);
            Log.LogException = new DelegateCatch(CatchException);
            iMEIInterfacebll.service = service;
            iMEIInterfacebll.Log = Log;
            commonHelper = new CommonHelper { OrganizationServiceAdmin = serviceAdmin, UserId = _context.UserId };
            log = _log;
        }
        #endregion
        public delegate void DelegatePrint(string msg, string Method, int Duration);
        private void Print(string msg,string Method, int Duration) {
            //写入日志信息
            log.PostTraceAsync(new AppInsightsLogModel()
            {
                RequestId = requestId,
                Msg = msg,
                ModuleName = logModuleName,
                Level = LogSeverityLevel.Informational,
                Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                ClassInfo = logClassInfo,
                Duration = Duration,
                Method = Method
            }, AppInsightsLogType.Trace);
        }

        public delegate void DelegateCatch(string msg, string Method, int Duration);
        private void CatchException(string msg, string Method, int Duration) {
            log.PostTraceAsync(new AppInsightsLogModel()
            {
                RequestId = requestId,
                Msg = msg,
                ModuleName = logModuleName,
                Level = LogSeverityLevel.Error,
                Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                ClassInfo = logClassInfo,
                Duration= Duration,
                Method = Method,
            }, AppInsightsLogType.Exception);
        }

        //private const string moduleName = "WarrantyQuerySearch";
        public List<WarrantyReturnBody> WarrantyQuery(WarrantySerachParameter warrantySerachParameter)
        {
            //接口返回数据，维保结果集合
            List<WarrantyReturnBody> warrantyReturnBodies = new List<WarrantyReturnBody>();
            try
            {
                Log.InfoMsg("IMEI维保入参（WarrantyQuery）：" + JsonConvert.SerializeObject(warrantySerachParameter), "WarrantyQuerySearch", 0);

                if (warrantySerachParameter.listparameterData.Any(m => string.IsNullOrWhiteSpace(m.country)))
                {
                    throw new InvalidProgramException(commonHelper.GetResource("warranty.countryempty", "參數[country]為空"));
                }

                foreach (var parameterData in warrantySerachParameter.listparameterData)
                {
                    if (string.IsNullOrEmpty(parameterData.item.sn))
                    {
                        if (string.IsNullOrEmpty(parameterData.item.imei))
                        {
                            throw new InvalidProgramException(commonHelper.GetResource("warranty.snandimeiempty", "參數[SN]或[IMEI]至少有一項不為空"));
                        }
                        else
                        {
                            //根据IMEI查询
                            var warrantyReturnBody =  IsInternationalWarranty(parameterData, parameterData.item.imei, UniqueType.imei);
                            warrantyReturnBodies.Add(warrantyReturnBody);
                        }
                    }
                    else
                    {
                        //根据SN查询
                        var warrantyReturnBody = IsInternationalWarranty(parameterData, parameterData.item.sn, UniqueType.sn);
                        warrantyReturnBodies.Add(warrantyReturnBody);
                    }
                }

                return warrantyReturnBodies;
            }
            catch (Exception ex)
            {
                swTotal.Stop();
                Log.LogException($"IMEI维保查询异常记录：{JsonConvert.SerializeObject(ex)}", "WarrantyQuerySearch", (int)swTotal.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException(ex.Message);
            }
            finally
            {
                swTotal.Stop();
                Log.InfoMsg($"IMEI维保查询 总耗时：{swTotal.ElapsedMilliseconds},返回值：{JsonConvert.SerializeObject(warrantyReturnBodies)}", "WarrantyQuerySearch", (int)swTotal.ElapsedMilliseconds);
                //CommonHelper.insertlog(serviceAdmin, "IMEI维保查询", "WarrantyQuerySearch", JsonConvert.SerializeObject(warrantySerachParameter), "WarrantyQuerySearch", "", true, null);
            }
        }

        /// <summary>
        /// 判断是否国际维保
        /// </summary>
        /// <param name="country">国家编码</param>
        /// <param name="unique">SN/IMEI</param>
        /// <param name="uniqueType">类型</param>
        /// <param name="imei">IMEI</param>
        /// <param name="log">日志</param>
        /// <returns></returns>
        /// 

        public WarrantyReturnBody IsInternationalWarranty(Model.WarrantyParameterDataSerach parameterDataSerach, string unique, UniqueType uniqueType)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg("判断是否国际维保开始", "IsInternationalWarranty", 0);
            try
            {
                WarrantyParam warrantyParam = new WarrantyParam();
                warrantyParam.country = parameterDataSerach.country;
                warrantyParam.unique = unique;
                warrantyParam.uniqueType = uniqueType;
                warrantyParam.imei = parameterDataSerach.item.imei;

                //查询该SN是否在东北欧生态链历史销售产品串号池，且区域为北欧
                var isEunesnmanage = GetEunesnmanage(warrantyParam.unique);
                DateTime startTime = new DateTime();
                if (isEunesnmanage.Item1)
                {
                    if (!string.IsNullOrWhiteSpace(warrantyParam?.activeReturnData?.active_time))
                    {
                        startTime = CommonHelper.ConvertToDateTime(long.Parse(warrantyParam.activeReturnData.active_time));
                    }
                    else
                    {
                        //激活时间不包含值，东北欧串号配置时间+90天
                        startTime = isEunesnmanage.Item2.AddDays(90);
                    }
                    DateTime dateTime = DateTime.UtcNow;
                    if (startTime.AddYears(1) >= dateTime)
                    {
                        return SetWarranty(warrantyParam.unique, warrantyParam.imei, commonHelper.GetResource("warranty.Nord365", "此产品由Nord365经销、并在出售1年内，由Nord365负责维修，请转寄给Nord365"), (int)IsWarranty.Warranty);
                    }
                }

                if (!string.IsNullOrEmpty(parameterDataSerach.new_packetsstarttime))
                {
                    warrantyParam.new_packetsstarttime = CommonHelper.ConvertTimestamp(Convert.ToDateTime(parameterDataSerach.new_packetsstarttime));
                }

                //调用imei服务
                warrantyParam.iMEIServiceReturnData = GetIMEIServiceOne(parameterDataSerach.item.sn, uniqueType);

                if (warrantyParam.iMEIServiceReturnData == null)
                {
                    return uniqueType == UniqueType.sn ? SetWarranty(unique, warrantyParam.imei, commonHelper.GetResource("warranty.snnotexist", "SN碼不存在"), (int)IsWarranty.NONENTITY) : SetWarranty("", warrantyParam.imei, "IMEI不存在", (int)IsWarranty.NONENTITY);
                }

                //将国家编码转换成id
                if (!string.IsNullOrWhiteSpace(warrantyParam.iMEIServiceReturnData.b2b_country))
                {
                    warrantyParam.iMEIServiceReturnData.b2b_country = GetCountryId(warrantyParam.iMEIServiceReturnData.b2b_country);
                }

                List<string> sale_region = warrantyParam.iMEIServiceReturnData.extend.Where(m => m.key.Contains("sale_region") && m.key != "sale_region_count").Select(m => m.value).ToList();
               
                warrantyParam.iMEIServiceReturnData.sales_scope = new List<string>();
                if (sale_region.Count > 0)
                {
                    foreach (var item in sale_region)
                    {
                        foreach (var item1 in item.Split(','))
                        {
                            warrantyParam.iMEIServiceReturnData.sales_scope.Add(item1);
                        }
                    }
                }
                //调用OC服务
                warrantyParam.oCReturnData = new OCReturnData() { country = parameterDataSerach?.oc_country, goods_id = parameterDataSerach?.goodid };

                warrantyParam.channel = parameterDataSerach.channel;

                //调用激活服务
                warrantyParam.activeReturnData = parameterDataSerach.ActiveReturnData;

                //将激活服务国家编码转换成id
                if (warrantyParam.activeReturnData != null)
                {
                    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                    {
                        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                        #region 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                        if (string.IsNullOrWhiteSpace(active_country))
                        {
                            warrantyParam.activeReturnData.active_country = "";
                        }
                        else
                        {
                            warrantyParam.activeReturnData.active_country = active_country;
                        }
                        //return SetWarranty(warrantyParam.unique, warrantyParam.imei, commonHelper.GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                        #endregion 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                    }

                }

                //判断手机/生态链
                warrantyParam.type = GetGoodsfiles(warrantyParam.iMEIServiceReturnData.goods_id);//根据一级品类编码验证
                
                if (warrantyParam.type != 1)//非手机
                {
                    return GetWarranty(warrantyParam);
                }

                //判断 销售地/激活地(如果有)/受理地 完全一致
                if (string.IsNullOrWhiteSpace(warrantyParam.activeReturnData?.active_country))
                {
                    if (warrantyParam.iMEIServiceReturnData?.b2b_country == parameterDataSerach.country)
                        return GetWarranty(warrantyParam);
                }

                if (warrantyParam.iMEIServiceReturnData?.b2b_country == warrantyParam.activeReturnData?.active_country && warrantyParam.activeReturnData?.active_country == parameterDataSerach.country)
                    return GetWarranty(warrantyParam);

                var isConfig = IsConfig(warrantyParam.iMEIServiceReturnData.goods_id.ToString());
                WarrantyReturnBody warrantyReturnBody =new WarrantyReturnBody();
                //判断受理物品GoodsId是否配置国际联保权益
                if (!isConfig.Item1)
                {
                    warrantyReturnBody = GetWarranty(warrantyParam);
                }
                else if (!isConfig.Item2)//判断权益是否需要注册
                {
                    warrantyReturnBody = GetInternationalWarranty(warrantyParam, isConfig.Item3);
                }
                else if (!GetInternationalwarrantyequityregister(isConfig.Item3, unique, warrantyParam.iMEIServiceReturnData.imei))//判断SN/IMEI是否有注册记录
                {
                    warrantyReturnBody = GetWarranty(warrantyParam);
                }
                else
                {
                    warrantyReturnBody = GetInternationalWarranty(warrantyParam, isConfig.Item3);
                }
                sw.Stop();
                Log.InfoMsg($"判断是否国际维保结束:{JsonConvert.SerializeObject(warrantyReturnBody)}", "IsInternationalWarranty", (int)sw.ElapsedMilliseconds);
                return warrantyReturnBody;
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"判断是否国际维保异常:{ex}", "IsInternationalWarranty", (int)sw.ElapsedMilliseconds);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 国际维保逻辑
        /// </summary>
        /// <param name="country"></param>
        /// <param name="unique"></param>
        /// <param name="uniqueType"></param>
        /// <param name="imei"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public WarrantyReturnBody GetInternationalWarranty(WarrantyParam warrantyParam, Guid new_interwarrantysettingid)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg("国际维保逻辑开始", "GetInternationalWarranty", 0);
            try
            {
                //获取销售渠道 自营/非自营
                var activeReturnData = warrantyParam.activeReturnData;
                var iMEIServiceReturnData = warrantyParam.iMEIServiceReturnData;
                var oCReturnData = warrantyParam.oCReturnData;

                bool xiaoshou = GetInternationalwarrantyequitynation(iMEIServiceReturnData.b2b_country, new_interwarrantysettingid);
                bool shouli = GetInternationalwarrantyequitynation(warrantyParam.country, new_interwarrantysettingid);
                bool jihuo = true;

                if (warrantyParam.channel == 1)//自营渠道
                {
                    //判断销售地、激活地（如果有）受理地是否在联保国家
                    if (!string.IsNullOrEmpty(activeReturnData?.active_country))
                        jihuo = GetInternationalwarrantyequitynation(activeReturnData.active_country, new_interwarrantysettingid);

                    if (xiaoshou && shouli && jihuo)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                    return GetWarranty(warrantyParam);
                }
                else if (warrantyParam.channel == 2)//非自营
                {
                    //判断是否官方销售渠道
                    //if (!GetIsOfficial(warrantyParam.country, oCReturnData.goods_id))
                    //    return GetWarranty(warrantyParam);

                    //判断是否有销售范围
                    if (iMEIServiceReturnData.sales_scope?.Count > 0)
                    {
                        //判断是否有激活地
                        if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                        {
                            //激活服务中渠道的激活可能会中文，统一转成id
                            //if (warrantyParam.activeReturnData != null)
                            //{
                            //    sw.Restart();
                            //    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                            //    {
                            //        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                            //        if (string.IsNullOrWhiteSpace(active_country))
                            //            return SetWarranty(warrantyParam.unique, warrantyParam.imei, commonHelper.GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                            //        warrantyParam.activeReturnData.active_country = active_country;
                            //    }

                            //    sw.Stop();
                            //    Log.InfoMsg($"查询激活国家id 用时:{sw.ElapsedMilliseconds}");
                            //}
                            //激活地是否在允许的销售范围
                            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                            {
                                //判断销售地、激活地、受理地是否在联保国家
                                jihuo = GetInternationalwarrantyequitynation(activeReturnData?.active_country, new_interwarrantysettingid);

                                if (xiaoshou && shouli && jihuo)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                                return GetWarranty(warrantyParam);
                            }

                            return GetWarranty(warrantyParam);
                        }

                        //jihuo = GetInternationalwarrantyequitynation(activeReturnData?.active_country, new_interwarrantysettingid);

                        if (xiaoshou && shouli && jihuo)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                        return GetWarranty(warrantyParam);
                    }

                    //判断销售地、激活地（如果有）受理地是否在联保国家
                    if (!string.IsNullOrEmpty(activeReturnData?.active_country))
                        jihuo = GetInternationalwarrantyequitynation(activeReturnData.active_country, new_interwarrantysettingid);

                    if (xiaoshou && shouli && jihuo)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                    return GetWarranty(warrantyParam);
                }

                sw.Stop();
                Log.InfoMsg("国际维保逻辑结束", "GetInternationalWarranty", (int)sw.ElapsedMilliseconds);
                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"国际维保逻辑异常:{ex}", "GetInternationalWarranty", (int)sw.ElapsedMilliseconds);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 维保结果查询
        /// </summary>
        /// <param name="country">国家</param>
        /// <param name="unique">sn/imei</param>
        /// <returns></returns>
        public WarrantyReturnBody GetWarranty(WarrantyParam warrantyParam)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg($"维保结果查询开始", "GetWarranty",0);
            try
            {
                //获取系统参数 国家id
                string hk = CommonHelper.GetSystemParameterValue(service, "sys_hkid");
                string ru = CommonHelper.GetSystemParameterValue(service, "sys_ruid");
                string cb = CommonHelper.GetSystemParameterValue(service, "sys_cbid");
                string em = CommonHelper.GetSystemParameterValue(service, "sys_emid");
                var activeReturnData = warrantyParam.activeReturnData;
                var iMEIServiceReturnData = warrantyParam.iMEIServiceReturnData;
                var oCReturnData = warrantyParam.oCReturnData;
                StringBuilder builder = new StringBuilder();
                builder.Append("激活国家：" + activeReturnData?.active_country);
                builder.Append("IMEI国家：" + iMEIServiceReturnData?.b2b_country);
                builder.Append("受理国家：" + warrantyParam?.country);
                builder.Append("oc国家:" + oCReturnData?.country);
                builder.Append("iMEIServiceReturnData.sales_scope.Count:" + iMEIServiceReturnData?.sales_scope?.Count);
                builder.Append("品类(1手机，2供应链):" + warrantyParam.type);
                builder.Append("渠道(1自营，2非自营):" + warrantyParam.channel);
                Log.InfoMsg($"维保结果查询参数：{builder}", "GetWarranty", (int)sw.ElapsedMilliseconds);

                if (warrantyParam.type == 1)//手机
                {

                    //判断销售地 = EM && 商品档案中根据new_sku后两位 = CN / TW / HK / IN
                    var new_sku = GetGoodssku(warrantyParam.iMEIServiceReturnData.goods_id);
                    if (!string.IsNullOrWhiteSpace(new_sku))
                    {
                        Log.InfoMsg($"维保结果查询-SKU：{new_sku}", "GetWarranty", (int)sw.ElapsedMilliseconds);
                        new_sku = new_sku.Substring(new_sku.Length - 2).ToUpper();
                        if ((new_sku == "CN" || new_sku == "TW" || new_sku == "HK" || new_sku == "IN") && warrantyParam.iMEIServiceReturnData.b2b_country == em)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FEMBD", "非EM本地电商售后可受理机型"), (int)IsWarranty.FALSE);
                    }

                    //激活地=受理地=俄罗斯 激活时间<2020.8 可维保
                    //if (activeReturnData != null && activeReturnData.active_country == warrantyParam?.country && warrantyParam?.country == ru)
                    if (activeReturnData != null && activeReturnData.active_country == warrantyParam?.country && warrantyParam?.country == "850000016")
                    {
                        if (CommonHelper.ConvertToDateTime(long.Parse(activeReturnData.active_time)) < new DateTime(2020, 8, 1))
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                    }

                    if (warrantyParam.channel == 1)//自营
                    {
                        //判断OC订单国家是否等于受理地
                        if (warrantyParam?.country == oCReturnData?.country)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //判断受理国家和销售国家是否联保
                        bool isCoinsurance = GetIsCoinsurance(warrantyParam?.country, oCReturnData?.country);
                        Log.InfoMsg($"判断受理国家和销售国家是否联保：{isCoinsurance}", "GetWarranty", (int)sw.ElapsedMilliseconds);
                        if (isCoinsurance)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFDG", "受理機構非訂單所屬國家，且非聯保"), (int)IsWarranty.FALSE);
                    }
                    else if (warrantyParam.channel == 2)//非自营
                    {
                        //判断是否可保修 取sn查imei服务【是否维保】
                        if (iMEIServiceReturnData.can_repair == 1 || iMEIServiceReturnData.can_repair == 0 || GetSpecialapply((int)IsWarranty.IMEINoWarranty, iMEIServiceReturnData.sn))
                        {
                            //判断受理地是否为 激活地判保例外区域
                            if (GetIsException(warrantyParam.country))
                            {
                                //判断受理地是否等于IMEI服务销售国家
                                if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和销售国家是否联保
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }
                            //判断该imei是否在imei配置表中
                            if (GetImeiexception(iMEIServiceReturnData.imei))
                            {
                                //判断【受理地】等于销售地（IMEI服务）
                                if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                ///【受理地】是否与销售地（IMEI服务）联保 （判断销售地和【入参国家】是否在【联保国家】配置表中）
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }
                            //是否有激活地【IMEI服务-激活地是否有值】
                            if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                            {
                                //激活服务中渠道的激活可能会中文，统一转成id
                                //if (warrantyParam.activeReturnData != null)
                                //{
                                //    sw.Restart();
                                //    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                                //    {
                                //        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                                //        if (string.IsNullOrWhiteSpace(active_country))
                                //            return SetWarranty(warrantyParam.unique, warrantyParam.imei, commonHelper.GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                                //        warrantyParam.activeReturnData.active_country = active_country;
                                //    }

                                //    sw.Stop();
                                //    Log.InfoMsg($"查询激活国家id 用时:{sw.ElapsedMilliseconds}");
                                //}
                                //判断是否有销售范围
                                if (iMEIServiceReturnData.sales_scope?.Count == 0)
                                {
                                    //判断受理国家是否等于销售国家
                                    if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    //判断受理国家和销售国家是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                                }
                                /*
                                //查询受理地是否在跨境电商配置表中
                                if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                {
                                    //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, activeReturnData.active_country))
                                    {
                                        //判断受理国家是否等于激活地
                                        if (warrantyParam.country == activeReturnData.active_country)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //判断受理地是否跨境电商允许销售国家
                                        if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                        {
                                            //判断受理地是否与激活地联保
                                            if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                        }

                                        //判断受理机构是否为香港机构 
                                        if (warrantyParam.country == hk)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                    }
                                    //判断受理地是否跨境电商允许销售国家
                                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                    {
                                        //判断受理地是否与激活地联保
                                        if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //if (activeReturnData.active_country == cb)
                                        if (activeReturnData.active_country == "850036864")
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理机构是否为香港机构 
                                    //if (warrantyParam.country == hk)
                                    if (warrantyParam.country == "3385")
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                                }
                                */
                                //激活地是否在允许的销售范围
                                //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                                if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                                {
                                    //判断激活地和受理国家是否相等
                                    if (activeReturnData.active_country == warrantyParam.country)
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理国家和和激活地是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }
                                //激活地是否与允许销售区域联保
                                if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                                {
                                    //判断激活地和受理国家是否相等
                                    if (activeReturnData.active_country == warrantyParam.country)
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理国家和和激活地是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);

                            }
                            //查询受理地是否在跨境电商配置表中
                            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            //判断受理国家是否等于销售国家
                            if (GetCountryId(iMEIServiceReturnData.b2b_country) == warrantyParam.country)
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            //判断受理国家和销售国家是否联保
                            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        }

                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);

                        ////判断是否官方渠道，受理国家与商品编码是否在【官方销售配置表】中
                        //if (GetIsOfficial(warrantyParam.country, oCReturnData.goods_id))
                        //{
                        //    //判断是否可保修 取sn查imei服务【是否维保】
                        //    if (iMEIServiceReturnData.can_repair == 1 || iMEIServiceReturnData.can_repair == 0)
                        //    {
                        //        //判断受理地是否为 激活地判保例外区域
                        //        if (GetIsException(warrantyParam.country))
                        //        {
                        //            //判断受理地是否等于IMEI服务销售国家
                        //            if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            //判断受理国家和销售国家是否联保
                        //            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //        }
                        //        //判断该imei是否在imei配置表中
                        //        if (GetImeiexception(iMEIServiceReturnData.imei))
                        //        {
                        //            //判断【受理地】等于销售地（IMEI服务）
                        //            if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            ///【受理地】是否与销售地（IMEI服务）联保 （判断销售地和【入参国家】是否在【联保国家】配置表中）
                        //            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //        }
                        //        //是否有激活地【IMEI服务-激活地是否有值】
                        //        if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                        //        {
                        //            //判断是否有销售范围
                        //            if (iMEIServiceReturnData.sales_scope?.Count == 0)
                        //            {
                        //                //判断受理国家是否等于销售国家
                        //                if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和销售国家是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //            }
                        //            //查询受理地是否在跨境电商配置表中
                        //            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //            {
                        //                //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                        //                if (GetIsAllow(iMEIServiceReturnData.b2b_country, activeReturnData.active_country))
                        //                {
                        //                    //判断受理国家是否等于激活地
                        //                    if (warrantyParam.country == activeReturnData.active_country)
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    //判断受理地是否跨境电商允许销售国家
                        //                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //                    {
                        //                        //判断受理地是否与激活地联保
                        //                        if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                        //                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //                    }

                        //                    //判断受理机构是否为香港机构 
                        //                    if (warrantyParam.country == "3385")
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //                }
                        //                //判断受理地是否跨境电商允许销售国家
                        //                if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //                {
                        //                    //判断受理地是否与激活地联保
                        //                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    if (activeReturnData.active_country == "850036864")
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                        //                }

                        //                //判断受理机构是否为香港机构  香港编码暂时不知道，回头补上
                        //                if (warrantyParam.country == "3385")
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                        //            }
                        //            //激活地是否在允许的销售范围
                        //            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                        //            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                        //            {
                        //                //判断激活地和受理国家是否相等
                        //                if (activeReturnData.active_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和和销售地是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //            }
                        //            //激活地是否与允许销售区域联保
                        //            if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                        //            {
                        //                //判断激活地和受理国家是否相等
                        //                if (activeReturnData.active_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和和销售地是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //            }

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);

                        //        }
                        //        //查询受理地是否在跨境电商配置表中
                        //        if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        //判断受理国家是否等于销售国家
                        //        if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        //判断受理国家和销售国家是否联保
                        //        if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //    }

                        //    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);
                        //}
                        //return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SYSF", "當前商品與受理機構國家非官方銷售渠道"), (int)IsWarranty.FALSE);
                    }

                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);

                }
                else if (warrantyParam.type == 2)//生态链
                {
                    ////imei服务 是否可保修 为否
                    if (iMEIServiceReturnData.can_repair == 2 && !GetSpecialapply((int)IsWarranty.IMEINoWarranty, iMEIServiceReturnData.sn))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);

                    //判断受理国家是否等于销售国家
                    if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家和销售国家是否联保
                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //受理地=澳门 && 三级品类=电视 && 销售地=中国，可维保
                    if (warrantyParam.country == "850000196" && iMEIServiceReturnData.b2b_country == "1")
                    {
                        Log.InfoMsg($"澳门特殊逻辑--受理地：{warrantyParam.country}，销售地：{iMEIServiceReturnData.b2b_country}，商品档案：{iMEIServiceReturnData.goods_id}", "GetWarranty", (int)sw.ElapsedMilliseconds);
                        //判断imei服务的商品的三级品类是否为电视[30000008]
                        if (GetGoodsFilesCategory3(iMEIServiceReturnData.goods_id.ToString()) == "30000008")
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                    }

                    //是否有激活地【IMEI服务-激活地是否有值】
                    if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                    {
                        //激活服务中渠道的激活可能会中文，统一转成id
                        if (warrantyParam.activeReturnData != null)
                        {
                            sw.Restart();
                            if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                            {
                                var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                                #region 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                                if (string.IsNullOrWhiteSpace(active_country))
                                {
                                    warrantyParam.activeReturnData.active_country = "";
                                }
                                else
                                {
                                    warrantyParam.activeReturnData.active_country = active_country;
                                }

                                //return SetWarranty(warrantyParam.unique, warrantyParam.imei, commonHelper.GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                                #endregion

                            }

                        }
                        if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                        {
                            //判断是否有销售范围
                            if (iMEIServiceReturnData.sales_scope?.Count == 0)
                            {
                                //判断受理国家是否等于销售国家
                                if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和销售国家是否联保
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }

                            //查询受理地是否在跨境电商配置表中
                            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                            {
                                //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                                var iscrosscommerce = GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country);
                                if (iscrosscommerce)
                                {
                                    //判断受理国家是否等于激活地
                                    if (warrantyParam.country == activeReturnData.active_country)
                                    {
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                                    }

                                    //判断受理地是否与激活地联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    {
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                                    }

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                    
                                    ////判断受理机构是否为香港机构 
                                    //if (warrantyParam.country == "3385")
                                    //    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    //return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }

                                //判断受理地是否跨境电商允许销售国家
                                //if (iscrosscommerce)
                                //{
                                //    //判断受理地是否与激活地联保
                                //    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                //        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                                //}

                                ////判断受理机构是否为香港机构  香港编码暂时不知道，回头补上
                                //if (warrantyParam.country == "3385")
                                //    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                            }

                            //激活地是否在允许的销售范围
                            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                            {
                                //判断激活地和受理国家是否相等
                                if (activeReturnData.active_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和和激活地是否联保
                                if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                            }

                            //激活地是否与允许销售区域联保
                            if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                            {
                                //判断激活地和受理国家是否相等
                                if (activeReturnData.active_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和和激活地是否联保
                                if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                            }

                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);
                        }
                    }

                    //查询受理地是否在跨境电商配置表中
                    if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家是否等于销售国家
                    if (GetCountryId(iMEIServiceReturnData.b2b_country) == warrantyParam.country)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家和销售国家是否联保
                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                }

                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, commonHelper.GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);

            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"维保结果查询异常:{JsonConvert.SerializeObject(ex)}", "GetWarranty",(int)sw.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException("错误详情：" + JsonConvert.SerializeObject(ex.Message));
            }
        }

        /// <summary>
        /// 获取商品的三级品类
        /// </summary>
        /// <param name="goods_id">商品id</param>
        /// <returns></returns>
        public string GetGoodsFilesCategory3(string goods_id)
        {
            //获取商品的三级品类
            QueryExpression query = new QueryExpression("new_goodsfiles");
            query.TopCount = 1;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, goods_id);
            LinkEntity category3 = new LinkEntity("new_goodsfiles", "new_category3", "new_category3_id", "new_category3id", JoinOperator.Inner);
            category3.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            category3.EntityAlias = "category3";
            category3.Columns.AddColumns("new_code");
            query.LinkEntities.Add(category3);
            var ec = service.RetrieveMultiple(query);
            if (ec?.Entities?.Count > 0)
                return ec.Entities[0].GetAliasAttributeValue<string>("category3.new_code");

            return "";
        }

        /// <summary>
        /// SN/IMEI是否有注册记录
        /// </summary>
        /// <param name="internationalwarrantyequityid"></param>
        /// <param name="sn"></param>
        /// <returns></returns>
        public bool GetInternationalwarrantyequityregister(Guid internationalwarrantyequityid, string unique, string imei)
        {
            var new_internationalwarrantyequityregister = GetCache("new_internationalwarrantyequityregister");
            if (new_internationalwarrantyequityregister is null || new_internationalwarrantyequityregister?.Count == 0)
            {
                QueryExpression qe = new QueryExpression("new_internationalwarrantyequityregister");
                qe.ColumnSet = new ColumnSet("new_sn", "new_imei");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var ec = service.RetrieveMultiple(qe);

                SetCache("new_internationalwarrantyequityregister", ec.Entities.ToList());//插入缓存
                new_internationalwarrantyequityregister = ec.Entities.ToList();
            }
            new_internationalwarrantyequityregister = new_internationalwarrantyequityregister.Where(m => m.Id == internationalwarrantyequityid).ToList();
            new_internationalwarrantyequityregister = new_internationalwarrantyequityregister.Where(m => m.GetAttributeValue<string>("new_sn") == unique || m.GetAttributeValue<string>("new_imei") == imei).ToList();

            return new_internationalwarrantyequityregister?.Count > 0 ? true : false;
        }

        /// <summary>
        /// 查询国家是否在联保国家
        /// </summary>
        /// <param name="countryId"></param>
        /// <returns></returns>
        public bool GetInternationalwarrantyequitynation(string countryId, Guid new_interwarrantysettingid)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg($"查询国家是否在联保国家开始：countryId{countryId}，new_interwarrantysettingid：{new_interwarrantysettingid}", "GetInternationalwarrantyequitynation", 0);
            try
            {
                QueryExpression qe = new QueryExpression("new_internationalwarrantyequitynation");
                qe.Criteria.AddCondition("new_interwarrantysetting_id", ConditionOperator.Equal, new_interwarrantysettingid);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity le = new LinkEntity("new_internationalwarrantyequitynation", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                le.LinkCriteria.AddCondition("new_id", ConditionOperator.Equal, countryId);
                qe.LinkEntities.Add(le);

                var ec = service.RetrieveMultiple(qe);
                sw.Stop();
                Log.InfoMsg($"查询国家是否在联保国家结束：是否联保{(ec?.Entities?.Count > 0 ? true:false)}", "GetInternationalwarrantyequitynation", (int)sw.ElapsedMilliseconds);
                return ec?.Entities?.Count > 0 ? true : false;
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"查询国家是否在联保国家异常：{JsonConvert.SerializeObject(ex)}", "GetInternationalwarrantyequitynation", (int)sw.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 调用imei服务
        /// </summary>
        /// <param name="unique">sn或imei</param>
        /// <param name="uniqueType">类别</param>
        /// <returns></returns>
        public IMEIServiceReturnData GetIMEIServiceOne(string unique, UniqueType uniqueType)
        {
            Stopwatch swTotal = new Stopwatch();
            swTotal.Start();
            Log.InfoMsg($"调用IMEI服务开始", "GetIMEIServiceOne",0);
            try
            {
                IMEIServiceReturnData iMEIServiceReturnData = new IMEIServiceReturnData();
                IMEIRequestData iMEIRequestData = new IMEIRequestData();
                Condition condition = new Condition();

                condition.business = CommonHelper.GetSystemParameterValue(service, "sys_IMEIBusiness");
                condition.has_extend = true;
                condition.limit = 100;
                string[] array = { unique };
                condition.q_type = Enum.GetName(typeof(UniqueType), uniqueType);
                iMEIRequestData.sns = array;
                iMEIRequestData.condition = condition;
                iMEIServiceReturnData = iMEIInterfacebll.ImeiSearchSn(iMEIRequestData);

                #region 港台米家销售区域数据错误引起判保错误修复
                List<string> HKmihomeList = new List<string>();
                List<string> TWmihomeList = new List<string>();
                HKmihomeList = CommonHelper.GetSystemParameterValue(service, "HKmihomeList").Split(',').ToList();
                TWmihomeList = CommonHelper.GetSystemParameterValue(service, "TWmihomeList").Split(',').ToList();
                if (iMEIServiceReturnData != null && !string.IsNullOrWhiteSpace(iMEIServiceReturnData.mihome.ToString()))
                {
                    var mh = iMEIServiceReturnData.mihome.ToString();
                    if (HKmihomeList.Count > 0 && HKmihomeList.Contains(mh) && !string.IsNullOrWhiteSpace(iMEIServiceReturnData.b2b_country) && iMEIServiceReturnData.b2b_country.ToLower() == "cn")
                    {
                        iMEIServiceReturnData.b2b_country = "hk";
                    }
                    if (TWmihomeList.Count > 0 && TWmihomeList.Contains(mh) && !string.IsNullOrWhiteSpace(iMEIServiceReturnData.b2b_country) && iMEIServiceReturnData.b2b_country.ToLower() == "cn")
                    {
                        iMEIServiceReturnData.b2b_country = "tw";
                    }
                }
                #endregion

                swTotal.Stop();
                Log.InfoMsg($"调用IMEI服务结束", "GetIMEIServiceOne", (int)swTotal.ElapsedMilliseconds);
                return iMEIServiceReturnData;
            }
            catch (Exception ex)
            {
                swTotal.Stop();
                Log.LogException($"调用IMEI服务异常：{JsonConvert.SerializeObject(ex)}", "GetIMEIServiceOne", (int)swTotal.ElapsedMilliseconds);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 单条维保结果
        /// </summary>
        /// <param name="msg">不维保原因</param>
        /// <param name="code">状态码  5000维保，5201不维保</param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public WarrantyReturnBody SetWarranty(string sn, string imei, string msg, int code, long startTime = 0, bool IsInternationalWarranty = false)
        {
            if (GetSpecialapply(code, sn))
            {
                code = (int)IsWarranty.TRUE;
                msg = "";
            }
            WarrantyReturnBody warrantyReturnBody = new WarrantyReturnBody();
            warrantyReturnBody.sn = sn;
            warrantyReturnBody.imei = imei;
            warrantyReturnBody.warranty = new Warranty();
            warrantyReturnBody.warranty.msg = msg;
            warrantyReturnBody.warranty.code = code;
            warrantyReturnBody.warranty.startTime = startTime;
            warrantyReturnBody.warranty.IsInternationalWarranty = IsInternationalWarranty;
            return warrantyReturnBody;
        }

        /// <summary>
        /// 特批单查询
        /// </summary>
        /// <param name="code">状态码  5013</param>
        /// <param name="sn"></param>
        /// <returns></returns>
        public bool GetSpecialapply(int code, string sn)
        {
            if ((int)IsWarranty.IMEINoWarranty == code)
            {
                QueryExpression qe = new QueryExpression("new_srv_specialapply");
                qe.ColumnSet = new ColumnSet("new_approvalstatus");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 8);
                qe.Criteria.AddCondition("new_sn", ConditionOperator.Equal, sn);
                qe.Criteria.AddCondition("new_approvalstatus", ConditionOperator.Equal, 3);//特批单状态 = 已审核
                var ec = serviceAdmin.RetrieveMultiple(qe);
                if (ec?.Entities?.Count > 0 )
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;

        }

        //获取 根据国家中文名/英文名/二位编码/三位编码 获取国家id，
        public string GetCountryId(string country)
        {
            Stopwatch swTotal = new Stopwatch();
            swTotal.Start();
            Log.InfoMsg($"根据国家中文名/英文名/二位编码/三位编码查询国家id开始", "GetCountryId", 0);
            try
            {
                country = country.ToUpper();
                var new_country = RedisHelper.GetValue(serviceAdmin,country, "WorkOrder_GetCountry");
                if (!string.IsNullOrWhiteSpace(new_country))
                    return new_country;

                QueryExpression qe = new QueryExpression("new_country");
                qe.NoLock = true;
                qe.ColumnSet = new ColumnSet("new_id", "new_name", "new_englishname", "new_code", "new_code1");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity le1 = new LinkEntity("new_country", "new_data_languageconfig", "new_countryid", "new_data_id", JoinOperator.Inner);
                le1.Columns = new ColumnSet("new_value");
                le1.EntityAlias = "le";
                qe.LinkEntities.Add(le1);

                LinkEntity le2 = new LinkEntity("new_data_languageconfig", "new_language", "new_language_id", "new_languageid", JoinOperator.LeftOuter);
                le2.LinkCriteria.AddCondition("new_langid", ConditionOperator.Equal, "2052");
                le1.LinkEntities.Add(le2);
                var ec = serviceAdmin.RetrieveMultiple(qe);

                Entity entity = ec.Entities.Where(m => m.GetAttributeValue<string>("new_id") == country || m.GetAttributeValue<string>("new_name") == country || (!string.IsNullOrWhiteSpace(m.GetAttributeValue<string>("new_englishname")) && m.GetAttributeValue<string>("new_englishname").ToUpper() == country) || m.GetAttributeValue<string>("new_code") == country || m.GetAttributeValue<string>("new_code1") == country || (m.Contains("le.new_value") && m.GetAttributeValue<AliasedValue>("le.new_value").Value.ToString() == country)).FirstOrDefault();

                if (entity != null)
                {
                    string new_id = entity.GetAttributeValue<string>("new_id");
                    RedisHelper.SetValue(serviceAdmin, country, new_id, "WorkOrder_GetCountry", (int)TimeSpan.FromHours(8).TotalSeconds);

                    swTotal.Stop();
                    Log.InfoMsg($"根据国家中文名/英文名/二位编码/三位编码查询国家id {new_id} 结束", "GetCountryId", (int)swTotal.ElapsedMilliseconds);
                    return new_id;
                }
                swTotal.Stop();
                Log.InfoMsg($"根据国家中文名/英文名/二位编码/三位编码查询国家id结束", "GetCountryId", (int)swTotal.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                swTotal.Stop();
                Log.LogException($"根据国家中文名/英文名/二位编码/三位编码【{country}】查询国家id异常{JsonConvert.SerializeObject(ex)}", "GetCountryId", (int)swTotal.ElapsedMilliseconds);
                throw new Exception(ex.Message);
            }
            return string.Empty;
        }


        /// <summary>
        /// 根据商品编码获取 手机/生态链
        /// </summary>
        /// <param name="goods_id">商品编码</param>
        /// <returns></returns>
        public int GetGoodsfiles(long goods_id)
        {
            Stopwatch swTotal = new Stopwatch();
            swTotal.Start();
            Log.InfoMsg($"根据商品编码获取 手机/生态链开始", "GetGoodsfiles", 0);
            try
            {
                // modified by Hyacinthhuang 优化查询，取缓存，系统参数
                string param = CommonHelper.GetSystemParameterValue(service, "firstcategory1_shoujipingban"); //查询一级品类编码

                QueryExpression qe = new QueryExpression("new_goodsfiles");
                qe.NoLock = true;
                qe.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, Convert.ToInt32(goods_id));
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                LinkEntity le = new LinkEntity("new_goodsfiles", "new_category1", "new_new_category1_id", "new_category1id", JoinOperator.LeftOuter);
                le.EntityAlias = "station";
                le.Columns = new ColumnSet("new_code");
                qe.LinkEntities.Add(le);
                var res = service.RetrieveMultiple(qe);
                if (res?.Entities?.Count == 0)
                    throw new Exception(commonHelper.GetResource("warranty.goodsIdisnotfound", "當前商品不支持維保，未查詢到此商品編碼"));

                var obj = res.Entities.FirstOrDefault();

                var new_code = obj.Contains("station.new_code") ? obj.GetAliasAttributeValue<string>("station.new_code") : string.Empty;

                int type = new_code == param ? 1 : 2;
                swTotal.Stop();

                Log.InfoMsg($"根据商品编码获取 手机/生态链结束（1手机，2生态链）：{type}", "GetGoodsfiles", (int)swTotal.ElapsedMilliseconds);
                return type;
            }
            catch (Exception ex)
            {
                swTotal.Stop();
                Log.LogException($"根据商品编码获取 手机/生态链异常：{JsonConvert.SerializeObject(ex)}", "GetGoodsfiles", (int)swTotal.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 根据商品编码查询sku
        /// </summary>
        /// <param name="goods_id"></param>
        /// <returns></returns>
        public string GetGoodssku(long goods_id)
        {
            QueryExpression qe = new QueryExpression("new_goodsfiles");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_sku");
            qe.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, Convert.ToInt32(goods_id));
            var res = service.RetrieveMultiple(qe);
            if (res?.Entities?.Count > 0)
            {
                return res[0].GetAttributeValue<string>("new_sku");
            }
            return default;
        }

        /// <summary>
        /// 判断受理国家和销售地是否联保
        /// </summary>
        /// <param name="country">受理国家</param>
        /// <param name="b2b_country">销售国家</param>
        /// <returns></returns>
        public bool GetIsCoinsurance(string country, string b2b_country)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                Log.InfoMsg($"判断受理国家和销售地是否联保开始", "GetIsCoinsurance", 0);
                if (string.IsNullOrWhiteSpace(country) || string.IsNullOrWhiteSpace(b2b_country)) return false;
                sw.Restart();
                bool flag = false;
                QueryExpression query = new QueryExpression("new_unprofor");
                query.ColumnSet = new ColumnSet("new_countrys");
                query.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //query.Criteria.AddCondition("new_countrys", ConditionOperator.Like, "%" + temp + "%");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var res = service.RetrieveMultiple(query);
                if (res == null || res.Entities == null || res.Entities.Count <= 0)
                    flag = false;
                foreach (var item in b2b_country.Split(','))
                {
                    int result = 0;
                    var temp = item;
                    if (int.TryParse(item, out result))
                    {
                        temp = GetCountryCode(item).ToLower();
                    }
                    if (string.IsNullOrEmpty(temp)) return false;
                    string new_countrys = res[0].GetAttributeValue<string>("new_countrys");
                    var countrysList = new_countrys.Split(',');
                    var f = countrysList.Where(p => p == temp).ToList();
                    if (f != null && f.Count > 0)
                        flag = true;
                    if (flag)
                        break;
                }
                sw.Stop();
                Log.InfoMsg($"判断受理国家和销售地是否联保结束，是否联保{flag}", "GetIsCoinsurance", (int)sw.ElapsedMilliseconds);
                return flag;
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"判断受理国家和销售地是否联保异常:{ex}", "GetIsCoinsurance", (int)sw.ElapsedMilliseconds);
                return false;
            }
        }

        /// <summary>
        /// 根据国家id获取国家编码
        /// </summary>
        /// <param name="country"></param>
        /// <returns></returns>
        public string GetCountryCode(string country)
        {
            if (string.IsNullOrWhiteSpace(country))
                return null;
            Entity e = IMEIInterfacebll.GetCountryEntityByNewId(service, country);
            return e?.GetAttributeValue<string>("new_code");
        }
        /// <summary>
        /// 查询受理地是否在跨境电商配置表中 并且是否允许销售为是
        /// </summary>
        /// <returns></returns>
        public bool GetIsAllow(string imeicountry, string country)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg($"查询受理地是否在跨境电商配置表中 并且是否允许销售:imeicountry {imeicountry},country {country}", "GetIsAllow", 0);
            try
            {
                var new_crosscommerce = GetCache("new_crosscommerce2");
                if (new_crosscommerce is null || new_crosscommerce?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_crosscommerce");
                    qe.ColumnSet = new ColumnSet("new_salesarea", "new_countryid", "new_aftersale");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = service.RetrieveMultiple(qe);

                    SetCache("new_crosscommerce2", ec.Entities.ToList());//插入缓存
                    new_crosscommerce = ec.Entities.ToList();
                }

                new_crosscommerce = new_crosscommerce.Where(m => m.GetAttributeValue<string>("new_countryid") == country && m.GetAttributeValue<OptionSetValue>("new_aftersale").Value == 100000000 && m.GetAttributeValue<string>("new_salesarea") == imeicountry).ToList();
                //QueryExpression qe = new QueryExpression("new_crosscommerce");
                //qe.ColumnSet = new ColumnSet("new_aftersale");
                //qe.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = service.RetrieveMultiple(qe);
                //return ec?.Entities?.Count > 0 && ec.Entities[0].GetAttributeValue<OptionSetValue>("new_aftersale").Value == 100000000 ? true : false;
                sw.Stop();
                Log.InfoMsg($"查询受理地是否在跨境电商配置表中结束，结果{(new_crosscommerce?.Count > 0 ? true : false)}", "GetIsAllow", (int)sw.ElapsedMilliseconds);
                return new_crosscommerce?.Count > 0 ? true : false;
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"查询受理地是否在跨境电商配置表中异常:{JsonConvert.SerializeObject(ex)}", "GetIsAllow", (int)sw.ElapsedMilliseconds);
                return false;
            }
        }

        /// <summary>
        /// 查询销售国家激活国家是否在跨境电商表中
        /// </summary>
        /// <returns></returns>
        public bool GetIsCross(string imeicountry, string country)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Log.InfoMsg($"查询销售国家激活国家是否在跨境电商表中 :销售国家 {imeicountry},激活国家 {country}", "GetIsCross", 0);
            try
            {
                imeicountry = GetCountryId(imeicountry);
                var new_crosscommerce = GetCache("new_crosscommerce1");
                if (new_crosscommerce is null || new_crosscommerce?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_crosscommerce");
                    qe.ColumnSet = new ColumnSet("new_salesarea", "new_name", "new_countryid");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = service.RetrieveMultiple(qe);

                    SetCache("new_crosscommerce1", ec.Entities.ToList());//插入缓存
                    new_crosscommerce = ec.Entities.ToList();
                }
                new_crosscommerce = new_crosscommerce.Where(m => m.GetAttributeValue<string>("new_countryid") == country && m.GetAttributeValue<string>("new_salesarea") == imeicountry).ToList();


                //QueryExpression qe = new QueryExpression("new_crosscommerce");
                //qe.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = service.RetrieveMultiple(qe);

                sw.Stop();
                Log.InfoMsg($"查询销售国家激活国家是否在跨境电商表中，结果{(new_crosscommerce?.Count > 0 ? true : false)}", "GetIsCross", (int)sw.ElapsedMilliseconds);
                return new_crosscommerce?.Count > 0 ? true : false;
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"查询受理地是否在跨境电商配置表中异常:{JsonConvert.SerializeObject(ex)}", "GetIsCross", 0);
                return false;
            }
        }

        /// <summary>
        /// 判断imei是否激活地例外判保
        /// </summary>
        /// <param name="imei"></param>
        /// <returns></returns>
        public bool GetImeiexception(string imei)
        {
            try
            {
                QueryExpression qe = new QueryExpression("new_imeiexception");
                qe.ColumnSet = new ColumnSet("new_name");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, imei);
                var ec = service.RetrieveMultiple(qe);
                return ec?.Entities?.Count > 0 ? true : false;

            }
            catch (Exception ex)
            {
                Log.LogException($"判断imei是否激活地例外判保异常: {JsonConvert.SerializeObject(ex)}", "GetImeiexception",0);
                return false;
            }
        }

        /// <summary>
        /// 判断受理地是否为 激活地判保例外区域
        /// </summary>
        /// <param name="country">受理国家</param>
        /// <returns></returns>
        public bool GetIsException(string country)
        {
            try
            {
                var new_exceptioncountry = GetCache("new_exceptioncountry");
                if (new_exceptioncountry is null || new_exceptioncountry?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_exceptioncountry");
                    qe.NoLock = true;
                    qe.ColumnSet = new ColumnSet("new_code");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = service.RetrieveMultiple(qe);

                    SetCache("new_exceptioncountry", ec.Entities.ToList());//插入缓存
                    new_exceptioncountry = ec.Entities.ToList();
                }
                new_exceptioncountry = new_exceptioncountry.Where(m => m.GetAttributeValue<string>("new_code") == country).ToList();
                //QueryExpression qe = new QueryExpression("new_exceptioncountry");
                //qe.Criteria.AddCondition("new_code", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = service.RetrieveMultiple(qe);
                return new_exceptioncountry?.Count > 0 ? true : false;
            }
            catch (Exception ex)
            {
                Log.LogException($"判断受理地是否为 激活地判保例外区域异常: {JsonConvert.SerializeObject(ex)}", "GetIsException",0);
                return false;
            }
        }

        /// <summary>
        /// 判断是否官方渠道，受理国家与商品编码是否在【官方销售配置表】中
        /// </summary>
        /// <returns></returns>
        public bool GetIsOfficial(string country, string goods_id)
        {
            try
            {
                QueryExpression qe = new QueryExpression("new_salechanneltable");
                qe.NoLock = true;
                qe.Criteria.AddCondition("new_goodsid", ConditionOperator.Equal, goods_id);
                qe.Criteria.AddCondition("new_countryid", ConditionOperator.Like, "%" + country + "%");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var ec = service.RetrieveMultiple(qe);
                return ec.Entities?.Count > 0 ? true : false;
            }
            catch (Exception ex)
            {
                Log.LogException($"判断是否官方渠道，受理国家与商品编码是否在【官方销售配置表】异常: {JsonConvert.SerializeObject(ex)}", "GetIsOfficial", 0);
                return false;
            }
        }


        /// <summary>
        /// 查询GoodsId 是否在国际联保与商品配置表中
        /// </summary>
        /// <param name="goodsid"></param>
        /// <returns></returns>
        public (bool, bool, Guid) IsConfig(string goodsid)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                Log.InfoMsg($"查询GoodsId 是否在国际联保与商品配置表中开始 :goodsid {goodsid}", "IsConfig", 0);
                QueryExpression qe = new QueryExpression("new_intewarrantyequityproduct");
                qe.Criteria.AddCondition("new_productid", ConditionOperator.Equal, goodsid);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity le = new LinkEntity("new_intewarrantyequityproduct", "new_interwarrantysetting", "new_interwarrantysetting_id", "new_interwarrantysettingid", JoinOperator.LeftOuter);
                le.Columns = new ColumnSet("new_isregister", "new_interwarrantysettingid");
                le.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                le.EntityAlias = "le";
                qe.LinkEntities.Add(le);

                var ec = service.RetrieveMultiple(qe);

                bool Item1 = ec?.Entities?.Count > 0 ? true : false;
                bool Item2 = false;
                Guid Item3 = new Guid();
                if (Item1)
                {
                    if (ec.Entities.First().Contains("le.new_isregister"))
                        Item2 = (bool)ec.Entities.First().GetAttributeValue<AliasedValue>("le.new_isregister").Value;

                    if (ec.Entities.First().Contains("le.new_interwarrantysettingid"))
                        Item3 = (Guid)ec.Entities.First().GetAttributeValue<AliasedValue>("le.new_interwarrantysettingid").Value;
                }

                sw.Stop();
                Log.InfoMsg($"查询GoodsId 是否在国际联保与商品配置表中结束,Item1:{Item1},Item2:{Item2},Item3:{Item3}", "IsConfig", (int)sw.ElapsedMilliseconds);
                return (Item1, Item2, Item3);
            }
            catch (Exception ex)
            {
                sw.Stop();
                Log.LogException($"查询GoodsId 是否在国际联保与商品配置表中 异常: {JsonConvert.SerializeObject(ex)}", "GetIsOfficial", (int)sw.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 查询东北欧串号配置表
        /// </summary>
        /// <param name="sn">sn</param>
        /// <returns></returns>
        public (bool, DateTime) GetEunesnmanage(string sn)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try {
                QueryExpression qe = new QueryExpression("new_eunesnmanage");
                qe.NoLock = true;
                qe.ColumnSet = new ColumnSet("new_sellin_time");
                qe.Criteria.AddCondition("new_sn", ConditionOperator.Equal, sn);
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                LinkEntity le = new LinkEntity("new_eunesnmanage", "new_region", "new_region_id", "new_regionid", JoinOperator.Inner);
                le.EntityAlias = "le";
                le.Columns = new ColumnSet("new_code");
                qe.LinkEntities.Add(le);

                var ec = service.RetrieveMultiple(qe);

                if (ec == null || ec.Entities == null || ec.Entities.Count == 0)
                    return (false, new DateTime());

                string new_code = ec.Entities[0].GetAliasAttributeValue<string>("le.new_code");
                //查询北欧区编码
                string sys_easterneuropecode = CommonHelper.GetSystemParameterValue(service, "sys_northerneurope");

                sw.Stop();
                Log.InfoMsg("查询东北欧串号配置表", "GetEunesnmanage", (int)sw.ElapsedMilliseconds);
                if (new_code != sys_easterneuropecode)
                {
                    return (false, new DateTime());
                }
                return (true, ec.Entities[0].GetAttributeValue<DateTime>("new_sellin_time"));
            } catch(Exception ex)
            {
                sw.Stop();
                Log.LogException($"查询东北欧串号配置表异常：{JsonConvert.SerializeObject(ex)}", "GetEunesnmanage", (int)sw.ElapsedMilliseconds);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 设置缓存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public void SetCache(string key, List<Entity> value)
        {
            RedisHelper.SetValue(serviceAdmin, key, JsonConvertHelper.Serialize(value), "WarranyQuery", 3600);
        }
        public List<Entity> GetCache(string key)
        {
            try
            {
                var result = RedisHelper.GetValue(serviceAdmin, key, "WarranyQuery");
                if (!string.IsNullOrWhiteSpace(result))
                    return JsonConvertHelper.DeSerialize<List<Entity>>(result);
                return default;
            }

            catch (Exception ex)
            {
                Log.LogException("GetCache2" + JsonConvert.SerializeObject(ex));
                throw new Exception(ex.Message);
            }

        }
    }
}
