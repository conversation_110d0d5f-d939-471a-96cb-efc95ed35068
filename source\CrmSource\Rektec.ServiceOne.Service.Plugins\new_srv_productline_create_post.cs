﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.OrganizationService.Common.Helper;
using RekTec.Crm.Plugin.Common;
using RekTec.Crm.Plugin.Helper;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RekTec.ServiceOne.Service.Plugins
{
    /// <summary>
    /// 受理物品创建后，创建退货明细
    /// </summary>
    //[RegisterCrmPlugin("Create", "new_srv_productline", PipelineStageEnum.PostOperation, 1)]
    public class new_srv_productline_create_post : PluginBase
    {
        protected override void ExecutePlugin()
        {
            if (!IsCreateMessage || PrimaryEntityName != "new_srv_productline")
                return;
            try
            {
                base.SurePrimaryEntityIs("new_srv_productline");
                base.SurePostOperationStage();
                if (Context.MessageName.ToLower() != "create")
                {
                    return;
                }
                Entity entity = this.Context.InputParameters["Target"] as Entity;
                Log.InfoMsg("创建退货明细栈条数" + this.Context.Depth);
                if (entity == null || this.Context.Depth > 3)
                {
                    return;
                }
                if (!entity.Contains("new_workorder_id") || entity.Attributes["new_workorder_id"] == null)
                {
                    return;
                }
                var order = OrganizationService.RetrieveWithBypassPlugin("new_srv_workorder", entity.GetAttributeValue<EntityReference>("new_workorder_id").Id, new ColumnSet("new_type", "new_refund_amount", "new_returntype", "new_station_id", "new_customerid", "new_productgroup_id", "new_productmodel_id", "new_product"
                                  , "new_userprofile_id", "new_warranty", "new_enddate", "new_buydate", "new_phone"));
                if (order == null)
                    return;
                if (!order.Contains("new_type") || order.GetAttributeValue<OptionSetValue>("new_type").Value != 3)
                    return;
                Guid returnlineId = Guid.Empty;
                LogUseTime(() =>
                {
                    var returnLine = new Entity("new_srv_changereturnline");
                    if (entity.GetAttributeValue<EntityReference>("new_goodsfiles_id") != null)
                    {
                        //根据商品档案找物料，和物料子类别
                        EntityCollection queryList = null;
                        LogUseTime(() =>
                        {
                            var query = new QueryExpression("product");
                            query.ColumnSet = new ColumnSet("productid", "new_materialcategory2_id", "productnumber");
                            query.Criteria.AddCondition("new_goods_id", ConditionOperator.Equal, entity.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id);
                            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                            //是否是商品为是
                            query.Criteria.AddCondition("new_isgoods", ConditionOperator.Equal, true);
                            queryList = OrganizationService.RetrieveMultipleWithBypassPlugin(query);
                        }, "Plugin：查询物料");
                        if (queryList != null && queryList.Entities != null && queryList.Entities.Count > 0)
                        {
                            returnLine["new_product_id"] = new EntityReference("product", queryList.Entities[0].Id);
                            if (queryList.Entities[0].GetAttributeValue<EntityReference>("new_materialcategory2_id") != null)
                            {
                                returnLine["new_materialcategory2_id"] = new EntityReference("new_materialcategory2", queryList.Entities[0].GetAttributeValue<EntityReference>("new_materialcategory2_id").Id);
                            }
                            returnLine["new_productcode"] = queryList.Entities[0].GetAttributeValue<string>("productnumber");
                        }
                        returnLine["new_goodsfiles_id"] = new EntityReference("new_goodsfiles", entity.GetAttributeValue<EntityReference>("new_goodsfiles_id").Id);
                    }
                    returnLine["new_workorder_id"] = new EntityReference("new_srv_workorder", order.Id);
                    returnLine["new_partscost"] = entity.GetAttributeValue<decimal>("new_product_price");
                    var quantity = Convert.ToDecimal(entity.GetAttributeValue<int>("new_applicationnum"));
                    returnLine["new_qty"] = quantity == 0 ? 1 : quantity;
                    returnLine["new_oldimei"] = entity.GetAttributeValue<string>("new_imei");
                    returnLine["new_oldsn"] = entity.GetAttributeValue<string>("new_sn");
                    //无理由 1：良品  质量 2：坏品
                    //returnLine["new_stocktype"] = order.GetAttributeValue<OptionSetValue>("new_returntype");
                    returnlineId = OrganizationService.Create(returnLine);
                }, "Plugin：创建退货明细");
                //把服务网点负责人分派给退货明细
                LogUseTime(() =>
                {
                    if (order.Contains("new_station_id"))
                    {
                        var station = OrganizationServiceAdmin.Retrieve("new_srv_station", order.GetAttributeValue<EntityReference>("new_station_id").Id, new ColumnSet("ownerid"));
                        if (station != null && station.Contains("ownerid"))
                        {
                            CommonHelper.Assign(OrganizationServiceAdmin, "new_srv_changereturnline", returnlineId, station.GetAttributeValue<EntityReference>("ownerid").Id);
                        }
                    }
                }, "Plugin：把服务网点负责人分派给退货明细");
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }


        public void LogUseTime(Action func, string desc)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            func.Invoke();
            sw.Stop();
            Log.InfoMsg_AppInsight($"{desc},耗时:{sw.ElapsedMilliseconds}");
        }
    }
}
