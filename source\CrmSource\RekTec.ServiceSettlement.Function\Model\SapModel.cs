﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RekTec.ServiceSettlement.Function.Model
{
    /// <summary>
    /// 批次
    /// </summary>
    public class sap_batch
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        public long batchId { get; set; }
        /// <summary>
        /// 该批次主表数据量(对应mains结构行数)
        /// </summary>
        public int mainCount { get; set; }
        /// <summary>
        /// 该批次详情表数据量(对应details结构行数，所有mains行里面的details行数相加)
        /// </summary>
        public int detailCount { get; set; }
        /// <summary>
        /// 该批次业务发生时间，不同时间请使用不同批次号yyyyMMdd
        /// </summary>
        public string businessDate { get; set; }
        /// <summary>
        /// 主表记录集合
        /// </summary>
        public List<sap_main> mains { get; set; }
    }
    /// <summary>
    /// 主表
    /// </summary>
    public class sap_main
    {
        /// <summary>
        /// 业务类型，SAP提供
        /// </summary>
        public string businessType { get; set; }
        /// <summary>
        /// 汇总号，10位数字 以sysId开头+8位自增序列
        /// </summary>
        public string summaryId { get; set; }
        /// <summary>
        /// 批次号(同上表的batchId)
        /// </summary>
        public long batchId { get; set; }
        /// <summary>
        /// 业务发生时间
        /// </summary>
        public string businessDate { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string supplier { get; set; }
        /// <summary>
        /// 加盟店客户代码
        /// </summary>
        public string customer { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string company { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string department { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string originalOrder { get; set; }
        /// <summary>
        /// 摘要文本
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 操作用户
        /// </summary>
        public string userId { get; set; }
        /// <summary>
        /// 详情表记录集合
        /// </summary>
        public List<sap_detail> details { get; set; }
        /// <summary>
        /// SAP批次的主键Guid
        /// </summary>
        public string batchguid { get; set; }
    }
    /// <summary>
    /// 明细
    /// </summary>
    public class sap_detail
    {
        /// <summary>
        /// 汇总号，10位数字 以sysId开头+8位自增序列
        /// </summary>
        public string summaryId { get; set; }
        /// <summary>
        /// 行号
        /// </summary>
        public int rowId { get; set; }
        /// <summary>
        /// 虚拟物料
        /// </summary>
        public string sku1 { get; set; }
        /// <summary>
        /// 实际业务SKU
        /// </summary>
        public string sku2 { get; set; }
        /// <summary>
        /// 官报三期成本需求细分物料号
        /// </summary>
        public string sku3 { get; set; }
        /// <summary>
        /// 数量1，没有值请传入0或者不传，请勿传输空串
        /// </summary>
        public string quantity1 { get; set; }
        /// <summary>
        /// 数量2，没有值请传入0或者不传，请勿传输空串
        /// </summary>
        public string quantity2 { get; set; }
        /// <summary>
        /// 商品原价，没有值请传入0或者不传，请勿传输空串
        /// </summary>
        public string originalPrice { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string receivables { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string currency { get; set; }
        /// <summary>
        /// 	批次号
        /// </summary>
        public long batchId { get; set; }
        /// <summary>
        /// 内部订单，发票号码
        /// </summary>
        public string originalOrder { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public string originalOrderDate { get; set; }
        /// <summary>
        /// 	备注文本
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 利润中心编码
        /// </summary>
        public string profitCenter { get; set; }
        /// <summary>
        /// 市场基金分配
        /// </summary>
        public string assignment { get; set; }
        /// <summary>
        /// 贸易伙伴
        /// </summary>
        public string tradePartner { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNumber { get; set; }
        /// <summary>
        /// refer1
        /// </summary>
        public string refer1 { get; set; }
        /// <summary>
        /// refer2
        /// </summary>
        public string refer2 { get; set; }
        /// <summary>
        /// refer3
        /// </summary>
        public string refer3 { get; set; }
        /// <summary>
        /// 辅助核算字段1
        /// </summary>
        public string zzz01 { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string costcenter { get; set; }//不传输到sap中，仓储物流结费用
    }
    public class SapModelCostCenter
    {
        /// <summary>
        /// SKU3
        /// </summary>
        public string new_sku { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string new_costcenter { get; set; }
        /// <summary>
        /// 内部订单号
        /// </summary>
        public string new_internalorder { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoney { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 工单数量
        /// </summary>
        public int new_workordercount { get; set; }
    }
    public class Result
    {
        public ResultHeader header { get; set; }
        public Body body { get; set; }
    }
    public class ResultHeader
    {
        public string code { get; set; }
        public string desc { get; set; }
    }
    public class Body
    {

    }

    public class Param
    {
        public int sap_type { get; set; }
        public Guid expense_claimId { get; set; }
    }
}
