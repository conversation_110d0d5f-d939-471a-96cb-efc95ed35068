﻿//#region 文件描述
/******************************************************************
 ** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved.
 ** 创建人   : KennyZuo
 ** 创建时间 : 2022-5-10 12:31:33
 ** 说明     : 案例
 ******************************************************************/
//#endregion
var formType;
var isproductModel = true;
var userId;
var UserRoleRibbons = [];
var userRoles = [];
var userSettings = null;
// 20241104 zhouqingrui 负责人Id
var ownerId;
//var new_casestatus;
//********优化Start
var new_l1casetype_Record = {};
var new_l2casetype_Record = {};
var new_l3casetype_Record = {};
//********优化End
var workOrderStationIdCallback = false;
var userRoleRibbonsCallback = false;
var workOrderStationIdRel = false;
var loop_Refresh;
var new_productmod_id = '';
var new_productmodel2_id = '';
var startTime = new Date();//开始时间
var entityId = '';//单据id
var new_l1casetype_list = [];//一级案例类型数据
var new_l2casetype_list = [];//二级案例类型数据
var new_l3casetype_list = [];//三级案例类型数据
//登录人与负责人是否为同一部门
var isSameBu = null;

function form_load() {
    GetEntityInfomation();
    //初始化参数
    initParam();
    //设置页面默认值
    setDefaultValue()
    //callto表单
    initCallToRecord();
    // 设置页面的状态
    setPageStatus();
    // 视图
    visibleTab();
    //初始化一、二、三级案例数据
    initCaseType();
    //是否购买产品显隐控制
    isBuyProdCtrl();
    //新建案例埋点
    initCreateIncidentTime();
}

function GetEntityInfomation() {

    top.window.entityId = Xrm.Page.data.getEntity().getId().replace(/[{}]/g, '');
    top.window.entityName = Xrm.Page.data.entity.getEntityName();
}
function hideOptionSetValue() {
    var optionSet = Xrm.Page.ui.controls.get("new_productline");
    var optionSetValues = optionSet.getAttribute().getOptions();
    // 清空选项  
    optionSet.clearOptions();

    // 遍历选项，并添加不需要隐藏的选项  
    optionSetValues.forEach(function (element) {
        if (element.value !== 2) { // 检查是否是要隐藏的选项  
            optionSet.addOption(element); // 仅添加不等于 2 的选项  
        }
    });
}
function initCallToRecord() {
    if (sessionStorage.getItem("phoneCallTriggered") === "true") {
        Xrm.Page.ui.tabs.get("Summary").sections.get("call_to").setVisible(true)
        console.log("电话联络已触发");
    }
    //绑定拨号按钮点击事件
    bindCallClick();
    parent.setCallToPageHide = window.setCallToPageHide = function () {
        Xrm.Page.ui.tabs.get("Summary").sections.get("call_to").setVisible(false);
    }

    parent.deleteSession = window.deleteSession = function () {
        sessionStorage.setItem("phoneCallTriggered", "false");

        //刷新表单
        top.Xrm.Page.data.refresh();
        //刷新Ribbon按钮条
        top.Xrm.Page.ui.refreshRibbon();
    }
}

function form2_load() {
    userId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
    formType = Xrm.Page.ui.getFormType();

    var startTime = new Date();
    var entityId = Xrm.Page.data.entity.getId();
    if (entityId == '') {
        entityId = "empty guid";
    }
    //async call retrieve field value
    processWorkorderStationId(entityId, "form2_load");
    //async get user role bibbon
    getUserRoleRibbonsFormLoad(entityId, "form2_load");

    //#region form load埋点   
    // setTimeout(() => {
    //     formLoadDurationLogging(entityId, startTime, "form2_load");
    // }, 800);
    //#endregion
}

function bindCustomerEvent(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-bindCustomerEvent", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-bindCustomerEvent function");
    /*埋点 Step1 End*/

    hide1casetypeAllSection(isLoad);
    hide2casetypeAllSection(isLoad);
    hideAllSection(isLoad);
    display1casetypeSection(isLoad);

    //Xrm.Page.getAttribute('new_l1casetype_id')?.addOnChange(new_l1casetype_id_onchange);
    //Xrm.Page.getAttribute('new_l2casetype_id')?.addOnChange(new_l2casetype_id_onchange);
    //Xrm.Page.getAttribute('new_l3casetype_id')?.addOnChange(new_l3casetype_id_onchange);

    setTimeout(() => {
        suggest(isLoad);
        critical(isLoad);
    }, 1000);

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}

function setDefaultValue(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-setDefaultValue", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-setDefaultValue function");
    /*埋点 Step1 End*/
    //2024-08-02 p-zhouxin26【来访渠道】=“在线聊天”赋值逻辑
    setLiveChatChannel();
    hideOptionSetValue();
    ///	<summary>设置页面的默认值</summary>
    if (formType == 1) {
        customerid_onchange(isLoad);
        //takeRegion();
        new_productline_load(isLoad);//加载过滤

    }
    else {
        //20230724 p-qilong3 非创建且来源为小米网寄修工单 是否接受满意度问卷 锁定
        var caseSource = Xrm.Page.getAttribute("new_casesource").getValue();
        if (caseSource == '100000006') {
            Xrm.Page.getControl("new_send_questionnaire_case").setDisabled(true);
        }
        //20231122 当优先级为高时，锁定
        var prioritycode = Xrm.Page.getAttribute('prioritycode').getValue();
        if (prioritycode == 1) {
            Xrm.Page.getControl('prioritycode').setDisabled(true);
        }
        filterProvince();
        filterCity();
        filterCounty();
        checkpendingtimeandreason();//校验pending时长和pending原因
        Xrm.Page.getAttribute('new_l1casetype_id').setRequiredLevel("required");
        Xrm.Page.getAttribute('new_l2casetype_id').setRequiredLevel("required");
        Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("required");
        new_productline_load(isLoad);//加载过滤
    }
    setTimeout(() => {
        //设置客户关联数据
        var obj = Xrm.Page.getControl('customerid');
        if (obj != null)
            obj.setEntityTypes(["account"]);
        if (formType == 1)
            getMiContextSetValue(isLoad);  //获取米网上下文，给对应控件赋值
    }, 500);

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/

    //2024-10-08 zhouqingrui 一线坐席不可以修改二线案例表单
    lockSecCaseWhenFrontline();
}

function setPageStatus() {
    ///	<summary>设置页面的状态</summary>
    //2024-08-12 p-zhouxin26【案例分类定义】模块显隐逻辑
    incidentTipsEnableSection();
    //加载刷新危机事件显示
    refreshIncidentProbDescrib();
    //创建时不加载批量上传
    DisplayBatchUpload()
    //修正微软编辑表单保存后，不刷新的问题
    overrideOnSave();
    if (formType != 1) {
        var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
        if (!XM.ISNULL(new_casestatus) && (new_casestatus == 5 || new_casestatus == 3 || new_casestatus == 4 || new_casestatus == 8 || new_casestatus == 9)) {
            XM.disableAll(true);
        }
        //当案例状态（new_casestatus）为 “已转售后服务单”（4）时，显示 解决案例节 p-qilong3 20230822
        if (new_casestatus == 6 || new_casestatus == 4) {
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get("Summary_section_25");
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
        var new_caselevel = Xrm.Page.getControl('header_new_caselevel').getAttribute().getValue();
        if (new_caselevel == 2) {//二线案例
            //二线案例显示pending节，其他不显示
            var section_pending = Xrm.Page.ui.tabs.get("Summary").sections.get("pendinginformation");
            // 当案例状态（new_casestatus）为 “已转售后服务单”（4）时，不显示
            if (new_casestatus == 4) {
                section_pending.setVisible(false);
            }
            else if (!XM.ISNULL(section_pending)) {
                section_pending.setVisible(true);
            }

            var section = Xrm.Page.ui.tabs.get("Summary").sections.get("level2agent");
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
        else {
            //二线案例显示pending节，其他不显示
            var section_pending = Xrm.Page.ui.tabs.get("Summary").sections.get("pendinginformation");
            if (!XM.ISNULL(section_pending)) {
                section_pending.setVisible(false);
            }
        }
    }
}

function getUserRoleRibbonsFormLoad(entityId, formLoadFuncName) {
    var startTime = new Date();
    var userRoleRibbonKey = "userRoleRibbons";
    // var ribbonRoleDM = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + "-GetUserRoleRibbon", entityId, XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + "-GetUserRoleRibbon function：案例页面加载-获取按钮角色");
    //async get user role bibbon
    //p-machengyi 2024.12.24 新增缓存
    var userRoleRibbons = getDataByLocalStorage(userRoleRibbonKey);
    if (userRoleRibbons != null && userRoleRibbons != undefined && userRoleRibbons.value != null && userRoleRibbons.value.length > 0) {
        UserRoleRibbons = userRoleRibbons.value;
        if (workOrderStationIdCallback) {
            Xrm.Page.ui.refreshRibbon();
            console.log("user role ribbon triggered");
        }
        else {
            userRoleRibbonsCallback = true;
        }
    } else {
        XM.GetUserRoleRibbon().then(res => {
            setDataByLocalStorage(userRoleRibbonKey, res)
            UserRoleRibbons = res;
            if (workOrderStationIdCallback) {
                Xrm.Page.ui.refreshRibbon();
                console.log("user role ribbon triggered");
            }
            else {
                userRoleRibbonsCallback = true;
            }
            //#region GetUserRoleRibbon埋点
            // ribbonRoleDM.DURATION = new Date() - startTime;
            // ribbonRoleDM.STRUCTUREDDATA = JSON.stringify(res);
            // top.AILogging.writeLogToAppInsights(ribbonRoleDM, top.AILogging.appInsightLogType().trace);
            //#endregion
            console.log("incident ribbon roles:" + JSON.stringify(res));
        });
    }
}

//process workorder station id custom ribbon button used
function processWorkorderStationId(entityId, formLoadFuncName) {
    //var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    //var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    var startTime = new Date();
    // var workStationDM = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + "-processWorkorderStationId", entityId, XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + "-processWorkorderStationId function：案例页面加载-获取三级案例类型的编码");

    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (new_casestatus != 4 && new_casestatus != 5 && new_casestatus != 6 && new_casestatus != 8 && new_casestatus != 9 && new_caselevel == 2) {
        var new_l3casetype_id = XM.getLookupId("new_l3casetype_id");
        if (new_l3casetype_id != null) {
            //投诉逻辑做了更新，变为可配置了，用tag new_iscomplainttype作为查询tag减少系统负担
            var complaintClick = Xrm.Page.getAttribute("new_iscomplainttype").getValue()
            if (complaintClick != null && complaintClick) {
                workOrderStationIdRel = true;
            }
            if (userRoleRibbonsCallback) {
                Xrm.Page.ui.refreshRibbon();
                console.log("work station id triggered");
            }
            else {
                workOrderStationIdCallback = true;
            }
            //#region form load埋点
            // workStationDM.DURATION = new Date() - startTime;
            // top.AILogging.writeLogToAppInsights(workStationDM, top.AILogging.appInsightLogType().trace);
            //#endregion
            //});
        }
        else {
            workOrderStationIdCallback = true;
        }
    }
    else {
        workOrderStationIdCallback = true;
    }
}
//form loading duration logging
function formLoadDurationLogging(entityId, startTime, formLoadFuncName) {
    // var formLoadDM = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-" + formLoadFuncName, entityId, XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + " function：案例页面加载");

    // formLoadDM.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(formLoadDM, top.AILogging.appInsightLogType().trace);
}

//form loading duration logging
function functionCallAiLogging(entityId, startTime, formLoadFuncName, msgDes) {
    // var formLoadDM = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-" + formLoadFuncName, entityId, XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-" + formLoadFuncName + " function：" + msgDes);

    // formLoadDM.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(formLoadDM, top.AILogging.appInsightLogType().trace);
}

function getCountryCodeForCaseCode(formType, entityId, startTime) {
    //20230606 start
    if (formType === 1) {
        var newCountryId = Xrm.Page.getAttribute('new_country_id').getValue();
        if (!XM.ISNULL(newCountryId)) {
            XM.RetrieveFiledValueAsync('new_countries', newCountryId[0].id.replace('{', '').replace('}', ''), 'new_code').then(res => {
                //Xrm.Page.getAttribute('new_casecode').setValue(res.new_code);
                Xrm.Page.getAttribute("new_casecode").setValue(res.new_code);
                console.log("new_casecode:" + Xrm.Page.getAttribute('new_casecode').getValue());
                //#region form load埋点
                // functionCallAiLogging(entityId, startTime, 'form_load', '异步查询国家编码');
                //#endregion
            });
        }
    }
    //20230606 end
}

//#endregion ONLOAD加载时执行的方法
//自定义保存按钮
function form_onsave(context) {
    var startTime = new Date();
    ///	<summary>页面保存时执行的方法</summary>
    //字段格式校验
    var flag = checkEmailFormat(Xrm.Page.getAttribute('emailaddress').getValue());
    if (!flag) { context.getEventArgs().preventDefault(); return; }
    flag = checkEmailFormat(Xrm.Page.getAttribute('new_email2').getValue());
    if (!flag) { context.getEventArgs().preventDefault(); return; }
    flag = checkPhoneNumber(Xrm.Page.getAttribute('new_phone').getValue());
    if (!flag) {
        context.getEventArgs().preventDefault(); return;
    }
    else {
        var phone = Xrm.Page.getAttribute('new_phone').getValue();
        if (phone != null && phone != '') {
            phone = phone.replace(/\s*/g, "");
            Xrm.Page.getAttribute('new_phone').setValue(phone);
        }
    }
    flag = checkPhoneNumber(Xrm.Page.getAttribute('new_contactnum2').getValue())
    if (!flag) {
        context.getEventArgs().preventDefault(); return;
    }
    else {
        var phone = Xrm.Page.getAttribute('new_contactnum2').getValue();
        if (phone != null && phone != '') {
            phone = phone.replace(/\s*/g, "");
            Xrm.Page.getAttribute('new_contactnum2').setValue(phone);
        }
    }
    flag = checkOnlyNumber(Xrm.Page.getAttribute('new_miid').getValue());
    if (!flag) { context.getEventArgs().preventDefault(); return; }

    var formType = Xrm.Page.ui.getFormType();
    if (formType == 1) {
        var countryId = XM.getLookupId("new_country_id");
        var casetypeId = XM.getLookupId("new_l1casetype_id");

        if (XM.ISNULL(countryId) || XM.ISNULL(casetypeId)) {
            return;
        }
    }
    //#region form load埋点
    var entityId = Xrm.Page.data.entity.getId();
    if (entityId == '') {
        entityId = "empty guid";
    }
    // functionCallAiLogging(entityId, startTime, 'form_onsave', '执行案例保存事件');
    //#endregion
}

/**
 *升级点击事件
 **/
function btnUpgradeClick() {
    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
    }
    else {
        var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
        ////var new_casestatus = rtcrm.getFieldValue(Xrm.Page.data.entity.getId(), XM.getEntitySetName(), 'new_casestatus', true)["new_casestatus"];
        if (new_casestatus == 3) {
            XM.refresh();
        }
        else {

            XM.openConfirmDialog($t("incident.Upgrade", "您是否要将当前案例升级？"), {}, function (confirm) {
                if (!confirm.confirmed) { return; }
                try {
                    var incidentId = Xrm.Page.data.entity.getId();
                    var obj = { incidentId: incidentId };
                    XM.layerLoading();
                    XM.ActionAsync('new_callcenter_service', { Api: 'Incident/Upgrade', Input: JSON.stringify(obj), LangId: Xrm.Page.context.userSettings.languageId }).then((res) => {
                        XM.closeLayerLoading();
                        if (!XM.ISNULL(res?.Output)) {
                            let secondIncidentId = JSON.parse(res.Output);
                            //form_load();
                            //Xrm.Page.data.save();
                            XM.refresh();
                            setTimeout(function () {
                                try {
                                    if (Microsoft.Apm?.getFocusedSession()?.canCreateTab()) {
                                        Microsoft.Apm.createTab({
                                            templateName: 'msdyn_entityrecord',
                                            appContext: new Map().set('entityName', 'incident').set('entityId', secondIncidentId),
                                            isFocused: true
                                        });
                                    } else {
                                        Xrm.Navigation.openForm({
                                            entityName: "incident",
                                            entityId: secondIncidentId
                                        });
                                    }
                                } catch (e) {
                                    console.log(e);
                                }
                            }, 500);
                        }
                    }).catch((err) => {
                        XM.closeLayerLoading();
                        XM.openAlertDialog(err, function () { XM.refresh(); });
                    });
                } catch (e) {
                    XM.closeLayerLoading();
                    XM.openAlertDialog(e, function () { XM.refresh(); });
                }
            }, function () {
                console.log('errorcallBack');
            });
        }
    }
}
/**
 * 升级按钮是否启用
 **/
function btnUpgradeRule() {
    try {
        //解决案例后更新new_status值
        var header_new_casestatus = Xrm.Page.getControl('header_new_casestatus').getAttribute().getValue();
        if (header_new_casestatus != 6) {
            var statecode = Xrm.Page.getAttribute("statecode").getValue();
            if (statecode == 1) {
                Xrm.Page.getControl('header_new_casestatus').getAttribute().setValue(6);
                Xrm.Page.data.save();
            }
        }
        //var isshow = getCanLocal("new.incident.Button.upgrade");
        //if (!isshow) {
        //    return false;
        //}
        if (!getCanLocal("new.incident.Button.upgrade")) {
            return false;
        }
        if (!UserRoleRibbons.includes("new.incident.Button.upgrade"))
            return false;

        var isEnabled = formType != 1;
        if (isEnabled) {
            var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
            var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
            //var new_casestatus = rtcrm.getFieldValue(Xrm.Page.data.entity.getId(), XM.getEntitySetName(), 'new_casestatus', true)["new_casestatus"];
            if (new_caselevel == 2)
                isEnabled = false;
            else {
                //一级案例 显示：待处理、处理中、已驳回
                if (new_casestatus == 1 || new_casestatus == 2 || new_casestatus == 7)
                    isEnabled = true;
                else
                    isEnabled = false;
            }
        }
        return isEnabled;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
/**
 * 督办提交事件
 **/
function btnSuperviseClick() {
    try {
        var formEntity = { entityType: "incident", id: Xrm.Page.data.entity.getId(), name: Xrm.Page.getAttribute('title').getValue() };
        //案例负责人
        var owner = Xrm.Page.getAttribute('ownerid').getValue();

        var params = { new_tasktype: 1, new_supervisedp: owner };
        Xrm.Utility.openQuickCreate('task', formEntity, params).then(
            function () { XM.refresh(); },
            function () { }
        );
    } catch (e) {
        Xrm.Utility.alertDialog(e);
    }
}
/**
 * 督办是否启用
 **/
function btnSuperviseRule() {
    try {
        //var isshow = getCanLocal("new.incident.Button.Supervise")
        //if (!isshow) {
        //    return false;
        //}
        if (!getCanLocal("new.incident.Button.Supervise")) {
            return false;
        }
        if (!UserRoleRibbons.includes("new.incident.Button.Supervise"))
            return false;

        var isEnabled = formType != 1;
        if (isEnabled) {
            var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
            var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
            ////var new_casestatus = rtcrm.getFieldValue(Xrm.Page.data.entity.getId(), XM.getEntitySetName(), 'new_casestatus', true)["new_casestatus"];
            if (new_caselevel == 1) {
                //一级案例 显示：待处理、处理中、已驳回、已转售后服务单
                if (new_casestatus == 1 || new_casestatus == 2 || new_casestatus == 4 || new_casestatus == 7)
                    isEnabled = true;
                else
                    isEnabled = false;
            }
            else {
                //二级案例 显示：待处理、处理中、已转售后服务单、已转危机事件
                if (new_casestatus == 1 || new_casestatus == 2 || new_casestatus == 4 || new_casestatus == 5 || new_casestatus == 8 || new_casestatus == 9)
                    isEnabled = true;
                else
                    isEnabled = false;
            }
        }
        return isEnabled;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}

//隐藏所有一级案例关联的节
async function hide1casetypeAllSection(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-hide1casetypeAllSection", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-hide1casetypeAllSection function");
    // /*埋点 Step1 End*/

    for (var i = 0; i < new_l1casetype_list.length; i++) {
        if (XM.ISNULL(new_l1casetype_list[i].new_code)) {
            continue;
        }
        var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_l1casetype_list[i].new_code);
        if (!XM.ISNULL(section)) {
            section.setVisible(false);
        }
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//隐藏所有二级案例关联的节
async function hide2casetypeAllSection(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-hide2casetypeAllSection", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-hide2casetypeAllSection function");
    /*埋点 Step1 End*/

    for (var i = 0; i < new_l2casetype_list.length; i++) {
        var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_l2casetype_list[i].new_code);
        if (!XM.ISNULL(section)) {
            section?.setVisible(false);
        }
    }

    var criticalClick = Xrm.Page.getAttribute("new_iscriticaltype").getValue();
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    if (!XM.ISNULL(new_l2casetype_id)) {
        //********优化Start
        var new_code = new_l2casetype_Record?.new_code;
        if (new_code == undefined || !isLoad) {
            var l2CaseTypeData = new_l2casetype_list.filter(p => p.new_l2casetypeid == new_l2casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            if (l2CaseTypeData.length > 0) {
                new_l2casetype_Record.new_code = l2CaseTypeData[0].new_code || '';
            } else {
                new_l2casetype_Record.new_code = '';
            }
            new_code = new_l2casetype_Record?.new_code;
            if (new_code != "comemr") {
                var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
                if (!XM.ISNULL(section)) {
                    section.setVisible(true);
                }
            }
        }
        //********优化End
        else {
            if (new_code.toLowerCase() != "comemr") {
                var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
                if (!XM.ISNULL(section)) {
                    section.setVisible(true);
                }
            }
        }

        if ((new_code.toLowerCase() == "comsen" || new_code.toLowerCase() == "comemr") || criticalClick == true) {
            new_code = "comemr";
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//隐藏所有三级案例关联的节
async function hideAllSection(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-hideAllSection", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-hideAllSection function");
    /*埋点 Step1 End*/

    if (new_l3casetype_list.length <= 0) {
        var fetchXML = "<fetch mapping='logical' distinct='true' version='1.0' >" +
            "<entity name = 'new_l3casetype'> " +
            "<filter> " +
            "<condition attribute='statecode' operator='neq' value='1' />  " +
            "</filter> " +
            "<attribute name = 'new_code' /> " +
            "<attribute name = 'new_l3casetypeid' /> " +
            "<attribute name = 'new_l2casetype_id' /> " +
            "</entity> " +
            "</fetch>";
        new_l3casetype_list = await XM.FetchAsync(fetchXML, 'new_l3casetypes');
    }

    for (var i = 0; i < new_l3casetype_list.length; i++) {
        var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_l3casetype_list[i].new_code);
        if (!XM.ISNULL(section)) {
            section.setVisible(false);
        }
    }

    var new_l3casetype_id = Xrm.Page.getAttribute('new_l3casetype_id').getValue();
    if (!XM.ISNULL(new_l3casetype_id)) {
        //********优化Start
        var new_code = new_l3casetype_Record?.new_code;
        if (new_code == undefined || !isLoad) {
            var l3CaseTypeData = new_l3casetype_list.filter(p => p.new_l3casetypeid == new_l3casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            if (l3CaseTypeData.length > 0) {
                new_l3casetype_Record.new_code = l3CaseTypeData[0].new_code || '';
            } else {
                new_l3casetype_Record.new_code = '';
            }
            new_code = new_l3casetype_Record?.new_code;

            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
        //********优化End
        else {
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }

    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//一级案例建议节的显隐
async function display1casetypeSection(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-display1casetypeSection", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-display1casetypeSection function");
    /*埋点 Step1 End*/
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    if (!XM.ISNULL(new_l1casetype_id)) {
        //********优化Start
        var new_code = new_l1casetype_Record?.new_code;
        if (new_code == undefined || !isLoad) {
            var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());

            setValueLv1CaseType(l1CaseTypeData);
            new_code = new_l1casetype_Record?.new_code;
            if(new_casestatus != 6){
                Xrm.Page.getAttribute('new_lv1casetypecode').setValue(new_l1casetype_Record.new_typename);
            }
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
        //********优化End
        else {
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
function formatDate(now) {
    var year = now.getFullYear();
    var month = now.getMonth() + 1;
    var date = now.getDate();
    var hour = now.getHours();
    var minute = now.getMinutes();
    var second = now.getSeconds();
    return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
}
//维保信息&售后服务单按钮的点击事件
function btnMaintenanceInfoAndWorkOrderClick() {
    //案例编码
    var incidentId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    if (XM.ISNULL(incidentId)) {
        return;
    }

    //#region 取值
    var incidents;
    if (Xrm.Page.getAttribute('new_country_id') == null) {
        incidents = XM.RetrieveFiledValue('incidents', Xrm.Page.data.entity.getId(), '_new_country_id_value,_new_region_id_value,_new_province_id_value,_new_city_id_value,new_isinvoice,new_invoicenum,new_invoicetime,new_custaddress');
    }
    //国家
    var new_country_id = Xrm.Page.getAttribute('new_country_id')?.getValue() ? Xrm.Page.getAttribute('new_country_id').getValue()[0].id : incidents?._new_country_id_value;
    if (XM.ISNULL(new_country_id)) {
        new_country_id = "";
    }
    //imei号
    var new_imei = Xrm.Page.getAttribute('new_imei').getValue();
    //SN号
    var new_sn = Xrm.Page.getAttribute('new_sn').getValue();
    //是否有发票
    var new_isinvoice = Xrm.Page.getAttribute('new_isinvoice')?.getValue() || incidents?.new_isinvoice || '';
    //发票号码
    var new_invoicenum = Xrm.Page.getAttribute('new_invoicenum')?.getValue() || incidents?.new_invoicenum || '';
    //发票时间
    var new_invoicetime = Xrm.Page.getAttribute('new_invoicetime')?.getValue() || incidents?.new_invoicetime || '';
    if (XM.ISNULL(new_invoicetime)) {
        new_invoicetime = "";
    }
    else {
        new_invoicetime = formatDate(new_invoicetime);
    }
    //客户姓名  
    var accountname = Xrm.Page.getAttribute('accountname')?.getValue();
    if (XM.ISNULL(accountname)) {
        accountname = "";
    }
    else {
        accountname = accountname.id;
    }
    //contact number
    var new_phone = Xrm.Page.getAttribute('new_phone').getValue();
    //Email Address
    var emailaddress = Xrm.Page.getAttribute('emailaddress').getValue();
    //性别
    var new_sex = Xrm.Page.getAttribute('new_sex')?.getValue();
    //区域  
    var new_region_id = Xrm.Page.getAttribute('new_region_id')?.getValue() ? Xrm.Page.getAttribute('new_region_id').getValue()[0].id : incidents?._new_region_id_value;
    if (XM.ISNULL(new_region_id)) {
        new_region_id = "";
    }
    //Provinces
    var new_province_id = Xrm.Page.getAttribute('new_province_id')?.getValue() ? Xrm.Page.getAttribute('new_province_id').getValue()[0].id : incidents?._new_province_id_value;
    if (XM.ISNULL(new_province_id)) {
        new_province_id = "";
    }
    //City of origin
    var new_city_id = Xrm.Page.getAttribute('new_city_id')?.getValue() ? Xrm.Page.getAttribute('new_city_id').getValue()[0].id : incidents?._new_city_id_value;
    if (XM.ISNULL(new_city_id)) {
        new_city_id = "";
    } 
    //Counties 
    var new_county_id = Xrm.Page.getAttribute('new_county_id')?.getValue() ? Xrm.Page.getAttribute('new_county_id').getValue()[0].id : '';
    if (XM.ISNULL(new_county_id)) {
        new_county_id = "";
    } 
    //详细地址
    var new_custaddress = Xrm.Page.getAttribute('new_custaddress')?.getValue() || incidents?.new_custaddress || '';
    //自营订单号
    var new_miordercode = Xrm.Page.getAttribute('new_miordercode').getValue();
    //#endregion
    
    var servicemode = Xrm.Page.getAttribute('new_servicemode');
    //来源
    var new_casesource = Xrm.Page.getAttribute('new_casesource')?.getValue();
    //用户期望上门时间
    var new_expvisitdate = Xrm.Page.getAttribute('new_expvisitdate')?.getValue();
    //校验案例上【服务方式】是否已填写
    /*
    if (!servicemode?.getValue()) {
        XM.openConfirmDialog({
            title: $t('ConfirmPrompt', '提示'),
            text: $t('incident.validServiceMode', '请您在创建工单前填写【服务方式】'),
            confirmButtonLabel: $t('common.btn.confirm', '确认'),
            cancelButtonLabel: $t('incident.btn.neednotcreateworkorder', '无需建单'),
        }, {}, function success(success) {
            if (success.confirmed) {
                //案例上【服务方式】更新为必填
                servicemode?.setRequiredLevel('required');
            } else {
            }
        });
        return;
    }
    */

    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t('incident.SaveReceipt', '请先保存单据！'));
        return;
    }
    var pageInput = {
        pageType: "webresource", 
        webresourceName: "new_/Service/new_srv_workorderdetail.html",
        data: encodeURIComponent("incidentid=" + incidentId + "&new_imei=" + new_imei + "&new_isinvoice=" + new_isinvoice + "&new_invoicenum=" + new_invoicenum
            + "&new_sn=" + new_sn + "&new_miordercode=" + new_miordercode + "&new_invoicetime=" + new_invoicetime + "&new_casesource=" + new_casesource),
    }
    var navigationOptions = {
        title: this.$t("incident.MaintenanceInformation", "售后服务单"),
        target: 2,
        width: 1800,
        height: 600,
        position: 1
    };
    Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
        function success(result) {
            // 20250521 转售后工单跳转问题，回传值改为全局变量 zhouqingrui1
            let res = parent.window.returnValue;
            console.log(res);
            //console.log(window.parent.returnValue);
            //console.log(res.returnValue);
            res = res.returnValue || res;
            XM.refresh();
            if (res.new_srv_workorderid) {
                setTimeout(function () {
                    try {
                        if (top.Microsoft.Apm?.getFocusedSession()?.canCreateTab()) {
                            top.Microsoft.Apm.createTab({
                                templateName: 'msdyn_entityrecord',
                                appContext: new Map().set('entityName', 'new_srv_workorder').set('entityId', res.new_srv_workorderid),
                                isFocused: true
                            });
                        }
                        // 20250521 转售后工单跳转问题 zhouqingrui1
                        setTimeout(function () {
                            var windowOptions = { openInNewWindow: false };
                            Xrm.Utility.openEntityForm("new_srv_workorder", res.new_srv_workorderid, null, windowOptions);
                        }, 500);
                        //创建解决案例
                        createIncidentSolution(incidentId, 4);
                    } catch (e) {
                        console.log(e);
                    }
                }, 500);
            }
            parent.window.returnValue = null;
        },
        function error(e) {
            XM.openAlertDialog(e);
        });
}
//维保信息&售后服务单按钮的显隐
function btnMaintenanceInfoAndWorkOrderEnabled() {
    if (formType == 1)
        return false;
    //var isSupervisor = getCanLocal("new.incident.Button.MaintenanceInfoAndWorkOrder");
    //if (!isSupervisor)
    //    return false
    if (!getCanLocal("new.incident.Button.MaintenanceInfoAndWorkOrder")) {
        return false;
    }
    if (!UserRoleRibbons.includes("new.incident.Button.MaintenanceInfoAndWorkOrder"))
        return false;

    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    // 2024-10-25 zhouqingrui 一线坐席不能操作二线案例
    if (checkFrontLineEditSecond(new_caselevel)) {
        return false;
    }
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (!XM.ISNULL(new_caselevel)) {
        if (new_caselevel == 1) {
            if (!XM.ISNULL(new_casestatus)) {
                if (new_casestatus == 2 || new_casestatus == 7) {
                    return true;
                }
                else {
                    return false;
                }
            }
            else {
                return false;
            }
        }
        else {
            if (!XM.ISNULL(new_casestatus)) {
                if (new_casestatus == 2) {
                    return true;
                }
                else {
                    return false;
                }
            }
            else {
                return false;
            }
        }
    }
    else {
        return false;
    }
}
//转危机事件按钮的点击事件
function btnConvertToCrisisEventClick() {
    /*埋点 Step1 Start*/
    var utc_startTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
    // var aiLogDataModelV2 = new top.AILogging.aiLogDataModelV2(Xrm.Page.data.entity.getId().replace('{', '').replace('}', ''), "案例", "new_incident.js", "function btnConvertToCrisisEventClick");
    /*埋点 Step1 End*/
    var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    if (!XM.ISNULL(entityId)) {
        if (Xrm.Page.data.entity.getIsDirty()) {
            XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
            return;
        }
        XM.openConfirmDialog($t("incident.CurrentCase", "您是否要将当前案例转为危机事件？"), {}, function (confirm) {
            if (!confirm.confirmed) { return; }
            try {
                var criticalClick = Xrm.Page.getAttribute("new_iscriticaltype").getValue();
                if (criticalClick) {
                    var parameters =
                        {
                            incidentId: entityId
                        }
                    XM.layerLoading();
                    XM.ActionAsync('new_callcenter_service', { Api: 'Incident/ConvertToCrisisEvent', Input: JSON.stringify(parameters), LangId: Xrm.Page.context.userSettings.languageId }).then(res => {
                        XM.closeLayerLoading();
                        if (!XM.ISNULL(res?.Output)) {
                            /*埋点 Step2 Start*/
                            var utc_endTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
                            // aiLogDataModelV2.Duration = new Date(utc_endTime) - new Date(utc_startTime);
                            // aiLogDataModelV2.Msg = "案例转危机事件按钮点击完成";
                            // aiLogDataModelV2.Level = top.AILogging.appInsightsLogLevel().Informational;
                            // aiLogDataModelV2.Datetime = utc_endTime;
                            // aiLogDataModelV2.RecordId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                            // aiLogDataModelV2.LoginUserId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
                            // top.AILogging.writeLogToAppInsightsV2(aiLogDataModelV2, top.AILogging.appInsightLogType().trace);
                            /*埋点 Step2 End*/
                            var new_criticalId = JSON.parse(res.Output);
                            XM.refresh();
                            try {
                                //workspace打开table, 其他的打开session
                                if (Microsoft.Apm?.getFocusedSession()?.canCreateTab()) {
                                    Microsoft.Apm.createTab({
                                        templateName: 'msdyn_entityrecord',
                                        appContext: new Map().set('entityName', 'new_critical').set('entityId', new_criticalId),
                                        isFocused: true
                                    });
                                } else {
                                    Xrm.Navigation.openForm({
                                        entityName: "incident",
                                        entityId: secondIncidentId
                                    });
                                }
                            } catch (e) {
                                console.log(e);
                            }
                        }
                    }).catch(err => {
                        XM.closeLayerLoading();
                        XM.openAlertDialog(err);
                    });
                }
                else {
                    XM.openAlertDialog(this.$t("incident.Performed", "您选择的案例类型不是危机事件，无法进行此操作"));
                }
            } catch (e) {
                XM.openAlertDialog(e);
            }
        });
    }
}
//转危机事件按钮的显隐
function btnConvertToCrisisEventEnabled() {
    if (formType == 1)
        return false;
    //var isSupervisor = getCanLocal("new.incident.Button.ConvertToCrisisEvent");
    //if (!isSupervisor)
    //    return false
    if (!getCanLocal("new.incident.Button.ConvertToCrisisEvent")) {
        return false;
    }
    if (!UserRoleRibbons.includes("new.incident.Button.ConvertToCrisisEvent"))
        return false;

    //案例等级
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    //案例状态
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (XM.ISNULL(new_caselevel) || XM.ISNULL(new_casestatus))
        return false;
    //一级案例
    if (new_caselevel == 1)
        return false;
    //显隐Tag
    var criticalClick = Xrm.Page.getAttribute("new_iscriticaltype").getValue();
    //1待处理4已转售后服务单5已转危机事件6已结案7已驳回
    if (new_casestatus == 1 || new_casestatus == 4 || new_casestatus == 5 || new_casestatus == 6 || new_casestatus == 7)
        return false;
    else if (criticalClick != null && criticalClick)
        return true;
}
//解决案例按钮的显隐
function btnResolveCaseEnabled() {
    if (formType == 1)
        return false;
    //案例等级
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    //案例状态
    //var new_casestatus = rtcrm.getFieldValue(Xrm.Page.data.entity.getId(), XM.getEntitySetName(), 'new_casestatus', true)["new_casestatus"];
    if (XM.ISNULL(new_caselevel) || XM.ISNULL(new_casestatus))
        return false;
    //一级案例
    if (new_caselevel == 1) {
        //2处理中 7已驳回 显示
        if (new_casestatus == 2 || new_casestatus == 7)
            return true;
        else
            return false;
    }
    else {
        //2显示  7已驳回 显示 //202306 二级案例结案修改
        if (new_casestatus == 2 || new_casestatus == 7) {
            //return getCanLocal("Mscrm.Form.incident.SaveAndClose");
            if (!getCanLocal("Mscrm.Form.incident.SaveAndClose")) {
                return false;
            }
            return UserRoleRibbons.includes("Mscrm.Form.incident.SaveAndClose");
        }
        else {
            return false;
        }
    }
}
//受理并保存按钮的点击事件
function btnAcceptAndSaveClick() {
    // 2024.12.30 p-machengyi 大家电案例走新逻辑
    //渠道
    var new_channel = Xrm.Page.getAttribute('new_channel').getValue();
    //来源
    var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
    if (new_channel == 5 && (new_casesource == 7 || new_casesource == 8 || new_casesource == 9)) {
        OpenBatchPage(2);
    } else {
        acceptAndSave();
    }
}
function acceptAndSave() {
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (new_casestatus != 1) {
        XM.openAlertDialog($t("incident.ChangeCasestatus", "当前单据的状态已改变！"), function () { XM.refresh(); });
        return;
    }

    var userId = Xrm.Page.context.getUserId().replace("{", "").replace("}", "");
    var userName = XM.RetrieveFiledValue('systemusers', Xrm.Page.context.getUserId(), 'yomifullname').yomifullname;
    Xrm.Page.getAttribute('ownerid').setValue([{ entityType: 'systemuser', id: userId, name: userName }]);
    Xrm.Page.getAttribute('new_casestatus').setValue(2);
    if (new_caselevel === 2) {
        Xrm.Page.getAttribute('new_acceptime').setValue(XM.getTimeNowForCurrentUserTimeZone());
        var createdon = XM.RetrieveFiledValue(XM.getEntitySetName(), Xrm.Page.data.entity.getId(), 'createdon').createdon;
        var cDate = createdon.replace('T', ' ').replace('Z', '').replace(/-/g, "/");    //2022-05-26T09:26:14Z
        var createdonDate = new Date(cDate);
        var isouttime = checkIsTimeOut(createdonDate, Xrm.Page.getAttribute('new_acceptime').getValue(), 24, 'h');//点击受理并保存，若受理时间-创建时间>24小时，则“是否响应超时”为是
        if (isouttime) {
            Xrm.Page.getAttribute('new_isrespovertime').setValue(true);
        }
        else {
            Xrm.Page.getAttribute('new_isrespovertime').setValue(false);
        }
    }
    //Xrm.Page.data.save().then(function () { XM.refresh(); });
    // 2025/04/02, zhouqingrui, 二线案例保存刷新会出现未保存弹窗
    Xrm.Page.data.save();
}
//受理并保存按钮的显隐
function btnAcceptAndSaveEnabled() {
    if (formType == 1)
        return false;
    //案例等级
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    //渠道
    var new_channel = Xrm.Page.getAttribute('new_channel').getValue();
    var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
    //案例状态
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (XM.ISNULL(new_caselevel) || XM.ISNULL(new_channel) || XM.ISNULL(new_casestatus) || XM.ISNULL(new_casesource))
        return false;
    if (!getCanLocal("Mscrm.Form.incident.AcceptAndSave")) {
        return false;
    }
    if (!UserRoleRibbons.includes("Mscrm.Form.incident.AcceptAndSave"))
        return false;

    if (new_caselevel == 2 && new_casestatus === 1 && userRoles.some(elem => elem === "二线坐席")) {
        return true
    } else if (((new_caselevel == 1 && (new_casesource === 2 || new_casesource === 10))) && new_casestatus === 1) {
        return true;
    } else {
        return false;
    }
}
/**
 * 案例驳回
 **/
function btnRejectClick() {
    try {
        var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
        var userId = Xrm.Page.context.getUserId().replace("{", "").replace("}", "");
        var incidents = XM.RetrieveFiledValue('incidents', entityId, '_ownerid_value');
        if (incidents._ownerid_value.toUpperCase() == userId) {
            var pageInput = {
                pageType: "webresource",
                webresourceName: "new_/incident/reject.html",
                data: encodeURIComponent("incidentId=" + Xrm.Page.data.entity.getId())
            }
            var navigationOptions = {
                title: $t("incident.title.Reject", "案例驳回"),
                target: 2,
                width: 450,
                height: 300,
                position: 1
            };
            Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(function success(res) {
                XM.refresh();
            });
        }
        else {
            XM.openAlertDialog($t("incident.noPermissions", "您不是当前案例的负责人，不能进行此操作!"));
        }
    } catch (e) {
        Xrm.Utility.alertDialog(e);
    }
}
/**
 * 案例驳回显示
 **/
function btnRejectRule() {
    try {
        //var isshow = rtcrm.isRibbonEnabledAccordingToRules("new.incident.Button.Reject");//角色权限，二线坐席才显示
        var isshow = UserRoleRibbons.includes("new.incident.Button.Reject");
        if (!isshow) {
            return false;
        }
        else {
            var isEnabled = formType != 1;
            if (isEnabled) {
                var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
                var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
                var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
                //var new_casestatus = rtcrm.getFieldValue(Xrm.Page.data.entity.getId(), XM.getEntitySetName(), 'new_casestatus', true)["new_casestatus"];
                if (new_caselevel == 1) {
                    isEnabled = false;
                }
                else {
                    //二级案例 显示：待处理、处理中、已转售后服务单、已转危机事件
                    if (new_casestatus == 1)
                        isEnabled = true;
                    else
                        isEnabled = false;
                    if (new_casesource == 7 || new_casesource == 8 || new_casesource == 9)
                        isEnabled = false;
                    if (new_casestatus == 7)
                        XM.disableAll();
                }
            }
            return isEnabled;
        }
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
//客户改变事件
function customerid_onchange(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-customerid_onchange", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-customerid_onchange function");
    /*埋点 Step1 End*/

    var fields = "new_somaccount,new_nickname,new_publicname,new_sex,new_miid,new_contactnum2,new_email2,new_country_id,new_city_id,new_custaddress,new_county_id,new_province_id,new_region_id,emailaddress,new_phone,new_language_id,new_postal_code".split(',');
    for (var i = 0; i < fields.length; i++) {
        Xrm.Page.getAttribute(fields[i])?.setValue(null);
    }
    var customerid = Xrm.Page.getAttribute('customerid').getValue();
    if (!XM.ISNULL(customerid)) {
        if (customerid[0].entityType == "contact") {
            XM.RetrieveFiledValueAsync('contacts', customerid[0].id.replace('{', '').replace('}', ''), '_parentcustomerid_value').then(contacts => {
                if (!XM.ISNULL(contacts._parentcustomerid_value)) {
                    customerid = [{ entityType: 'account', id: contacts._parentcustomerid_value, name: contacts["<EMAIL>"] }];
                    Xrm.Page.getAttribute('customerid').setValue(customerid);
                    customerid_onchange_setAccount(customerid[0].id, isLoad);
                }
            });
        } else {
            customerid_onchange_setAccount(customerid[0].id, isLoad);
        }
    } else {
        //根据客户过滤售后服务单
        filternew_srv_workorder_id(isLoad);
        new_country_id_onchange(isLoad);
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
function customerid_onchange_setAccount(accountid, isLoad) {
    XM.RetrieveFiledValueAsync('accounts', accountid.replace('{', '').replace('}', ''), 'new_somaccount,new_nickname,new_publicname,new_sex,new_mi_id,telephone1,telephone2,emailaddress1,emailaddress2,_new_region_id_value,_new_country_value,_new_province_id_value,_new_city_value,_new_county_value,new_address,_new_language_id_value,new_accountlevel,new_issensitiveuser,address1_postalcode').then(accounts => {
        //社媒账号
        if (!XM.ISNULL(accounts.new_somaccount)) {
            Xrm.Page.getAttribute('new_somaccount').setValue(accounts.new_somaccount);
        }
        //昵称/称呼前缀
        if (!XM.ISNULL(accounts.new_nickname)) {
            Xrm.Page.getAttribute('new_nickname').setValue(accounts.new_nickname);
        }
        //媒体/机构名称
        if (!XM.ISNULL(accounts.new_publicname)) {
            Xrm.Page.getAttribute('new_publicname').setValue(accounts.new_publicname);
        }
        //性别
        if (!XM.ISNULL(accounts.new_sex)) {
            Xrm.Page.getAttribute('new_sex').setValue(accounts.new_sex);
        }
        //米聊号
        if (!XM.ISNULL(accounts.new_mi_id)) {
            Xrm.Page.getAttribute('new_miid').setValue(accounts.new_mi_id);
        }
        //Contact number
        if (!XM.ISNULL(accounts.telephone1)) {
            Xrm.Page.getAttribute('new_phone').setValue(accounts.telephone1);
        }


        //联系电话2
        if (!XM.ISNULL(accounts.telephone2)) {
            Xrm.Page.getAttribute('new_contactnum2').setValue(accounts.telephone2);
        }
        //Email Address
        if (!XM.ISNULL(accounts.emailaddress1)) {
            Xrm.Page.getAttribute('emailaddress').setValue(accounts.emailaddress1);
        }
        //电子邮件地址2
        if (!XM.ISNULL(accounts.emailaddress2)) {
            Xrm.Page.getAttribute('new_email2').setValue(accounts.emailaddress2);
        }
        //详细地址
        if (!XM.ISNULL(accounts.new_address)) {
            Xrm.Page.getAttribute('new_custaddress').setValue(accounts.new_address);
        }
        //语言new.incident.Button.upgrade
        if (!XM.ISNULL(accounts._new_language_id_value)) {
            Xrm.Page.getAttribute('new_language_id').setValue([{ entityType: 'msdyn_oclanguage', id: accounts._new_language_id_value, name: accounts["<EMAIL>"] }]);
        }
        //是否敏感用户 ******** p-qilong3
        new_issensitiveuser = accounts.new_issensitiveuser != null ? accounts.new_issensitiveuser : false;
        Xrm.Page.getAttribute('new_issensitiveuser').setValue(new_issensitiveuser);
        //修改客户信息优先级
        if (!XM.ISNULL(accounts.new_accountlevel) && accounts.new_accountlevel == 2) {
            Xrm.Page.getAttribute('prioritycode').setValue(********);
        }
        else if (new_issensitiveuser) {
            Xrm.Page.getAttribute('prioritycode').setValue(1);
        }
        //优先级 敏感用户为高优先级
        if (new_issensitiveuser) {
            Xrm.Page.getControl('prioritycode').setDisabled(true); //不可编辑
        }
        //邮政编码
        if (!XM.ISNULL(accounts.address1_postalcode)) {
            Xrm.Page.getAttribute('new_postal_code').setValue(accounts.address1_postalcode);
        }
        //区域
        if (!XM.ISNULL(accounts._new_region_id_value)) {
            Xrm.Page.getAttribute('new_region_id').setValue([{ entityType: 'new_region', id: accounts._new_region_id_value, name: accounts["<EMAIL>"] }]);
        }
        //国家
        if (!XM.ISNULL(accounts._new_country_value)) {
            Xrm.Page.getAttribute('new_country_id').setValue([{ entityType: 'new_country', id: accounts._new_country_value, name: accounts["<EMAIL>"] }]);
        }

        //根据客户过滤售后服务单
        filternew_srv_workorder_id(isLoad);
        new_country_id_onchange(isLoad);

        setTimeout(function () {
            //Provinces
            if (!XM.ISNULL(accounts._new_province_id_value)) {
                Xrm.Page.getAttribute('new_province_id').setValue([{ entityType: 'new_province', id: accounts._new_province_id_value, name: accounts["<EMAIL>"] }]);
            }
            //City of origin
            if (!XM.ISNULL(accounts._new_city_value)) {
                Xrm.Page.getAttribute('new_city_id').setValue([{ entityType: 'new_city', id: accounts._new_city_value, name: accounts["<EMAIL>"] }]);
            }
            //Counties
            if (!XM.ISNULL(accounts._new_county_value)) {
                Xrm.Page.getAttribute('new_county_id').setValue([{ entityType: 'new_county', id: accounts._new_county_value, name: accounts["<EMAIL>"] }]);
            }
        }, 500);
    });
}
//建议
async function suggest(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-suggest", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-suggest function");
    /*埋点 Step1 End*/
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    if (!XM.ISNULL(new_l1casetype_id)) {
        //********优化Start
        var new_code = new_l1casetype_Record?.new_code;
        if (new_code == undefined || !isLoad) {
            var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            setValueLv1CaseType(l1CaseTypeData);
            new_code = new_l1casetype_Record?.new_code;
            if (new_code.toLowerCase() == "suggest") {
                Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("none");
            }
            else {
                Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("required");
            }
            if(new_casestatus != 6){
                Xrm.Page.getAttribute('new_lv1casetypecode').setValue(new_l1casetype_Record.new_typename);
            }
        }
        //********优化End
        else {
            if (new_code.toLowerCase() == "suggest") {
                Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("none");
            }
            else {
                Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("required");
            }
        }
    }
    else {
        Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("none");
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//危机事件
async function critical(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-critical", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-critical function");
    /*埋点 Step1 End*/
    var new_iscriticaltype = Xrm.Page.getAttribute('new_iscriticaltype').getValue();
    if (!XM.ISNULL(new_iscriticaltype)) {
        //********优化Start
        //危机事件验证已经更换了动态的方式。
        if (new_iscriticaltype) {
            Xrm.Page.getAttribute('new_ismediarisk').setRequiredLevel("required");
            Xrm.Page.getAttribute('new_isinjury').setRequiredLevel("required");
            Xrm.Page.getAttribute('new_crisisplace').setRequiredLevel("required");
        }
        else {
            Xrm.Page.getAttribute('new_ismediarisk').setRequiredLevel("none");
            Xrm.Page.getAttribute('new_isinjury').setRequiredLevel("none");
            Xrm.Page.getAttribute('new_crisisplace').setRequiredLevel("none");
        }
        //********优化End

    }
    else {
        Xrm.Page.getAttribute('new_l3casetype_id').setRequiredLevel("none");
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//订单查询按钮的点击事件
function btnOrderQueryClick() {
    var incidentId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    var new_miordercode = Xrm.Page.getAttribute('new_miordercode').getValue();//自营订单号
    var new_phone = Xrm.Page.getAttribute('new_phone').getValue();//Contact number
    var emailaddress = Xrm.Page.getAttribute('emailaddress').getValue();//Email Address
    var new_miid = Xrm.Page.getAttribute('new_miid').getValue();//米聊号
    var new_sn = Xrm.Page.getAttribute('new_sn').getValue();//SN号
    // 优先级获取keyword
    var keyword =
        new_miordercode ||
        new_sn ||
        new_miid ||
        new_phone ||
        emailaddress ||
        "";
    if (!XM.ISNULL(incidentId)) {
        deleteOrderQueryStorage();
        Xrm.Page.data.save();
        //web资源地址
        var webresourceurl = "new_/OrderList/cs-customer.html"
        try {
            //Omnichannel 打开页面  配置的模板名称：open_web_resource
            var tabInput = { templateName: "open_web_resource", appContext: new Map().set("webresourceName", webresourceurl).set("data", keyword), isFocused: true };
            // 捕获tab页打开超过8个的异常
            Microsoft.Apm.createTab(tabInput).catch(function (error) {
                if (error.errorCode === 2415919112) {
                    var pageInput = {
                        pageType: "webresource",
                        webresourceName: webresourceurl,
                        data: keyword,
                    }
                    var navigationOptions = {
                        title: this.$t("incident.OrderList", "订单列表"),
                        target: 2,
                        width: 1200,
                        height: 600,
                        position: 1
                    };
                    //使用弹窗的格式打开
                    Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                        function success(res) {
                        },
                        function error(e) {
                            XM.openAlertDialog(e);
                        });
                }
            });;
            setTimeout(function () {
                if (window.top.Microsoft != null && window.top.Microsoft.Apm != null && window.top.Microsoft.Apm.getFocusedSession() != null) {
                    const tab = window.top.Microsoft.Apm.getFocusedSession().getFocusedTab();
                    tab.title = $t("incident.OrderList", "订单列表");
                }
            }, 200);
        } catch (error) {
            var pageInput = {
                pageType: "webresource",
                webresourceName: webresourceurl,
                data: keyword,
            }
            var navigationOptions = {
                title: this.$t("incident.OrderList", "订单列表"),
                target: 2,
                width: 1200,
                height: 600,
                position: 1
            };
            Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                function success(res) {
                    //res = res.returnValue || res;
                    //XM.refresh();
                },
                function error(e) {
                    XM.openAlertDialog(e);
                });
        }
    }
}
/**
 * 描述：删除订单查询页面参数缓存
 * 作者：JasonZhu
 **/
function deleteOrderQueryStorage() {
    //查询参数
    sessionStorage.removeItem("cc_order_query_param");
    //订单号
    sessionStorage.removeItem("cc_order_id");
    //sn串码
    sessionStorage.removeItem("cc_uniquecode");
    //email
    sessionStorage.removeItem("cc_email");
    //电话
    sessionStorage.removeItem("cc_tel");
    //密聊号
    sessionStorage.removeItem("cc_user_id");
}

//订单查询按钮的显隐
function btnOrderQueryEnabled() {
    if (formType == 1) return false;

    if (!getCanLocal("new.incident.Button.OrderQuery")) {
        return false;
    }
    return UserRoleRibbons.includes("new.incident.Button.OrderQuery");
}

//国家改变事件
function new_country_id_onchange(isLoad) {
    //国家
    var new_country_id = Xrm.Page.getAttribute('new_country_id').getValue();
    if (!XM.ISNULL(new_country_id)) {
        XM.RetrieveFiledValueAsync('new_countries', new_country_id[0].id.replace('{', '').replace('}', ''), 'new_code').then(res => {
            Xrm.Page.getAttribute('new_casecode').setValue(res.new_code);

            //#region 国家change后，邮件必填逻辑
            //【服务方式】
            var servicemode = Xrm.Page.getAttribute('new_servicemode')?.getValue() || -1;
            //【服务方式】等于“寄修”or“上门”
            var isServiceMode = [2, 3].indexOf(servicemode) > -1;
            //【国家/地区】等于【法国】、【意大利】、【德国】
            var isCountry = ['FR', 'IT', 'DE'].indexOf(res.new_code) > -1;

            //【服务方式】等于“寄修”or“上门” 和【国家/地区】等于【法国】、【意大利】、【德国】，邮政编码必填
            Xrm.Page.getAttribute('new_postal_code')?.setRequiredLevel(isServiceMode && isCountry ? 'required' : 'none');
            //#endregion

            var fields = "new_province_id,new_city_id,new_county_id".split(',');
            for (var i = 0; i < fields.length; i++) {
                Xrm.Page.getAttribute(fields[i])?.setValue(null);
            }
            takeRegion().then(res => {
                filterProvince();
                filterCity();
                filterCounty();
            });
        });
    }
    else {
        var fields = "new_casecode,new_province_id,new_city_id,new_county_id".split(',');
        for (var i = 0; i < fields.length; i++) {
            Xrm.Page.getAttribute(fields[i])?.setValue(null);
        }
        takeRegion().then(res => {
            filterProvince();
            filterCity();
            filterCounty();
        });
    }
}
//一级案例类型带编码
function new_l1casetype_idcode_onchange(isLoad) {
    //一级案例类型变事件
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if(new_casestatus == 6){
        return;
    }
    if (!XM.ISNULL(new_l1casetype_id)) {
        //********优化Start
        var new_typename = new_l1casetype_Record?.new_typename;
        if (new_typename == undefined || !isLoad) {
            var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            new_typename = new_l1casetype_Record?.new_typename;
            Xrm.Page.getAttribute('new_lv1casetypecode').setValue(new_typename);
        }
        //********优化End
        else {
            Xrm.Page.getAttribute('new_lv1casetypecode').setValue(new_typename);
        }
    }
    else {
        Xrm.Page.getAttribute('new_lv1casetypecode').setValue(null);
    }
}
/**
 * 查询优惠券点击事件
 * 作者：JasonZhu
 **/
function btnQueryCouponClick() {
    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
    }
    else {
        try {
            var new_miid = Xrm.Page.getAttribute('new_miid').getValue();
            var incidentid = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
            //参数
            var param = encodeURIComponent("new_miid=" + new_miid + "&incidentid=" + incidentid);
            //web资源地址
            var webresourceurl = "new_/customer/customerCouponList.html";
            try {
                //Omnichannel 打开页面  配置的模板名称：open_web_resource
                var tabInput = { templateName: "open_web_resource", appContext: new Map().set("webresourceName", webresourceurl).set("data", param), isFocused: true };
                Microsoft.Apm.createTab(tabInput);
                setTimeout(function () {
                    if (window.top.Microsoft != null && window.top.Microsoft.Apm != null && window.top.Microsoft.Apm.getFocusedSession() != null) {
                        const tab = window.top.Microsoft.Apm.getFocusedSession().getFocusedTab();
                        tab.title = $t("incident.window.querycoupon", "查询优惠券");
                    }
                }, 200);
            } catch {
                var pageInput = {
                    pageType: "webresource",
                    webresourceName: webresourceurl,
                    data: param,
                }
                var navigationOptions = {
                    title: $t("incident.window.querycoupon", "查询优惠券"),
                    target: 2,
                    width: 1200,
                    height: 600,
                    position: 1
                };
                Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                    function success(res) {
                        //res = res.returnValue || res;
                        //XM.refresh();
                    },
                    function error(e) {
                        XM.openAlertDialog(e);
                    });
            }
        } catch (e) {
            Xrm.Utility.alertDialog(e);
        }
    }
}
/**
 * 查询优惠券按钮是否启用
 * 作者：JasonZhu
 **/
function btnQueryCouponRule() {
    try {
        if (formType == 1) return false;

        if (!getCanLocal("new.incident.Button.QueryCoupon")) {
            return false;
        }
        return UserRoleRibbons.includes("new.incident.Button.QueryCoupon");

        //var isshow = getCanLocal("new.incident.Button.QueryCoupon");
        //if (!isshow) return false;

        //return true;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}


/**
 * 查询积分点击事件
 * 作者：JasonZhu
 **/
function btnQueryScoreClick() {
    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
    }
    else {
        try {
            var new_miid = Xrm.Page.getAttribute('new_miid').getValue();
            var new_country_id = Xrm.Page.getAttribute('new_country_id')?.getValue() || XM.RetrieveFiledValue('incidents', Xrm.Page.data.entity.getId(), '_new_country_id_value')._new_country_id_value;
            if (XM.ISNULL(new_country_id)) {
                return;
            }
            //参数
            var param = encodeURIComponent("new_miid=" + new_miid + "&countryId=" + new_country_id[0].id + "&countryName=" + new_country_id[0].name);
            console.log(param);
            //web资源地址
            var webresourceurl = "new_/customer/customerPointsList.html";
            try {
                //Omnichannel 打开页面  配置的模板名称：open_web_resource
                var tabInput = { templateName: "open_web_resource", appContext: new Map().set("webresourceName", webresourceurl).set("data", param), isFocused: true };
                Microsoft.Apm.createTab(tabInput);
                setTimeout(function () {
                    if (window.top.Microsoft != null && window.top.Microsoft.Apm != null && window.top.Microsoft.Apm.getFocusedSession() != null) {
                        const tab = window.top.Microsoft.Apm.getFocusedSession().getFocusedTab();
                        tab.title = $t("incident.window.queryscore", "查询积分");
                    }
                }, 200);
            } catch {
                var pageInput = {
                    pageType: "webresource",
                    webresourceName: webresourceurl,
                    data: param,
                }
                var navigationOptions = {
                    title: $t("incident.window.queryscore", "查询积分"),
                    target: 2,
                    width: 1200,
                    height: 600,
                    position: 1
                };
                Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                    function success(res) {
                        //res = res.returnValue || res;
                        //XM.refresh();
                    },
                    function error(e) {
                        XM.openAlertDialog(e);
                    });
            }
        } catch (e) {
            Xrm.Utility.alertDialog(e);
        }
    }
}
/**
 * 查询积分按钮是否启用
 * 作者：JasonZhu
 **/
function btnQueryScoreRule() {
    try {
        if (formType == 1) return false;

        if (!getCanLocal("new.incident.Button.QueryScore")) {
            return false;
        }
        return UserRoleRibbons.includes("new.incident.Button.QueryScore");

        //var isshow = getCanLocal("new.incident.Button.QueryScore");
        //if (!isshow) return false;
        //return true;

    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}

/**
 * 发放优惠券点击事件
 * 作者：JasonZhu
 **/
function btnAssignDiscountClick() {
    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
    }
    else {
        try {
            var title = Xrm.Page.getAttribute('title')?.getValue() || XM.RetrieveFiledValue('incidents', Xrm.Page.data.entity.getId(), 'title').title;
            var incident = [{ entityType: 'incident', id: Xrm.Page.data.entity.getId(), name: title }];

            var params = { new_incident_id: incident, new_accountnumber: Xrm.Page.getAttribute('new_miid').getValue() };
            XM.openForm({ entityName: "new_discount" }, params, function () {
                //XM.refresh();
            });
        } catch (e) {
            Xrm.Utility.alertDialog(e);
        }
    }
}
/**
 * 发放优惠券按钮是否启用
 * 作者：JasonZhu
 **/
function btnAssignDiscountRule() {
    try {
        if (formType == 1) return false;
        //new ribbon button display rule
        if (!getCanLocal("new.incident.Button.AssignDiscount")) {
            return false;
        }
        return UserRoleRibbons.includes("new.incident.Button.AssignDiscount");
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
/**
 * Teams视频点击事件
 * 作者：xiewenyang
 **/
function btn_remoteAppointment() {
    if (Xrm.Page.data.entity.getIsDirty()) {
        XM.openAlertDialog($t("incident.SaveReceipt", "请先保存单据！"));
    }
    else {
        try {
            var title = Xrm.Page.getAttribute('title')?.getValue() || XM.RetrieveFiledValue('incidents', Xrm.Page.data.entity.getId(), 'title').title;
            var incident = [{ entityType: 'incident', id: Xrm.Page.data.entity.getId(), name: title }];
            //查询收件人
            var recipent = Xrm.Page.getAttribute('customerid')?.getValue();
            var RecipentXML = "<fetch version='1.0' mapping='logical'>\
                        <entity name='account'>\
                        <attribute name='emailaddress1'/>\
                        <filter type='and'>\
                        <condition attribute='accountid' operator='eq' value='"+ recipent[0].id.replace(/[{}]/g, '') + "' />\
                        </filter>\
                        </entity>\
                        </fetch>";
            var account = XM.Fetch(RecipentXML, "accounts");
            if(rtcrm.isNull(account[0].emailaddress1)) {
                var recipentemail = Xrm.Page.getAttribute('emailaddress')?.getValue();
                var accountEn = {};
                accountEn.emailaddress1 = recipentemail;
                if (!rtcrm.isNull(recipentemail)) {
                    Xrm.WebApi.updateRecord("account", recipent[0].id, accountEn).then(
                        function success(result) {
                            var updatedId = result.id;
                            console.log(updatedId);
                        },
                        function (error) {
                            console.log(error.message);
                        }
                    );
                }else {
                    XM.openAlertDialog($t("incident.JoinAppointment", "邮件地址为空！请填写邮件地址"));
                    return;
                }
            }
            var userid = Xrm.Page.context.getUserId().replace(/[{}]/g, '');
            //查询发件人
            var SendfetchXML = "<fetch version='1.0' mapping='logical'>\
                        <entity name='new_agentrecord'>\
                        <attribute name='new_emailqueue_id'/>\
                        <filter type='and'>\
                        <condition attribute='new_agentuser_id' operator='eq' value='"+ userid + "' />\
                        </filter>\
                        </entity>\
                        </fetch>";
            var new_agentrecord = XM.Fetch(SendfetchXML, "new_agentrecords");
            var sender = [{ entityType: 'queue', id: new_agentrecord[0]["_new_emailqueue_id_value"], name: new_agentrecord[0]["<EMAIL>"]}];
            var params = { regardingobjectid: incident, subject: title+" "+"Meeting", new_sender: sender, new_recipient: recipent};
            XM.openForm({ entityName: "appointment" }, params, function () {
                //XM.refresh();
            });
        } catch (e) {
            Xrm.Utility.alertDialog(e);
        }
    }
}
/**
 * Teams视频按钮是否启用
 * 作者：xiewenyang
 **/
function btn_remoteAppointmentRule() {
    try {
        var new_casestatus = Xrm.Page.getAttribute('new_casestatus')?.getValue();
        var userid = Xrm.Page.context.getUserId().replace(/[{}]/g, '');

        var fetchXML = "<fetch version='1.0' mapping='logical'>\
                    <entity name='new_agentrecord'>\
                    <attribute name='new_teamslicense'/>\
                    <filter type='and'>\
                    <condition attribute='new_agentuser_id' operator='eq' value='"+ userid + "' />\
                    <condition attribute='statecode' operator='eq' value='0'/>\
                    </filter>\
                    </entity>\
                    </fetch>";
        var new_agentrecord = XM.Fetch(fetchXML, "new_agentrecords");

        if (formType == 1) return false;
        //new ribbon button display rule
        if (!getCanLocal("new.incident.Button.TeamsAppointment")) {
            return false;
        }
        if (new_casestatus === 6) {
            return false;
        }
        if(new_agentrecord != null && new_agentrecord.length > 0 && new_agentrecord[0]["new_teamslicense"]) {
            return true;
        }

    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
/**
 * 新建Team会议预定按钮是否显示
 * 作者：xiewenyang
 **/
function btn_creatAppointmentRule() {
    // 20250430 zhaorongqing 异步获取数据
    return btn_remoteAppointmentRule();
}
/**
 * 描述：获取米网上下文，给对应控件赋值
 * 作者：JasonZhu
 * 时间：2021-11-15
 **/
function getMiContextSetValue() {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-getMiContextSetValue", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-getMiContextSetValue function");
    /*埋点 Step1 End*/

    try {
        if (formType != 1)
            return;
        window.top.Microsoft.Omnichannel.getConversationId().then(
            function success(ConversationId) {
                Xrm.WebApi.online.retrieveMultipleRecords("msdyn_ocliveworkitemcontextitem", "?$select=msdyn_name,msdyn_value&$filter=(_msdyn_ocliveworkitemid_value eq '" + ConversationId + "' and (msdyn_name eq 'OrderId' or msdyn_name eq 'Source'))").then(
                    function success(data) {
                        /*埋点 Step2 Start*/
                        // aiLogDataModel.DURATION = new Date() - startTime;
                        // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
                        /*埋点 Step2 End*/

                        if (data == null || data.entities == null || data.entities.length == 0)
                            return;

                        var orderid = ""; var source = "";
                        for (var i = 0; i < data.entities.length; i++) {
                            if (data.entities[i].msdyn_name == "OrderId")
                                orderid = data.entities[i].msdyn_value;
                            if (data.entities[i].msdyn_name == "Source")
                                source = data.entities[i].msdyn_value;
                            console.log(data.entities[i].msdyn_name + "=" + data.entities[i].msdyn_value);
                        }
                        if (orderid) {
                            Xrm.Page.getAttribute('new_isbuyprod').setValue(true);
                            buyProdShow();
                            Xrm.Page.getAttribute('new_miordercode').setValue(orderid);
                        }
                        if (source)
                            Xrm.Page.getAttribute('new_source').setValue(parseInt(source));
                    },
                    function (error) {
                        console.log(error);
                    });
            },
            function error(error) {
                console.log(error);
            });
    } catch (err) {
        console.log(err);
    }
}

/**
 * 描述：购买产品显示
 * 作者：JasonZhu
 * 时间：2021-11-15
 **/
function buyProdShow() {
    Xrm.Page.getControl('new_purchasename').setVisible(true);
    Xrm.Page.getControl('new_purchasetime').setVisible(true);
    Xrm.Page.getControl('new_ismi').setVisible(true);
    Xrm.Page.getControl('new_miordercode').setVisible(true);
    Xrm.Page.getControl('new_warrantyornot').setVisible(true);
    Xrm.Page.getControl('new_srv_workorder_id').setVisible(true);
    Xrm.Page.getControl('new_saleactivity_id').setVisible(true);
    Xrm.Page.getControl('new_imei').setVisible(true);
    Xrm.Page.getControl('new_sn').setVisible(true);
    Xrm.Page.getControl('new_miui').setVisible(true);
    Xrm.Page.getControl('new_appname').setVisible(true);
    Xrm.Page.getControl('new_appversion').setVisible(true);
}
//Save按钮的显隐
function btnSavePrimaryEnabled() {
    //案例状态为待处理隐藏
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (new_casestatus == 1) {
        return false;
    }
    //案例类型
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (new_caselevel == 2) {
        //return getCanLocal("Mscrm.Form.incident.Save");
        if (!getCanLocal("Mscrm.Form.incident.Save")) {
            return false;
        }
        return UserRoleRibbons.includes("Mscrm.Form.incident.Save");
    }
    else {
        return true;
    }
}
//Save&Close按钮的显隐
function btnSaveAndCloseEnabled() {
    //案例状态为待处理隐藏
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (new_casestatus == 1) {
        return false;
    }
    //案例类型
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (new_caselevel == 2) {
        //return getCanLocal("Mscrm.Form.incident.SaveAndClose");
        if (!getCanLocal("Mscrm.Form.incident.SaveAndClose")) {
            return false;
        }
        return UserRoleRibbons.includes("Mscrm.Form.incident.SaveAndClose");
    }
    else {
        return true;
    }
}


//过滤省份
function filterProvince() {
    var new_country_id = Xrm.Page.getAttribute('new_country_id').getValue();
    var fetchXML = ""
    if (XM.ISNULL(new_country_id)) {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_province'>"
            + "<attribute name='new_provinceid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    else {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_province'>"
            + "<attribute name='new_provinceid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "<condition attribute='new_country_id' operator='eq' value='" + new_country_id[0].id + "' />"
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='new_provinceid'>"
        + "<cell name='new_name' width='150'/>"
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl('new_province_id').addCustomView(XM.NEWID(), 'new_province', '自定义查找', fetchXML, layoutXML, true);
}
//过滤城市
function filterCity() {
    var new_province_id = Xrm.Page.getAttribute('new_province_id').getValue();
    var fetchXML = ""
    if (XM.ISNULL(new_province_id)) {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_city'>"
            + "<attribute name='new_cityid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    else {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_city'>"
            + "<attribute name='new_cityid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "<condition attribute='new_province_id' operator='eq' value='" + new_province_id[0].id + "' />"
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='new_cityid'>"
        + "<cell name='new_name' width='150'/>"
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl('new_city_id').addCustomView(XM.NEWID(), 'new_city', '自定义查找', fetchXML, layoutXML, true);
}
//过滤区县
function filterCounty() {
    var new_city_id = Xrm.Page.getAttribute('new_city_id').getValue();
    var fetchXML = ""
    if (XM.ISNULL(new_city_id)) {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_county'>"
            + "<attribute name='new_countyid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    else {
        fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
            + "<entity name='new_county'>"
            + "<attribute name='new_countyid' />"
            + "<attribute name='new_name' />"
            + "<order attribute='new_name' descending='false' />"
            + "<filter type='and'>"
            + "<condition attribute='statecode' operator='eq' value='0' />  "
            + "<condition attribute='new_city_id' operator='eq' value='" + new_city_id[0].id + "' />"
            + "</filter>"
            + "</entity>"
            + "</fetch>";
    }
    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='new_countyid'>"
        + "<cell name='new_name' width='150'/>"
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl('new_county_id').addCustomView(XM.NEWID(), 'new_county', '自定义查找', fetchXML, layoutXML, true);
}
//带出区域
function takeRegion() {
    return new Promise(function (resolve, reject) {
        var new_country_id = Xrm.Page.getAttribute('new_country_id').getValue();
        if (!XM.ISNULL(new_country_id)) {
            XM.RetrieveFiledValueAsync('new_countries', new_country_id[0].id.replace('{', '').replace('}', ''), '_new_region_id_value').then(new_countries => {
                if (!XM.ISNULL(new_countries._new_region_id_value)) {
                    Xrm.Page.getAttribute('new_region_id').setValue([{ entityType: 'new_region', id: new_countries._new_region_id_value, name: new_countries["<EMAIL>"] }]);
                }
                resolve();
            });
        } else { resolve(); }
    });
}
//带出国家/地区
function takeCountry() {
    var new_province_id = Xrm.Page.getAttribute('new_province_id').getValue();
    if (!XM.ISNULL(new_province_id)) {
        var new_provinces = XM.RetrieveFiledValue('new_provinces', new_province_id[0].id, '_new_country_id_value');
        if (!XM.ISNULL(new_provinces._new_country_id_value)) {
            Xrm.Page.getAttribute('new_country_id').setValue([{ entityType: 'new_country', id: new_provinces._new_country_id_value, name: new_provinces["<EMAIL>"] }]);
        }
        var new_country_id = Xrm.Page.getAttribute('new_country_id').getValue();
        if (!XM.ISNULL(new_country_id)) {
            var new_code = XM.RetrieveFiledValue('new_countries', new_country_id[0].id, 'new_code').new_code;
            Xrm.Page.getAttribute('new_casecode').setValue(new_code);
        }
        else {
            Xrm.Page.getAttribute('new_casecode').setValue(null)
        }
    }
}
//带出省份
function takeProvince() {
    var new_city_id = Xrm.Page.getAttribute('new_city_id').getValue();
    if (!XM.ISNULL(new_city_id)) {
        var new_cities = XM.RetrieveFiledValue('new_cities', new_city_id[0].id, '_new_province_id_value');
        if (!XM.ISNULL(new_cities._new_province_id_value)) {
            Xrm.Page.getAttribute('new_province_id').setValue([{ entityType: 'new_province', id: new_cities._new_province_id_value, name: new_cities["<EMAIL>"] }]);
        }
    }
}
//带出城市
function takeCity() {
    var new_county_id = Xrm.Page.getAttribute('new_county_id').getValue();
    if (!XM.ISNULL(new_county_id)) {
        var new_counties = XM.RetrieveFiledValue('new_counties', new_county_id[0].id, '_new_city_id_value');
        if (!XM.ISNULL(new_counties._new_city_id_value)) {
            Xrm.Page.getAttribute('new_city_id').setValue([{ entityType: 'new_city', id: new_counties._new_city_id_value, name: new_counties["<EMAIL>"] }]);
        }
    }
}
//省份改变事件
function new_province_id_onchange() {
    Xrm.Page.getAttribute('new_city_id').setValue(null);
    Xrm.Page.getAttribute('new_county_id').setValue(null);
    takeCountry();
    takeRegion().then(res => {
        filterProvince();
        filterCity();
        filterCounty();
    });
}
//城市改变事件
function new_city_id_onchange() {
    Xrm.Page.getAttribute('new_county_id').setValue(null);
    takeProvince();
    takeCountry();
    takeRegion().then(res => {
        filterProvince();
        filterCity();
        filterCounty();
    });
}
//区县改变事件
function new_county_id_onchange() {
    takeCity();
    takeProvince();
    takeCountry();
    takeRegion().then(res => {
        filterProvince();
        filterCity();
        filterCounty();
    });
}

/**
 * 描述：对界面邮箱地址以及手机号、米聊号进行格式校验
 * 作者：jankinWang
 * 时间：2022-03-02
 **/
//邮箱改变事件
//对邮箱emailaddress格式进行校验
function emailaddress_onchange() {
    checkEmailFormat(Xrm.Page.getAttribute('emailaddress').getValue());
}
//对邮箱new_email2格式进行校验
function new_email2_onchange() {
    checkEmailFormat(Xrm.Page.getAttribute('new_email2').getValue());
}
//检查邮箱格式
function checkEmailFormat(emailaddress) {
    var flag = true;
    if (emailaddress != null) {
        var regEmail = new RegExp(/^[a-zA-Z0-9_.-]+@([\da-zA-Z\.-]+)\.([a-zA-Z0-9-]+|[\u2E80-\u9FFF]+)$/);
        if (!regEmail.test(emailaddress)) {
            XM.openAlertDialog($t("incident.ErrorEmailFormat", "邮箱格式错误"));
            flag = false;
            //var i = window.parent.document.getElementById("id-b05c1e9c-94d0-46c1-8968-df49b8f33ec7-164-new_contactnum28c10015a-b339-4982-9474-a95fe05631a5-new_contactnum2.fieldControl-phone-text-input");
            //i.style.color = 'red';
        }
    }
    return flag;
}

//手机号改变事件
//对手机号new_phone格式进行校验
function new_phone_onchange() {
    checkPhoneNumber(Xrm.Page.getAttribute('new_phone').getValue());
}
//对手机号new_contactnum2格式进行校验
function new_contactnum2_onchange() {
    checkPhoneNumber(Xrm.Page.getAttribute('new_contactnum2').getValue());
}
//检查手机号格式 +在最前,中间以-分割，可以纯数字
function checkPhoneNumber(value) {
    if ("anonymous" == value) {
        return true;
    }
    var flag = true;
    if (value != null) {
        //var reg_phone = new RegExp("^(\\+\\d{1,}-)?(\\d{1,}-)*\\d{1,}$");
        var reg_phone = /^(\+?)([0-9\(\)\-\s]*)$/;
        if (!reg_phone.test(value)) {
            XM.openAlertDialog($t("incident.ErrorPhoneNumberFormat", "手机号格式错误"));
            flag = false;
        }
    }
    return flag;
}
//米聊号改变事件
//对米聊号new_miid格式进行校验
function new_miid_onchange() {
    checkOnlyNumber(Xrm.Page.getAttribute('new_miid').getValue());
}
function checkOnlyNumber(value) {
    var flag = true;
    if (value != null) {
        var reg = new RegExp(/^[0-9]*$/);//只允许出现数字
        if (!reg.test(value)) {
            XM.openAlertDialog($t("incident.ErrorMiidFormat", "米聊号格式错误"));
            flag = false;

        }
    }
    return flag;
}

//根据客户过滤售后服务单
function filternew_srv_workorder_id(isLoad) {
    try {
        var customerid = Xrm.Page.getAttribute('customerid').getValue();
        if (!XM.ISNULL(customerid)) {
            var fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='new_srv_workorder'>"
                + "<attribute name='new_srv_workorderid' />"
                + "<attribute name='new_name' />"
                + "<attribute name='new_dealstatus' />"
                + "<attribute name='new_station_id' />"
                + "<attribute name='new_model3_id' />"
                + "<order attribute='createdon' descending='true'/>"
                + "<filter type='and'>"
                + "<condition attribute='statecode' operator='eq' value='0' />"
                + "<condition attribute='new_customerid' operator='eq' value='" + customerid[0].id + "' />"
                + "</filter>"
                + "</entity>"
                + "</fetch>";
        }
        else {//若无其他判断逻辑，可不写此else
            var fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='new_srv_workorder'>"
                + "<attribute name='new_srv_workorderid' />"
                + "<attribute name='new_name' />"
                + "<attribute name='new_dealstatus' />"
                + "<attribute name='new_station_id' />"
                + "<attribute name='new_model3_id' />"
                + "<order attribute='createdon' descending='true'/>"
                + "<filter type='and'>"
                + "<condition attribute='statecode' operator='eq' value='0' />"
                + "</filter>"
                + "</entity>"
                + "</fetch>";
        }
        var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
            + "<row name='result' id='new_srv_workorderid'>"
            + "<cell name='new_name' width='150'/>"
            + "<cell name='new_dealstatus' width='150'/>"
            + "<cell name='new_station_id' width='150'/>"
            + "<cell name='new_model3_id' width='150'/>"
            + "</row>"
            + "</grid>";
        Xrm.Page.getControl('new_srv_workorder_id').addCustomView(XM.NEWID(), 'new_srv_workorder', '自定义查找', fetchXML, layoutXML, true);
    }
    catch (ex) {
        console.log(ex);
    }
}
var isCanClick2 = true;
//客诉转售后站长按钮
function customerWebmaster() {
    /*埋点 Step1 Start*/
    var utc_startTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
    // var aiLogDataModelV2 = new top.AILogging.aiLogDataModelV2(Xrm.Page.data.entity.getId().replace('{', '').replace('}', ''), "案例", "new_incident.js", "function customerWebmaster");
    /*埋点 Step1 End*/
    try {
        if (Xrm.Page.data.entity.getIsDirty()) {
            XM.openAlertDialog($t("common.pleaseSave", "请先保存单据！"));
            return;
        }
        var workorderId = XM.getLookupId("new_srv_workorder_id");
        var id = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
        if (!id) {
            Xrm.Utility.alertDialog($t("incident.Assign.CannotIncidentId", "未获取到当前单据的id"));
            return;
        }
        //p-machengyi 20241219 点击按钮时进行验证
        var res = rtcrm.retrieve(`new_srv_complains?$select=new_srv_complainid&$filter=(statecode eq 0 and new_billtype eq 1 and _new_incident_id_value eq ${id})&$top=2`, true);
        //该案例存在反馈单时，隐藏
        if (res && res.value && res.value.length && res.value.length > 0) {
            XM.openAlertDialog($t("incident.existCusComplainData", "当前案例已存在客诉单，请勿重复创建"));
            return;
        }

        if (XM.ISNULL(workorderId)) {
            this.selectStation(id);
        } else {
            var workorder = XM.RetrieveFiledValue('new_srv_workorders', workorderId, '_new_station_id_value');

            if (workorder == null) {
                XM.openAlertDialog($t("incident.workorderNotData", "未获取到售后服务单信息"));
                return;
            }

            var stationId = workorder["_new_station_id_value"];

            if (XM.ISNULL(stationId)) {
                this.selectStation(id);
                return;
            }
            if (isCanClick2) {//判断是否可以点击
                isCanClick2 = false;
                //2025-02-08 p-luozhaoyao 1. 客诉单提示：【一级案例类型】=“售后投诉”，【三级案例类型】=“micon106”、“micom042”、“micom043”或“micon044”时，点击按钮提示如图：
                var isComplaintType = Xrm.Page.getAttribute("new_iscomplainttype").getValue();
                //单据类型 1 反馈单 2、客诉单
                var tickType = 1;
                var webResourceUrl = "";
                var title = "";
                if (isComplaintType) {
                    tickType = 2;
                    title = $t("complain.closeCaseAlertTitle", "确认客诉责任方");
                    webResourceUrl = "new_/Service/new_ComplaintPartyConfirm.html";
                }
                else {
                    title = $t("CallNumber.prompt", "提示");
                    webResourceUrl = "new_/Service/new_ComplaintConfirm.html";
                }
                //弹窗选择客诉责任确认
                var pageInput = {
                    pageType: "webresource",
                    webresourceName: webResourceUrl,
                    data: null
                }
                var navigationOptions = {
                    title: title,
                    target: 2,
                    width: 800,
                    height: 303,
                    position: 1
                };
                //页面返回值
                var returnValue = -1;
                Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                    function success(res) {
                        if (sessionStorage.getItem("complaintParty")) {
                            returnValue = res.returnValue;
                            XM.layerLoading();
                            XM.ActionAsync('new_callcenter_service', { Api: 'OrderStations/CreateComplain', Input: JSON.stringify({ incidentId: id, complainStr: 'station', station_id: stationId, ticketType: tickType, complaintPartyVal: returnValue }), LangId: Xrm.Page.context.userSettings.languageId }).then((res) => {
                                XM.closeLayerLoading();
                                //XM.refresh();
                                if (!XM.ISNULL(res?.Output)) {
                                    /*埋点 Step2 Start*/
                                    var utc_endTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
                                    // aiLogDataModelV2.Duration = new Date(utc_endTime) - new Date(utc_startTime);
                                    // aiLogDataModelV2.Msg = "客诉转售后站长按钮点击完成";
                                    // aiLogDataModelV2.Level = top.AILogging.appInsightsLogLevel().Informational;
                                    // aiLogDataModelV2.Datetime = utc_endTime;
                                    // aiLogDataModelV2.RecordId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                                    // aiLogDataModelV2.LoginUserId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
                                    // top.AILogging.writeLogToAppInsightsV2(aiLogDataModelV2, top.AILogging.appInsightLogType().trace);
                                    /*埋点 Step2 End*/
                                    var result = JSON.parse(JSON.parse(res.Output));
                                    if (result.statusCode == 300) {
                                        XM.closeLayerLoading();
                                        rtcrm.alertDialog(result.message);
                                        return false
                                    }
                                    //var complainId = JSON.parse(res.Output);
                                    //var formId = "";
                                    setTimeout(function () {
                                        try {
                                            var entityFormOptions = {
                                                entityName: "new_srv_complain",
                                                entityId: result.complainId
                                            };
                                            isCanClick2 = true;
                                            XM.openForm(entityFormOptions);
                                        } catch (e) {
                                            isCanClick2 = true;
                                            console.log(e);
                                        }
                                    }, 200);
                                }
                            }).catch((err) => {
                                isCanClick2 = true;
                                XM.openAlertDialog(err);
                                XM.closeLayerLoading();
                            });
                           
                        }
                        else {
                            isCanClick2 = true;
                        }
                    },
                    function error(err) {
                        isCanClick2 = true;
                    }
                );


            }
        }
    } catch (ex) {
        isCanClick2 = true;
        XM.openAlertDialog(ex);
        console.log(ex);
    }
}
//转技术组按钮显隐
function TransToTecEnable() {
    
    return new Promise((resolve, reject) => {
        var formType = Xrm.Page.ui.getFormType();
        if (formType == 1) {//如果是创建，则直接隐藏
            resolve(false);
        }
        var caselevel = Xrm.Page.getAttribute('new_caselevel')?.getValue();
        // 2024-10-25 zhouqingrui 一线坐席不能操作二线案例
        if (checkFrontLineEditSecond(caselevel)) {
            resolve(false);
        }
        var l1casetypeid = Xrm.Page.getAttribute('new_l1casetype_id')?.getValue();

        var record = null;
        var localStorageKey = l1casetypeid[0].id.replace(/[{}]/g, '');
        var caseStatus = Xrm.Page.getAttribute('new_casestatus').getValue();
        //从loaclStorage中获取缓存数据
        var result = getDataByLocalStorage(localStorageKey)
        if (result != null) {
            record = result.value;
            if (caseStatus != 1 && caseStatus != 6 && caseStatus != 8 && caseStatus != 9 && caseStatus != 10) {
                resolve(caselevel == 2 && record.new_typename.toLowerCase() == 'ps');
            } else {
                resolve(false);
            }
        } else {
            Xrm.webApi.retrieveRecord('new_l1casetypes', l1casetypeid[0].id.replace(/[{}]/g, '?$select=new_type_name,new_code'),).then(
                function (response) {
                    setDataByLocalStorage(localStorageKey, response);
                    if (caseStatus != 1 && caseStatus != 6 && caseStatus != 8 && caseStatus != 9 && caseStatus != 10) {
                        resolve(caselevel == 2 && record.new_typename.toLowerCase() == 'ps');
                    } else {
                        resolve(false);
                    }
                },
                function (error) {
                    reject(error);
                }
            );
        }
    });
}

function isGuidEmpty(guid) {
    const emptyGuid = '00000000-0000-0000-0000-000000000000';
    // 修剪 GUID 两边的空格和双引号
    return guid.trim().replace(/"/g, '') === emptyGuid;
}
// 转技术组按钮action
function TransferToTec() {
    var id = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '')

    //p-machengyi 20241219 点击按钮时进行验证
    var res = rtcrm.retrieve(`new_srv_complains?$select=new_srv_complainid&$filter=(statecode eq 0 and new_billtype eq 3 and _new_incident_id_value eq ${id})&$top=2`, true);
    //该案例存在技术反馈单时，隐藏
    if (res && res.value && res.value.length && res.value.length > 0) {
        XM.openAlertDialog($t("incident.existCusComplainData", "当前案例已存在客诉单，请勿重复创建"));
        return;
    }
    var confirmStrings = {
        text: $t('new_srv_complain.transToTech', '确认要转技术组吗？'),
        title: $t('new_srv_complain.confirm', '确认操作')
    }
    var confirmOptions = { height: 200, width: 450 }
    var button = top.document.querySelector('button[data-id="incident|NoRelationship|Form|new.incident.Button.TransToTec"]');
    button.disabled = true;
    // 显示确认对话框
    Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(
        function success(result) {
            if (result.confirmed) {
                // 如果用户点击了确认，则执行API调用
                XM.ActionAsync('new_callcenter_service', {
                    Api: 'OrderStations/CreateComplain',
                    Input: JSON.stringify({
                        incidentId: id,
                        complainStr: 'transToTech',
                        station_id: '',
                        ticketType: 3
                    }),
                    LangId: Xrm.Page.context.userSettings.languageId
                })
                    .then((res) => {
                        var outputValue = res.Output;
                        var result = JSON.parse(JSON.parse(outputValue));
                        if (result.statusCode == 300) {
                            rtcrm.alertDialog(result.message);
                            return false
                        }
                        console.log(outputValue);
                        if (result.complainId != null && !isGuidEmpty(result.complainId)) {
                            Xrm.Page.getAttribute('new_casestatus').setValue(10)
                            Xrm.Page.data.save();
                            // 显示成功提示
                            var successConfirmStrings = {
                                text: $t('new_srv_complain.createFeedbackSucess', '创建技术反馈单成功')
                            }
                            var successConfirmOptions = {
                                height: 100,
                                width: 400
                            }
                            Xrm.Navigation.openConfirmDialog(successConfirmStrings, successConfirmOptions).then(
                                function success(result) {
                                    if (result.confirmed) {
                                        top.document.querySelector(`button[data-id $= 'RefreshModernButton']`).click();
                                        // rtcrm.closeLayerLoading(); // 如果需要关闭加载层
                                    } else {
                                        console.log('用户取消了操作。')
                                    }
                                },
                                function error() {
                                    console.log('弹窗关闭时发生错误。')
                                }
                            )
                            try {
                                var entityFormOptions = {
                                    entityName: 'new_srv_complain',
                                    entityId: result.complainId
                                }
                                XM.openForm(entityFormOptions)
                            } catch (e) {
                                console.log(e)
                            }
                        } else {
                            var alertStrings = { text: $t('new_srv_complain.feedbackHasCreated', '技术反馈单已创建') };
                            var alertOptions = { height: 120, width: 260 };
                            Xrm.Navigation.openAlertDialog(alertStrings, alertOptions).then(
                                function success() {
                                    console.log("Alert dialog closed successfully.");
                                },
                                function error() {
                                    console.log("Error closing alert dialog.");
                                }
                            );
                        }
                    })
                    .catch((ex) => {
                        XM.openAlertDialog(ex)
                        console.log(ex)
                    })
            } else {
                console.log('用户取消了操作。')
                button.disabled = false;
            }
        },
        function error() {
            console.log('弹窗关闭时发生错误。')
            button.disabled = false;
        }
    )
}

function retrieveTypeCode(typeid) {
    return new Promise((resolve, reject) => {
        var localStorageKey = typeid[0].id.replace('{', '').replace('}', '');
        //从loaclStorage中获取缓存数据
        var result = getDataByLocalStorage(localStorageKey)
        if (result != null) {
            const val = result.value;
            resolve(val.new_code);
        } else {
            XM.RetrieveFiledValueAsync('new_l1casetypes', typeid[0].id.replace('{', '').replace('}', ''), 'new_typename,new_code').then(function (res) {
                resolve(res.new_code);
                setDataByLocalStorage(localStorageKey, res)
            }).catch(function (error) {
                reject(error);
            });
        }
    });
}
//2025-02-10 p-luozhaoyao 获取三级案例类型编码 
function retrieveType3Code(typeid) {
    var l3CaseTypeData = new_l3casetype_list.filter(p => p.new_l3casetypeid == typeid[0].id.replace('{', '').replace('}', '').toLowerCase());
    if (l3CaseTypeData.length > 0) {
        return  l3CaseTypeData[0].new_code || '';
    }
    return '';
}
//客诉转售后站长按钮显示隐藏逻辑
async function customerWebmasterBtnEnable() {
    //2024-07-08 p-zhouxin26 【案例状态】=“待处理”和“已驳回”时，按钮隐藏
    var casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if ([1, 7].indexOf(casestatus) > -1) {
        return false;
    }

    var CurrentUserId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
    var userId = Xrm.Page.getAttribute("ownerid").getValue()[0].id.replace("{", "").replace("}", "");
    var caselevel = Xrm.Page.getAttribute("new_caselevel").getValue();
    //if (CurrentUserId != userId || caselevel != 2) {
    //2024-06-06 p-zhouxin26 案例【转服务站站长】按钮的隐藏不需要负责人条件
    if (caselevel != 2) {
        return false;
    }
    // 2024-10-25 zhouqingrui 一线坐席不能操作二线案例
    if (checkFrontLineEditSecond(caselevel)) {
        return false;
    }
    //2024-06-11 p-zhouxin26【案例状态】!=“已结案”or“已转售后服务单”or“已转危机事件”or“已转售后经理”or“已转售后站长”or“已驳回”显示；反之，不显示
    var casestatus = Xrm.Page.getAttribute("new_casestatus").getValue();
    if ([4, 5, 6, 7, 8, 9].indexOf(casestatus) > -1) {
        return false;
    }

    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    if (!XM.ISNULL(new_l1casetype_id)) {
        var new_code = await retrieveTypeCode(new_l1casetype_id);
        //functionCallAiLogging(entityId, startTime, 'form_load', '异步查询一级案例类型编码');
        if (new_code.toLowerCase() == "after sale support") {
            return true;
        }
    }

    //2025-02-08 p-luozhaoyao【一级案例类型】=“售后投诉”，【三级案例类型】=“micon106”、“micom042”、“micom043”或“micon044”时，也显示
    var isComplaintType = Xrm.Page.getAttribute("new_iscomplainttype").getValue();
    var new_l3casetype_id = Xrm.Page.getAttribute("new_l3casetype_id").getValue();
    if (isComplaintType && !XM.ISNULL(new_l3casetype_id)) {
        var new_l3code =  retrieveType3Code(new_l3casetype_id);
        new_l3code = new_l3code.toLowerCase();
        if (new_l3code == "micom106" || new_l3code == "micom042" || new_l3code == "micom043" || new_l3code == "micom044" ) {
            return true;
        }
    }
    return false;
}
/**
 * 客诉转服务经理按钮是否启用
 **/
function serviceManagerBtnEnable() {
    var id = Xrm.Page.data.entity.getId().replace(/[{}]/g, '');

    //2024-07-08 p-zhouxin26 【案例状态】=“待处理”和“已驳回”时，按钮隐藏
    var casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if ([1, 7].indexOf(casestatus) > -1) {
        return false;
    }
    // 2024-10-25 zhouqingrui 一线坐席不能操作二线案例
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (checkFrontLineEditSecond(new_caselevel)) {
        return false;
    }
    var isShow = false;
    try {
        //var isshow = rtcrm.isRibbonEnabledAccordingToRules("new.incident.Button.ServiceManager")
        var isshow = UserRoleRibbons.includes("new.incident.Button.ServiceManager");
        if (!isshow) {
            return false;
        }
        //2025-02-08 p-luozhaoyao  当【三级案例类型】=“micon106”、“micom042”、“micom043”或“micon044”时 不显示
        var new_l3casetype_id = Xrm.Page.getAttribute("new_l3casetype_id").getValue();
        if (!XM.ISNULL(new_l3casetype_id)) {
            var new_l3code =  retrieveType3Code(new_l3casetype_id);
            new_l3code = new_l3code.toLowerCase();
            if (new_l3code == "micom106" || new_l3code == "micom042" || new_l3code == "micom043" || new_l3code == "micom044") {
                return false;
            }
        }
        return workOrderStationIdRel;
        //var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
        //var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
        //if (new_casestatus != 4 && new_casestatus != 5 && new_casestatus != 6 && new_casestatus != 8 && new_casestatus != 9 && new_caselevel == 2) {
        //    var new_l3casetype_id = XM.getLookupId("new_l3casetype_id");
        //    if (new_l3casetype_id != null) {
        //        var workorder_stationId = rtcrm.getFieldValue(new_l3casetype_id.replace("{", "").replace("}", ""), "new_l3casetypes", "new_code", true);
        //        if (workorder_stationId != null && workorder_stationId["new_code"] == "com006") {
        //            isShow = true;
        //        }
        //    }
        //}
        //else {
        //    return false;
        //}
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
    return isShow;
}
var isCanClick = true;
//转售后服务经理
function btnSendCaseToserviceManagerClick() {
    /*埋点 Step1 Start*/
    var utc_startTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
    // var aiLogDataModelV2 = new top.AILogging.aiLogDataModelV2(Xrm.Page.data.entity.getId().replace('{', '').replace('}', ''), "案例", "new_incident.js", "function btnSendCaseToserviceManagerClick");
    /*埋点 Step1 End*/
    if (isCanClick) {//判断是否可以点击
        isCanClick = false;

        var webresourceurl = "new_/Service/new_ComplaintPartyConfirm.html";
        //弹窗选择客诉责任确认
        var pageInput = {
            pageType: "webresource",
            webresourceName: webresourceurl,
            data: null
        }
        var navigationOptions = {
            title: $t("complain.closeCaseAlertTitle", "确认客诉责任方"),
            target: 2,
            width: 800,
            height: 303,
            position: 1
        };
        Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
            function success(res) {
                if (res.returnValue != null) {
                    if (Xrm.Page.data.entity.getIsDirty()) {
                        isCanClick = true;
                        XM.openAlertDialog($t("common.pleaseSave", "请先保存单据！"));
                        return;
                    }
                    var id = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                    if (!id) {
                        isCanClick = true;
                        Xrm.Utility.alertDialog($t("incident.Assign.CannotIncidentId", "未获取到当前单据的id"));
                        return;
                    }
                    var entityId = id;
                    XM.layerLoading();
                    XM.ActionAsync('new_callcenter_service', { Api: 'OrderStations/CreateComplain', Input: JSON.stringify({ incidentId: entityId, complainStr: 'manager', station_id: null, ticketType: 2, complaintPartyVal: res.returnValue }), LangId: Xrm.Page.context.userSettings.languageId }).then((res) => {
                        XM.closeLayerLoading();
                        //XM.refresh();
                        if (!XM.ISNULL(res?.Output)) {
                            /*埋点 Step2 Start*/
                            var utc_endTime = XM.FormatUTCDate(new Date(), "yyyy-MM-dd hh:mm:ss.S");
                            // aiLogDataModelV2.Duration = new Date(utc_endTime) - new Date(utc_startTime);
                            // aiLogDataModelV2.Msg = "转售后服务经理按钮点击完成";
                            // aiLogDataModelV2.Level = top.AILogging.appInsightsLogLevel().Informational;
                            // aiLogDataModelV2.Datetime = utc_endTime;
                            // aiLogDataModelV2.RecordId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                            // aiLogDataModelV2.LoginUserId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
                            // top.AILogging.writeLogToAppInsightsV2(aiLogDataModelV2, top.AILogging.appInsightLogType().trace);
                            /*埋点 Step2 End*/
                            var result = JSON.parse(JSON.parse(res.Output));
                            if (result.statusCode == 300) {
                                rtcrm.alertDialog(result.message);
                                return false
                            }
                            setTimeout(function () {
                                try {
                                    isCanClick = true;
                                    // workspace打开table, 其他的打开session
                                    if (Microsoft.Apm?.getFocusedSession()?.canCreateTab()) {
                                        Microsoft.Apm.createTab({
                                            templateName: 'msdyn_entityrecord',
                                            appContext: new Map().set('entityName', 'new_srv_complain').set('entityId', result.complainId),
                                            isFocused: true
                                        });
                                    } else {
                                        Xrm.Navigation.openForm({
                                            entityName: "new_srv_complain",
                                            entityId: result.complainId
                                        });
                                    }
                                } catch (e) {
                                    console.log(e);
                                    isCanClick = true;
                                }
                            }, 200);
                        }
                        XM.refresh();
                    }).catch((err) => {
                        isCanClick = true;
                        XM.openAlertDialog(err);
                        XM.closeLayerLoading();
                    });
                }
                else {
                    isCanClick = true;
                }
            },
            function error(err) {
                isCanClick = true;
            }
        );
    }
}
//自定义页面选择服务站
function selectStation(id) {
    var param = encodeURIComponent("id=" + id)
    var webresourceurl = "new_/Service/customerWebmaster.html";

    var pageInput = {
        pageType: "webresource",
        webresourceName: webresourceurl,
        data: param,
    }
    var navigationOptions = {
        title: $t("incident.selectStation", "选择服务站"),
        target: 2,
        width: 1200,
        height: 600,
        position: 1
    };
    Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
        function success(res) {
            res = res.returnValue || res;
            console.log(res);
            if (res.result) {
                Xrm.Page.getAttribute('new_casestatus').setValue(9);
                Xrm.Page.data.save();
                console.log("id:" + res.complainId);
                setTimeout(function () {
                    try {
                        var entityFormOptions = {
                            entityName: "new_srv_complain",
                            entityId: res.complainId
                        };
                        isCanClick = true;
                        isCanClick2 = true;
                        XM.openForm(entityFormOptions);
                    } catch (e) {
                        isCanClick = true;
                        isCanClick2 = true;
                        console.log(e);
                    }
                }, 200);
            }
        },
        function error(e) {
            isCanClick = true;
            isCanClick2 = true;
            XM.openAlertDialog(e);
        });
}

//给new_probdescrib问题描述=>即案例录单模板赋值
//清空案例类型值时，已经有的录单模板保持不变不清空，改变时将新的模板附在后边
function setValue_new_probdescrib() {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    if (!XM.ISNULL(new_l2casetype_id)) {
        ////录单模板赋值
        if (new_l2casetype_id[0].id != null && new_l2casetype_id[0].id != undefined && new_l2casetype_id[0].id != "") {
            var fetchXml = "<fetch version='1.0' mapping='logical'>\
                    <entity name='new_casetemplate'>\
	                <attribute name='new_templatecontent'/>\
	                <filter type='and'>\
		            <condition attribute='new_l2casetype_id' operator='eq' value='"+ new_l2casetype_id[0].id + "' />\
                    <condition attribute = 'statecode' operator = 'eq' value = '0' />\
                    </filter>\
                    </entity>\
                    </fetch>";
            var result = XM.Fetch(fetchXml, 'new_casetemplates');
            if (result != null && result.length > 0 && result[0].new_templatecontent != null) {
                var old_template = Xrm.Page.getAttribute('new_probdescrib').getValue();
                old_template = old_template != null ? old_template + '\n' : "";
                Xrm.Page.getAttribute('new_probdescrib').setValue(old_template + result[0].new_templatecontent);
            }
        }
    }
    //else {
    //    rtcrm.clearField("new_probdescrib");
    //}
}
//校验pending时长，以确定是否需要pending原因必填
function checkpendingtimeandreason() {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-checkpendingtimeandreason", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-checkpendingtimeandreason function");
    /*埋点 Step1 End*/

    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (new_caselevel == 2) {//案例级别二线案例才需要pending原因等操作
        getPendingTimeFrom_new_systemparameter().then(res => {
            var pendingtime = res;
            if (pendingtime != null) {
                var new_pendingtime = Xrm.Page.getAttribute('new_pendingtime').getValue();
                if (new_pendingtime != null) {
                    if (new_pendingtime > pendingtime) {//如果当前的pending时长超过系统设置，则原因必填
                        Xrm.Page.getAttribute('new_pendingreason').setRequiredLevel("required"); //设为必填
                        Xrm.Page.data.save();
                    }
                }
            }
        });
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//从系统参数表获取配置的系统pending时长
function getPendingTimeFrom_new_systemparameter() {
    return new Promise(function (resolve, reject) {
        var new_country_id = Xrm.Page.getAttribute('new_country_id').getValue();
        if (new_country_id == null || new_country_id[0].id == null) {
            Xrm.Utility.alertDialog($t("new_incident.noCountryInfo", "未获取到当前单据的国家信息"));
        }
        var tempCountryId = new_country_id[0].id.replace('{', '').replace('}', '');
        //从loaclStorage中获取缓存数据
        var result = getDataByLocalStorage(tempCountryId)
        if (result != null) {
            const res = result.value;
            return getPendingTimeBySystemParam(res);
        } else {
            XM.RetrieveFiledValueAsync('new_countries', new_country_id[0].id.replace('{', '').replace('}', ''), 'new_code').then(res => {
                setDataByLocalStorage(tempCountryId, res)
                return getPendingTimeBySystemParam(res);
            });
        }
    });
}
function getPendingTimeBySystemParam(res) {
    return new Promise(function (resolve, reject) {
        if (res.new_code) {
            var new_paramer_name = 'incident_pending_' + res.new_code;
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage(new_paramer_name)
            if (result != null) {
                const res2 = result.value;
                if(res2 === -1) {
                    resolve(pendingTimeBySystemParamCode(null));
                }else{
                    resolve(pendingTimeBySystemParamCode(res2));
                }
            } else {
                XM.GetParameterAsync(new_paramer_name).then(res2 => {
                    if(!res2) {
                        setDataByLocalStorage(new_paramer_name, -1);
                    }else{
                        setDataByLocalStorage(new_paramer_name, res2);
                    }
                    resolve(pendingTimeBySystemParamCode(res2));
                });
            }
        } else {
            Xrm.Utility.alertDialog($t("new_country.noCountryCode", "未获取到当前单据对应国家的编码"));
        }
    })
}
function pendingTimeBySystemParamCode(res2) {
    return new Promise(function (resolve, reject) {
        var pendingtime = res2;
        if (pendingtime) {
            var reg = new RegExp(/^\d+(\.\d+)?$/);//只允许出现数字(整数和小数)
            if (!reg.test(pendingtime)) {
                XM.openAlertDialog($t("new_incident.errorPendingTimeFormat", "当前案例pending时长为：{0}，系统参数有误，请联系管理员").format(pendingtime));
                pendingtime = null;
            }
            if (pendingtime <= 0 && pendingtime != null) {
                XM.openAlertDialog($t("new_incident.errorPendingTimeLessZero", "当前案例pending时长系统参数为：{0}，必须大于0，请联系管理员").format(pendingtime));
                pendingtime = null;
            }
            resolve(pendingtime);
        } else {
            const localStorageKey = "incident_pending_";
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage(localStorageKey)
            if (result != null) {
                const res3 = result.value;
                resolve(pendingTimeBySystemParam(res3));
            } else {
                //说明系统参数里未配置相关pending时长，则使用默认配置8小时
                XM.GetParameterAsync(localStorageKey).then(res3 => {
                    setDataByLocalStorage(localStorageKey, res3)
                    resolve(pendingTimeBySystemParam(res3));
                });
            }

        }
    })
}
function pendingTimeBySystemParam(res3) {
    return new Promise(function (resolve, reject) {
        pendingtime = res3;//获取默认时间
        if (pendingtime) {
            var reg = new RegExp(/^\d+(\.\d+)?$/);//只允许出现数字(整数和小数)
            if (!reg.test(pendingtime)) {
                XM.openAlertDialog($t("new_incident.errorPendingTimeFormat", "当前案例pending时长为：{0}，系统参数有误，请联系管理员").format(pendingtime));
                pendingtime = null;
            }
            if (pendingtime <= 0 && pendingtime != null) {
                XM.openAlertDialog($t("new_incident.errorPendingTimeLessZero", "当前案例pending时长系统参数为：{0}，必须大于0，请联系管理员").format(pendingtime));
                pendingtime = null;
            }
            resolve(pendingtime);
        } else {
            XM.openAlertDialog($t("new_incident.noSystemPendingTime", "缺少案例pending时长系统参数，请联系管理员"));
        }
    })
}
//判断是否超时
function checkIsTimeOut(time1, time2, outtime, datepart) {
    var isOutTime = false;
    //datepart类型：y年q季m月w周d天h时n分s秒ms毫秒
    switch (datepart) {
        case 'd': outtime = outtime * 24 * 60 * 60 * 1000; break;//时转换成毫秒
        case 'h': outtime = outtime * 60 * 60 * 1000; break;//时转换成毫秒
        case 'n': outtime = outtime * 60 * 1000; break;//时转换成毫秒
        default: break;
    }
    var difftime = time2.getTime() - time1.getTime();//getTime获取1970-1-1来的毫秒数
    if (difftime > outtime) {
        isOutTime = true;
    }
    return isOutTime;
}
/******************************************************************
 ** jankin wang
 ** 2022-10-14 15:19:33
 ** 说明: 案例新增一级机型，产品线与机型的过滤逻辑修改
 ******************************************************************/
//产品线加载事件
function new_productline_load(isLoad) {
    /*埋点 Step1 Start*/
    var startTime = new Date();
    // var aiLogDataModel = new top.AILogging.aiLogDataModel("RekTec.Crm.Web-new_incident.js-new_productline_load", Xrm.Page.data.entity.getId(), XM.FormatUTCDate(startTime, "yyyy-MM-dd hh:mm:ss.S"), "RekTec.Crm.Web-new_incident.js-new_productline_load function");
    /*埋点 Step1 End*/

    var new_productline = Xrm.Page.getAttribute('new_productline').getValue();
    if (new_productline == 1) {//改为手机，取消过滤非手机，过滤手机
        Xrm.Page.getControl("new_productmodel2_id").setVisible(false);
        Xrm.Page.getControl("new_productmod_id").setVisible(true);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("required"); //一级机型必填
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("none"); //二级机型不必填
        //过滤一级机型
        filter_new_productmod_id();
    }
    else if (new_productline == 2 ||
        new_productline == 4 ||
        new_productline == 5 ||
        new_productline == 6 ||
        new_productline == 7 ||
        new_productline == 8) {//非手机
        Xrm.Page.getControl("new_productmodel2_id").setVisible(true);
        Xrm.Page.getControl("new_productmod_id").setVisible(false);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("none"); //一级机型不必填
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("required"); //二级机型必填
        //过滤二级机型
        filter_new_productmodel2_id();
    }
    else if (new_productline == 3 || new_productline == null) {//null 或 用户无法提供型号 机型不必填不显示
        Xrm.Page.getControl('new_productmod_id').setVisible(false);
        Xrm.Page.getControl('new_productmodel2_id').setVisible(false);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("none");
    }
    disabled23casetype(true, isLoad);//解锁或锁定二三级案例
    new_l1casetype_id_load(isLoad);//触发案例加载事件

    //特殊，对历史数据处理
    var new_productmodel2_id = Xrm.Page.getAttribute('new_productmodel2_id').getValue();
    if (!XM.ISNULL(new_productmodel2_id)) {
        Xrm.Page.getControl('new_productmodel2_id').setVisible(true);
    }

    /*埋点 Step2 Start*/
    // aiLogDataModel.DURATION = new Date() - startTime;
    // top.AILogging.writeLogToAppInsights(aiLogDataModel, top.AILogging.appInsightLogType().trace);
    /*埋点 Step2 End*/
}
//产品线onchange事件
function new_productline_onchange() {
    var new_productline = Xrm.Page.getAttribute('new_productline').getValue();
    Xrm.Page.getAttribute('new_productmod_id').setValue(null); //一级机型置空
    Xrm.Page.getAttribute('new_productmodel2_id').setValue(null); //二级机型置空
    disabled23casetype(true);//解锁或锁定二三级案例

    //重新过滤二级案例-产品线一改变就需要重新过滤
    filter_new_l2casetype_id();

    if (new_productline == 1) {//改为手机，取消过滤非手机，过滤手机
        Xrm.Page.getControl("new_productmodel2_id").setVisible(false);
        Xrm.Page.getControl("new_productmod_id").setVisible(true);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("required"); //一级机型必填
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("none"); //二级机型不必填

        //取消过滤二级机型
        public_filter_field("new_productmodel2_id", true);
        //重新过滤一级机型
        filter_new_productmod_id();
    }
    else if (new_productline == 2 ||
        new_productline == 4 ||
        new_productline == 5 ||
        new_productline == 6 ||
        new_productline == 7 ||
        new_productline == 8) {//非手机
        Xrm.Page.getControl("new_productmodel2_id").setVisible(true);
        Xrm.Page.getControl("new_productmod_id").setVisible(false);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("none"); //一级机型不必填
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("required"); //二级机型必填

        //取消过滤一级机型
        public_filter_field("new_productmod_id", true);
        //重新过滤二级机型
        filter_new_productmodel2_id();
    }
    else if (new_productline == 3 || new_productline == null) {//null 或 用户无法提供型号 机型不必填不显示 并取消一二机型过滤
        public_filter_field("new_productmod_id", true);//取消过滤一级机型
        public_filter_field("new_productmodel2_id", true);//取消过滤二级机型
        Xrm.Page.getControl('new_productmod_id').setVisible(false);
        Xrm.Page.getControl('new_productmodel2_id').setVisible(false);
        Xrm.Page.getAttribute('new_productmod_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_productmodel2_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_productline_id').setValue(null);
    }
}

//一级机型onchange事件
function new_productmod_id_onchange() {
    var new_productmod_id = Xrm.Page.getAttribute('new_productmod_id').getValue();
    filter_new_l2casetype_id();//机型改变触发二级案例过滤事件
    filter_new_l3casetype_id();//机型改变触发三级案例过滤事件
    disabled23casetype(true);//解锁或锁定二三级案例
    if (!XM.ISNULL(new_productmod_id)) {
        set_new_productline_id("new_productmod_id");//旧产品线赋值
        checkclear23casetype();//是否需要清空二三级案例
    }
}
//一级机型过滤事件
function filter_new_productmod_id() {
    var new_productline = Xrm.Page.getAttribute('new_productline').getValue();
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();

    if (XM.ISNULL(new_productline)) {//产品线为空或3，直接取消一级机型过滤
        public_filter_field("new_productmod_id", true);
        return;
    }
    public_filter_field("new_productmod_id", "value");
}

//二级机型onchange事件
function new_productmodel2_id_onchange() {
    var new_productmodel2_id = Xrm.Page.getAttribute('new_productmodel2_id').getValue();
    filter_new_l2casetype_id();//机型改变触发二级案例过滤事件
    filter_new_l3casetype_id();
    disabled23casetype(true);//解锁或锁定二三级案例
    if (!XM.ISNULL(new_productmodel2_id)) {
        set_new_productline_id("new_productmodel2_id");//旧产品线赋值
        checkclear23casetype();//是否需要清空二三级案例
    }
}
//二级机型过滤事件
function filter_new_productmodel2_id() {
    var new_productline = Xrm.Page.getAttribute('new_productline').getValue();
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();

    if (XM.ISNULL(new_productline)) {//产品线为空或3，直接取消一级机型过滤
        public_filter_field("new_productmodel2_id", true);
        return;
    }
    public_filter_field("new_productmodel2_id", "value");
}

//一级案例加载事件
function new_l1casetype_id_load(isLoad) {
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();

    if (!XM.ISNULL(new_l1casetype_id)) {//一级案例类型不为空
        filter_new_l2casetype_id(isLoad);//过滤二级案例
        //原先既有的逻辑
        PageSectionHideDisplay(500, isLoad);//界面字段或节显隐，及三级案例是否必填校验
        if (!XM.ISNULL(new_l2casetype_id)) {//二级案例类型不为空
            new_l2casetype_id_load(isLoad);//触发二级案例加载事件
        }
    }
}
//一级案例onchange事件
function new_l1casetype_id_onchange() {
    var new_l1casetype_id = Xrm.Page?.getAttribute('new_l1casetype_id').getValue();
    if (XM.ISNULL(new_l1casetype_id)) {//如果为null，即未选择，则返回
        new_l1casetype_Record = {};
    }
    else {
        var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        setValueLv1CaseType(l1CaseTypeData);
    }
    new_l1casetype_idcode_onchange();
    //清空二级三级案例
    Xrm.Page.getAttribute('new_l2casetype_id').setValue(null);
    Xrm.Page.getAttribute('new_l3casetype_id').setValue(null);
    disabled23casetype(true);//解锁或锁定二三级案例
    filter_new_productmod_id();//重新过滤一级机型
    filter_new_productmodel2_id();//重新过滤二级机型
    filter_new_l2casetype_id();//重新过滤二级案例
    filter_new_l3casetype_id();//重新过滤三级案例

    //原先既有的逻辑
    PageSectionHideDisplay(500);//界面字段或节显隐，及三级案例是否必填校验
}

//二级案例加载事件
function new_l2casetype_id_load(isLoad) {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();

    if (!XM.ISNULL(new_l2casetype_id)) {//二级案例类型不为空
        filter_new_l3casetype_id();//过滤三级案例
        //原先既有的逻辑
        PageSectionHideDisplay(800, isLoad);//界面字段或节显隐，及三级案例是否必填校验
    }
}
//二级案例onchange事件
function new_l2casetype_id_onchange() {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    if (XM.ISNULL(new_l2casetype_id)) {//如果为null，即未选择，则返回
        new_l2casetype_Record = {};
    }
    else {
        var l2CaseTypeData = new_l2casetype_list.filter(p => p.new_l2casetypeid == new_l2casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        setValueLv2CaseType(l2CaseTypeData);
    }
    setValue_new_probdescrib();////二级案例类型改变或清空时相应对录单模板进行赋值();
    //清空三级案例
    Xrm.Page.getAttribute('new_l3casetype_id').setValue(null);

    //一级案例赋值并重新过滤二三级案例
    set_new_l1casetype_id();
    filter_new_l2casetype_id();
    filter_new_l3casetype_id();//

    filter_new_productmod_id();//重新过滤一级机型
    filter_new_productmodel2_id();//重新过滤二级机型

    //原先既有的逻辑
    PageSectionHideDisplay(800);//界面字段或节显隐，及三级案例是否必填校验
}

//二级案例过滤事件
async function filter_new_l2casetype_id(isLoad) {
    var new_productmod_id = Xrm.Page.getAttribute('new_productmod_id').getValue();//一级机型
    var new_productmodel2_id = Xrm.Page.getAttribute('new_productmodel2_id').getValue();//二级机型
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();//一级案例类型
    var case1 = check_new_l1casetype(isLoad);
    if (case1 == null) {//如果一级案例为空，二级案例根据机型过滤
        if (!XM.ISNULL(new_productmod_id) || !XM.ISNULL(new_productmodel2_id)) {
            var new_prosupmod_id_value = 
                !XM.ISNULL(new_productmod_id) ? await get_model_prosupmod("new_productmod_id") : await get_model_prosupmod("new_productmodel2_id");
            if (!XM.ISNULL(new_prosupmod_id_value)) {
                FilterL2CaseType(new_prosupmod_id_value);
            } else {
                public_filter_field("new_l2casetype_id", true);
            }
        } else {
            public_filter_field("new_l2casetype_id", true);
        }

    }
    else if (!case1) {//如果不是产品支持,则根据一级案例过滤二级案例
        public_filter_field("new_l2casetype_id", "value", null, new_l1casetype_id[0].id);
    }
    else if (case1) {//如果是产品支持
        var result = "";
        if (!XM.ISNULL(new_productmod_id) && XM.ISNULL(new_productmodel2_id)) {//一级机型有值
            result = await get_model_prosupmod("new_productmod_id");
            if (result != null) {//一级机型对应模板不为空
                public_filter_field("new_l2casetype_id", "value", "fetch1", new_l1casetype_id[0].id, result);
            }
            else {//一级机型对应模板为空
                public_filter_field("new_l2casetype_id", false);
            }
        }
        else if (XM.ISNULL(new_productmod_id) && !XM.ISNULL(new_productmodel2_id)) {//二级机型有值
            result = await get_model_prosupmod("new_productmodel2_id");
            if (result != null) {
                public_filter_field("new_l2casetype_id", "value", "fetch1", new_l1casetype_id[0].id, result);
            }
            else {
                public_filter_field("new_l2casetype_id", false);
            }
        }
        else if (XM.ISNULL(new_productmod_id) && XM.ISNULL(new_productmodel2_id)) {//一二机型都无值
            public_filter_field("new_l2casetype_id", "value", "fetch2", new_l1casetype_id[0].id, null);
        }
    }
}

//三级案例onchange事件
function new_l3casetype_id_onchange() {
    var new_l3casetype_id = Xrm.Page.getAttribute('new_l3casetype_id').getValue();
    if (XM.ISNULL(new_l3casetype_id)) {//如果为null，即未选择，则返回
        new_l3casetype_Record = {};
    }
    else {
        var l3CaseTypeData = new_l3casetype_list.filter(p => p.new_l3casetypeid == new_l3casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        setValueLv3CaseType(l3CaseTypeData);
    }
    set_new_l2casetype_id();//先二级案例赋值
    filter_new_l3casetype_id();//三级案例过滤
    set_new_l1casetype_id();//再一级案例赋值
    filter_new_l2casetype_id();//二级案例过滤

    filter_new_productmod_id();//重新过滤一级机型
    filter_new_productmodel2_id();//重新过滤二级机型

    //2024-08-12 p-zhouxin26 注释原逻辑
    //三级案例类型改变时，触发tips
    //displayTipsSection();

    //2024-08-12 p-zhouxin26【案例分类定义】模块显隐逻辑
    incidentTipsEnableSection();
}
//三级案例过滤事件
async function filter_new_l3casetype_id() {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    var new_productmod_id = Xrm.Page?.getAttribute('new_productmod_id').getValue();
    var new_productmodel2_id = Xrm.Page?.getAttribute('new_productmodel2_id').getValue();
    if (!XM.ISNULL(new_l2casetype_id)) {
        public_filter_field("new_l3casetype_id", "value", null, new_l2casetype_id[0].id);
        return;
    }
    if (!XM.ISNULL(new_productmod_id) || !XM.ISNULL(new_productmodel2_id)) {
        var new_prosupmod_id_value = 
            !XM.ISNULL(new_productmod_id) ? await get_model_prosupmod("new_productmod_id") : await get_model_prosupmod("new_productmodel2_id");
        if (!XM.ISNULL(new_prosupmod_id_value)) {
            FilterL3CaseType(new_prosupmod_id_value);
            return;
        }
    }
    public_filter_field("new_l3casetype_id", true);
}
///////////////////////////////////////////////////////公共方法
//判断一级案例类型是否是产品支持,返回null,true,false
function check_new_l1casetype(isLoad) {
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();//一级案例类型
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    if (XM.ISNULL(new_l1casetype_id)) {//一级案例类型为空
        return null;
    }
    //********优化Start
    var new_code = new_l1casetype_Record?.new_code;
    if (new_code == undefined || !isLoad) {
        var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        setValueLv1CaseType(l1CaseTypeData)
        new_code = new_l1casetype_Record?.new_code;
        if(new_casestatus != 6){
            Xrm.Page.getAttribute('new_lv1casetypecode').setValue(new_l1casetype_Record.new_typename);
        }
    }
    //********优化End
    if (new_code.toLowerCase() == "product support") {//如果选择的是产品支持
        return true;
    }
    else {
        return false;
    }
}
//获取二级案例绑定的机型模板数据
function get_new_l2casetype_prosupmod() {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    if (XM.ISNULL(new_l2casetype_id)) {
        return null;
    }
    var l2casetype = new_l2casetype_list.filter(p => p.new_l2casetypeid == new_l2casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
    if (l2casetype != null && l2casetype._new_prosupmod_id_value != null) {
        return l2casetype._new_prosupmod_id_value;
    }
    else {
        return null;
    }
}
//获取机型绑定的模板数据
function get_model_prosupmod(value) {
    return new Promise(async function(resolve, reject) {
        if (value == "new_productmod_id") {
            if (XM.ISNULL(Xrm.Page.getAttribute('new_productmod_id').getValue())) {
                resolve(null);
            }
        } else {
            if (XM.ISNULL(Xrm.Page.getAttribute('new_productmodel2_id').getValue())) {
                resolve(null);
            }
        }
        var id = value == "new_productmod_id" ? Xrm.Page.getAttribute('new_productmod_id').getValue()[0].id : Xrm.Page.getAttribute('new_productmodel2_id').getValue()[0].id;
        var entity = value == "new_productmod_id" ? "new_model1s" : "new_model2s";
        var modelInfo = XM.RetrieveFiledValue(entity, id, '_new_prosupmod_id_value');
        if (modelInfo != null && modelInfo._new_prosupmod_id_value != null) {
            resolve(modelInfo._new_prosupmod_id_value);
        } else {
            resolve(null);
        }
    });
}
//一级案例赋值
function set_new_l1casetype_id() {
    var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
    if (XM.ISNULL(new_l2casetype_id)) {//二级案例类型为空
        return;
    }
    if (new_l2casetype_list.length <= 0) {
        Getl2CaseTypeList().then(function () {
            var new_l2casetypeData = new_l2casetype_list.filter(p => p.new_l2casetypeid == new_l2casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            if (new_l2casetypeData.length > 0 && new_l2casetypeData[0]?._new_l1casetype_id_value != undefined && new_l2casetypeData[0]?._new_l1casetype_id_value != null) {
                Xrm.Page.getAttribute('new_l1casetype_id').setValue([{ entityType: 'new_l1casetype', id: new_l2casetypeData[0]._new_l1casetype_id_value, name: new_l2casetypeData[0]["<EMAIL>"] }]);
            }
            //原先既有的逻辑
            PageSectionHideDisplay(500);//界面字段或节显隐，及三级案例是否必填校验
        })
    } else {
        var new_l2casetypeData = new_l2casetype_list.filter(p => p.new_l2casetypeid == new_l2casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        if (new_l2casetypeData.length > 0 && new_l2casetypeData[0]?._new_l1casetype_id_value != undefined && new_l2casetypeData[0]?._new_l1casetype_id_value != null) {
            Xrm.Page.getAttribute('new_l1casetype_id').setValue([{ entityType: 'new_l1casetype', id: new_l2casetypeData[0]._new_l1casetype_id_value, name: new_l2casetypeData[0]["<EMAIL>"] }]);
        }
        //原先既有的逻辑
        PageSectionHideDisplay(500);//界面字段或节显隐，及三级案例是否必填校验
    }
}
//二级案例赋值
function set_new_l2casetype_id() {
    if (new_l3casetype_list.length <= 0) {
        Getl3CaseTypeList().then(function () {
            var new_l3casetype_id = Xrm.Page.getAttribute('new_l3casetype_id').getValue();
            if (XM.ISNULL(new_l3casetype_id)) {//三级案例类型为空
                return;
            }
            var new_l3casetypeData = new_l3casetype_list.filter(p => p.new_l3casetypeid == new_l3casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
            if (new_l3casetypeData.length > 0 && new_l3casetypeData[0]?._new_l2casetype_id_value != undefined && new_l3casetypeData[0]?._new_l2casetype_id_value != null) {
                Xrm.Page.getAttribute('new_l2casetype_id').setValue([{ entityType: 'new_l2casetype', id: new_l3casetypeData[0]._new_l2casetype_id_value, name: new_l3casetypeData[0]["<EMAIL>"] }]);
            }
            if (XM.ISNULL(new_l2casetype_id)) {//如果一级案例类型为空需要给一级案例赋值
                setValue_new_probdescrib();////二级案例类型改变或清空时相应对录单模板进行赋值();
            }
            //原先既有的逻辑
            PageSectionHideDisplay(1000);//界面字段或节显隐，及三级案例是否必填校验

        });
    } else {
        var new_l2casetype_id = Xrm.Page.getAttribute('new_l2casetype_id').getValue();
        var new_l3casetype_id = Xrm.Page.getAttribute('new_l3casetype_id').getValue();
        if (XM.ISNULL(new_l3casetype_id)) {//三级案例类型为空
            return;
        }
        var new_l3casetypeData = new_l3casetype_list.filter(p => p.new_l3casetypeid == new_l3casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        if (new_l3casetypeData.length > 0 && new_l3casetypeData[0]?._new_l2casetype_id_value != undefined && new_l3casetypeData[0]?._new_l2casetype_id_value != null) {
            Xrm.Page.getAttribute('new_l2casetype_id').setValue([{ entityType: 'new_l2casetype', id: new_l3casetypeData[0]._new_l2casetype_id_value, name: new_l3casetypeData[0]["<EMAIL>"] }]);
        }
        if (XM.ISNULL(new_l2casetype_id)) {//如果一级案例类型为空需要给一级案例赋值
            setValue_new_probdescrib();////二级案例类型改变或清空时相应对录单模板进行赋值();
        }
        //原先既有的逻辑
        PageSectionHideDisplay(1000);//界面字段或节显隐，及三级案例是否必填校验
    }

    //}
}
//旧产品线new_productline_id赋值
function set_new_productline_id(value) {
    var id = null;
    var entity = null;
    if (value == null) {
        Xrm.Page.getAttribute('new_productline_id').setValue(null); //产品线置空
    }
    else if (value == "new_productmod_id") {
        id = Xrm.Page.getAttribute('new_productmod_id').getValue()[0].id;
        entity = "new_model1s";
    }
    else if (value == "new_productmodel2_id") {
        id = Xrm.Page.getAttribute('new_productmodel2_id').getValue()[0].id;
        entity = "new_model2s";
    }
    else {
    }
    if (entity == null || id == null) {
        return;
    }
    var modelInfo = XM.RetrieveFiledValue(entity, id, '_new_category2_id_value');
    if (modelInfo != null && modelInfo._new_category2_id_value != null) {
        Xrm.Page.getAttribute('new_productline_id').setValue([{ id: modelInfo._new_category2_id_value, name: modelInfo["<EMAIL>"], entityType: "new_category2" }]);
    }

}
//锁定和解锁二三级案例类型
function disabled23casetype(flag, isLoad) {
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    //某些状态不需要可编辑34589加载时disabledAll.6已关单是系统自带功能，加载时未disabledAll，所以此处需要添加，不可编辑
    if (!XM.ISNULL(new_casestatus) && (new_casestatus == 5 || new_casestatus == 3 || new_casestatus == 4 || new_casestatus == 8 || new_casestatus == 9) || new_casestatus == 6) {
        Xrm.Page.getControl('new_l2casetype_id').setDisabled(true);//二级案例锁定
        Xrm.Page.getControl('new_l3casetype_id').setDisabled(true);//三级案例锁定
    }
    else {
        var deal = false;
        var new_productline = Xrm.Page.getAttribute('new_productline').getValue();
        var new_productmod_id = Xrm.Page.getAttribute('new_productmod_id').getValue();
        var new_productmodel2_id = Xrm.Page.getAttribute('new_productmodel2_id').getValue();
        if (flag) {//锁定
            if (check_new_l1casetype(isLoad)) {//一级案例是产品支持才锁定
                if (XM.ISNULL(new_productmod_id) && XM.ISNULL(new_productmodel2_id) && new_productline != 3) {
                    deal = true;
                }
            }
        }
        Xrm.Page.getControl('new_l2casetype_id').setDisabled(deal);//二级案例锁定或解锁
        Xrm.Page.getControl('new_l3casetype_id').setDisabled(deal);//三级案例锁定或解锁
    }
}
//判断是否需要清空二三级案例类型
async function checkclear23casetype() {
    //加载不要清空值，只有改变时清除
    var needclear = false;
    var new_productmod_id = Xrm.Page.getAttribute('new_productmod_id').getValue();//一级机型
    var new_productmodel2_id = Xrm.Page.getAttribute('new_productmodel2_id').getValue();//二级机型
    var l2casetype_prosupmod = get_new_l2casetype_prosupmod();
    var result = "";


    if (check_new_l1casetype()) {//一级案例是产品支持
        if (!XM.ISNULL(new_productmod_id) && XM.ISNULL(new_productmodel2_id)) {//一级机型有值
            result = await get_model_prosupmod("new_productmod_id");

        }
        else if (XM.ISNULL(new_productmod_id) && !XM.ISNULL(new_productmodel2_id)) {//二级机型有值
            result = await get_model_prosupmod("new_productmodel2_id");
        }

        if (l2casetype_prosupmod == null || result == null || l2casetype_prosupmod != result) {//机型模板为空或案例模板为空或不相等
            needclear = true;
        }
        if (needclear) {
            Xrm.Page.getAttribute('new_l2casetype_id').setValue(null);
            Xrm.Page.getAttribute('new_l3casetype_id').setValue(null);
        }
    }
}

// 服务方式 加载处理
function serviceModeOnLoad() {
    var field = 'new_servicemode'
    //移除 代收 选项
    Xrm.Page.getControl(field)?.removeOption(4);
    //加载触发【服务方式】变更事件
    serviceModeOnChange();

    parent.fieldGetAttribute = window.fieldGetAttribute = function (field) {
        return Xrm.Page.getAttribute(field);
    }
}

// 服务方式 onchange 事件
function serviceModeOnChange() {
    var countryAttr = Xrm.Page.getAttribute('new_country_id')?.getValue() || null;
    if (!countryAttr || !countryAttr.length)
        return;
    //【服务方式】
    var servicemode = Xrm.Page.getAttribute('new_servicemode')?.getValue() || -1;
    //【国家】
    var countryId = countryAttr[0].id.replace(/[{}]/g, '');
    var country = null;
    //从loaclStorage中获取缓存数据
    var result = getDataByLocalStorage(countryId)
    if (result != null) {
        country = result.value;
    } else {
        country = XM.RetrieveFiledValue('new_countries', countryId, 'new_code')
        setDataByLocalStorage(countryId, country)
    }

    //【服务方式】等于“寄修”or“上门”
    var isServiceMode = [2, 3].indexOf(servicemode) > -1;
    //【国家/地区】等于【法国】、【意大利】、【德国】
    var isCountry = ['FR', 'IT', 'DE'].indexOf(country.new_code) > -1;

    //联系电话、电子邮件地址、联系电话2、详细地址、所属省份
    var fields = ['new_phone', 'emailaddress', 'new_contactnum2'];
    fields.forEach(function (field) {
        //【服务方式】等于“寄修”or“上门”，联系电话、电子邮件地址必填。  (详细地址、所属省份必填已移除)
        Xrm.Page.getAttribute(field)?.setRequiredLevel(isServiceMode ? 'required' : 'none');
    });

    //【服务方式】等于“寄修”or“上门” 和【国家/地区】等于【法国】、【意大利】、【德国】，邮政编码必填
    Xrm.Page.getAttribute('new_postal_code')?.setRequiredLevel(isServiceMode && isCountry ? 'required' : 'none');
}

//原先既有的逻辑,对界面显示或隐藏或字段是否必填
function PageSectionHideDisplay(time, isLoad) {

    suggest(isLoad);//一级案例类型建议时，三级案例是否必填
    setTimeout(function () {
        hide1casetypeAllSection(isLoad);
        hide2casetypeAllSection(isLoad);
        hideAllSection(isLoad);
        display1casetypeSection(isLoad);
        critical(isLoad);//二级案例危机事件时，三级案例必填
        new_l1casetype_idcode_onchange(isLoad);//给一级案例类型编码赋值
    }, time);
}
//对字段进行过滤
//field:要过滤的字段;     flag:过滤全部true，空false还是条件过滤"value";     value:要过滤的条件值
function public_filter_field(field, flag, type, value, value2) {
    var filedone = field;
    var entity = "";
    var fetchXML = "";
    var fetchcondation = "";
    var fetchlink = "";
    var cell = "";
    var new_productline = Xrm.Page.getAttribute('new_productline')?.getValue();
    switch (field) {
        //一级机型 产品线为手机/pad，1
        case "new_productmod_id":
            entity = "new_model1";
            switch (flag) {
                case true: fetchcondation = ""; fetchlink = ""; break;//获取全部-既取消过滤
                case false: fetchcondation = " <condition attribute='" + entity + "id' operator='eq' value='00000000-0000-0000-0000-000000000000'/>"; fetchlink = ""; break;//获取空
                case "value":
                    fetchlink = "<link-entity name='new_category3' from='new_category3id' to='new_category3_id' alias='x' link-type='inner'>"
                        + "<filter>"
                        + "<condition attribute='statecode' operator='eq' value='0' />"
                        + "<filter type='or'>"
                        + "<condition attribute='new_productline' operator='eq' value='" + new_productline + "' />"
                        + "</filter>"
                        + "</filter>"
                        + "</link-entity>";
                    if (type == "fetch1") {
                        fetchcondation = " <condition attribute='new_prosupmod_id' operator='eq' value='" + value + "'/>";
                    }
                    break;
                default: break;
            }
            var fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='" + entity + "'>"
                + "<attribute name='" + entity + "id' />"
                + "<attribute name='new_name' />"
                + "<order attribute='new_name' descending='false' />"
                + "<filter>"
                + "<condition attribute='statecode' operator='eq' value='0' />"
                + fetchcondation
                + "</filter>"
                + fetchlink
                + "</entity>"
                + "</fetch>";
            break;
        //二级机型 产品线为 Eco、电视、空调、冰箱、洗衣机
        case "new_productmodel2_id":
            entity = "new_model2";
            switch (flag) {
                case true: fetchcondation = ""; fetchlink = ""; break;//获取全部-既取消过滤
                case false: fetchcondation = " <condition attribute='" + entity + "id' operator='eq' value='00000000-0000-0000-0000-000000000000'/>"; fetchlink = ""; break;//获取空
                case "value":
                    fetchlink = "<link-entity name='new_category3' from='new_category3id' to='new_category3_id' alias='x' link-type='inner'>"
                        + "<filter>"
                        + "<condition attribute='statecode' operator='eq' value='0' />"
                        + "<filter type='or'>"
                        + "<condition attribute='new_productline' operator='eq' value='" + new_productline + "' />"
                        + "</filter>"
                        + "</filter>"
                        + "</link-entity>";
                    if (type == "fetch1") {
                        fetchcondation = " <condition attribute='new_prosupmod_id' operator='eq' value='" + value + "'/>";
                    }
                    break;
                default: break;
            }
            var fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='" + entity + "'>"
                + "<attribute name='" + entity + "id' />"
                + "<attribute name='new_name' />"
                + "<order attribute='new_name' descending='false' />"
                + "<filter>"
                + "<condition attribute='statecode' operator='eq' value='0' />"
                + fetchcondation
                + "</filter>"
                + fetchlink
                + "</entity>"
                + "</fetch>";
            break;
        //二级案例
        case "new_l2casetype_id":
            entity = "new_l2casetype";
            cell = "<cell name='new_modelname' width='150'/>";
            switch (flag) {
                case true: fetchcondation = ""; fetchlink = ""; break;//获取全部-既取消过滤
                case false: fetchcondation = " <condition attribute='" + entity + "id' operator='eq' value='00000000-0000-0000-0000-000000000000'/>"; fetchlink = ""; break;//获取空
                case "value":
                    fetchcondation = " <condition attribute='new_l1casetype_id' operator='eq' value='" + value + "' />";
                    if (type == "fetch1") {
                        fetchcondation += "<condition attribute='new_prosupmod_id' operator='eq' value='" + value2 + "' />"
                    }
                    else if (type == "fetch2") {
                        fetchlink = "<link-entity name='new_prosupmod' from='new_prosupmodid' to='new_prosupmod_id' link-type='inner'> "
                            + "<filter>"
                            + "<condition attribute='new_code' operator='eq' value='MD000004' />"
                            + "</filter>"
                            + "</link-entity>";
                    }
                    else if (type == "fetch3") { }
                    break;
                default: break;
            }
            fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='" + entity + "'>"
                + "<attribute name='" + entity + "id' />"
                + "<attribute name='new_name' />"
                + "<attribute name='new_modelname' />"
                + "<order attribute='new_name' descending='false' />"
                + "<filter type='and'>"
                + "<condition attribute='statecode' operator='eq' value='0' />  "
                + fetchcondation
                + "</filter>"
                + fetchlink
                + "</entity>"
                + "</fetch>";
            break;
        //三级案例，20230627: 三级案例类型优化，新增2级类型别名和一级类型显示
        case "new_l3casetype_id":
            entity = "new_l3casetype";
            switch (flag) {
                case true: fetchcondation = ""; break;//获取全部-既取消过滤
                case false: fetchcondation = " <condition attribute='" + entity + "id' operator='eq' value='00000000-0000-0000-0000-000000000000'/>"; fetchlink = ""; break;//获取空
                case "value":
                    fetchcondation = "<condition attribute='new_l2casetype_id' operator='eq' value='" + value + "' />"
                    break;
                default: break;
            }
            fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
                + "<entity name='" + entity + "'>"
                + "<attribute name='" + entity + "id' />"
                + "<attribute name='new_name' />"
                + "<attribute name='new_code' />"
                + "<attribute name='new_l1casetype_id' />"
                //三级案例修改排序 已搜索排序显示 zhangfeng 2023-7-10
                + "<order attribute='new_displaysequence' descending='false' />"
                + "<filter type='and'>"
                + "<condition attribute='statecode' operator='eq' value='0' />  "
                + fetchcondation
                + "</filter>"
                + "<link-entity name='new_l2casetype' from='new_l2casetypeid' to='new_l2casetype_id' visible='false' link-type='outer' alias='l2type'>"
                + "<attribute name='new_modelname' />"
                + "</link-entity>"
                + "</entity>"
                + "</fetch>";
            cell = "<cell name='l2type.new_modelname' width='250'/>"
                + "<cell name='new_l1casetype_id' width='150'/>";
            break;
    }
    //公共方法
    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='" + entity + "id'>"
        + "<cell name='new_name' width='150'/>"
        + cell
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl(filedone).addCustomView(XM.NEWID(), entity, '自定义查找', fetchXML, layoutXML, true);
}
//按钮权限缓存                     
function getCanLocal(key) {
    var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
    //结案状态并且非查询按钮 直接返回false
    if (new_casestatus == 6 && keys.includes(key)) {
        return false;
    }
    else {
        return true;
    }
}

//202308迭代新增，案例tab显隐
function visibleTab() {
    var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
    if (new_caselevel == 1) {
        Xrm.Page.ui.tabs.get('relatedcasenote').setVisible(false);
    }
    else if (new_caselevel == 2) {
        Xrm.Page.ui.tabs.get('relatedcasenote').setVisible(true);
    }
    var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
    var orderNumber = Xrm.Page?.getAttribute('new_miordercode')?.getValue();
    //同一订单生成的案例选项卡显隐
    if ((new_casesource == 7 ||
        new_casesource == 8 ||
        new_casesource == 9) && orderNumber) {
        Xrm.Page.ui.tabs.get('tab_14').setVisible(true);
    } else {
        Xrm.Page.ui.tabs.get('tab_14').setVisible(false);
    }
}

var subGrid = Xrm.Page?.getControl('Subgrid_new_6');
subGrid.addOnLoad(tab14onload)
function tab14onload() {
    GetEntityInfomation();
    var orderNumber = Xrm.Page?.getAttribute('new_miordercode')?.getValue();
    var subGrid = Xrm.Page?.getControl('Subgrid_new_6');
    var key = top.window.entityId + orderNumber;
    if (getItemWithExpiry(key) != null)
        return;
    var filter = "<filter type='and'>" +
        "<condition attribute='new_miordercode' operator='eq' value='" + orderNumber + "' />" +
        "<condition attribute='new_caselevel' operator='eq' value='2' />" +
        "<condition attribute='new_channel' operator='eq' value='5' />" +
        "<condition attribute='incidentid' operator='ne' value='" + top.window.entityId + "' />" +
        "</filter>";
    subGrid.setFilterXml(filter);
    setItemWithExpiry(top.window.entityId + orderNumber, "setted", 3000)
    subGrid.refresh(); // 刷新子网格以应用过滤  
}
// 存储数据并设置过期时间  
function setItemWithExpiry(key, value, ttl) {
    const now = new Date();

    // `ttl` 是过期时间（毫秒）  
    const item = {
        value: value,
        expiry: now.getTime() + ttl, // 当前时间加上过期时间  
    };

    // 存储到 localStorage  
    localStorage.setItem(key, JSON.stringify(item));
}
// 获取数据并检查是否过期  
function getItemWithExpiry(key) {
    const itemStr = localStorage.getItem(key);

    // 如果没有找到，返回 null  
    if (!itemStr) {
        return null;
    }

    const item = JSON.parse(itemStr);
    const now = new Date();

    // 检查是否过期  
    if (now.getTime() > item.expiry) {
        // 如果过期，删除该项并返回 null  
        localStorage.removeItem(key);
        return null;
    }

    return item.value;
}

//创建解决案例记录 并 更新案例状态 p-qilong3 20230822
async function createIncidentSolution(incidentid, value) {

    var record = {};
    record["<EMAIL>"] = "/incidents(" + incidentid + ")"; // Lookup
    record.new_resolvetime = new Date(); // Date Time
    record.new_resolvetype = 1; // Choice
    record.resolutiontypecode = 5; // Choice
    record.subject = "Case Closed"; // Text

    try {
        Xrm.WebApi.createRecord("incidentresolution", record).then(
            function success(result) {
                console.log("Case Closed");

                var incidentEn = {};
                incidentEn.new_casestatus = value; // Choice
                incidentEn.statecode = 0; // State

                Xrm.WebApi.updateRecord("incident", incidentid, incidentEn).then(
                    function success(result) {
                        var updatedId = result.id;
                        console.log(updatedId);
                    },
                    function (error) {
                        console.log(error.message);
                    }
                );
            },
            function (error) {
                console.log(error.message);
            }
        );
    } catch (ex) {
        console.log(ex);
    }
}


/**
 * 
 * 20230911 点击发送sms按钮显隐规则
 * Author:liujuan
 **/
function SendSmsEnable() {
    try {

        if (!UserRoleRibbons.includes("new.incident.Button.sendsms"))
            return false;
        var formType = Xrm.Page.ui.getFormType();
        if (formType !== 1 && Xrm.Page.getAttribute('ticketnumber')) {
            var ticketnumber = Xrm.Page.getAttribute('ticketnumber').getValue();
            if (ticketnumber !== null)
                return true;
        }
        return false;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}


/**
 * 20230911 点击发送sms按钮弹出页面
 * Author:liujuan
 **/
function SendSmsBtn() {
    try {
        var isSms = false;
        if (XM.ISNULL(Xrm.Page.getAttribute('new_country_id').getValue()) || XM.ISNULL(Xrm.Page.getAttribute('customerid').getValue())) {
            Xrm.Utility.alertDialog($t("incident.noCountry", "请维护案例国家。"));
            return;
        }
        if (!Xrm.Page.getAttribute('new_isallowsms').getValue()) {
            Xrm.Utility.alertDialog($t("incident.notAllowSMS", "此案例客户不同意发送短信"));
            return;
        }
        else {
            var countryId = Xrm.Page.getAttribute('new_country_id').getValue()[0].id.replace('{', '').replace('}', '');
            var mobile = Xrm.Page.getAttribute('new_phone').getValue();
            isSms = XM.RetrieveFiledValue('new_countries', countryId, 'new_isccsms').new_isccsms;
            if (isSms) {
                var incidentId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                var contact = Xrm.Page.getAttribute('customerid').getValue()[0].name;
                var new_senttmp = Xrm.Page.getAttribute('new_senttmp').getValue();
                var pageInput = {
                    pageType: "webresource",
                    webresourceName: "new_/Service/IncidentSms.html",
                    data: encodeURIComponent("incidentid=" + incidentId + "&new_country_id=" + countryId + "&mobile=" + mobile + "&contact=" + contact + "&new_senttmp=" + new_senttmp),
                }
                var navigationOptions = {
                    title: this.$t("incident.sendSms", "短信发送"),
                    target: 2,
                    width: 1200,
                    height: 600,
                    position: 1
                };
                Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
                    function success(res) {

                    },
                    function error(e) {
                        XM.openAlertDialog(e);
                    });
            }
            else {
                Xrm.Utility.alertDialog($t("incident.noSms", "暂不支持短信发送。"));
                return;
            }
        }
    }
    catch (e) {
        Xrm.Utility.alertDialog(e);
    }
}

//202309，创建时不加载批量上传
async function DisplayBatchUpload() {
    var incidentId = Xrm.Page.data.entity.getId();

    if (XM.ISNULL(incidentId)) {
        Xrm.Page.ui.tabs.get("Summary").sections.get("Summary_section_38").setVisible(false);//隐藏tips
    }
    else {
        Xrm.Page.ui.tabs.get("Summary").sections.get("Summary_section_38").setVisible(true);//显示tips
    }
}

var keys = new Array('Mscrm.Form.incident.SaveAndClose', 'Mscrm.Form.incident.Save', 'new.incident.Button.AssignDiscount', 'Mscrm.Form.incident.AcceptAndSave', 'Mscrm.Form.incident.SaveAndClose', 'new.incident.Button.ConvertToCrisisEvent', 'new.incident.Button.MaintenanceInfoAndWorkOrder', 'new.incident.Button.Supervise');

//2024-07-05 p-zhouxin26 去除【问题描述】hover浮窗
function clearIncidentProbDescrib() {
    setTimeout(function () {
        var dom = top.$(`[data-id = 'new_probdescrib.fieldControl-text-box-text']`);
        if (dom && dom.length) {
            if (dom.title) dom.title = ''
            dom.on('blur focus mouseover mouseout', function (e) {
                if (e.target.title) e.target.title = ''
            });
        } else {
            clearIncidentProbDescrib();
        }
    }, 500);
}

//2024-08-02 p-zhouxin26【来访渠道】=“在线聊天”赋值逻辑
function setLiveChatChannel() {
    //获取querystring参数
    var param = Xrm.Page.context.getQueryStringParameters();
    //表单类型=“新建” and 没有设置渠道 and 应用中存在Session时
    if (formType == 1 && !param?.new_channel && top.Microsoft.Apm?.getFocusedSession()) {
        //同一个session，存在getConversationId时
        top.Microsoft?.Omnichannel?.getConversationId().then(function success(conversationId) {
            //设置【来访渠道】=“在线聊天”
            Xrm.Page.getAttribute('new_channel')?.setValue(2);
        });
    }
}

//2024-08-12 p-zhouxin26【案例分类定义】模块显隐逻辑
function incidentTipsEnableSection() {
    //【三级案例类型】
    var l3CaseType = Xrm.Page.getAttribute('new_l3casetype_id')?.getValue() || null;
    //【案例状态】
    var caseStatus = Xrm.Page.getAttribute('new_casestatus')?.getValue() || -1;
    //【三级案例类型】有值&【案例状态】≠“已结案”，则显示该模块
    var isEnabled = l3CaseType && l3CaseType.length && [6].indexOf(caseStatus) == -1;
    //【案例分类定义】模块
    Xrm.Page.ui.tabs.get('Summary').sections.get('Summary_section_37').setVisible(isEnabled);
    Xrm.Page.getControl('WebResource_incidentTips')?.getObject()?.contentWindow.location.reload();
}

//初始化参数
function initParam() {
    if (!userSettings)
        userSettings = Xrm.Page.context.userSettings;
    this.userRoles = Array.prototype.slice.call(userSettings.roles.get().map((x) => x.name));//用户角色

    startTime = new Date();
    entityId = Xrm.Page.data.entity.getId();//单据id
    if (entityId == '') {
        entityId = "empty guid";
    }

    userId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');//用户id
    formType = Xrm.Page.ui.getFormType();//窗体状态
    if (top.window.entityId != undefined) {
        top.window.entityId = Xrm.Page.data.getEntity().getId().replace(/[{}]/g, '');//单据id
    }
    if (top.window.entityName != undefined) {
        top.window.entityName = Xrm.Page.data.entity.getEntityName();//单据name
    }
    top.isClose = false;
    // 20241104 zhouqingrui 负责人id
    ownerId = Xrm.Page.getAttribute('ownerid').getValue()[0].id.replace(/[{}]/g, '');//负责人id

    //登录人与负责人是否同部门
    if (!isSameBu) {
        Promise.all([isSameBusinessUnit()]).then(function ([res1]) {
            //2024-07-05 p-zhouxin26 登录人与负责人是否为同一部门
            isSameBu = res1 || false;
        });
    }
}

//刷新危机事件显示
function refreshIncidentProbDescrib() {
    if (Xrm.Page.ui.getFormType() != 1) {
        var criticalClick = Xrm.Page.getAttribute("new_iscriticaltype").getValue();
        if (criticalClick != undefined && criticalClick) {
            var new_code = "comemr";
            var section = Xrm.Page.ui.tabs.get("Summary").sections.get(new_code);
            if (!XM.ISNULL(section)) {
                section.setVisible(true);
            }
        }
    }
}

//初始化一、二、三级案例数据
function initCaseType() {
    Promise.all([Getl1CaseTypeList(), Getl2CaseTypeList(), Getl3CaseTypeList()]).then(function (res1, res2, res3) {
        //数据加载完成之后继续执行后续代码
        dataLoadComplete();
    });
}

//获取所有可用的一级案例数据
function Getl1CaseTypeList() {
    return new Promise(function (resolve, reject) {
        try {
            var localStorageKey = "new_l1casetype_list";
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage(localStorageKey)
            if (result != null) {
                new_l1casetype_list = result.value;
                resolve(true)
            } else {
                var fetchXML = "<fetch mapping='logical' distinct='true' version='1.0' >" +
                    "<entity name = 'new_l1casetype'> " +
                    "<filter> " +
                    "<condition attribute='statecode' operator='neq' value='1' />  " +
                    "</filter> " +
                    "<attribute name = 'new_typename' /> " +
                    "<attribute name = 'new_code' /> " +
                    "<attribute name = 'new_l1casetypeid' /> " +
                    "</entity> " +
                    "</fetch>";
                XM.FetchAsync(fetchXML, 'new_l1casetypes').then((res) => {
                    new_l1casetype_list = res;
                    //将值存到localStorage中
                    setDataByLocalStorage(localStorageKey, new_l1casetype_list)
                    resolve(true);
                }).catch(err => {
                    reject(err);
                });
            }
        } catch (error) {
            reject(error);
        }
    })
}
//获取所有可用的二级案例数据
function Getl2CaseTypeList() {
    return new Promise(function (resolve, reject) {
        try {
            var localStorageKey = "new_l2casetype_list";
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage(localStorageKey)
            if (result != null) {
                new_l2casetype_list = result.value;
                resolve(true)
            } else {
                var fetchXML = "<fetch mapping='logical' distinct='true' version='1.0' >" +
                    "<entity name = 'new_l2casetype'> " +
                    "<filter> " +
                    "<condition attribute='statecode' operator='neq' value='1' />  " +
                    "</filter> " +
                    "<attribute name = 'new_code' /> " +
                    "<attribute name = 'new_l2casetypeid' /> " +
                    "<attribute name = 'new_l1casetype_id' /> " +
                    "</entity> " +
                    "</fetch>";
                XM.FetchAsync(fetchXML, 'new_l2casetypes').then((res) => {
                    new_l2casetype_list = res;
                    setDataByLocalStorage(localStorageKey, new_l2casetype_list)
                    resolve(true);
                }).catch(err => {
                    reject(err);
                });
            }
        } catch (error) {
            reject(error);
        }
    })
}
//获取所有可用的三级案例数据
function Getl3CaseTypeList() {
    return new Promise(function (resolve, reject) {
        try {
            var localStorageKey = "new_l3casetype_list";
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage(localStorageKey)
            if (result != null) {
                new_l3casetype_list = result.value;
                resolve(true)
            } else {
                var fetchXML = "<fetch mapping='logical' distinct='true' version='1.0' >" +
                    "<entity name = 'new_l3casetype'> " +
                    "<filter> " +
                    "<condition attribute='statecode' operator='neq' value='1' />  " +
                    "</filter> " +
                    "<attribute name = 'new_code' /> " +
                    "<attribute name = 'new_l3casetypeid' /> " +
                    "<attribute name = 'new_l2casetype_id' /> " +
                    "</entity> " +
                    "</fetch>";
                XM.FetchAsync(fetchXML, 'new_l3casetypes').then((res) => {
                    new_l3casetype_list = res;
                    setDataByLocalStorage(localStorageKey, new_l3casetype_list)
                    resolve(true);
                }).catch(err => {
                    reject(err);
                });
            }
        } catch (error) {
            reject(error);
        }
    })
}
//一、二、三及案例类型加载完成
function dataLoadComplete() {
    //********优化Start
    var new_l1casetype_id = Xrm.Page.getAttribute('new_l1casetype_id').getValue();
    if (!XM.ISNULL(new_l1casetype_id)) {
        var l1CaseTypeData = new_l1casetype_list.filter(p => p.new_l1casetypeid == new_l1casetype_id[0].id.replace('{', '').replace('}', '').toLowerCase());
        setValueLv1CaseType(l1CaseTypeData);
    }

    setTimeout(() => {
        bindCustomerEvent(true);
    }, 300)

    setTimeout(() => {
        // 设定页面的默认值
        setDefaultValue(true);
    }, 2000)

    //async call retrieve field value
    processWorkorderStationId(entityId, "form_load");
    //async get user role bibbon
    getUserRoleRibbonsFormLoad(entityId, "form_load");

    //#region form load埋点   
    // setTimeout(() => {
    //     formLoadDurationLogging(entityId, startTime, "form_load");
    // }, 800);
    //#endregion

    getCountryCodeForCaseCode(formType, entityId, startTime);

    //【服务方式】变更事件
    Xrm.Page.getAttribute('new_servicemode')?.addOnChange(serviceModeOnChange);
    //【服务方式】加载处理
    serviceModeOnLoad();

    //2024-07-05 p-zhouxin26 去除【问题描述】hover浮窗
    clearIncidentProbDescrib();
    isBuyProdOnChange();
}
//修正微软编辑表单保存后，不刷新的问题
function overrideOnSave() {
    Xrm.Page.data.entity?.addOnSave(function (event) {
        var saveMode = event._eventArgs?._saveMode;
        if (formType != 1 && saveMode == 1) {
            //等待弹出层出现
            let loopCount_alert = 0;
            let loop_alert = setInterval(function () {
                var isAlter = top.document.querySelector('span[role=alert]')?.innerHTML?.indexOf('...') > -1 || top.document.querySelector('.fui-Spinner') !== null;
                var isEditFormRoot = top.document.querySelector('span[role=alert]')?.parentNode?.parentNode?.getAttribute('id')?.indexOf('editFormRoot') > -1 || top.document.querySelector('.fui-Spinner')?.parentNode?.parentNode?.getAttribute('id')?.indexOf('editFormRoot') !== null;
                if (isAlter && isEditFormRoot) {
                    clearInterval(loop_alert);

                    //等待弹出层消失
                    if (loop_Refresh) { clearInterval(loop_Refresh); }
                    let loopCount = 0;
                    loop_Refresh = setInterval(function () {
                        var isAlter = top.document.querySelector('span[role=alert]')?.innerHTML?.indexOf('...') > -1 || top.document.querySelector('.fui-Spinner') !== null;
                        var isEditFormRoot = top.document.querySelector('span[role=alert]')?.parentNode?.parentNode?.getAttribute('id')?.indexOf('editFormRoot') > -1 || top.document.querySelector('.fui-Spinner')?.parentNode?.parentNode?.getAttribute('id')?.indexOf('editFormRoot') !== null;
                        if (!(isAlter && isEditFormRoot)) {
                            clearInterval(loop_Refresh);
                            //因现案例解决修改为js方法，解决案例后需关闭窗口，这段代码会在关闭后再打开窗口，修改
                            if (!top.isClose) {
                                if (Microsoft.Apm?.getFocusedSession()?.getFocusedTab()) {
                                    Microsoft.Apm.getFocusedSession().getFocusedTab().refresh();
                                } else {
                                    Xrm.Page.ui.close();
                                    Xrm.Utility.openEntityForm(Xrm.Page.data.entity.getEntityName(), entityId);
                                }
                            }
                            else {
                                top.isClose = false;
                            }
                        } else {
                            if (loopCount < 30) { loopCount++; } else { clearInterval(loop_Refresh); }
                        }
                    }, 500);
                } else {
                    if (loopCount_alert < 5) { loopCount_alert++; } else { clearInterval(loop_alert); }
                }
            }, 100);
        }
    });
}
//设置一级案例类型值
function setValueLv1CaseType(data) {
    if (data.length > 0) {
        new_l1casetype_Record.new_typename = data[0].new_typename || '';
        new_l1casetype_Record.new_code = data[0].new_code || '';
    } else {
        new_l1casetype_Record.new_typename = '';
        new_l1casetype_Record.new_code = '';
    }
}
//设置二级案例类型值
function setValueLv2CaseType(data) {
    if (data.length > 0) {
        new_l2casetype_Record.new_code = data[0].new_code || '';
    } else {
        new_l2casetype_Record.new_code = '';
    }
}
//设置三级案例类型值
function setValueLv3CaseType(data) {
    if (data.length > 0) {
        new_l3casetype_Record.new_code = data[0].new_code || '';
    } else {
        new_l3casetype_Record.new_code = '';
    }
}
//绑定拨号按钮点击事件
function bindCallClick() {
    setTimeout(function () {
        var phonedataid = parent.document.querySelector(`[data-id = 'new_phone.fieldControl-phone-action-icon']`)
        if (phonedataid) {
            phonedataid.onclick = function () {
                Xrm.Page.ui.tabs.get("Summary").sections.get("call_to").setVisible(true);
                sessionStorage.setItem("phoneCallTriggered", "true");
            }
        } else {
            bindCallClick();
        }
    }, 200)
}
//从localStorage中存数据
function getDataByLocalStorage(key) {
    //从loaclStorage中获取缓存数据
    var sessionData = sessionStorage.getItem(key);
    var result = sessionData ? JSON.parse(sessionData) : "";
    const now = new Date();//获取当前时间
    if (result != "" && result.value && now.getTime() < result.expiry) {
        return result;
    } else {
        return null;
    }
}
//往localStorage中存数据
function setDataByLocalStorage(key, val) {
    //将值存到localStorage中
    const ttl = 1000 * 60 * 60 * 1;//设置过期时间
    const now = new Date();//当前时间
    // 将过期时间存储在同一个键中
    const item = {
        value: val,
        expiry: now.getTime() + ttl
    };
    sessionStorage.setItem(key, JSON.stringify(item));

}

// 2024-10-25 zhouqingrui 一线坐席不能操作二线案例
function lockSecCaseWhenFrontline() {
    var caselevel = Xrm.Page.getAttribute("new_caselevel").getValue();
    if (checkFrontLineEditSecond(caselevel)) {
        XM.disableAll(true);
    }
}
function checkFrontLineEditSecond(new_caselevel) {
    let disableCheck = false;
    if (new_caselevel === 2 && this.userRoles.some(item => item === "一线坐席")) {
        disableCheck = true;
    }
    if (this.userRoles.some(item => item === "二线坐席") || this.userId === this.ownerId) {
        disableCheck = false;
    }
    return disableCheck;
}
//转物流运营按钮点击
function upgradeToLogisticsClick() {
    showComplaintReminderList(1)
}
//转物流运营按钮显隐
async function upgradeToLogisticsEnable() {
    if (!UserRoleRibbons.includes("new.incident.Button.UpgardeToLogistics"))
        return false;
    //获取物流投诉进度
    var logisticsprogress = Xrm.Page.getAttribute("new_progresslogisticscomplaint").getValue();
    if (logisticsprogress != null && logisticsprogress.indexOf(1) > -1)
        return false;
    return await btnVisible();
}
//转米网运营按钮点击
function upgradeToMiNetClick() {
    showComplaintReminderList(2)
}
//转米网运营按钮显隐
async function upgradeToMiNetEnable() {
    if (!UserRoleRibbons.includes("new.incident.Button.UpgradeToMiNet"))
        return false;
    //获取物流投诉进度
    var logisticsprogress = Xrm.Page.getAttribute("new_progresslogisticscomplaint").getValue();
    if (logisticsprogress != null && logisticsprogress.indexOf(2) > -1)
        return false;
    //获取升级至物流时间
    var new_upgradetologistics_datetime = Xrm.Page.getAttribute("new_upgradelogisticstime").getValue()
    var isShow = await btnVisible();
    if (isShow && new_upgradetologistics_datetime != null) {
        return true;
    } else {
        return false;
    }
}
//按钮显隐公共逻辑
function btnVisible() {
    return new Promise((resolve, reject) => {
        //获取案例状态
        var new_casestatus = Xrm.Page.getAttribute("new_casestatus").getValue();
        //获取案例级别
        var new_caselevel = Xrm.Page.getAttribute("new_caselevel").getValue();
        //获取二级案例类型code
        var new_l2casetype_id = Xrm.Page.getAttribute("new_l2casetype_id").getValue();
        var new_l2casetype_idcode = "";
        if (new_l2casetype_id != null && new_l2casetype_id.length > 0 && new_l2casetype_id[0].id != null) {
            //从loaclStorage中获取缓存数据
            var result = getDataByLocalStorage("new_l2casetype_list");
            var id = new_l2casetype_id[0].id.replace(/[{}]/g, '').toLowerCase();
            if (result != null) {
                var temp_new_l2casetype_list = result.value.filter(p => p.new_l2casetypeid.toLowerCase() == id);
                if (temp_new_l2casetype_list != null && temp_new_l2casetype_list.length > 0 && temp_new_l2casetype_list[0].new_code != null) {
                    new_l2casetype_idcode = temp_new_l2casetype_list[0].new_code;
                }
                if (new_caselevel == 2 && new_casestatus == 2 && new_l2casetype_idcode == "mi004" && isSameBu) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            } else {
                var new_l2casetype = XM.RetrieveFiledValue('new_l2casetypes', id, 'new_typename,new_code')
                Xrm.WebApi.retrieveRecord('new_l2casetypes', '?$select=new_typename,new_code').then(
                    function (response) {
                        new_l2casetype_idcode = new_l2casetype.new_code;
                        if (new_caselevel == 2 && new_casestatus == 2 && new_l2casetype_idcode == "mi004" && isSameBu) {
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                        setDataByLocalStorage("new_l2casetype_list", response);
                    },
                    function (error) {
                        reject(false);
                    }
                );
            }  
        }
    });
}
//打开弹窗
function showComplaintReminderList(type) {
    var countryId = Xrm.Page.getAttribute('new_country_id').getValue()[0].id.replace('{', '').replace('}', '');
    var param = encodeURIComponent("type=" + type + "&countryId=" + countryId + "&incidentId=" + Xrm.Page.data.entity.getId().replace('{', '').replace('}', ''))
    var webresourceurl = "new_/Service/new_complaintReminderList.html";

    var pageInput = {
        pageType: "webresource",
        webresourceName: webresourceurl,
        data: param,
    }
    var navigationOptions = {
        title: $t("incident.logistics_complaint_alerttitle", "投诉联系列表"),
        target: 2,
        width: 600,
        height: 500,
        position: 1
    };
    Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(
        function success(res) {
            Xrm.Page.data.refresh(true).then(function () {
                Xrm.Page.ui.refreshRibbon();
            });
        });
}
//是否已购买产品字段变更
function isBuyProdOnChange() {
    //获取是否已购买产品字段值
    var isBuyProd = Xrm.Page.getAttribute("new_isbuyprod").getValue();
    if (isBuyProd == false) {
        //设置非自营订单号字段为非必填
        Xrm.Page.getAttribute("new_ordercode").setRequiredLevel("none");
        //设置自营订单号字段为非必填
        Xrm.Page.getAttribute("new_miordercode").setRequiredLevel("none");
    } else {
        ismiOnChange();
    }
}

//是否自营字段变更
function ismiOnChange() {
    //获取二级案例类型code
    var new_l2casetype_id = Xrm.Page.getAttribute("new_l2casetype_id").getValue();
    var new_l2casetype_idcode = "";
    if (new_l2casetype_id != null && new_l2casetype_id.length > 0 && new_l2casetype_id[0].id != null) {
        //从loaclStorage中获取缓存数据
        var result = getDataByLocalStorage("new_l2casetype_list");
        var id = new_l2casetype_id[0].id.replace(/[{}]/g, '').toLowerCase();
        if (result != null) {
            var temp_new_l2casetype_list = result.value.filter(p => p.new_l2casetypeid.toLowerCase() == id);
            if (temp_new_l2casetype_list != null && temp_new_l2casetype_list.length > 0 && temp_new_l2casetype_list[0].new_code != null) {
                new_l2casetype_idcode = temp_new_l2casetype_list[0].new_code;
            }
        } else {
            var new_l2casetype = XM.RetrieveFiledValue('new_l2casetypes', id, 'new_typename,new_code')
            new_l2casetype_idcode = new_l2casetype.new_code;
        }
    }
    if (new_l2casetype_idcode != "mi004") {
        //设置非自营订单号字段为非必填
        Xrm.Page.getAttribute("new_ordercode").setRequiredLevel("none");
        //设置自营订单号字段为非必填
        Xrm.Page.getAttribute("new_miordercode").setRequiredLevel("none");
        return;
    }
    //获取是否自营字段值
    var ismi = Xrm.Page.getAttribute("new_ismi").getValue();
    if (ismi == true) {
        //设置自营订单号字段为必填
        Xrm.Page.getAttribute("new_miordercode").setRequiredLevel("required");
        //设置非自营订单号字段为非必填
        Xrm.Page.getAttribute("new_ordercode").setRequiredLevel("none");
    } else {
        //设置自营订单号字段为非必填
        Xrm.Page.getAttribute("new_miordercode").setRequiredLevel("none");
        //设置非自营订单号字段为必填
        Xrm.Page.getAttribute("new_ordercode").setRequiredLevel("required");
    }
}
//当前登录者与案例负责人同一部门
function isSameBusinessUnit() {
    return new Promise(function (resolve, reject) {
        try {
            if (this.userid == this.ownerid) {
                resolve(true);
                return;
            }
            var arr = [this.userid, this.ownerid];
            var filter = Array.prototype.slice.call(arr.map(function (x) {
                return `systemuserid eq ${x}`;
            })).join(' or ');
            var res = null;
            var businessUnitResultKey = "businessUnitResult";
            var result = getDataByLocalStorage(businessUnitResultKey);
            if (result != null && result != undefined && result.value != null && result.value.length > 0) {
                res = result.value;
            } else {
                res = rtcrm.retrieve(`systemusers?$select=_businessunitid_value&$filter=(${filter})&$top=2`, true);
            }
            if (!res || !res.value || !res.value.length || res.value.length != 2) {
                resolve(false);
                return;
            }
            var data = Array.prototype.slice.call(res.value.map(function (x) {
                return x._businessunitid_value;
            }));
            resolve(new Set(data).size == 1)
        } catch (err) {
            reject(err);
        }
    })
}
//2024-12-3 gongyingyan 是否购买产品 业务规则改为js 
function isBuyProdCtrl() {
    var new_isbuyprod = Xrm.Page?.getAttribute("new_isbuyprod")?.getValue();
    if (new_isbuyprod) {
        Xrm.Page.getControl('new_purchasename').setVisible(true);
        Xrm.Page.getControl('new_purchasetime').setVisible(true);
        Xrm.Page.getControl('new_ismi').setVisible(true);
        Xrm.Page.getControl('new_warrantyornot').setVisible(true);
        Xrm.Page.getControl('new_imei').setVisible(true);
        Xrm.Page.getControl('new_sn').setVisible(true);
        Xrm.Page.getControl('new_miui').setVisible(true);
        Xrm.Page.getControl('new_appname').setVisible(true);
        Xrm.Page.getControl('new_appversion').setVisible(true);
    } else {
        Xrm.Page.getControl('new_purchasename').setVisible(false);
        Xrm.Page.getControl('new_purchasetime').setVisible(false);
        Xrm.Page.getControl('new_ismi').setVisible(false);
        Xrm.Page.getControl('new_warrantyornot').setVisible(false);
        Xrm.Page.getControl('new_imei').setVisible(false);
        Xrm.Page.getControl('new_sn').setVisible(false);
        Xrm.Page.getControl('new_miui').setVisible(false);
        Xrm.Page.getControl('new_appname').setVisible(false);
        Xrm.Page.getControl('new_appversion').setVisible(false);
        Xrm.Page.getAttribute('new_province_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_city_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_county_id').setRequiredLevel("none");
        Xrm.Page.getAttribute('new_custaddress').setRequiredLevel("none");
    }
}
//用户期望上门时间和发货解冻时间显隐判断
function checkVisibility() {
    const caseTypeId = Xrm.Page.getAttribute('new_casesource').getValue();
    if (caseTypeId != 7) {
        Xrm.Page.getControl('WebResource_expvisitdate')?.setVisible(false);
        Xrm.Page.getControl('new_unfreezetime').setVisible(false);
    } else {
        Xrm.Page.getControl('WebResource_expvisitdate')?.setVisible(true);
        Xrm.Page.getControl('new_unfreezetime').setVisible(true);
    }
}
function FilterL2CaseType(new_prosupmod_id_value) {
    //二级案例
    var cell = "<cell name='new_modelname' width='150'/>";

    var fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
        + "<entity name='new_l2casetype'>"
        + "<attribute name='new_l2casetypeid' />"
        + "<attribute name='new_name' />"
        + "<attribute name='new_modelname' />"
        + "<order attribute='new_name' descending='false' />"
        + "<filter type='and'>"
        + "<condition attribute='statecode' operator='eq' value='0' />  "
        + "<filter type='or'>"
        + "<condition attribute='new_prosupmod_id' operator='null'/>"
        + "<condition attribute='new_prosupmod_id' operator='eq' value='" + new_prosupmod_id_value + "' /> "
        + "</filter>"
        + "</filter>"
        + "</entity>"
        + "</fetch>";
    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='new_l2casetypeid'>"
        + "<cell name='new_name' width='150'/>"
        + cell
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl("new_l2casetype_id").addCustomView(XM.NEWID(), "new_l2casetype", '自定义查找', fetchXML, layoutXML, true);
}
function FilterL3CaseType(new_prosupmod_id_value) {
    fetchXML = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>"
        + "<entity name='new_l3casetype'>"
        + "<attribute name='new_l3casetypeid' />"
        + "<attribute name='new_name' />"
        + "<attribute name='new_code' />"
        + "<attribute name='new_l1casetype_id' />"
        //三级案例修改排序 已搜索排序显示
        + "<order attribute='new_displaysequence' descending='false' />"
        + "<filter type='and'>"
        + "<condition attribute='statecode' operator='eq' value='0' />  "
        + "</filter>"
        + "<link-entity name='new_l2casetype' from='new_l2casetypeid' to='new_l2casetype_id' visible='false' link-type='inner' alias='l2type'>"
        + "<attribute name='new_modelname' />"
        + "<filter type='and'>"
        + "<condition attribute='statecode' operator='eq' value='0' />  "
        + "<filter type='or'>"
        + "<condition attribute='new_prosupmod_id' operator='null'/>"
        + "<condition attribute='new_prosupmod_id' operator='eq' value='" + new_prosupmod_id_value + "' /> "
        + "</filter>"
        + "</filter>"
        + "</link-entity>"
        + "</entity>"
        + "</fetch>";

    cell = "<cell name='l2type.new_modelname' width='250'/>"
        + "<cell name='new_l1casetype_id' width='150'/>";

    var layoutXML = "<grid name='resultset' object='10' jump='name' select='1' icon='1' preview='1'>"
        + "<row name='result' id='new_l3casetypeid'>"
        + "<cell name='new_name' width='150'/>"
        + cell
        + "</row>"
        + "</grid>";
    Xrm.Page.getControl("new_l3casetype_id").addCustomView(XM.NEWID(), "new_l3casetype", '自定义查找', fetchXML, layoutXML, true);
}
//批量分配点击事件
function BatchAssignClick() {
    OpenBatchPage(1)
}
//批量分配显隐
function BatchAssignEnable() {
    try {
        var isEnabled = formType != 1;
        //渠道
        var new_channel = Xrm.Page.getAttribute('new_channel').getValue();
        //来源
        var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
        //非大家电不显示
        if (new_channel != 5 || (new_casesource != 7 && new_casesource != 8 && new_casesource != 9)) {
            isEnabled = false;
        }
        //非坐席组长不显示
        if (!UserRoleRibbons.includes("new.incident.Button.BatchAssign")) {
            isEnabled = false;
        }
        var new_caselevel = Xrm.Page.getAttribute('new_caselevel').getValue();
        var new_casestatus = Xrm.Page.getAttribute('new_casestatus').getValue();
        //一线案例不显示
        if (new_caselevel != 2)
            isEnabled = false;
        //非待处理和处理中不显示
        if (new_casestatus != 1 && new_casestatus != 2)
            isEnabled = false;
        return isEnabled;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
//打开批量分配弹窗
function OpenBatchPage(type) {
    var orderNo = Xrm.Page.getAttribute('new_miordercode').getValue();
    var incidentId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    var curUserId = Xrm.Page.context.getUserId().replace('{', '').replace('}', '');
    var pageInput = {
        pageType: "webresource",
        webresourceName: "new_/Service/new_BatchAssignIncident.html",
        data: encodeURIComponent("incidentid=" + incidentId + "&curUserId=" + curUserId + "&orderno=" + orderNo + "&type=" + type)
    }
    var navigationOptions = {
        title: "  ",
        target: 2,
        width: 700,
        height: 500,
        position: 1
    };
    Xrm.Navigation.navigateTo(pageInput, navigationOptions).then(res => {
        if (res.returnValue && res.returnValue == "ok") {
            rtcrm.closeLayerLoading();
            XM.refresh();
        }
    })
}

//分派按钮显隐
function AssignBtnEnable() {
    try {
        var isEnabled = formType != 1;
        //渠道
        var new_channel = Xrm.Page.getAttribute('new_channel').getValue();
        //来源
        var new_casesource = Xrm.Page.getAttribute('new_casesource').getValue();
        //大家电不显示
        if (new_channel == 5 && (new_casesource == 7 || new_casesource == 8 || new_casesource == 9)) {
            isEnabled = false;
        }
        return isEnabled;
    } catch (e) {
        Xrm.Utility.alertDialog(e);
        return false;
    }
}
// 发货单解冻
function waybillUnfreezing() {
    var oldTime = localStorage.getItem('old_unfreezing_time');
    var time = Xrm.Page.getAttribute('new_unfreezetime').getValue();
    var waybillId = Xrm.Page.getAttribute('new_shipordernumber').getValue();
    var caseStatus =  Xrm.Page.getAttribute('new_casestatus').getValue();
    if(time == null || oldTime === null || time.toString() === oldTime) {
        console.log(`🚀 ~ res`, "未更改时间, 不触发解冻");
        return;
    }
    if( caseStatus !== 4) {
        console.log(`🚀 ~ res`, "案例状态不正确，订单无法解冻");
        return;
    }
    var res = rtcrm.invokeAction('new_WaybillUnfreezingAction', {
        input: JSON.stringify({
            waybillId: waybillId,
            earliestAvailableTime: time.getTime() / 1000,
            incidentId: Xrm.Page.data.entity.getId().replace('{','').replace('}',''),
        })
    });
    localStorage.removeItem('old_unfreezing_time')
    console.log(`🚀 ~ 解冻时间:`,  time.getTime() / 1000);
}
// 发货单解冻
function waybillUnfreezing() {
    var oldTime = localStorage.getItem('old_unfreezing_time');
    var time = Xrm.Page.getAttribute('new_unfreezetime').getValue();
    var waybillId = Xrm.Page.getAttribute('new_shipordernumber').getValue();
    var caseStatus =  Xrm.Page.getAttribute('new_casestatus').getValue();
    if(time == null || oldTime === null || time.toString() === oldTime) {
        console.log(`🚀 ~ res`, "未更改时间, 不触发解冻");
        return;
    }
    if( caseStatus !== 4) {
        console.log(`🚀 ~ res`, "案例状态不正确，订单无法解冻");
        return;
    }
    var res = rtcrm.invokeAction('new_WaybillUnfreezingAction', {
        input: JSON.stringify({
            waybillId: waybillId,
            earliestAvailableTime: time.getTime() / 1000,
            incidentId: Xrm.Page.data.entity.getId().replace('{','').replace('}',''),
        })
    });
    localStorage.removeItem('old_unfreezing_time')
    console.log(`🚀 ~ 解冻时间:`,  time.getTime() / 1000);
}
function initCreateIncidentTime(){
    var new_acwstartedon = Xrm.Page.getAttribute("new_acwstartedon");
    if(new_acwstartedon == null || new_acwstartedon?.getValue() != null || formType != 1){
        return;
    }
    var session = Microsoft.Apm?.getFocusedSession();
    if(session){
        var time = sessionStorage.getItem( session.sessionId+'|'+"CreateIncidentClickTime");
        if(time){
            new_acwstartedon.setValue(new Date(time));
            sessionStorage.removeItem(session.sessionId+'|'+"CreateIncidentClickTime");
        }else{
            new_acwstartedon.setValue(new Date());
        }
    } else{
        new_acwstartedon.setValue(new Date());
    }
}