﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RekTec.ServiceSettlement.Function.Model
{
    public class AdjustModel
    {
        /// <summary>
        /// 服务单id
        /// </summary>
        public string new_srv_workorderid { get; set; }
        /// <summary>
        /// 结算单id
        /// </summary>
        public string new_srv_expense_claimid { get; set; }
        /// <summary>
        /// 特殊费用
        /// </summary>
        public decimal new_specialfeeshare { get; set; }
        /// <summary>
        /// 维修劳务费（KPI）
        /// </summary>
        public decimal new_repairfeekpi { get; set; }
        /// <summary>
        /// 检测费kpi
        /// </summary>
        public decimal new_detectionapi { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 调差金额
        /// </summary>
        public decimal new_changemoney { get; set; }
        /// <summary>
        /// 是否最后一条服务单
        /// </summary>
        public bool new_isend { get; set; }
        /// <summary>
        /// 区域
        /// </summary>
        public string new_area { get; set; }
        /// <summary>
        /// 换机local buy费（Buysell）
        /// </summary>
        public decimal new_localbuyfeebuysell { get; set; }
        /// <summary>
        /// 换机mark up费（Buysell）
        /// </summary>
        public decimal new_localbuyfeemarkupbuysell { get; set; }
        /// <summary>
        /// 预提费（劳务费）
        /// </summary>
        public decimal new_withholdingfeerepair { get; set; }
        /// <summary>
        /// 预提反冲费（劳务费）
        /// </summary>
        public decimal new_withholdingfeerecoilrepair { get; set; }
        /// <summary>
        /// 工单费用表id
        /// </summary>
        public string new_workorder_costtable_id { get; set; }
    }
    public class AdjustModelMaitrox
    {
        private Dictionary<string, decimal> properties = new Dictionary<string, decimal>();

        public decimal this[string key]
        {
            get
            {
                if (properties.ContainsKey(key))
                {
                    return properties[key];
                }
                return 0; // 或者抛出异常，视情况而定
            }
            set
            {
                properties[key] = value;
            }
        }
        /// <summary>
        /// 服务单id
        /// </summary>
        public string new_srv_workorderid { get; set; }
        /// <summary>
        /// 结算单id
        /// </summary>
        public string new_srv_expense_claimid { get; set; }
        /// <summary>
        /// kpi值
        /// </summary>
        public decimal kpivalue { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoney { get; set; }
        /// <summary>
        /// 是否最后一条服务单
        /// </summary>
        public bool new_isend { get; set; }
        /// <summary>
        /// 换机local buy费用
        /// </summary>
        public decimal new_localbuyreplacementcost { get; set; }
        /// <summary>
        /// 换机local buy markup费用
        /// </summary>
        public decimal new_markupreplacementcost { get; set; }
        /// <summary>
        /// 客户退款
        /// </summary>
        public decimal new_customerrefund
        {
            get { return this["new_customerrefund"]; }
            set { this["new_customerrefund"] = value; }
        }
        /// <summary>
        /// 生态链品类回购费
        /// </summary>
        public decimal new_ecosystemcategorybuybackfee 
        {
            get { return this["new_ecosystemcategorybuybackfee"]; }
            set { this["new_ecosystemcategorybuybackfee"] = value; }
        }
        /// <summary>
        /// 迈创其他费用
        /// </summary>
        public decimal new_othermiscellaneouscharges
        {
            get { return this["new_othermiscellaneouscharges"]; }
            set { this["new_othermiscellaneouscharges"] = value; }
        }
        /// <summary>
        /// 物流费markup
        /// </summary>
        public decimal new_markuplogisticsfee
        {
            get { return this["new_markuplogisticsfee"]; }
            set { this["new_markuplogisticsfee"] = value; }
        }
        /// <summary>
        /// 仓储费
        /// </summary>
        public decimal new_warehousingfee
        {
            get { return this["new_warehousingfee"]; }
            set { this["new_warehousingfee"] = value; }
        }
        /// <summary>
        /// 固定服务费
        /// </summary>
        public decimal new_fixedservicefee
        {
            get { return this["new_fixedservicefee"]; }
            set { this["new_fixedservicefee"] = value; }
        }
        /// <summary>
        /// 资本利息费
        /// </summary>
        public decimal new_capitalinterestexpense
        {
            get { return this["new_capitalinterestexpense"]; }
            set { this["new_capitalinterestexpense"] = value; }
        }
        /// <summary>
        /// 高维费用
        /// </summary>
        public decimal new_refurbishfee
        {
            get { return this["new_refurbishfee"]; }
            set { this["new_refurbishfee"] = value; }
        }
        /// <summary>
        /// 备件服务费KPI
        /// </summary>
        public decimal new_partservicecostkpi { get; set; }
        /// <summary>
        /// 备件服务费
        /// </summary>
        public decimal new_partservicecost { get; set; }
        /// <summary>
        /// 备件费
        /// </summary>
        public decimal new_sparepartscost { get; set; }
        /// <summary>
        /// 工单量保底费用
        /// </summary>
        public decimal new_minimumworkorderfee { get; set; }
        /// <summary>
        /// 直发费用
        /// </summary>
        public decimal new_directshippingcost { get; set; }
        /// <summary>
        /// 迈创预提费
        /// </summary>
        public decimal new_withholdingfeemaitrox { get; set; }
        /// <summary>
        /// 预提反冲费
        /// </summary>
        public decimal new_withholdingfeerecoil { get; set; }
    }
    public class AdjustModelActivate
    {
        /// <summary>
        /// 激活结算单明细id
        /// </summary>
        public string new_srv_expense_activationlineid { get; set; }
        /// <summary>
        /// 结算单id
        /// </summary>
        public string new_expense_claim_id { get; set; }
        /// <summary>
        /// 特殊费用
        /// </summary>
        public decimal new_specialfeeshare { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoney { get; set; }
        /// <summary>
        /// 调差金额
        /// </summary>
        public decimal new_changemoney { get; set; }
        /// <summary>
        /// 预提费（激活）
        /// </summary>
        public decimal new_withholdingfeeactivate { get; set; }
        /// <summary>
        /// 预提反冲费（激活）
        /// </summary>
        public decimal new_withholdingfeerecoilactivate { get; set; }
    }
    public class AdjustModelHandling
    {
        /// <summary>
        /// 服务单id
        /// </summary>
        public string new_srv_workorderid { get; set; }
        /// <summary>
        /// 结算单id
        /// </summary>
        public string new_srv_expense_claimid { get; set; }
        /// <summary>
        /// 特殊费用
        /// </summary>
        public decimal new_othermiscellaneouschargeshandling { get; set; }
        /// <summary>
        /// 呼叫费
        /// </summary>
        public decimal new_callfeehandling { get; set; }
        /// <summary>
        /// 销售额
        /// </summary>
        public decimal new_saleamounthandling { get; set; }
        /// <summary>
        /// 收集费
        /// </summary>
        public decimal new_handlingfee { get; set; }
        /// <summary>
        /// 收集费KPI
        /// </summary>
        public decimal new_handlingfeekpi { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoneyhandling { get; set; }
        /// <summary>
        /// 调差金额
        /// </summary>
        public decimal new_changemoneyhandling { get; set; }
        /// <summary>
        /// 预提费（handling）
        /// </summary>
        public decimal new_withholdingfeehandling { get; set; }
        /// <summary>
        /// 预提反冲费
        /// </summary>
        public decimal new_withholdingfeerecoilhandling { get; set; }
        /// <summary>
        /// 工单费用表id
        /// </summary>
        public string new_workorder_costtable_id { get; set; }
    }
    public class AdjustModelStorage 
    {
        /// <summary>
        /// 工单费用
        /// </summary>
        public string new_workorder_costtableid { get; set; }
        /// <summary>
        /// 结算单
        /// </summary>
        public Guid new_expenseclaimidstorage { get; set; }
        /// <summary>
        /// 人力固定费
        /// </summary>
        public decimal new_fixedlaborcost { get; set; }
        /// <summary>
        /// 运营固定费-管理费
        /// </summary>
        public decimal new_fixedoperationcosts_management { get; set; }
        /// <summary>
        /// 运营固定费-其他
        /// </summary>
        public decimal new_fixedoperationcosts_other { get; set; }
        /// <summary>
        /// 运营固定费-包材
        /// </summary>
        public decimal new_fixedoperationcosts_packaging { get; set; }
        /// <summary>
        /// 运营固定费-托盘报废
        /// </summary>
        public decimal new_fixedoperationcosts_scrapped { get; set; }
        /// <summary>
        /// 运营固定费-水电网
        /// </summary>
        public decimal new_fixedoperationcosts_utilities { get; set; }
        /// <summary>
        /// 租金固定费
        /// </summary>
        public decimal new_fixedrentalfee { get; set; }
        /// <summary>
        /// 入库上架费
        /// </summary>
        public decimal new_inboundshelvingcharge { get; set; }
        /// <summary>
        /// 出入库操作费
        /// </summary>
        public decimal new_entryexithandlingfee { get; set; }
        /// <summary>
        /// 大小件操作费
        /// </summary>
        public decimal new_itemsizehandlingfee { get; set; }
        /// <summary>
        /// 物流费
        /// </summary>
        public decimal new_storageandlogisticscost { get; set; }
        /// <summary>
        /// 打包费
        /// </summary>
        public decimal new_packingfee { get; set; }
        /// <summary>
        /// 质检费
        /// </summary>
        public decimal new_qualityinspectionfee { get; set; }
        /// <summary>
        /// 租金货架费
        /// </summary>
        public decimal new_shelfrentalfee { get; set; }
        /// <summary>
        /// 电视操作费
        /// </summary>
        public decimal new_televisionoperationfee { get; set; }
        /// <summary>
        /// 人力变动费-基本工资
        /// </summary>
        public decimal new_variablelaborcost_basic { get; set; }
        /// <summary>
        /// 人力变动费-工作日加班
        /// </summary>
        public decimal new_variablelaborcost_overtime { get; set; }
        /// <summary>
        /// 人力变动费-节假日加班
        /// </summary>
        public decimal new_variablelaborcost_overtime1 { get; set; }
        /// <summary>
        /// 租金扩仓费
        /// </summary>
        public decimal new_warehouseexpansionfee { get; set; }
        /// <summary>
        /// 其他特殊费用
        /// </summary>
        public decimal new_storageotherfee { get; set; }
        /// <summary>
        /// 其他特殊费用(KPI)
        /// </summary>
        public decimal new_storageotherfeekpi { get; set; }
        /// <summary>
        /// 其他费用-包材费(KPI)
        /// </summary>
        public decimal new_storageotherfeekpi1 { get; set; }
        /// <summary>
        /// 其他费用-保险费(KPI)

        /// </summary>
        public decimal new_storageotherfeekpi2 { get; set; }
        /// <summary>
        /// 其他费用-进口报关费(KPI)
        /// </summary>
        public decimal new_storageotherfeekpi3 { get; set; }
        /// <summary>
        /// 其他费用-包材费
        /// </summary>
        public decimal new_storageotherfee1 { get; set; }
        /// <summary>
        /// 其他费用-保险费

        /// </summary>
        public decimal new_storageotherfee2 { get; set; }
        /// <summary>
        /// 其他费用-进口报关费
        /// </summary>
        public decimal new_storageotherfee3 { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoneystorage { get; set; }
        public decimal new_changemoneystorage { get; set; }
    }
    /// <summary>
    /// 非B2X和B2X结算单调差对象Model
    /// </summary>
    public class AdjustModelRequest 
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public int settlementType { get; set; }
        public List<AdjustModel> amlist { get; set; }
    }
    /// <summary>
    /// Maitrox结算单调差对象Model
    /// </summary>
    public class AdjustModelMaitroxRequest
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public List<AdjustModelMaitrox> amlist { get; set; }
    }
    /// <summary>
    /// 激活结算单调差对象Model
    /// </summary>
    public class AdjustModelActivateRequest 
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public List<AdjustModelActivate> amlist { get; set; }
    }
    /// <summary>
    /// 运营商、运营商转派结算单调差对象Model
    /// </summary>
    public class AdjustModelHandlingRequest 
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public List<AdjustModelHandling> amlist { get; set; }
    }
    /// <summary>
    /// 仓储结费结算单调差对象Model
    /// </summary>
    public class AdjustModelStorageRequest
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public List<AdjustModelStorage> amlist { get; set; }
    }
    /// <summary>
    /// SPM返回的tokenModel
    /// </summary>
    public class AuthorizationModel
    {
        /// <summary>
        /// token类型，仅支持Bearer
        /// </summary>
        public string token_type { get; set; }
        /// <summary>
        /// token有效时间，秒为单位（默认1小时）
        /// </summary>
        public string expires_in { get; set; }
        /// <summary>
        /// 暂时用不到
        /// </summary>
        public string ext_expires_in { get; set; }
        /// <summary>
        /// token失效时间，数值是从1970-01-01T0:0:0Z UTC 到失效时间的偏移量，秒为单位
        /// </summary>
        public string expires_on { get; set; }
        /// <summary>
        /// token生效时间，数值是从1970-01-01T0:0:0Z UTC 到生效时间的偏移量，秒为单位
        /// </summary>
        public string not_before { get; set; }
        /// <summary>
        /// token对应的服务资源，与请求中的参数一致
        /// </summary>
        public string resource { get; set; }
        /// <summary>
        /// token内容
        /// </summary>
        public string access_token { get; set; }
    }
    public class Tax
    {
        public string name { get; set; }
        public string contractbody { get; set; }
        public string tax { get; set; }
        public string sku1 { get; set; }
        public string sku2 { get; set; }
    }
    /// <summary>
    /// 费用权重配置
    /// </summary>
    public class CostWeightConfig
    {
        /// <summary>
        /// 三级品类
        /// </summary>
        public string new_category3_id { get; set; }
        /// <summary>
        /// 工单量
        /// </summary>
        public int workordercount { get; set; }
        /// <summary>
        /// 权重
        /// </summary>
        public decimal weight { get; set; }
        /// <summary>
        /// 工单量乘以权重的结果
        /// </summary>
        public decimal result { get; set; }
        /// <summary>
        /// 三级品类按照权重分摊费用
        /// </summary>
        public decimal apportionfee { get; set; }
    }
    /// <summary>
    /// 安装结算单调差model
    /// </summary>
    public class AdjustModelInstall 
    {
        /// <summary>
        /// 服务单id
        /// </summary>
        public string new_srv_workorderid { get; set; }
        /// <summary>
        /// 工单费用表id
        /// </summary>
        public string new_workorder_costtable_id { get; set; }
        /// <summary>
        /// 特殊费用
        /// </summary>
        public decimal new_specialfee { get; set; }
        /// <summary>
        /// 安装劳务费（KPI）
        /// </summary>
        public decimal new_installfeekpi { get; set; }
        /// <summary>
        /// 预提费
        /// </summary>
        public decimal new_withholdingfeeinstall { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoneyinstall { get; set; }
        /// <summary>
        /// 安装固定月度费
        /// </summary>
        public decimal new_fixedmonthfeeinstall { get; set; }
    }
    /// <summary>
    /// 安装结算单调差对象Model
    /// </summary>
    public class AdjustModelInstallRequest
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public int settlementType { get; set; }
        public List<AdjustModelInstall> amlist { get; set; }
    }
    public class AdjustModelHighdimensionalfactory
    {
        /// <summary>
        /// 修整单明细id
        /// </summary>
        public string new_trimming_orderdetailid { get; set; }
        /// <summary>
        /// 结算单id
        /// </summary>
        public string new_expense_claim_id { get; set; }
        /// <summary>
        /// 高维工厂劳务费
        /// </summary>
        public decimal new_high_dimensionalfee { get; set; }
        /// <summary>
        /// 高维工厂劳务费KPI
        /// </summary>
        public decimal new_high_dimensionalfeekpi { get; set; }
        /// <summary>
        /// 特殊费用
        /// </summary>
        public decimal new_specialfeeshare { get; set; }
        /// <summary>
        /// 费用合计
        /// </summary>
        public decimal new_settlementmoney { get; set; }
        /// <summary>
        /// 预提金额
        /// </summary>
        public decimal new_withholdingmoney { get; set; }
        /// <summary>
        /// 调差金额
        /// </summary>
        public decimal new_changemoney { get; set; }
        /// <summary>
        /// 预提费（激活）
        /// </summary>
        public decimal new_withholdingfeeHighdimensionalfactory { get; set; }
        /// <summary>
        /// 预提反冲费（激活）
        /// </summary>
        public decimal new_withholdingfeerecoilHighdimensionalfactory { get; set; }
    }
    /// <summary>
    /// 高维工厂结算单调差请求对象Model
    /// </summary>
    public class AdjustModelHighdimensionalfactoryRequest
    {
        public string new_srv_expense_claimid { get; set; }
        public string new_settlementasyncjoblogId { get; set; }
        public int settlementType { get; set; }
        public List<AdjustModelHighdimensionalfactory> amlist { get; set; }
    }
}
