﻿using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using Microsoft.Xrm.Sdk.Messages;
using System.Diagnostics;
using System.IdentityModel.Metadata;
using System.Data.OleDb;
using System.Data;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using System.Configuration;

namespace SyncActivityNameTranslation
{
    /// <summary>
    /// 读取表格Sheet的数据集合
    /// </summary>
    public class SheetDataInfo
    {
        // 项目编码
        public string Code { get; set; }
        // 项目ID
        public string DataId { get; set; }
        // 项目名称
        public string Name { get; set; }
        // 汉语简体翻译ID
        public string LangIdCN { get; set; }
        // 汉语简体翻译内容
        public string NameCN { get; set; }
        // 英语翻译ID
        public string LangIdEN { get; set; }
        // 英语翻译内容
        public string NameEN { get; set; }
        // 汉语繁体（台湾）翻译ID
        public string LangIdTW { get; set; }
        // 汉语繁体（台湾）翻译内容
        public string NameTW { get; set; }
        // 汉语繁体（香港）翻译ID
        public string LangIdHK { get; set; }
        // 汉语繁体（香港）翻译内容
        public string NameHK { get; set; }
    }

    // 表格数据
    public class ExcelData
    {
        // 亚洲环境数据
        public List<SheetDataInfo> AsiaDt { get; set; }
        // 欧洲环境数据
        public List<SheetDataInfo> EuropeDt { get; set; }
        // 拉美环境数据
        public List<SheetDataInfo> LatinDt { get; set; }
        // 中东环境数据
        public List<SheetDataInfo> UaeDt { get; set; }
        // 测试环境数据
        public List<SheetDataInfo> UatDt { get; set; }
    }
    public class Program
    {
        // UAT
        public static string UatConnectionString = ConfigurationManager.AppSettings["UatConnectionString"];
        // 亚洲
        public static string AsiaConnectionString = ConfigurationManager.AppSettings["AsiaConnectionString"];
        // 欧洲
        public static string EuropeConnectionString = ConfigurationManager.AppSettings["EuropeConnectionString"];
        // 拉美
        public static string LatinConnectionString = ConfigurationManager.AppSettings["LatinConnectionString"];
        // 中东
        public static string UaeConnectionString = ConfigurationManager.AppSettings["UaeConnectionString"];
        // 汉语简体
        public static string SimplifiedChinese = "2052";
        // 英语
        public static string English = "1033";
        // 汉语繁体（中国香港）
        public static string TraditionalChineseHK = "3076";
        // 汉语繁体（中国台湾）
        public static string TraditionalChineseTW = "1028";

        static void Main(string[] args)
        {
            var filePath = "D:\\项目名称翻译补充-uat.xlsx";
            var dt = ReadExcelWithNPOI(filePath);

            #region UAT
            if (!string.IsNullOrEmpty(UatConnectionString))
            {
                try
                {
                    ServiceClient UatService = new ServiceClient(UatConnectionString);

                    if (dt.UatDt.Count > 0)
                    {
                        // 获取语言ID
                        var ChineseLangId = GetLangId(UatService, SimplifiedChinese);
                        var EnglishLangId = GetLangId(UatService, English);
                        var ChineseTWLangId = GetLangId(UatService, TraditionalChineseTW);
                        var ChineseHKLangId = GetLangId(UatService, TraditionalChineseHK);

                        foreach (var item in dt.UatDt)
                        {
                            try
                            {
                                if (string.IsNullOrEmpty(item.LangIdCN) && !string.IsNullOrEmpty(item.NameCN))
                                {
                                    CreateLanguageConfig(ChineseLangId, item, UatService, SimplifiedChinese);
                                }
                                if (string.IsNullOrEmpty(item.LangIdEN) && !string.IsNullOrEmpty(item.NameEN))
                                {
                                    CreateLanguageConfig(EnglishLangId, item, UatService, English);
                                }
                                if (string.IsNullOrEmpty(item.LangIdHK) && !string.IsNullOrEmpty(item.NameHK))
                                {
                                    CreateLanguageConfig(ChineseHKLangId, item, UatService, TraditionalChineseHK);
                                }
                                if (string.IsNullOrEmpty(item.LangIdTW) && !string.IsNullOrEmpty(item.NameTW))
                                {
                                    CreateLanguageConfig(ChineseTWLangId, item, UatService, TraditionalChineseTW);
                                }
                            }
                            catch (Exception ex)
                            {
                                WriteLog(@"D:\Crm\", "亚洲数据处理", $"{item.Code} 执行异常{ex.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(@"D:\Crm\", "亚洲数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 亚洲
            if (!string.IsNullOrEmpty(AsiaConnectionString))
            {
                try
                {
                    ServiceClient AsiaService = new ServiceClient(AsiaConnectionString);

                    if (dt.AsiaDt.Count > 0)
                    {
                        var ChineseLangId = GetLangId(AsiaService, SimplifiedChinese);
                        var EnglishLangId = GetLangId(AsiaService, English);
                        var ChineseTWLangId = GetLangId(AsiaService, TraditionalChineseTW);
                        var ChineseHKLangId = GetLangId(AsiaService, TraditionalChineseHK);

                        foreach (var item in dt.AsiaDt)
                        {
                            if (string.IsNullOrEmpty(item.LangIdCN) && !string.IsNullOrEmpty(item.NameCN))
                            {
                                CreateLanguageConfig(ChineseLangId, item, AsiaService, SimplifiedChinese);
                            }
                            if (string.IsNullOrEmpty(item.LangIdEN) && !string.IsNullOrEmpty(item.NameEN))
                            {
                                CreateLanguageConfig(EnglishLangId, item, AsiaService, English);
                            }
                            if (string.IsNullOrEmpty(item.LangIdHK) && !string.IsNullOrEmpty(item.NameHK))
                            {
                                CreateLanguageConfig(ChineseHKLangId, item, AsiaService, TraditionalChineseHK);
                            }
                            if (string.IsNullOrEmpty(item.LangIdTW) && !string.IsNullOrEmpty(item.NameTW))
                            {
                                CreateLanguageConfig(ChineseTWLangId, item, AsiaService, TraditionalChineseTW);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(@"D:\Crm\", "亚洲数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 欧洲
            if (!string.IsNullOrEmpty(EuropeConnectionString))
            {
                try
                {
                    ServiceClient EuropeService = new ServiceClient(EuropeConnectionString);

                    if (dt.EuropeDt.Count > 0)
                    {
                        var ChineseLangId = GetLangId(EuropeService, SimplifiedChinese);
                        var EnglishLangId = GetLangId(EuropeService, English);

                        foreach (var item in dt.EuropeDt)
                        {
                            if (string.IsNullOrEmpty(item.LangIdCN) && !string.IsNullOrEmpty(item.NameCN))
                            {
                                CreateLanguageConfig(ChineseLangId, item, EuropeService, SimplifiedChinese);
                            }
                            if (string.IsNullOrEmpty(item.LangIdEN) && !string.IsNullOrEmpty(item.NameEN))
                            {
                                CreateLanguageConfig(EnglishLangId, item, EuropeService, English);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(@"D:\Crm\", "欧洲数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 拉美
            if (!string.IsNullOrEmpty(LatinConnectionString))
            {
                try
                {
                    ServiceClient LatinService = new ServiceClient(LatinConnectionString);

                    if (dt.LatinDt.Count > 0)
                    {
                        var ChineseLangId = GetLangId(LatinService, SimplifiedChinese);
                        var EnglishLangId = GetLangId(LatinService, English);

                        foreach (var item in dt.LatinDt)
                        {
                            if (string.IsNullOrEmpty(item.LangIdCN) && !string.IsNullOrEmpty(item.NameCN))
                            {
                                CreateLanguageConfig(ChineseLangId, item, LatinService, SimplifiedChinese);
                            }
                            if (string.IsNullOrEmpty(item.LangIdEN) && !string.IsNullOrEmpty(item.NameEN))
                            {
                                CreateLanguageConfig(EnglishLangId, item, LatinService, English);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(@"D:\Crm\", "拉美数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion

            #region 中东
            if (!string.IsNullOrEmpty(UaeConnectionString))
            {
                try
                {
                    ServiceClient UaeService = new ServiceClient(UaeConnectionString);

                    if (dt.UaeDt.Count > 0)
                    {
                        var ChineseLangId = GetLangId(UaeService, SimplifiedChinese);
                        var EnglishLangId = GetLangId(UaeService, English);

                        foreach (var item in dt.UaeDt)
                        {
                            if (string.IsNullOrEmpty(item.LangIdCN) && !string.IsNullOrEmpty(item.NameCN))
                            {
                                CreateLanguageConfig(ChineseLangId, item, UaeService, SimplifiedChinese);
                            }
                            if (string.IsNullOrEmpty(item.LangIdEN) && !string.IsNullOrEmpty(item.NameEN))
                            {
                                CreateLanguageConfig(EnglishLangId, item, UaeService, English);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(@"D:\Crm\", "中东数据处理", $"执行异常{ex.Message}");
                }
            }
            #endregion
        }

        /// <summary>
        /// 创建语言翻译
        /// </summary>
        /// <param name="langId"></param>
        /// <param name="item"></param>
        /// <param name="service"></param>
        public static void CreateLanguageConfig(Guid langId, SheetDataInfo item, ServiceClient service, string langCode)
        {
            string translationValue = string.Empty;
            if (langCode == SimplifiedChinese)
            {
                translationValue = item.NameCN;
            }
            else if (langCode == English)
            {
                translationValue = item.NameEN;
            }
            else if (langCode == TraditionalChineseHK)
            {
                translationValue = item.NameHK;
            }
            else if (langCode == TraditionalChineseTW)
            {
                translationValue = item.NameTW;
            }
            else
            {
                translationValue = item.Name;
            }

            Entity newEnt = new Entity("new_data_languageconfig");
            newEnt["new_entity_name"] = "new_projectinfo";
            newEnt["new_attribute_name"] = "new_name";
            newEnt["new_language_id"] = new EntityReference("new_language", langId);
            newEnt["new_value"] = translationValue;
            newEnt["new_code"] = item.Code;
            newEnt["new_data_id"] = item.DataId;
            newEnt["new_language_code"] = langCode;
            service.Create(newEnt);
        }

        /// <summary>
        /// 获取语言ID
        /// </summary>
        /// <param name="service"></param>
        /// <param name="langId"></param>
        /// <returns></returns>
        public static Guid GetLangId(ServiceClient service, string langId)
        {
            QueryExpression query = new QueryExpression("new_language");
            query.NoLock = true;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_langid", ConditionOperator.Equal, langId);
            EntityCollection collection = service.RetrieveMultiple(query);
            if (collection.Entities.Count > 0)
            {
                return collection.Entities[0].Id;
            }
            else
            {
                return Guid.Empty;
            }
        }

        /// <summary>
        /// 从表格中获取数据
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static ExcelData ReadExcelWithNPOI(string filePath)
        {
            var data = new ExcelData();
            using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook;

                if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                    workbook = new XSSFWorkbook(stream);
                else
                    workbook = new HSSFWorkbook(stream);

                for (int i = 0; i < ((NPOI.XSSF.UserModel.XSSFWorkbook)workbook).Count; i++)
                {
                    ISheet sheet = workbook.GetSheetAt(i); // 第一个工作表

                    var dataList = new List<SheetDataInfo>();
                    for (int row = 1; row <= sheet.LastRowNum; row++)
                    {
                        IRow currentRow = sheet.GetRow(row);
                        if (currentRow == null) continue;

                        SheetDataInfo dt = new SheetDataInfo();
                        dt.Code = currentRow.GetCell(0).ToString();
                        dt.DataId = currentRow.GetCell(1).ToString();
                        dt.Name = currentRow.GetCell(2)?.ToString();
                        dt.LangIdCN = currentRow.GetCell(3)?.ToString();
                        dt.NameCN = currentRow.GetCell(4)?.ToString();
                        dt.LangIdEN = currentRow.GetCell(5)?.ToString();
                        dt.NameEN = currentRow.GetCell(6)?.ToString();
                        dt.LangIdTW = currentRow.GetCell(7)?.ToString();
                        dt.NameTW = currentRow.GetCell(8)?.ToString();
                        dt.LangIdHK = currentRow.GetCell(9)?.ToString();
                        dt.NameHK = currentRow.GetCell(10)?.ToString();
                        dataList.Add(dt);
                    }
                    var sheetName = ((NPOI.XSSF.UserModel.XSSFSheet)sheet).SheetName;
                    if (sheetName == "欧洲")
                    {
                        data.EuropeDt = dataList;
                    }
                    if (sheetName == "拉美")
                    {
                        data.LatinDt = dataList;
                    }
                    if (sheetName == "中东")
                    {
                        data.UaeDt = dataList;
                    }
                    if (sheetName == "亚洲")
                    {
                        data.AsiaDt = dataList;
                    }
                    if (sheetName == "uat")
                    {
                        data.UatDt = dataList;
                    }
                }
            }

            return data;
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="strTxtFileHead"></param>
        /// <param name="strInfo"></param>
        public static void WriteLog(string dir, string strTxtFileHead, string strInfo)
        {
            dir = dir + "\\" + DateTime.Now.ToString("yyyy/MM/dd").Replace("/", "-");
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            var finfo = new FileInfo(dir + "\\" + strTxtFileHead + ".log");
            FileStream fs = !finfo.Exists ? finfo.Create() : finfo.OpenWrite();
            //加入时间
            string writeinfo = DateTime.Now.ToString() + ": " + strInfo;
            var w = new StreamWriter(fs);
            try
            {
                w.BaseStream.Seek(0, SeekOrigin.End);
                w.Write(writeinfo);
                w.Write("\r\n\r\n");
            }
            finally
            {
                w.Flush();
                w.Close();
                w.Dispose();
                fs.Close();
                fs.Dispose();
            }
        }
    }
}
