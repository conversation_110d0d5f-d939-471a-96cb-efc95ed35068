﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using RekTec.Crm.OrganizationService.Common.Helper;

namespace New_srv_workorderPlugin.Create
{
    /// <summary>
    /// Module ID：无
    /// Author：p-liuzanxiang
    /// Create Date：2023-04-11
    /// Depiction：服务单创建或更新工单状态为待关单时更新界面逻辑控制字段
    /// URL：https://dev.azure.com/XiaomiServiceCRM/ISP%20CS/_workitems/edit/1685
    /// </summary>
    public class New_srv_workorderPostCreateWayBillWhetherVisible : IPlugin
    {
        #region Secure/Unsecure Configuration Setup
        private string _secureConfig = null;
        private string _unsecureConfig = null;

        public New_srv_workorderPostCreateWayBillWhetherVisible(string unsecureConfig, string secureConfig)
        {
            _secureConfig = secureConfig;
            _unsecureConfig = unsecureConfig;
        }
        #endregion
        public void Execute(IServiceProvider serviceProvider)
        {
            /*
             * 工单创建 或 工单状态变更时触发
             * 
             * 工单选项集客户类型和换货方式的区域控制
             * 所属网点国家编码是否存在于系统参数 CheckIsOrNotCountry 国家列表中
             *  不存修改 界面逻辑控制字段  CheckIsOrNotCountry 修改值为0 否
             *  存在修改 界面逻辑控制字段  CheckIsOrNotCountry 修改值为1 是
             *  
             * 下单适用地区
             * 所属网点国家编码是否存在于系统参数 waybill-region 国家列表中
             *  不存修改 界面逻辑控制字段  WayBillWhetherVisible 修改值为0 否
             *  存在则验证 是否存在物流配置表
             *      根据 工单状态、所属网点编码、工单类型、订单来源查询 物流配置表
             *       未找到物流配置表   WayBillWhetherVisible 修改值为0 否
             *       找到物流配置表    WayBillWhetherVisible 修改值为1 是
             * 
             * **/
            ITracingService tracer = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory factory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            //IOrganizationService service = factory.CreateOrganizationService(context.UserId);
            IOrganizationService service = factory.CreateOrganizationService(null);
            try
            {
                Entity target;//工单状态
                var dealstatus = 0;//工单状态
                var new_station_id = Guid.NewGuid();//所属网点
                var entity = (Entity)context.InputParameters["Target"];
                if (context.MessageName.ToLower() == "update")
                {
                    target = (Entity)context.PreEntityImages["Imeages"];

                    //修改时 工单状态不等于 8 待关单 不触发 
                    if (checkNewStationIdNotChange(entity)) 
                    {
                        if (!entity.Contains("new_dealstatus")) 
                        {
                            return;
                        }

                        if (entity.GetAttributeValue<OptionSetValue>("new_dealstatus").Value != 8) 
                        {
                            return;
                        }
    
                    } 

                    if (!entity.Contains("new_dealstatus") && target.Contains("new_dealstatus"))
                    {
                        dealstatus = target.GetAttributeValue<OptionSetValue>("new_dealstatus").Value;
                    }

                    if (checkNewStationIdNotChange(entity))
                    {
                        new_station_id = target.GetAttributeValue<EntityReference>("new_station_id").Id;                        
                    }
                    else
                    {
                        new_station_id = entity.GetAttributeValue<EntityReference>("new_station_id").Id;
                    }
                }
                else
                {
                    target = (Entity)context.InputParameters["Target"];
                    if (!target.Contains("new_station_id"))
                    {
                        //var workOrder = new Entity(target.LogicalName, target.Id);
                        entity["new_fromcontrol"] = "{\"WayBillWhetherVisible\":0,\"CheckIsOrNotCountry\":0}";
                        //service.Update(workOrder);
                        return;
                    }
                    new_station_id = target.GetAttributeValue<EntityReference>("new_station_id").Id;

                }
                //服务类型
                var type = target.Contains("new_type") ? target.GetAttributeValue<OptionSetValue>("new_type").Value : 0;
                //查询所属网点信息
                var station = GetStationDate(service, new_station_id);
                if (dealstatus == 0)
                {
                    dealstatus = target.Contains("new_dealstatus") ? target.GetAttributeValue<OptionSetValue>("new_dealstatus").Value : 0;
                }
                //界面逻辑控制字段
                Dictionary<string, object> controlObj = new Dictionary<string, object>();

                //国家编码
                var cntyCode = string.Empty;
                if (station.Contains("country.new_id"))
                {
                    cntyCode = station.GetAttributeValue<AliasedValue>("country.new_id").Value.ToString();
                }

                //服务方式
                var serviceMode = -1;
                if (entity.Contains("new_servicemode"))
                {
                    serviceMode = entity.GetAttributeValue<OptionSetValue>("new_servicemode").Value;
                }

                //网点编码
                var stationcode = string.Empty;
                if (station.Contains("new_code"))
                {
                    stationcode = station.GetAttributeValue<string>("new_code");
                }

                //界面逻辑控制json
                if (target.Contains("new_fromcontrol"))
                {
                    var controlStr = target["new_fromcontrol"].ToString();
                    controlObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(controlStr);
                }

                //订单来源
                var orderForm = 0;
                if (target.Contains("new_orderform"))
                {
                    var new_orderform = target.GetAttributeValue<EntityReference>("new_orderform");
                    var new_orderfrom_channel_rel = service.Retrieve(new_orderform.LogicalName, new_orderform.Id, new ColumnSet("new_orderfrom"));
                    orderForm = new_orderfrom_channel_rel.Contains("new_orderfrom") ? new_orderfrom_channel_rel.GetAttributeValue<OptionSetValue>("new_orderfrom").Value : 0;
                }

                //下运单适用的地区
                int whether1 = CheckIsOrNotCountry(service, cntyCode, "waybill-region");

                //工单选项集客户类型和换货方式的区域控制
                int whether2 = CheckIsOrNotCountry(service, cntyCode, "new_srv_workorder_region");

                //地区是是否可用
                controlObj = OperationMember(controlObj, "WayBillWhetherVisible", whether1);
                controlObj = OperationMember(controlObj, "CheckIsOrNotCountry", whether2);

                //下运单适用的地区 可用
                if (whether1 != 0)
                {
                    //是否满足物流配置表
                    var whetherConfig = GetLogisticsConfiguration(service, orderForm, type, stationcode, dealstatus, serviceMode);
                    controlObj = OperationMember(controlObj, "WayBillWhetherVisible", whetherConfig);
                }

                if (context.MessageName.ToLower() == "update")
                {
                    entity["new_fromcontrol"] = JsonConvert.SerializeObject(controlObj);
                }
                else
                {
                    //var workOrder = new Entity(target.LogicalName, target.Id);
                    entity["new_fromcontrol"] = JsonConvert.SerializeObject(controlObj);
                    //service.Update(workOrder);
                }
            }
            catch (Exception e)
            {
                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        private static bool checkNewStationIdNotChange(Entity entity)
        {
            return (!entity.Contains("new_station_id") || entity.GetAttributeValue<EntityReference>("new_station_id") == null);
        }

        /// <summary>
        /// 是否满足物流配置表
        /// </summary>
        /// <param name="pService"></param>
        /// <param name="orderForm">订单来源</param>
        /// <param name="type">工单类型</param>
        /// <param name="stationcode">网点编码</param>
        /// <param name="dealstatus">工单状态</param>
        /// <returns></returns>
        public int GetLogisticsConfiguration(IOrganizationService pService, int orderForm, int type, string stationcode, int dealstatus,int serviceMode)
        {
            QueryExpression queryExpression = new QueryExpression("new_logistics_configuration");
            queryExpression.NoLock = true;
            queryExpression.ColumnSet.AddColumns("new_orderfrom", "new_isautoinverselogistics", "new_ismultiplewaybills", "new_logisticsinterfaceconfig_id");
            queryExpression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            queryExpression.Criteria.AddCondition("new_stationcode", ConditionOperator.Like, "%" + stationcode + "%");
            if(serviceMode != -1)
            {
                queryExpression.Criteria.AddCondition("new_servicemode", ConditionOperator.ContainValues, serviceMode);
            }
            queryExpression.Criteria.AddCondition("new_type", ConditionOperator.ContainValues, type);
            if (dealstatus == 8)
            {
                queryExpression.Criteria.AddCondition("new_isautoinverselogistics", ConditionOperator.Equal, true);
            }
            var List = pService.RetrieveMultiple(queryExpression);
            if (List.Entities.Count > 0)
            {
                if (List.Entities.Any(m => m.Contains("new_orderfrom") && m["new_orderfrom"].ToString().LastIndexOf(orderForm.ToString()) != -1))
                {
                    return 1;
                }
                else if (List.Entities.Any(m => !m.Contains("new_orderfrom")))
                {
                    return 1;
                }
            }
            return 0;
        }

        /// <summary>
        /// 校验当前网点是否为可用地区使用
        /// </summary>
        /// <param name="pService"></param>
        /// <param name="pCntyCode">国家编码</param>
        /// <returns></returns>
        public int CheckIsOrNotCountry(IOrganizationService pService, string pCntyCode, string pConfig)
        {
            string systemParameter = GetSystemParameterValue(pService, pConfig);//需要维护
            string[] toAddressList = systemParameter.Split(',');
            if (toAddressList == null || toAddressList.Length == 0)
            {
                return 0;
            }

            if (!string.IsNullOrEmpty(pCntyCode) && toAddressList.Any(m => m == pCntyCode))
            {
                return 1;
            }
            else
            {
                return 0;
            }
        }


        /// <summary>
        /// 查询服务网点所属国家代码
        /// </summary>
        /// <param name="pService"></param>
        /// <param name="pStationID">所属网点</param>
        /// <returns></returns>
        public Entity GetStationDate(IOrganizationService pService, Guid pStationID)
        {
            var fetchXml = @"<fetch  no-lock='true' mapping='logical'>
                              <entity name='new_srv_station'>
                                <attribute name='new_code' />
                                <filter>
                                    <condition attribute='new_srv_stationid' operator='eq' value='{0}' />
                                </filter>
                                <link-entity name='new_country' to='new_country_id' from='new_countryid' link-type='outer' alias='country'>
                                  <attribute name='new_id' />
                                </link-entity>
                              </entity>
                            </fetch>";
            var ret = pService.RetrieveMultipleWithBypassPlugin(new FetchExpression(string.Format(fetchXml, pStationID)));
            return ret.Entities[0];
        }

        /// <summary>
        /// 查询相关系统参数
        /// </summary>
        /// <param name="pService"></param>
        /// <param name="paramName"></param>
        /// <returns></returns>
        /// <exception cref="InvalidPluginExecutionException"></exception>
        public string GetSystemParameterValue(IOrganizationService pService, string paramName)
        {
            var fetchXml = @"<fetch no-lock='true' mapping='logical'>
                              <entity name='new_systemparameter'>
                                <attribute name='new_name' />
                                <attribute name='new_value' />
                                <filter type='and'>
                                  <condition attribute='new_name' operator='eq' value='{0}' />
                                </filter>
                              </entity>
                            </fetch>";
            var ret = pService.RetrieveMultiple(new FetchExpression(string.Format(fetchXml, paramName)));
            if (ret == null || ret.Entities.Count == 0)
            {
                throw new InvalidPluginExecutionException("缺少系统参数:" + paramName + "，请添加！");
            }
            if (ret.Entities.Count > 1)
            {
                throw new InvalidPluginExecutionException(string.Format("存在多个名称为{0}的系统参数", paramName));
            }
            return ret.Entities[0].GetAttributeValue<string>("new_value");
        }

        /// <summary>
        /// Object虚拟类操作
        /// </summary>
        /// <param name="controlObj"></param>
        /// <param name="pAttrKey"></param>
        /// <param name="pAttrValue"></param>
        /// <returns></returns>
        public Dictionary<string, object> OperationMember(Dictionary<string, object> controlObj, string pAttrKey, int pAttrValue)
        {
            if (controlObj.ContainsKey(pAttrKey))
            {
                controlObj[pAttrKey] = pAttrValue;
            }
            else
            {
                controlObj.Add(pAttrKey, pAttrValue);
            }
            return controlObj;
        }
    }
}