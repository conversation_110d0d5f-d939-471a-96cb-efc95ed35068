﻿//#region 文件描述
/******************************************************************
** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved. 
** 创建人   :   ludwig-dong
** 创建时间 :   2022-08-29
** 说明     :   费用结算单脚本
******************************************************************/
//#endregion

function form_load() {
    this.new_edition_update();
    this.new_formstatus_update();
    this.field_rule();
    WorkorderSectiondisplay();
    SetSectiondisplay();
}
//结算单版本更改事件
function new_edition_update() {
    var new_edition = rtcrm("#new_edition").val();
    var new_formstatus = rtcrm("#new_formstatus").val();
    if (new_edition == 1) {
        rtcrm("#new_withholdingstatus").visible(true);
        rtcrm("#new_settleaccountsstatus").visible(false);
        rtcrm("#new_invoicestatus").visible(false);
        rtcrm("#new_paymentstatus").visible(false);
    }
    if (new_edition == 2) {
        rtcrm("#new_withholdingstatus").visible(false);
        rtcrm("#new_settleaccountsstatus").visible(false);
        rtcrm("#new_invoicestatus").visible(false);
        rtcrm("#new_paymentstatus").visible(false);
        if (new_formstatus == 1) {
            rtcrm("#new_settleaccountsstatus").visible(true);
        }
        if (new_formstatus == 7) {
            rtcrm("#new_invoicestatus").visible(true);
        }
        if (new_formstatus == 8) {
            rtcrm("#new_paymentstatus").visible(true);
        }
    }
}
//费用确认按钮显示隐藏
function btnSettConfirmEnabled() {
    //结算版本
    var edition = rtcrm("#new_edition").val();
    //结算单状态
    var new_formstatus = rtcrm("#new_formstatus").val();
    //结算状态为待确认，结算版本为结算 
    if (edition == 2 && new_formstatus == 5)
        return false;
    return true;
}
//费用确认
function btnSettConfirm() {
    try {
        rtcrm.confirmDialog($t("ServiceSettlement.beenconfirmed", "是否已确认结算单费用？"), function () {
            rtcrm.layerLoading();
            rtcrm.invokeHiddenApiAsync("new_service", "ServiceSettlement/ExpenseConfirmation", { new_srv_expense_claimid: rtcrm.getEntityId() }).then(res => {
                rtcrm.closeLayerLoading();
                rtcrm.alertDialog($t("ServiceSettlement.successful", "确认成功！"), function () {
                    rtcrm.refresh();
                });
            })
        }, function () {
            rtcrm.refresh();
        });
    } catch (e) {
        rtcrm.alertDialog(e.message);
    }
}

// 结算标准-付款申请按钮显示规则 add by Hyacinthhuang 2021/9/23
// 结算单状态="已审核" && 结算单版本="结算单" && 自定义按钮(安全角色="结算专员")
// <summary>
// Modifier：p-dongbinbib
// Modification Date：2023-07-12
// Modify Depiction：调差状态=已调差 & 发票推送SAP状态=已推送 & 结算单版本=结算单 &结算单状态= 发票已接收
// Modify Depiction：加上显示条件，审核状态为空的时候显示 edit by p-songyongxiang 20240111
// </summary>
function showPayApplyBtnRule() {
    var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    var status = rtcrm("#new_formstatus").val(); //状态
    var edition = rtcrm("#new_edition").val(); // 版本
    var new_adjuststatus = Xrm.Page.getAttribute("new_adjuststatus").getValue(); //调差状态
    var new_invoicestatus = Xrm.Page.getAttribute("new_invoicestatus").getValue(); //发票推送SAP状态
    var approvalstatus = rtcrm("#new_approvalstatus").val(); // 审核状态
    //var new_srv_expense_claim = rtcrm.getFieldValue(entityId, "new_srv_expense_claims", "new_approvalstatus", true);
    //if (new_srv_expense_claim != null) {
    //    new_approvalstatus = new_srv_expense_claim["new_approvalstatus"];
    //}
    var ishow = rtcrm.isRibbonEnabledAccordingToRules('new.new_srv_expense_claim.BillCheck.Button');
    if (new_adjuststatus == 3 && new_invoicestatus == 3 && ishow && edition == 2 && status == 7 && approvalstatus == null) {
        return true;
    }
    return false;
}
// 抛BPM动作
function BillCheckAction() {
    if (isFormSaved()) {
        //显示一个警告消息
        Xrm.Navigation.openAlertDialog({ confirmButtonLabel: "OK", text: $t("settlement.checksave", "提交之前先保存更改！") });
    } else {
        //判断当前用户安全角色是否包含“XM服务商结算专员”，该角色不可操作
        var is_jszy = rtcrm.isUserHasRoles(rtcrm.getUserId(), "XM服务商结算专员");
        if (is_jszy) {
            rtcrm.alertDialog($t("ServiceSettlement.SecurityRoleVerification", "安全角色为“XM服务商结算专员”，不可操作！"));
        } else {
            var new_latestpaymentdate = rtcrm("#new_latestpaymentdate").val();
            if (new_latestpaymentdate != null && new_latestpaymentdate != undefined) {
                rtcrm.confirmDialog($t("settlement.confirmpay", "是否确认付款？"), function () {
                    var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                    rtcrm.layerLoading();
                    var type = rtcrm("#new_formstatus").val();
                    var businesstype = rtcrm("#new_businesstype").val();
                    var businesskey = rtcrm("#new_businesskey").val();
                    var isrollback = rtcrm("#new_isrollback").val();
                    if (isrollback == null) {
                        isrollback = false;
                    }
                    if (entityId) {
                        var param = {};
                        param.id = entityId;
                        param.formType = "6";
                        param.businesstype = businesstype;
                        param.businesskey = businesskey;
                        param.isrollback = isrollback;
                        var result = rtcrm.invokeHiddenApi("new_service", "BpmInterface/BpmExpenseSubmit", param);
                        if (result == "200") {
                            rtcrm.closeLayerLoading();
                            rtcrm.alertDialog($t("settlement.pushsuccess", "已提交！"), function () {
                                rtcrm.refresh();
                            });
                        } else {
                            rtcrm.closeLayerLoading();
                            rtcrm.alertDialog(result);
                        }
                    }
                });
            } else {
                rtcrm.alertDialog($t("settlement.checklatestpaymentdate", "请填写最迟付款日期！"));
            }
        }
    }
}

//结算单推送sap按钮事件
function btnSendSap() {
    if (isFormSaved()) {
        //显示一个警告消息
        Xrm.Navigation.openAlertDialog({ confirmButtonLabel: "OK", text: $t("settlement.checksave", "提交之前先保存更改！") });
    } else {
        //判断当前用户安全角色是否包含“XM服务商结算专员”，该角色不可操作
        var is_jszy = rtcrm.isUserHasRoles(rtcrm.getUserId(), "XM服务商结算专员");
        if (is_jszy) {
            rtcrm.alertDialog($t("ServiceSettlement.SecurityRoleVerification", "安全角色为“XM服务商结算专员”，不可操作！"));
        } else {
            var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
            var new_adjuststatus = rtcrm("#new_adjuststatus").val();
            var new_invoicestatus = rtcrm("#new_invoicestatus").val();
            var new_totalcost = rtcrm("#new_totalcost").val();
            if (new_adjuststatus == 3 && new_invoicestatus == 1) {
                var systemparameter = rtcrm.retrieveMultiple("new_systemparameters?$select=new_value&$filter=new_name eq 'JS_WithoutTax' and statecode eq 0 ", false);
                var systemparametervalue = null;
                if (systemparameter != undefined && systemparameter.length > 0) {
                    systemparametervalue = systemparameter[0]["new_value"];
                }
                var stationcode = null;
                var srv_station = rtcrm.getLookupId("new_srv_station_id");
                if (srv_station != null && srv_station != undefined) {
                    var srv_station_id = srv_station.replace("{", "").replace("}", "");
                    var stationentity = rtcrm.retrieve('new_srv_stations(' + srv_station_id + ')?$select=new_code', true);
                    stationcode = stationentity.new_code;
                    var systemparametervaluejson = JSON.parse(systemparametervalue);
                    var matchelement = systemparametervaluejson.filter(element => element.stationcode == stationcode);
                    if (matchelement.length > 0) {
                        var totalcoststr = (new_totalcost / parseFloat(matchelement[0].tax)).toFixed(2);
                        new_totalcost = parseFloat(totalcoststr);
                    }
                }
                var new_receivablessum = 0;
                var sap_batch = rtcrm.retrieveMultiple("new_sap_batchs?$select=new_receivablessum&$filter=_new_expensetno_value eq " + entityId + " and new_infotype eq 3 and statecode eq 0 ", false);
                if (sap_batch != undefined && sap_batch.length > 0) {
                    new_receivablessum = sap_batch[0]["new_receivablessum"];
                }
                if (parseFloat(new_receivablessum) != new_totalcost) {
                    rtcrm.alertDialog($t("ServiceSettlement.InvoiceAmountVerification", "发票汇总号数据执行中，暂无法提交，请去汇总号批次号查询表查看进度！"));
                    return;
                }
            }
            var new_edition = rtcrm("#new_edition").val();
            var new_edition = rtcrm("#new_edition").val();
            var new_formstatus = rtcrm("#new_formstatus").val();
            try {
                rtcrm.confirmDialog($t("settlement.beenconfirmed", "是否推送至SAP？"), function () {

                    if (new_edition == 1) {
                        rtcrm("#new_withholdingstatus").val(2);
                    }
                    if (new_edition == 2) {
                        if (new_formstatus == 1) {
                            //不推调差数据 edit by p-songyongxiang ********
                            //rtcrm("#new_settleaccountsstatus").val(2);
                        }
                        if (new_formstatus == 7) {
                            var srv_station = rtcrm.getLookupId("new_srv_station_id");
                            if (srv_station != null && srv_station != undefined) {
                                var srv_station_id = srv_station.replace("{", "").replace("}", "");
                                var data = rtcrm.retrieve('new_srv_stations(' + srv_station_id + ')?$select=new_providertype', true);
                                var providertype = -1;
                                if (data != null) {
                                    providertype = data.new_providertype;
                                }
                                if (providertype == 7) {
                                    var isreversecharge = rtcrm("#new_isreversecharge").val();
                                    if (isreversecharge == null || isreversecharge == undefined) {
                                        rtcrm.alertDialog($t("settlement.checkreversecharge", "请选择是否reverse charge"));
                                        return;
                                    }
                                }
                                var bankcountry = rtcrm("#new_bancountry").val();
                                var accountbankname = rtcrm("#new_accountbankname").val();
                                var bankaccount = rtcrm("#new_bankaccount").val();
                                var bank = rtcrm("#new_bank").val();
                                if (bankcountry == null || accountbankname == null || bankaccount == null || bank == null) {
                                    rtcrm.alertDialog($t("settlement.checkbankinfo", "结算信息为空，无法提交"));
                                    return;
                                }
                                //rtcrm("#new_invoicestatus").val(2);
                                //更新反冲状态为反冲中,先推反冲，再推发票到SAP，edit by p-songyongxiang ********
                                rtcrm("#new_coverstatus").val(2);
                            }
                        }
                        if (new_formstatus == 8) {
                            rtcrm("#new_paymentstatus").val(2);
                        }

                    }
                    Xrm.Page.data.save();
                    rtcrm.alertDialog($t("settlement.pushsuccess", "提交成功"));
                });

            } catch (e) {
                rtcrm.alertDialog(e.message);
            }
        }
    }
}

//结算单推送sap按钮 显示规则
function btnSendSapEnabled() {
    var new_edition = rtcrm("#new_edition").val();
    var new_formstatus = rtcrm("#new_formstatus").val();
    if (new_edition == 1) {
        var new_withholdingstatus = rtcrm("#new_withholdingstatus").val();
        if (new_withholdingstatus == null || new_withholdingstatus == 1) {
            return true;
        }
    }
    if (new_edition == 2) {

        if (new_formstatus == 1) {
            //不推调差数据
            //var new_settleaccountsstatus = rtcrm("#new_settleaccountsstatus").val();
            //if (new_settleaccountsstatus == null || new_settleaccountsstatus == 1) {
            //    return true;
            //}
        }
        if (new_formstatus == 7) {
            //结算单状态等于发票已接收时，并且反冲状态等于未反冲，发票推送SAP状态等于未推送，显示发送SAP按钮，先推反冲，再推发票
            var new_invoicestatus = rtcrm("#new_invoicestatus").val();
            var new_coverstatus = rtcrm("#new_coverstatus").val();
            if ((new_invoicestatus == null || new_invoicestatus == 1)
                && (new_coverstatus == null || new_coverstatus == 1)) {
                return true;
            }
        }
        if (new_formstatus == 8) {
            var new_paymentstatus = rtcrm("#new_paymentstatus").val();
            if (new_paymentstatus == null || new_paymentstatus == 1) {
                return true;
            }
        }
    }

    return false;
}

//已接收发票 
function InvoiceReceived() {
    //判断当前用户安全角色是否包含“XM服务商结算专员”，该角色不可操作
    var is_jszy = rtcrm.isUserHasRoles(rtcrm.getUserId(), "XM服务商结算专员");
    if (is_jszy) {
        rtcrm.alertDialog($t("ServiceSettlement.SecurityRoleVerification", "安全角色为“XM服务商结算专员”，不可操作！"));
    } else {
        var invoiceno = rtcrm("#new_invoiceno").val();
        if (invoiceno == null) {
            rtcrm.alertDialog($t("settlement.invoicenocheck", "请填写发票号！"));
        } else {
            rtcrm.confirmDialog($t("settlement.confirmreceipt", "是否确认接收发票？"), function () {
                var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
                var expense_claim = { new_formstatus: 7, new_invoicedate: new Date() };
                rtcrm.update("new_srv_expense_claims", entityId, expense_claim);
                Xrm.Page.data.save();
                rtcrm.alertDialog($t("settlement.invoicesuccess", "发票接收成功"), () => {
                    rtcrm.refresh();
                });
            });
        }
    }
}

//已接收发票按钮显示规则
function InvoiceReceivedEnabled() {
    var formstatus = rtcrm("#new_formstatus").val();
    if (formstatus == 6) {
        return true;
    }
    return false;
}

//结算状态修改事件
function new_formstatus_update() {
    var new_formstatus = rtcrm("#new_formstatus").val();
    if (new_formstatus == 6) {
        rtcrm("#new_confirmdate").disabled(false);
        rtcrm("#new_invoiceno").disabled(false);
        rtcrm("#new_invoicenumber").disabled(false);
        rtcrm("#new_taxno").disabled(false);
        rtcrm("#new_transactionpostscript").disabled(false);
        //发票号设置必填
        rtcrm("#new_invoiceno").req("required");
    } else {
        rtcrm("#new_confirmdate").disabled(true);
        rtcrm("#new_invoiceno").disabled(true);
        rtcrm("#new_invoicenumber").disabled(true);
        rtcrm("#new_taxno").disabled(true);
        rtcrm("#new_transactionpostscript").disabled(true);
        rtcrm("#new_invoiceno").req("none");
    }
    this.new_edition_update();
}

//调差按钮
function adjust() {
    //判断当前用户安全角色是否包含“XM服务商结算专员”，该角色不可操作
    var is_jszy = rtcrm.isUserHasRoles(rtcrm.getUserId(), "XM服务商结算专员");
    if (is_jszy) {
        rtcrm.alertDialog($t("ServiceSettlement.SecurityRoleVerification", "安全角色为“XM服务商结算专员”，不可操作！"));
    } else {
        rtcrm.confirmDialog($t("settlement.", "是否开始调差？"), function () {
            rtcrm("#new_adjuststatus").val(2);
            Xrm.Page.data.save();
            rtcrm.alertDialog($t("settlement.", "开始执行"), () => {
                rtcrm.refresh();
            });
        });
    }
}

function adjustShow() {
    var new_edition = rtcrm("#new_edition").val();
    var new_withholdingstatus = rtcrm("#new_withholdingstatus").val();
    var new_adjuststatus = rtcrm("#new_adjuststatus").val();
    if (new_edition == 1 && new_withholdingstatus == 3 && new_adjuststatus == 1)
        return true;
    return false;
}

//校验发票号new_invoiceno字段长度不得超过20个字符
function Check_new_invoicenoLength() {
    var new_invoiceno = rtcrm("#new_invoiceno").val();
    if (new_invoiceno.length > 20) {
        rtcrm.alertDialog(this.$t("ServiceSettlement.Check_new_invoicenoLength", "发票号字段长度不得超过20！"));
        rtcrm("#new_invoiceno").val("");
        return;
    }
}


//校验发票代码new_invoicenumber字段长度不得超过12个字符
function Check_new_invoicenumberLength() {
    var new_invoicenumber = rtcrm("#new_invoicenumber").val();
    if (new_invoicenumber.length > 12) {
        rtcrm.alertDialog(this.$t("ServiceSettlement.Check_new_invoicenumberLength", "发票代码字段长度不得超过12！"));
        rtcrm("#new_invoicenumber").val("");
        return;
    }
}

//字段规则
function field_rule() {
    //反冲状态=未反冲 可编辑
    var new_coverstatus = Xrm.Page.getAttribute("new_coverstatus").getValue();
    if (new_coverstatus == 1) {
        Xrm.Page.getControl("new_decimaladjustment").setDisabled(false);
    } else {
        Xrm.Page.getControl("new_decimaladjustment").setDisabled(true);
    }

    //反冲状态=未反冲  && XM总部结算专员 字段可编辑
    var is_jszy = rtcrm.isUserHasRoles(rtcrm.getUserId(), "XM总部结算专员");
    if (is_jszy && new_coverstatus == 1) {
        Xrm.Page.getControl("new_withholdingtax").setDisabled(false);
    } else {
        Xrm.Page.getControl("new_withholdingtax").setDisabled(true);
    }
}
//加减项特殊费用导入显隐规则
function ImportSpecialExpenseEnableRules() {
    //只有XM总部结算专员,并且调差状态 = 未调差，才能显示按钮
    var adjuststatus = rtcrm("#new_adjuststatus").val();
    if (rtcrm.isCurrentUserHasRoles("XM总部结算专员") && adjuststatus == 1) {
        var businesstype = rtcrm("#new_businesstype").val();
        if (businesstype == 5) {
            //仓储结算单不显示导入按钮
            return false;
        }
        return true;
    } else {
        return false;
    }
}
//加减项特殊费用导入
function ImportSpecialExpense() {
    try {
        rtcrm.showImportWindow("new_srv_specialexpense")

    } catch (e) {
        Xrm.Utility.alertDialog(e);
    }
}
//服务单tab 按照服务商判断显示工单section还是备件section
//查询所属服务商的服务商类型，为Maitrox 则显示迈创服务商结算单的服务单tab，否则显示工单结算单的服务单页面
//服务商类型为 B2X 则显示B2X服务商结算单的服务单tab
function WorkorderSectiondisplay() {
    var srv_station = rtcrm.getLookupId("new_srv_station_id");
    if (srv_station == null || srv_station == undefined) {
        return;
    }
    var businesstype = rtcrm("#new_businesstype").val();
    var srv_station_id = srv_station.replace("{", "").replace("}", "");
    var data = rtcrm.retrieve('new_srv_stations(' + srv_station_id + ')?$select=new_providertype', true);
    if (data != null) {
        if (data.new_providertype == 5) {
            //Maitrox 
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
        } else if (data.new_providertype == 6) {
            //B2X 
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
        } else if (data.new_providertype == 7 || data.new_providertype == 8) {
            //Operator
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_12").setVisible(false); // 注释选项卡
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
            //handling费发票环节提交sap时是否reverse charge字段设置必填
            var new_formstatus = rtcrm("#new_formstatus").val();
            var new_invoicestatus = rtcrm("#new_invoicestatus").val();
            var new_coverstatus = rtcrm("#new_coverstatus").val();
            if (data.new_providertype == 7) {
                Xrm.Page.getControl("new_isreversecharge").setVisible(true);
                if (new_formstatus == 7) {
                    //结算单状态 = 发票已接收
                    if ((new_invoicestatus == null || new_invoicestatus == 1)
                        && (new_coverstatus == null || new_coverstatus == 1)) {
                        rtcrm("#new_isreversecharge").req("required");
                    }
                }
            }
        } else if (data.new_providertype == 10) {
            //仓储 
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
        }
        else if (data.new_providertype == 11) { // 物流
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(true);
            //结算类型和网点类型字段设置隐藏  + 物流费用动态显隐
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
            set_logisticsfield_whetherVisible();
        } else if (businesstype == 9)
        {
            //安装
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
        }
        else if (businesstype == 10) {
            //高维工厂
            Xrm.Page.ui.tabs.get("tab_3").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(true);
            //结算类型和网点类型字段设置隐藏
            Xrm.Page.getControl("new_type").setVisible(false);
            Xrm.Page.getControl("new_stietype").setVisible(false);
        }
        else {
            Xrm.Page.ui.tabs.get("tab_3").setVisible(true);
            Xrm.Page.ui.tabs.get("tab_10").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_11").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_13").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_14").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_15").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_16").setVisible(false);
            Xrm.Page.ui.tabs.get("tab_17").setVisible(false);
        }
    }
    //设置new_isreversecharge字段一直必填
    rtcrm("#new_isreversecharge").req("required");
}
// 检查表单是否有未保存的更改
function isFormSaved() {
    return Xrm.Page.data.entity.getIsDirty();
}
//费用结算单子网格加减项费用在结算单调差状态！=未调差，加减项费用页面隐藏【新建加减项费用单】
function SpecialexpenseAddButtonEnableRules() {
    var adjuststatus = rtcrm("#new_adjuststatus").val();
    if (adjuststatus == 1) {
        return true;
    } else {
        return false;
    }
}
//根据结算单类型对section进行显隐设置
function SetSectiondisplay() {
    var businesstype = rtcrm("#new_businesstype").val();
    if (businesstype == 6) {
        //结算单类型 为 激活
        Xrm.Page.ui.tabs.get("tab_5").setVisible(true);//激活结算单明细
        Xrm.Page.ui.tabs.get("tab_3").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_10").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_11").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_13").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_14").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_15").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_16").setVisible(false);//服务单
        Xrm.Page.ui.tabs.get("tab_17").setVisible(false);//高维工厂
        Xrm.Page.ui.tabs.get("tab_6").setVisible(false);//受理单
        Xrm.Page.ui.tabs.get("tab_7").setVisible(false);//运营商Hf
        Xrm.Page.ui.tabs.get("tab_12").setVisible(false);//注释
        //结算类型和网点类型字段设置隐藏
        Xrm.Page.getControl("new_type").setVisible(false);
        Xrm.Page.getControl("new_stietype").setVisible(false);
    } else {
        //结算单类型 不为 激活
        Xrm.Page.ui.tabs.get("tab_5").setVisible(false);//激活结算单明细
        Xrm.Page.ui.tabs.get("tab_6").setVisible(false);//受理单
        Xrm.Page.ui.tabs.get("tab_7").setVisible(false);//运营商Hf
        Xrm.Page.ui.tabs.get("tab_12").setVisible(false);//注释
    }
}
//仓储结费导出工单费用明细
function Exportdetaildata() {
    var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    var new_name = rtcrm("#new_name").val();
    var new_businesstype = rtcrm("#new_businesstype").val();
    var new_adjuststatus = rtcrm("#new_adjuststatus").val();
    var isadjust = false;//是否已调差
    if (new_adjuststatus == 3)
        isadjust = true;
    var businesstypevalue = "";
    if (new_businesstype == 5)
        businesstypevalue = "Warehousing";
    else if (new_businesstype == 7)
        businesstypevalue = "Logistics";
    var workorderdetaillist = [];
    var fieldslist = [];//全部查询返回的标题集合
    //查询excel列标题
    var resultfields = rtcrm.invokeAction('new_WarehousingWorkorderdetailExport', { expenseclaimid: entityId, querytype: businesstypevalue, pageIndex: 1 });
    var resultJson = JSON.parse(resultfields.output);
    var fields = resultJson.fields;
    //var fieldsArray = [];
    //fields.forEach(function (item) {
    //    fieldsArray.push(item.prop);
    //});
    //var titleArray = [];
    //fields.forEach(function (item) {
    //    titleArray.push(item.label);
    //});
    //查询币种和结算单号
    var expense_claim = rtcrm.retrieve('new_srv_expense_claims(' + entityId + ')?$expand=new_transactioncurrency_id($select=isocurrencycode)', true);
    var currency = expense_claim.new_transactioncurrency_id.isocurrencycode;
    var businesstype = new_businesstype.toString();
    var workordercount = rtcrm("#new_workordercount").val();
    var batchCount = 5000;
    var pageCount = workordercount / batchCount + 1;
    for (var i = 1; i <= pageCount; i++) {
        try {
            var result = rtcrm.invokeAction('new_WarehousingWorkorderdetailExport', { expenseclaimid: entityId, querytype: businesstype, pageIndex: i, currency: currency, isadjust: isadjust });
            var resultjson = JSON.parse(result.output);
            workorderdetaillist.push(...resultjson.data);
            fieldslist.push(...resultjson.fields);
        } catch (ex) {
            Xrm.Utility.alertDialog(ex);
        }
    }
    var fieldsArray = [];
    var titleArray = [];
    fields.forEach(function (item) {
        const filteredArray = fieldslist.filter(element => element.prop == item.prop);
        if (filteredArray.length > 0) {
            fieldsArray.push(item.prop);
            titleArray.push(item.label);
        }
    });
    var data = [];
    data.push(titleArray); // 添加列标题
    for (let i = 0; i < workorderdetaillist.length; i++) {
        var row = [];
        fieldsArray.forEach(function (item) {
            var cellvalue = workorderdetaillist[i][item];
            row.push(cellvalue);
        });
        data.push(row);
    }
    // 创建一个包含 CSV 数据的表格元素
    const table = document.createElement('table');
    // 遍历二维数组，创建行和单元格，并添加到表格中
    data.forEach(rowData => {
        const tr = document.createElement('tr');
        rowData.forEach(cellData => {
            const td = document.createElement('td');
            td.textContent = cellData;
            tr.appendChild(td);
        });
        table.appendChild(tr);
    });
    // 解析 CSV 数据并转换为工作表对象
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(table);
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    // 将工作簿对象转换为 Excel 文件的 Blob 对象
    const excelBlob = new Blob([s2ab(XLSX.write(wb, { type: 'binary', bookType: 'xlsx' }))], { type: 'application/octet-stream' });

    // 创建下载链接并触发下载
    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(excelBlob);
    downloadLink.download = new_name + '-Service order details.xlsx';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // 将 ArrayBuffer 转换为二进制字符串
    function s2ab(s) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }
}
//导出明细按钮显隐规则
function ExportdetailEnableRules() {
    //仓储结算单显示导出明细按钮
    var businesstype = rtcrm("#new_businesstype").val();
    if (businesstype == 5 || businesstype == 7) {
        return true;
    } else {
        return false;
    }
}
//重置调差
function ResetAdjust() {
    //执行重置调差时，调差状态更新为未调差，结算单版本状态更新为预提单
    rtcrm.confirmDialog($t("settlement.confirmresetadjust", "是否确认重置调差？"), function () {
        rtcrm("#new_adjuststatus").val(1);
        rtcrm("#new_edition").val(1);
        Xrm.Page.data.save();
        rtcrm.alertDialog($t("settlement.resetadjustsuccess", "重置调差成功"), () => {
            rtcrm.refresh();
        });
    });
}
//重置调差按钮显隐规则
function ResetAdjustEnableRules() {
    //调差状态等于已调差，且发票推送状态等于未推送,反冲状态 = 未反冲时，重置调差按钮显示
    var adjuststatus = rtcrm("#new_adjuststatus").val();
    var invoicestatus = rtcrm("#new_invoicestatus").val();
    var coverstatus = rtcrm("#new_coverstatus").val();
    if (adjuststatus == 3 && invoicestatus == 1 && coverstatus == 1) {
        return true;
    } else {
        return false;
    }
}
//失败重推付款申请到SAP
function RepushPayment() {
    rtcrm.confirmDialog($t("settlement.confirmrepushpayment", "是否确认重推付款申请？"), function () {
        var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
        var new_name = rtcrm("#new_name").val();
        var param = {};
        param.entityid = entityId;
        param.new_name = new_name;
        var requesturl = getSysytemPara("RepushPaymentAzureFunctionUrl");
        var response = rtcrm.postResponse(requesturl, JSON.stringify(param));
        var result = JSON.parse(response);
        if (result.statusCode == "200") {
            rtcrm("#new_formstatus").val(10);//结算单状态 付款审批完成
            Xrm.Page.data.save();
            rtcrm.alertDialog($t("settlement.repushpaymentsuccess", "重推付款申请成功"), () => {
                rtcrm.refresh();
            });
        } else {
            rtcrm.alertDialog($t("settlement.repushpaymenterror", result.message));
        }
    });
}
//失败重推付款申请按钮显隐规则
function RepushPaymentEnableRules() {
    //结算单状态等于付款失败，或者付款申请推送状态 = 推送失败时，重推付款申请按钮显示
    var entityId = Xrm.Page.data.entity.getId().replace('{', '').replace('}', '');
    var expense_claim = rtcrm.retrieve('new_srv_expense_claims(' + entityId + ')?$select=new_formstatus,new_paymentrequestpush', true);
    var formstatus = expense_claim.new_formstatus;
    var paymentrequestpush = expense_claim.new_paymentrequestpush;
    if (formstatus == 9 || paymentrequestpush == 20) {
        return true;
    } else {
        return false;
    }
}
///获取系统参数
function getSysytemPara(key) {
    var systemParaArr = rtcrm.retrieveMultiple("new_systemparameters?$select=new_desc,new_value&$filter=new_name eq '" + key + "' ", true);
    if (systemParaArr.length > 0) {
        var systemPara = systemParaArr[0];
        var systemValue = systemPara["new_value"];
        return systemValue;
    }
    return "";
}
function set_logisticsfield_whetherVisible() {
    var logic_name = ["new_adcfilefee", "new_allocatefee", "new_dgcheckingfee", "new_dghandlingfee", "new_dglicencefee", "new_fuchrgfuefee", "new_greenplusfee",
        "new_imofee", "new_doafee", "new_racfee", "new_terminalfee", "new_tollfee", "new_vgmfee", "new_insurancefee", "new_operatefee", "new_operateservicefee",
        "new_exportgroundfee", "new_exportspecialfee", "new_magneticfee", "new_commissionfee", "new_agencyfee", "new_registeredparkingfee", "new_bookingservicefee",
        "new_servicechargefee", "new_portsurchargefee", "new_factoryloadingfee", "new_domesticportmiscellaneousfee", "new_domesticfreightfee", "new_domestictransportfee",
        "new_foreignthcfee", "new_foreignhandlingfee", "new_foreigntelexfee", "new_foreigndeliveryfee", "new_foreigncustomsfee", "new_foreignpickfee", "new_foreignmiscellaneousfee",
        "new_foreigndocumentmakingfee", "new_transitfee", "new_oceanfreightfee", "new_9typeelectricfee", "new_documenttransferfee", "new_airfreightfee", "new_landfreightfee",
        "new_lssfee", "new_timberingfee", "new_deliveryfee", "new_goodsdeclarationfee", "new_hksurchargefee", "new_landingfee", "new_indonesiastampfee", "new_freightfee",
        "new_lastmilefee", "new_milkrunfee", "new_documentationfee", "new_capitalservicefee", "new_otherincidentalfee", "new_customscommissionfee", "new_tarifffee",
        "new_unstackablesurchargefee", "new_essfee", "new_correctionsurchargefee", "new_remotepickupfee", "new_surchargeoverfee", "new_foreigninspectionfee", "new_internationaltransportfee",
        "new_foreigntradefee", "new_foreigndangerousoperatefee", "new_foreigndeclarationfee", "new_packagingfee", "new_unboxfee", "new_registrationfee", "new_storagefee",
        "new_containerloadfee", "new_dangerousgoodssurchargefee", "new_freightforwardingfee", "new_freighttruckfee", "new_apifee", "new_handwritinglabelfee", "new_warehousesmovingfee",
        "new_packing2fee", "new_indonesiasurchargefee", "new_indonesiantaxfee"
    ]
    logic_name.forEach(function (element) {
        let value = Xrm.Page.getAttribute(element)?.getValue();
        if (value == null || value == 0) {
            Xrm.Page.getControl(element)?.setVisible(false);
        }
    });
}