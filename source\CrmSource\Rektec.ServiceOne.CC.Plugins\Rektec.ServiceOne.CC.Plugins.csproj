﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E675A5B0-9210-4CDB-9FE4-0C8DF432F1C5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Rektec.ServiceOne.CC.Plugins</RootNamespace>
    <AssemblyName>Rektec.ServiceOne.CC.Plugins</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>rektec.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Crm.Sdk.Proxy, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.9.0.2.25\lib\net462\Microsoft.Crm.Sdk.Proxy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xrm.Sdk, Version=9.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.9.0.2.25\lib\net462\Microsoft.Xrm.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="RekTec.Crm.Cache.Redis">
      <HintPath>..\..\..\DLL\RekTec.Crm.Cache.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Core" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Workflow.Activities" />
    <Reference Include="System.Workflow.ComponentModel" />
    <Reference Include="System.Workflow.Runtime" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="account_create_post.cs" />
    <Compile Include="account_create_update_pre.cs" />
    <Compile Include="account_merge_pre.cs" />
    <Compile Include="account_retrievemultiple_post.cs" />
    <Compile Include="account_retrieve_post.cs" />
    <Compile Include="account_retrievemultiple_pre.cs" />
    <Compile Include="annotation_create_post.cs" />
    <Compile Include="annotation_delete_pre.cs" />
    <Compile Include="account_update_post.cs" />
    <Compile Include="appointment_update_post.cs" />
    <Compile Include="knowledgearticle_delete.cs" />
    <Compile Include="email_create_pre.cs" />
    <Compile Include="email_update_post.cs" />
    <Compile Include="incidentresolution_create_post.cs" />
    <Compile Include="incident_create_post.cs" />
    <Compile Include="incident_create_post_close.cs" />
    <Compile Include="incident_create_update_pre.cs" />
    <Compile Include="incident_retrieve_retrievemultiple_post.cs" />
    <Compile Include="incident_retrievemultiple_pre.cs" />
    <Compile Include="knowledgearticle_create_pre.cs" />
    <Compile Include="knowledgearticle_create_update_pre.cs" />
    <Compile Include="knowledgearticle_retrievemultiple_post.cs" />
    <Compile Include="knowledgearticle_retrieve_post.cs" />
    <Compile Include="msdyn_agentstatushistory_create_pre.cs" />
    <Compile Include="msfp_surveyinvite_create_post.cs" />
    <Compile Include="msfp_surveyresponse_create_post.cs" />
    <Compile Include="msfp_unsubscribedrecipient_create_post.cs" />
    <Compile Include="msfp_unsubscribedrecipient_delete_pre.cs" />
    <Compile Include="new_applyforgifts_update_post.cs" />
    <Compile Include="new_qa_update_post.cs" />
    <Compile Include="new_cc_gdpr_preverify.cs" />
    <Compile Include="phonecall_create_update_pre.cs" />
    <Compile Include="phonecall_upadte_post.cs" />
    <Compile Include="task_create_post.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="rektec.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.Common\RekTec.Crm.Common.csproj">
      <Project>{f7c53d80-ce20-475c-abce-213386d66446}</Project>
      <Name>RekTec.Crm.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Biz\RekTec.Api.Biz.csproj">
      <Project>{F6923484-6C78-404C-9466-F952BA5385C2}</Project>
      <Name>RekTec.Api.Biz</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Common\RekTec.Api.Common.csproj">
      <Project>{0F7870C3-987E-4A29-A7F9-B854F0E56F55}</Project>
      <Name>RekTec.Api.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Interface\RekTec.Api.Interface.csproj">
      <Project>{C18B6A42-E0D3-44A0-8B0F-D656E37E322D}</Project>
      <Name>RekTec.Api.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Model\RekTec.Api.Model.csproj">
      <Project>{949872A4-FE32-4A72-A3D5-0146889E7097}</Project>
      <Name>RekTec.Api.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.Common.XiaoMi\RekTec.Crm.Common.XiaoMi.csproj">
      <Project>{369FD852-80BB-4CA6-BD3D-19463029405D}</Project>
      <Name>RekTec.Crm.Common.XiaoMi</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.OrganizationService.Common\RekTec.Crm.OrganizationService.Common.csproj">
      <Project>{2c567bb0-2d83-40e2-906e-7330f0afe82a}</Project>
      <Name>RekTec.Crm.OrganizationService.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>