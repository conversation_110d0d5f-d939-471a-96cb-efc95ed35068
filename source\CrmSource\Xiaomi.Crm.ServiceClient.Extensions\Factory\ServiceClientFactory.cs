﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace Xiaomi.Crm.ServiceClientPool.Factory
{
    public class ServiceClientFactory
    {
        /// <summary>
        /// 连接池
        /// </summary>
        private readonly ConcurrentQueue<ServiceClient> _serviceClientPool = new ConcurrentQueue<ServiceClient>();

        /// <summary>
        /// 当前连接池大小
        /// </summary>
        private int _currentPoolSize;

        /// <summary>
        ///     核心连接数
        /// </summary>
        private int _corePoolSize;

        /// <summary>
        /// 最大容量
        /// </summary>
        private readonly int _maxPoolSize;

        /// <summary>
        /// 定时任务清理频率（毫秒）
        /// </summary>
        private int _cleanupInterval = 5 * 60 * 1000; // 5 分钟

        /// <summary>
        /// 轮询使用的连接字符串
        /// </summary>
        private string[] _connectionStrings;

        /// <summary>
        /// 上次清理时间
        /// </summary>
        private long _lastCleanupTime;

        /// <summary>
        /// 链接过期时间
        /// </summary>
        private int _connectionTimeout = -1; // 59 分钟

        private ILogger _logger;

        private int _connectionStringIndex = 0;

        private readonly object _lock = new object();

        public ServiceClientFactory(int initialPoolSize, int corePoolSize, int maxPoolSize, int connectionTimeout, string[] connectionStrings, ILogger logger = null)
        {
            if (initialPoolSize <= 0 || maxPoolSize <= 0 || connectionStrings.Length == 0)
            {
                throw new ArgumentException("Invalid arguments for initializing ServiceClientFactory.");
            }

            _maxPoolSize = maxPoolSize;
            _connectionStrings = connectionStrings;
            _logger = logger;
            _corePoolSize = corePoolSize;
            _lastCleanupTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            _connectionTimeout = connectionTimeout;

            for (int i = 0; i < initialPoolSize; i++)
            {
                AddClientToPool(GetNextConnectionString());
            }

            _currentPoolSize = initialPoolSize;
            _logger?.LogInformation("ServiceClientFactory initialized. Initial pool size: {0}; Max pool size: {1}", initialPoolSize, maxPoolSize);

            // 启动定时任务
            StartCleanupTask();
        }

        /// <summary>
        /// 获取连接实例
        /// </summary>
        /// <returns>ServiceClient 实例</returns>
        public ServiceClient Acquire(ILogger logger = null)
        {
            if(_logger == null)
            {
                _logger = logger;
            }
            lock (_lock)
            {
                if (!_serviceClientPool.TryDequeue(out var client))
                {
                    // 如果连接池为空，则动态扩展
                    AddClientToPool(GetNextConnectionString());
                    _serviceClientPool.TryDequeue(out client);
                }
                else if(!IsConnectionValid(client))
                {
                    // 如果连接无效，则重新创建
                    client = new ServiceClient(GetNextConnectionString());
                }
                Interlocked.Decrement(ref _currentPoolSize);
                _logger.LogInformation("ServiceClient acquired from the pool. Current pool size: {0}", _currentPoolSize);
                return client ?? throw new InvalidOperationException("Failed to acquire a ServiceClient instance.");
            }
        }

        /// <summary>
        /// 使用完毕后将连接实例放回池中
        /// </summary>
        /// <param name="client">使用过的 ServiceClient 实例</param>
        public void Release(ServiceClient client, ILogger logger = null)
        {
            if (_logger == null)
            {
                _logger = logger;
            }
            if (client == null)
            {
                _logger.LogError("连接为空，不放回连接池");
                return;
            }

            lock (_lock)
            {
                if (_currentPoolSize < _maxPoolSize)
                {
                    _serviceClientPool.Enqueue(client);
                    Interlocked.Increment(ref _currentPoolSize);
                    _logger?.LogInformation("ServiceClient released to the pool. Current pool size: {0}; Max pool size : {1}", _currentPoolSize, _maxPoolSize);
                }
                else
                {
                    // 如果池子已满，丢弃释放的连接
                    client.Dispose();
                    _logger?.LogInformation("ServiceClient disposed due to pool is full.{0}", _currentPoolSize);
                }
            }
        }

        /// <summary>
        /// 动态扩展连接池
        /// </summary>
        /// <param name="connectionString">用于创建连接的连接字符串</param>
        private void AddClientToPool(string connectionString)
        {
            try
            {
                //Todo: 优化连接池的创建
                var client = new ServiceClient(connectionString);
                _serviceClientPool.Enqueue(client);
                Interlocked.Increment(ref _currentPoolSize);
                _logger?.LogInformation("ServiceClient added to the pool. Current pool size: {0}", _currentPoolSize);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to add ServiceClient to the pool.");
                throw;
            }
        }

        /// <summary>
        /// 按轮询策略获取下一个连接字符串
        /// </summary>
        /// <returns>连接字符串</returns>
        private string GetNextConnectionString()
        {
            lock (_lock)
            {
                var connectionString = _connectionStrings[_connectionStringIndex];
                _connectionStringIndex = (_connectionStringIndex + 1) % _connectionStrings.Length;
                return connectionString;
            }
        }

        /// <summary>
        /// 定时任务：清理连接池中的过期连接
        /// </summary>
        private void StartCleanupTask()
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(_cleanupInterval);

                    lock (_lock)
                    {
                        int itemsToRemove = (int)(_serviceClientPool.Count * 0.4);

                        for (int i = 0; i < itemsToRemove && _currentPoolSize > _corePoolSize; i++)
                        {
                            if (_serviceClientPool.TryDequeue(out var client))
                            {
                                client.Dispose();
                                Interlocked.Decrement(ref _currentPoolSize);
                            }
                        }
                        if(_connectionTimeout != -1)
                            UpdateConnection();
                        _logger?.LogInformation("ServiceClient pool cleanup completed. Current pool size: {0}. Cleanup count: {1}", _currentPoolSize, itemsToRemove);
                    }
                }
            });
        }

        /// <summary>
        /// 检查连接是否有效
        /// </summary>
        /// <param name="client"></param>
        /// <returns></returns>
        private bool IsConnectionValid(ServiceClient client)
        {
            //检查基础状态
            if (client == null || !client.IsReady)
            {
                return false;
            }
            //主动探火验证
            try
            {
                var whoAmIResponse = (WhoAmIResponse)client.Execute(new WhoAmIRequest());
                return whoAmIResponse != null && whoAmIResponse.UserId != Guid.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message + "检查连接是否有效异常");
                return false;
            }
        }

        public void UpdateConnection()
        {
            ///一段时间后清理所有链接
            if (_lastCleanupTime + _connectionTimeout < DateTimeOffset.Now.ToUnixTimeMilliseconds())
            {
                int updateConnectionCount = _serviceClientPool.Count;
                for (int i = 0; i < updateConnectionCount; i++)
                {
                    if (_serviceClientPool.TryDequeue(out var client))
                    {
                        client.Dispose();
                    }
                }
                for (int i = 0; i < updateConnectionCount; i++)
                {
                    AddClientToPool(GetNextConnectionString());
                    Interlocked.Decrement(ref _currentPoolSize);
                }
                _logger?.LogInformation("ServiceClient pool cleanup completed. Current pool size: {0}. Update count: {1}", _currentPoolSize, updateConnectionCount);
            }
        }

    }
}
