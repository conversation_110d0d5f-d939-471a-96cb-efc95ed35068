﻿using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xiaomi.XMSAction.Service;
using Xiaomi.XMSAction.Model;
using System.Diagnostics;
using Xiaomi.XMSAction.Enum;
namespace Xiaomi.XMSAction
{
    public class XMSNotify : IPlugin
    {

        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext PluginExecutionContext = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory factory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService SystemUserService = factory.CreateOrganizationService(null);
            ITracingService TracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                var workorderInputParameters = PluginExecutionContext.InputParameters["new_XMSRequest"] as string;
                TracingService.Trace("isp-cs-be入参:"+ workorderInputParameters);
                var input = JsonConvert.DeserializeObject<XMSNotifyModel>(workorderInputParameters);
                var action = input.Action;

                Stopwatch sw =  new Stopwatch() ;
                sw.Start();
                //建单消息
                if (action == XmsAction.CatreWorkOrder.ToString())
                {
                    new DealXMSMessage(input.Data, SystemUserService, TracingService).CreateWorkOrder();
                }
                //关单消息
                else if (action == XmsAction.CloseWorkOrder.ToString())
                {
                    new DealXMSMessage(input.Data, SystemUserService, TracingService).CloseWorkOrder();
                }
                else if (action == XmsAction.EngineerHanding.ToString())
                {
                    new DealXMSMessage(input.Data, SystemUserService, TracingService).WorkerHanding();
                }
                else if (action == "ServiceScope")
                {
                    new DealXMSServiceMessage(input.Data,  SystemUserService, TracingService).DealServiceScope();
                }else if (action == "ServiceWorker")
                {
                    //new DealXMSServiceMessage(input.Data,  SystemUserService, TracingService).DealServiceWorker();
                }else if (action == "ServiceStation")
                {
                    new DealXMSServiceMessage(input.Data,  SystemUserService, TracingService).DealServiceStation();
                }
                else if (action == XmsAction.WorkOrderTimeLine.ToString())
                {
                    new DealXMSMessage(input.Data, SystemUserService, TracingService).WorkOrderTimeLine();
                }
                sw.Stop();

                TracingService.Trace($"耗时{sw.ElapsedMilliseconds}毫秒");
                PluginExecutionContext.OutputParameters["new_Response"] = JsonConvert.SerializeObject(new { code = 200, msg = "OK" });

            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.ToString());
            }
        }
    }
}
