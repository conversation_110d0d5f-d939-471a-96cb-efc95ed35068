﻿using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Client;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Metadata;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using RekTec.ServiceSettlement.Function.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace RekTec.ServiceSettlement.Function.Common
{
    public class SapCommand_new2
    {
        ILogger log;
        /// <summary>
        /// 组织服务
        /// </summary>
        private IOrganizationService OrganizationService;
        /// <summary>
        /// 所有可用的SKU1集合
        /// </summary>
        private EntityCollection sku1collection = new EntityCollection();
        /// <summary>
        /// 所有可用的SKU2集合
        /// </summary>
        private EntityCollection sku2collection = new EntityCollection();
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="organizationService">组织服务</param>
        public SapCommand_new2(ILogger log, IOrganizationService OrganizationService)
        {
            this.log = log;
            if (OrganizationService == null)
            {
                this.OrganizationService = CommonHelper.CreateConnection();
            }
            else
            {
                this.OrganizationService = OrganizationService;
            }
            if (OrganizationService != null)
            {
                sku1collection = GetSKU1Collection(OrganizationService);
                //sku2collection = GetSKU2Collection(OrganizationService);
            }
        }

        /// <summary>
        /// Module ID：无
        /// Author：p-liuzanxiang
        /// Create Date：2023/5/23
        /// Depiction：调整SAP日志生成逻辑
        /// URL:https://jira.n.xiaomi.com/browse/ISPCS-2418
        /// 服务商信息改为从从结算单上取 edit by p-songyongxiang 20240705
        /// </summary>
        /// <param name="sap_type">推送类型</param>
        /// <param name="expense_claimId">结算单名称</param>
        /// <param name="typestr">推送描述</param>
        /// <param name="remark_FC">反冲的remark</param>
        /// <returns></returns>
        public OrganizationRequestCollection CreateSAPLog(int sap_type, Entity expense_Claim, Entity src_station, string typestr)
        {
            //创建sapbatch数据之前，先停用原来相同类型的批次数据
            DisabledSapbatch(sap_type, expense_Claim.Id.ToString(), OrganizationService);
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            //主表guid
            Guid mainguid = Guid.Empty;
            //批次明细总数
            decimal summoney = 0m;
            decimal new_totalcost = 0m;//结算单上total cost
            //批次业务发生时间
            DateTime businessDate = DateTime.UtcNow.AddDays(0 - DateTime.Now.Day);
            if (sap_type != 1)
            {
                if (sap_type == 4)
                {
                    //付款环节 业务日期取 付款申请接口回传的付款日期
                    businessDate = expense_Claim.Contains("new_paymentdate") ? expense_Claim.ToDefault<DateTime>("new_paymentdate") : DateTime.UtcNow;
                }
                else
                {
                    businessDate = DateTime.UtcNow;
                }
            }
            OrganizationRequestCollection orColl = new OrganizationRequestCollection();
            int expenseclaimtype = expense_Claim.ToDefault<int>("new_businesstype");//结算单类型
            try
            {
                string js_tax = "";
                string js_withouttax = "";
                JS_WithoutTax_Model jswithouttax = null;
                if (sap_type == 3)
                {
                    if (expenseclaimtype != 9)
                        js_tax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_Tax");
                    else
                        js_tax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_Tax_Install");
                    if (expenseclaimtype != 9)
                        js_withouttax = CommonHelper.GetSystemParamValue(OrganizationService, "JS_WithoutTax");
                }
                //结算单
                //var expense_claim = OrganizationService.Retrieve("new_srv_expense_claim", new Guid(expense_claimId), new ColumnSet("new_srv_station_id", "new_year", "new_month", "new_invoiceno", "new_sapsubject", "new_invoicenumber", "new_stietype"));
                var expense_claim = expense_Claim;
                string expense_claimId = expense_claim.Id.ToString();
                string currency = expense_claim.ToDefault<string>("new_sapcurrency");//币种
                new_totalcost = expense_claim.ToDefault<decimal>("new_totalcost");//币种
                int isreversecharge = expense_claim.ToDefault<int>("new_isreversecharge");//是否reverse charge
                var new_srv_station_id = expense_claim.GetAttributeValue<EntityReference>("new_srv_station_id");
                var new_srv_station = OrganizationService.Retrieve(new_srv_station_id.LogicalName, new_srv_station_id.Id, new ColumnSet("new_code", "new_contractingbody", "new_providertype", "new_stationabbreviation"));
                int providertype = new_srv_station.ToDefault<int>("new_providertype");
                if (sap_type == 3)
                {
                    if (!string.IsNullOrWhiteSpace(js_withouttax))
                    {
                        List<JS_WithoutTax_Model> jswithouttaxlist = JsonConvert.DeserializeObject<List<JS_WithoutTax_Model>>(js_withouttax);
                        jswithouttax = jswithouttaxlist.Where(a => a.stationcode == new_srv_station.ToDefault<string>("new_code")
                        && a.contractbody == new_srv_station.ToDefault<string>("new_contractingbody")).FirstOrDefault();
                        //dnsstation = CommonHelper.GetDNSstation("110383", expense_claim.ToDefault<string>("new_contractingbody"), OrganizationService);
                    }
                }
                //服务商
                //var station = OrganizationService.Retrieve(new_srv_station_id.LogicalName, new_srv_station_id.Id, new ColumnSet(true));
                var station = src_station;
                EntityCollection order_handMergeColl = new EntityCollection();
                if (expenseclaimtype == 6)
                {
                    //结算单类型 = 激活
                    order_handMergeColl = GetActivatedetail(expense_claimId);
                }
                else if (expenseclaimtype == 9)
                {
                    //结算单类型 = 安装
                    order_handMergeColl = GetworkorderInstall(expense_claimId);
                }
                else if (expenseclaimtype == 10)
                {
                    //结算单类型 = 高维工厂
                    order_handMergeColl = GetTrimmorderdetail(expense_claimId);
                }
                else
                {
                    //服务单
                    var orderbySkuColl = GetWorkOrder(expense_claimId, expenseclaimtype);

                    var handbySkuColl = GetServiceHanding(expense_claimId);
                    //handbySkuColl.Entities.AddRange(orderbySkuColl.Entities);
                    order_handMergeColl = order_handMerge(orderbySkuColl, handbySkuColl);
                }

                #region 基础信息获取
                var new_remark_main = string.Empty;
                var new_remark_detail = string.Empty;
                //结算年
                int year = expense_claim.GetAttributeValue<OptionSetValue>("new_year").Value;
                //结算月
                int month = expense_claim.GetAttributeValue<OptionSetValue>("new_month").Value;
                //发票代码
                string new_invoicenumber = expense_claim.GetAttributeValue<string>("new_invoicenumber");
                //发票号
                string new_invoiceno = expense_claim.GetAttributeValue<string>("new_invoiceno");
                //结算单上签约主体
                string new_contractingbody = expense_claim.ToDefault<string>("new_contractingbody");
                //服务商简称
                //string name = station.GetAttributeValue<string>("new_stationabbreviation");
                //string name = station?.ToDefault<string>("new_stationabbreviation");
                //服务商简称从服务商上取，服务网点的服务商简称是空值
                string name = new_srv_station?.ToDefault<string>("new_stationabbreviation");
                //利润中心编码
                //string profitCenter = station.GetAttributeValue<string>("new_profitcenter");
                //string profitCenter = station?.ToDefault<string>("new_profitcenter");
                string profitCenter = expense_claim.GetAttributeValue<string>("new_profitcenter");
                //供应商SAPID
                //string refer1 = station.GetAttributeValue<string>("new_supplier");
                //string refer1 = station?.ToDefault<string>("new_supplier");
                string refer1 = expense_claim.GetAttributeValue<string>("new_sapcode");
                //国家名称
                //string countryname = station.GetAttributeValue<EntityReference>("new_country_id").Name;
                string countryname = expense_claim.Contains("new_country_id") ? expense_claim.GetAttributeValue<EntityReference>("new_country_id").Name : "";
                //if(station != null)
                //    countryname = station.Contains("new_country_id") ? station.GetAttributeValue<EntityReference>("new_country_id").Name : "";
                //公司代码(签约主体)
                //string company = station.GetAttributeValue<string>("new_contractingbody");
                //string company = station?.ToDefault<string>("new_contractingbody");
                string company = expense_claim.GetAttributeValue<string>("new_contractingbody");
                //成本中心
                //string costcenter = station.GetAttributeValue<string>("new_costcenter");
                //string costcenter = station?.ToDefault<string>("new_costcenter");
                string costcenter = expense_claim.GetAttributeValue<string>("new_costcenter");
                //内部订单，发票号
                string originalOrder = null;
                if (!string.IsNullOrWhiteSpace(costcenter))
                {
                    if (expense_claim.Contains("new_country_id"))
                        originalOrder = GetInternalOrder(expense_claim.GetAttributeValue<EntityReference>("new_country_id").Id.ToString(), company);
                }
                //网点类型
                string new_stietypetext = expense_claim.Contains("new_stietype") ? expense_claim.FormattedValues["new_stietype"] : "";
                Dictionary<string, string> infoType = new Dictionary<string, string>() { { "预提", "Accrual" }, { "反冲", "Reversal" }, { "发票", "Invoice" }, { "付款", "Payment" } };
                typestr = typestr + infoType.Where(a => a.Key == typestr).FirstOrDefault().Value;
                // 结算单类型-显示名称
                string expenseclaimtype_name = GetOptionsSetText("new_srv_expense_claim", "new_businesstype", expenseclaimtype);
                new_remark_main = typestr + year + month + expenseclaimtype_name;
                new_remark_detail = typestr + year + month + expenseclaimtype_name + countryname + name;
                new_remark_main = new_remark_main.Length > 25 ? new_remark_main[..25] : new_remark_main;
                if (expenseclaimtype == 9)
                {
                    new_remark_main = (typestr + year + month + "安装install结费");
                    new_remark_main = new_remark_main.Length > 25 ? new_remark_main[..25] : new_remark_main;
                    new_remark_detail = (typestr + year + month + "安装install结费" + countryname + name);
                    new_remark_detail = new_remark_detail.Length > 50 ? new_remark_detail[..50] : new_remark_detail;
                }
                new_remark_detail = new_remark_detail.Length > 50 ? new_remark_detail[..50] : new_remark_detail;
                #endregion
                //获取reverse charge 配置数据
                EntityCollection reversechargecols = GetReverseCharge(new_contractingbody);
                #region 批次创建
                //批次
                Entity sapBatch = new Entity("new_sap_batch");
                sapBatch.Id = Guid.NewGuid();
                //批次结构的批次业务发生时间  
                sapBatch["new_businessdate"] = businessDate;
                //单据类型
                sapBatch["new_infotype"] = new OptionSetValue(sap_type);
                sapBatch["new_expensetno"] = new EntityReference("new_srv_expense_claim", new Guid(expense_claimId));

                orColl.Add(new CreateRequest() { Target = sapBatch });
                #endregion
                int detailCount = 0;
                // 记录涉及金额明细计算的sap_detail
                HashSet<Guid> originalSapDetail = new HashSet<Guid>();
                foreach (var item in order_handMergeColl.Entities)
                {
                    //虚拟物料，会计科目匹配优先级：1、签约主体+类型+结算单类型+reverse charge，2、签约主体+类型+reverse charge
                    var skuconfiglist = sku1collection.Entities.Where(a => a.ToDefault<string>("new_contractingbody") == new_contractingbody
                   && a.ToDefault<int>("new_infotype") == sap_type
                   /*&& a.ToDefault<int>("new_isreversecharge") == isreversecharge*/).ToList();
                    if (sap_type == 3)
                        skuconfiglist = skuconfiglist.Where(a => a.ToDefault<int>("new_isreversecharge") == isreversecharge).ToList();
                    var config_sku1list = skuconfiglist.Where(a => a.ToDefault<int>("new_businesstype") == expenseclaimtype).ToList();
                    if (config_sku1list.Count == 0)
                        config_sku1list = skuconfiglist.Where(a => !a.Contains("new_businesstype")).ToList();
                    if (expenseclaimtype == 9)
                    {
                        //结算单类型 = 安装
                        if (item.IsNotNull("new_isforceinstall") && item.GetAttributeValue<OptionSetValue>("new_isforceinstall").Value == 1)
                        {
                            //是否强安装 = 是,通过是否使用虚拟服务，是否强安装，三级品类再进行匹配
                            config_sku1list = config_sku1list.Where(a => a.GetAttributeValue<bool>("new_isvirtualservices") == item.GetAttributeValue<bool>("new_isuseinstallrights")
                                && a.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value == item.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value
                                && a.GetAttributeValue<string>("new_category3_id").Contains(item.GetAttributeValue<EntityReference>("new_category3_id").Id.ToString())).ToList();
                        }
                        else
                        {
                            //是否强安装 != 是,通过 是否使用虚拟服务，是否强安装 进行匹配
                            //config_sku1list = config_sku1list.Where(a => a.GetAttributeValue<bool>("new_isvirtualservices") == item.GetAttributeValue<bool>("new_isuseinstallrights")
                            //   && a.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value == item.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value).ToList();
                            var isforceinstall = item.IsNotNull("new_isforceinstall") ? item.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value : 2;//工单 是否强安装商品为 空时，默认为否处理
                            config_sku1list = config_sku1list.Where(a => a.GetAttributeValue<bool>("new_isvirtualservices") == item.GetAttributeValue<bool>("new_isuseinstallrights")
                              && a.GetAttributeValue<OptionSetValue>("new_isforceinstall")?.Value == isforceinstall).ToList();
                        }
                    }
                    var skuconfig = new Entity();
                    if (config_sku1list.Count > 0)
                        skuconfig = config_sku1list.First();
                    var otherskuconfig = config_sku1list.Where(a => a.Id != skuconfig.Id).ToList();//维护了多条sku
                    //明细
                    Entity sapDetail = new Entity("new_sap_detail");
                    sapDetail.Id = Guid.NewGuid();
                    if (item.Contains("sku"))
                    {
                        //sku3
                        if (expenseclaimtype == 9)
                        {
                            //结算单类型 = 安装
                            sapDetail["new_sku3"] = item.GetAttributeValue<string>("sku");
                            sapDetail["new_quantity1"] = item.ToDefault<int>("workordercount2").ToString();
                        }
                        else
                        {
                            sapDetail["new_sku3"] = item.GetAliasAttributeValue<string>("sku");
                            sapDetail["new_quantity1"] = item.ToDefault<int>("workordercount2").ToString();
                        }
                    }
                    //利润中心编码
                    sapDetail["new_profitcenter"] = profitCenter;
                    //供应商SAPID
                    if (expenseclaimtype != 9)
                        sapDetail["new_refer1"] = refer1;
                    else
                        sapDetail["new_refer1"] = item.GetAttributeValue<string>("sapchannel");//传  sap_channel
                    if (expenseclaimtype == 9)
                        sapDetail["new_zzz01"] = item.GetAttributeValue<string>("storecode");//传 门店编码
                    if (item.Contains("isocurrencycode"))
                    {
                        if (expenseclaimtype == 9)
                            sapDetail["new_currency"] = item.GetAttributeValue<string>("isocurrencycode");
                        else
                            sapDetail["new_currency"] = item.GetAliasAttributeValue<string>("isocurrencycode");
                    }
                    //内部订单，发票号
                    sapDetail["new_originalorder"] = originalOrder;
                    //备注
                    sapDetail["new_remark"] = new_remark_detail;
                    //行id
                    sapDetail["new_rowid"] = detailCount + 1;
                    //币种
                    if (sap_type == 4)
                    {
                        //支付环节，币种传SAP回传币种
                        sapDetail["new_currency"] = currency;
                    }
                    //200条放一个主表
                    if (detailCount % 200 == 0)
                    {
                        //创建sap主表
                        Entity sapMain = new Entity("new_sap_main");
                        sapMain.Id = Guid.NewGuid();
                        //批次业务类型
                        if (sap_type == 5)
                            sapMain["new_businesstype"] = "HWSHR";
                        else
                            sapMain["new_businesstype"] = "HWSH";
                        if (expenseclaimtype == 9)
                        {
                            //结算单类型 = 安装
                            if (sap_type == 5)
                                sapMain["new_businesstype"] = "HWAZR";
                            else
                                sapMain["new_businesstype"] = "HWAZ";
                        }
                        //批次结构的批次业务发生时间
                        sapMain["new_businessdate"] = businessDate;
                        //公司代码(签约主体)
                        sapMain["new_company"] = company;
                        //成本中心
                        if (expenseclaimtype != 9)
                            sapMain["new_department"] = costcenter;
                        else
                            sapMain["new_department"] = item.GetAttributeValue<string>("storecode");//传 门店编码
                                                                                                    //sapBatch
                        sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
                        //内部订单，发票号
                        sapMain["new_originalorder"] = originalOrder;
                        //备注
                        sapMain["new_remark"] = new_remark_main;
                        if (expenseclaimtype == 9)
                        {
                            //结算单类型 = 安装
                            sapMain["new_supplier"] = refer1;
                        }
                        orColl.Add(new CreateRequest() { Target = sapMain });
                        mainguid = sapMain.Id;
                    }
                    detailCount++;
                    //明细关联主表
                    sapDetail["new_sap_mainid"] = new EntityReference("new_sap_main", mainguid);
                    sapDetail["new_receivables"] = item.GetAttributeValue<decimal>("new_settlementmoney2").ToString();
                    sapDetail["new_sku1"] = skuconfig.ToDefault<string>("new_name");//sku1 = 虚拟物料
                    var config_sku2 = skuconfig;//sku1配置表和sku2配置表合并
                    if (config_sku2 != null)
                        sapDetail["new_sku2"] = config_sku2.ToDefault<string>("new_acctsubjects");//sku2 = 会计科目
                    if (sap_type == 1 || sap_type == 5)
                    {
                        //预提、反冲
                        if (item.LogicalName != "new_service_handing")
                        {
                            //预提金额
                            sapDetail["new_receivables"] = item.GetAttributeValue<decimal>("new_withholdingmoney2").ToString();
                            summoney += item.GetAttributeValue<decimal>("new_withholdingmoney2");
                        }
                    }
                    else if (sap_type == 2)
                    {
                        if (item.LogicalName != "new_service_handing")
                        {
                            //调差
                            sapDetail["new_receivables"] = item.GetAttributeValue<decimal>("new_changemoney2").ToString();
                            summoney += item.GetAttributeValue<decimal>("new_changemoney2");
                        }
                    }
                    else if (sap_type == 3)
                    {
                        if (item.LogicalName != "new_service_handing")
                        {
                            //发票
                            if (jswithouttax != null)
                            {
                                sapDetail["new_sku1"] = jswithouttax.sku1;
                                sapDetail["new_receivables"] = Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney2") / Convert.ToDecimal(jswithouttax.tax), 2).ToString();
                                summoney += Math.Round(item.GetAttributeValue<decimal>("new_settlementmoney2") / Convert.ToDecimal(jswithouttax.tax), 2);
                            }
                            else
                            {
                                sapDetail["new_receivables"] = item.GetAttributeValue<decimal>("new_settlementmoney2").ToString();
                                summoney += item.GetAttributeValue<decimal>("new_settlementmoney2");
                            }
                        }
                        //发票代码
                        sapDetail["new_refer2"] = new_invoicenumber;
                        //发票号
                        sapDetail["new_refer3"] = new_invoiceno;
                    }
                    else if (sap_type == 4)
                    {
                        if (item.LogicalName != "new_service_handing")
                        {
                            //付款
                            sapDetail["new_receivables"] = item.GetAttributeValue<decimal>("new_settlementmoney2").ToString();
                            summoney += item.GetAttributeValue<decimal>("new_settlementmoney2");
                        }
                        //sku2
                        if (skuconfig?.ToDefault<string>("new_name") == "PGE4009MI" || skuconfig?.ToDefault<string>("new_name") == "PGE4001MI")
                        {
                            //sku2
                            sapDetail["new_sku2"] = expense_claim.ToDefault<string>("new_sapsubject");
                        }
                        else
                        {
                            //sku2
                            sapDetail["new_sku2"] = config_sku2?.ToDefault<string>("new_acctsubjects");
                        }
                        //发票代码
                        sapDetail["new_refer2"] = new_invoicenumber;
                        //发票号
                        sapDetail["new_refer3"] = new_invoiceno;
                    }
                    orColl.Add(new CreateRequest() { Target = sapDetail });
                    originalSapDetail.Add(sapDetail.Id);
                    foreach (var othersku in otherskuconfig)
                    {
                        var sapDetail2 = new Entity("new_sap_detail");
                        for (int i = 0; i < sapDetail.Attributes.Count; i++)
                        {
                            var butesName = sapDetail.Attributes.Keys.ToList();
                            sapDetail2[butesName[i]] = sapDetail[butesName[i]];
                        }
                        //行id
                        sapDetail2["new_rowid"] = detailCount + 1;
                        detailCount++;
                        //sku1
                        sapDetail2["new_sku1"] = othersku?.ToDefault<string>("new_name");
                        if (othersku?.ToDefault<string>("new_name") == "PGE4009MI" || othersku?.ToDefault<string>("new_name") == "PGE4001MI")
                        {
                            if (sap_type == 4)
                            {
                                //sku2
                                sapDetail2["new_sku2"] = expense_claim.ToDefault<string>("new_sapsubject");
                            }
                            else
                            {
                                //sku2
                                sapDetail2["new_sku2"] = othersku?.ToDefault<string>("new_acctsubjects");
                            }
                        }
                        else
                        {
                            //sku2
                            sapDetail2["new_sku2"] = othersku?.ToDefault<string>("new_acctsubjects");
                        }
                        orColl.Add(new CreateRequest() { Target = sapDetail2 });
                    }
                }
                if (jswithouttax != null)
                {
                    var totalcost_withouttax = Math.Round(new_totalcost / Convert.ToDecimal(jswithouttax.tax), 2);
                    if (totalcost_withouttax != summoney)
                    {
                        //对比发票数据表头的金额是否和明细数据总额一致，如果不一致，则将表头金额-明细总额的差额，加到第一个行明细的金额上
                        Guid firstGuid = originalSapDetail.FirstOrDefault();
                        var firstsapdetail = orColl.Where(a => ((CreateRequest)a).Target.LogicalName == "new_sap_detail"
                            && ((CreateRequest)a).Target.Id == firstGuid).FirstOrDefault();
                        decimal receivables = Convert.ToDecimal(((CreateRequest)firstsapdetail).Target["new_receivables"]);
                        ((CreateRequest)firstsapdetail).Target["new_receivables"] = (receivables + (totalcost_withouttax - summoney)).ToString();
                        summoney = totalcost_withouttax;
                    }
                }
                if (!string.IsNullOrWhiteSpace(js_tax))
                {
                    if (IsJson(js_tax))
                    {
                        var transactioncurrency_id = expense_claim.ToDefault<Guid>("new_transactioncurrency_id");
                        var jsdcurrency = "";//结算单关联的币种
                        if (transactioncurrency_id != Guid.Empty)
                        {
                            Entity transactioncurrency = OrganizationService.Retrieve("transactioncurrency", transactioncurrency_id, new ColumnSet("isocurrencycode"));
                            jsdcurrency = transactioncurrency.ToDefault<string>("isocurrencycode");
                        }
                        List<Js_Tax_Model> jstaxlist = JsonConvert.DeserializeObject<List<Js_Tax_Model>>(js_tax);
                        var taxlist = jstaxlist.Where(a => a.contractbody == new_contractingbody).ToList();
                        Func<Js_Tax_Model, Guid, int, Entity> createtaxdetail = (item, mainid, rowid) =>
                        {
                            Entity tax_detail = new Entity("new_sap_detail");
                            tax_detail["new_sku1"] = item.sku1;
                            tax_detail["new_sku2"] = item.sku2;
                            tax_detail["new_currency"] = jsdcurrency;
                            tax_detail["new_remark"] = new_remark_detail;
                            tax_detail["new_refer1"] = refer1;
                            tax_detail["new_refer2"] = new_invoicenumber;
                            tax_detail["new_refer3"] = new_invoiceno;
                            tax_detail["new_rowid"] = rowid;
                            tax_detail["new_profitcenter"] = profitCenter;
                            //内部订单，发票号
                            tax_detail["new_originalorder"] = originalOrder;
                            tax_detail["new_receivables"] = ((int)Math.Round(new_totalcost * Convert.ToDecimal(item.tax), MidpointRounding.AwayFromZero)).ToString();
                            tax_detail["new_sap_mainid"] = new EntityReference("new_sap_main", mainid);
                            return tax_detail;
                        };
                        Func<Entity> createsapmain = () =>
                        {
                            //创建sap主表
                            Entity sapMain = new Entity("new_sap_main");
                            sapMain.Id = Guid.NewGuid();
                            //批次业务类型
                            if (sap_type == 5)
                                sapMain["new_businesstype"] = "HWSHR";
                            else
                                sapMain["new_businesstype"] = "HWSH";
                            if (expenseclaimtype == 9)
                            {
                                //结算单类型 = 安装
                                if (sap_type == 5)
                                    sapMain["new_businesstype"] = "HWAZR";
                                else
                                    sapMain["new_businesstype"] = "HWAZ";
                            }
                            //批次结构的批次业务发生时间
                            sapMain["new_businessdate"] = businessDate;
                            //公司代码(签约主体)
                            sapMain["new_company"] = company;
                            //成本中心
                            sapMain["new_department"] = costcenter;
                            //sapBatch
                            sapMain["new_sapbatch"] = new EntityReference("new_sap_batch", sapBatch.Id);
                            //内部订单，发票号
                            sapMain["new_originalorder"] = originalOrder;
                            //备注
                            sapMain["new_remark"] = new_remark_main;
                            return sapMain;
                        };
                        if (taxlist.Count > 0)
                        {
                            var sapmain = createsapmain();
                            orColl.Add(new CreateRequest() { Target = sapmain });
                            foreach (var item in taxlist)
                            {
                                int index = taxlist.IndexOf(item) + 1;
                                orColl.Add(new CreateRequest() { Target = createtaxdetail(item, sapmain.Id, detailCount + index) });
                            }
                            detailCount = detailCount + taxlist.Count;
                        }
                    }
                }

                #region 判断SAP账单是否需要整数处理
                string js_integer = CommonHelper.GetSystemParamValue(OrganizationService, "SettlementSendSapDetailIntegerProcess");
                if (!string.IsNullOrWhiteSpace(js_integer) && IsJson(js_integer))
                {
                    List<string> IntegerProcess = JsonConvert.DeserializeObject<List<string>>(js_integer);
                    if (IntegerProcess.Contains(new_contractingbody))
                    { // 根据签约主体判断Sap批次及其明细是否需要整数处理
                        decimal editSummoney = 0M; // 修改后的总金额明细
                        for (int i = 0; i < orColl.Count(); i++)
                        {
                            var sapdetail = orColl[i];
                            if (((CreateRequest)sapdetail).Target.LogicalName != "new_sap_detail") continue;

                            Entity entity = ((CreateRequest)sapdetail).Target;
                            decimal receivables = Convert.ToDecimal(entity["new_receivables"]);
                            receivables = Math.Round(receivables, 0, MidpointRounding.AwayFromZero);
                            entity["new_receivables"] = receivables.ToString();

                            // 判断是否需要参与计算总额
                            if (originalSapDetail.Contains(entity.Id)) editSummoney += receivables;
                        }

                        summoney = Math.Round(summoney, 0, MidpointRounding.AwayFromZero); // 以原有summoney取整为准
                        if (summoney != editSummoney)
                        { // 修改后的总额与总额不一致，添加差值到第一个行明细金额上
                            Guid firstGuid = originalSapDetail.FirstOrDefault();
                            var firstsapdetail = orColl.Where(a => ((CreateRequest)a).Target.LogicalName == "new_sap_detail"
                                && ((CreateRequest)a).Target.Id == firstGuid).FirstOrDefault();

                            Entity entity = ((CreateRequest)firstsapdetail).Target;
                            decimal receivables = Convert.ToDecimal(((CreateRequest)firstsapdetail).Target["new_receivables"]);
                            entity["new_receivables"] = (receivables + (summoney - editSummoney)).ToString();
                        }
                    }
                }
                #endregion

                //更新批次表
                Entity updatesapBatch = new Entity("new_sap_batch");
                updatesapBatch.Id = sapBatch.Id;
                //该批次主表数据量
                //updatesapBatch["new_maincount"] = (detailCount / 200) + 1;
                var or_sapmain = orColl.Where(a => a.RequestName == "Create"
                && ((CreateRequest)a).Target.LogicalName == "new_sap_main");
                updatesapBatch["new_maincount"] = or_sapmain.Count();
                //该批次详情表数据量
                updatesapBatch["new_detailcount"] = detailCount;
                //批次明细的总金额
                updatesapBatch["new_receivablessum"] = summoney;
                orColl.Add(new UpdateRequest() { Target = updatesapBatch });
                log.LogInformation("创建SAP结束");
                stopwatch.Stop();
                log.LogInformation("创建SAP日志耗时：" + stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return orColl;
        }


        /// <summary>
        /// Module ID：无
        /// Author：p-liuzanxiang
        /// Create Date：2023/5/23
        /// Modify Depiction：服务单+受理单分组合并
        /// URL:https://jira.n.xiaomi.com/browse/ISPCS-2418
        /// </summary>
        /// <param name="orderColl">服务单</param>
        /// <param name="handColl">受理单</param>
        /// <returns></returns>
        public EntityCollection order_handMerge(EntityCollection orderColl, EntityCollection handColl)
        {
            var handColl_New = new List<Entity>();
            var orderColl_New = new EntityCollection();
            handColl_New = handColl_New.Union(handColl.Entities).ToList();


            foreach (Entity order in orderColl.Entities)
            {
                order["new_withholdingmoney2"] = order.GetAliasAttributeValue<decimal>("new_withholdingmoney");
                order["new_changemoney2"] = order.GetAliasAttributeValue<decimal>("new_changemoney");
                order["new_settlementmoney2"] = order.GetAliasAttributeValue<decimal>("new_settlementmoney");
                order["workordercount2"] = order.GetAliasAttributeValue<int>("workordercount");
                string orderSku = order.Contains("sku") ? order.GetAliasAttributeValue<string>("sku") : string.Empty;
                var handbyskuColl = handColl_New.Where(m => (m.Contains("sku") ? m.GetAliasAttributeValue<string>("sku") : string.Empty) == orderSku).ToList();
                if (handbyskuColl.Count() > 0)
                {
                    var handsettlMoney = handbyskuColl.Sum(m => m.GetAliasAttributeValue<decimal>("new_settlementmoney"));
                    var sumcount = handbyskuColl.Sum(m => m.GetAliasAttributeValue<int>("workordercount"));//将服务单和受理单相同sku的工单数量汇总起来 edit by p-songyongxiang 20231018
                    order["workordercount2"] = order.GetAliasAttributeValue<int>("workordercount") + sumcount;
                    order["new_withholdingmoney2"] = order.GetAliasAttributeValue<decimal>("new_withholdingmoney") + handsettlMoney;
                    order["new_changemoney2"] = order.GetAliasAttributeValue<decimal>("new_changemoney") + handsettlMoney;
                    order["new_settlementmoney2"] = order.GetAliasAttributeValue<decimal>("new_settlementmoney") + handsettlMoney;
                    foreach (Entity hand in handbyskuColl)
                    {
                        handColl.Entities.Remove(hand);
                    }
                }
                orderColl_New.Entities.Add(order);
            }
            if (handColl.Entities.Count > 0)
            {
                foreach (Entity hand in handColl.Entities)
                {
                    hand["new_settlementmoney2"] = hand.GetAliasAttributeValue<decimal>("new_settlementmoney");
                    orderColl_New.Entities.Add(hand);
                }
            }
            return orderColl_New;
        }

        /// <summary>
        /// Module ID：无
        /// Author：p-liuzanxiang
        /// Create Date：2023/5/23
        /// Modify Depiction：根据结算单查询受理单清单
        /// URL:https://jira.n.xiaomi.com/browse/ISPCS-2418
        /// </summary>
        /// <param name="expenseClaimId">结算单ID</param>
        /// <returns></returns>
        public EntityCollection GetServiceHanding(string expenseClaimId)
        {
            string fetchXml = @"<fetch mapping='logical' no-lock='true' version='1.0' aggregate='true'>
                                  <entity name='new_service_handing'>
                                    <attribute name='new_service_handingid' alias='workordercount' aggregate='count' />
                                    <attribute name='new_settlementmoney' alias='new_settlementmoney' aggregate='sum' />
                                    <attribute name='new_transactioncurrency_id' alias='tra' groupby='true' />
                                    <attribute name='new_goodsfiles_id' alias='sp' groupby='true' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='new_srv_expense_claim_id' operator='eq' value='{0}' uitype='new_srv_expense_claim' />
                                    </filter>
                                    <link-entity name='new_goodsfiles' from='new_goodsfilesid' to='new_goodsfiles_id' link-type='outer' alias='goods'>
                                      <attribute name='new_goodsfilesid' alias='goodsid' groupby='true' />
                                      <attribute name='new_sku' alias='sku' groupby='true' />
                                    </link-entity>
                                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' link-type='outer' alias='hb'>
                                      <attribute name='isocurrencycode' alias='isocurrencycode' groupby='true' />
                                    </link-entity>
                                  </entity>
                                </fetch>";
            return OrganizationService.RetrieveMultiple(new FetchExpression(string.Format(fetchXml, expenseClaimId)));
        }
        /// <summary>
        /// Module ID：无
        /// Author：p-liuzanxiang
        /// Create Date：2023/5/23
        /// Modify Depiction：根据结算单查询服务单清单
        /// URL:https://jira.n.xiaomi.com/browse/ISPCS-2418
        /// expesneclaimtype = 1 (工单)、expesneclaimtype = 2（备件）,expesneclaimtype = 5 (Handing)
        /// </summary>
        /// <param name="expenseClaimId">结算单ID</param>
        /// <returns></returns>
        public EntityCollection GetWorkOrder(string expenseClaimId, int expenseclaimtype)
        {
            string expesneclaim_id = "new_expenseclaimid";
            string withholdingmoney_filed = "new_withholdingmoney";
            string changemoney_filed = "new_changemoney";
            string settlementmoney_filed = "new_settlementmoney";
            string transactioncurrency_service_filed = "new_transactioncurrency_service_id";
            if (expenseclaimtype == 1 || expenseclaimtype == 2)
            {
                //非B2X、B2X
                expesneclaim_id = "new_expenseclaimid";
                withholdingmoney_filed = "new_withholdingmoney";
                changemoney_filed = "new_changemoney";
                settlementmoney_filed = "new_settlementmoney";
                transactioncurrency_service_filed = "new_transactioncurrency_service_id";
            }
            else if (expenseclaimtype == 3)
            {
                //迈创
                expesneclaim_id = "new_expenseclaimidmaitrox";
                withholdingmoney_filed = "new_withholdingmoneymaitrox";
                changemoney_filed = "new_changemoneymaitrox";
                settlementmoney_filed = "new_settlementmoneymaitrox";
                transactioncurrency_service_filed = "new_transactioncurrency_maitroxservice_id";
            }
            else if (expenseclaimtype == 4 || expenseclaimtype == 8)
            {
                //运营商、收集点
                expesneclaim_id = "new_new_expenseclaimidhandling";
                withholdingmoney_filed = "new_withholdingmoneyhandling";
                changemoney_filed = "new_changemoneyhandling";
                settlementmoney_filed = "new_settlementmoneyhandling";
                transactioncurrency_service_filed = "new_transactioncurrency_handlingservice_id";
            }
            string fetchXml = @$"<fetch mapping='logical'  no-lock='true'  version='1.0' aggregate='true'>
                                  <entity name='new_srv_workorder'>
                                    <attribute name='new_srv_workorderid' alias='workordercount' aggregate='count' />
                                    <attribute name='{withholdingmoney_filed}' alias='new_withholdingmoney' aggregate='sum' />
                                    <attribute name='{changemoney_filed}' alias='new_changemoney' aggregate='sum' />
                                    <attribute name='{settlementmoney_filed}' alias='new_settlementmoney' aggregate='sum' />
                                    <attribute name='{transactioncurrency_service_filed}' alias='tra' groupby='true' />
                                    <attribute name='new_goodsfiles_id' alias='sp' groupby='true' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='{expesneclaim_id}' operator='eq' value='{expenseClaimId}'  uitype='new_srv_expense_claim' />
                                    </filter>
                                    <link-entity name='new_goodsfiles' from='new_goodsfilesid' to='new_goodsfiles_id' link-type='outer' alias='goods'>
                                      <attribute name='new_goodsfilesid' alias='goodsid' groupby='true' />
                                      <attribute name='new_sku' alias='sku' groupby='true' />
                                    </link-entity>
                                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='{transactioncurrency_service_filed}' link-type='outer' alias='hb'>
                                      <attribute name='isocurrencycode' alias='isocurrencycode' groupby='true' />
                                    </link-entity>
                                  </entity>
                                </fetch>";
            return OrganizationService.RetrieveMultiple(new FetchExpression(fetchXml));
        }
        /// <summary>
        /// 根据结算单查询激活结算单明细
        /// </summary>
        /// <param name="expenseClaimId"></param>
        /// <returns></returns>
        public EntityCollection GetActivatedetail(string expenseClaimId)
        {
            string fetchXml = @$"<fetch mapping='logical'  no-lock='true'  version='1.0' aggregate='true'>
                                  <entity name='new_srv_expense_activationline'>
                                    <attribute name='new_srv_expense_activationlineid' alias='workordercount2' aggregate='count' />
                                    <attribute name='new_withholdingmoney' alias='new_withholdingmoney2' aggregate='sum' />
                                    <attribute name='new_changemoney' alias='new_changemoney2' aggregate='sum' />
                                    <attribute name='new_settlementmoney' alias='new_settlementmoney2' aggregate='sum' />
                                    <attribute name='new_transactioncurrency_id' alias='tra' groupby='true' />
                                    <attribute name='new_sku_code' alias='sku' groupby='true' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='new_expense_claim_id' operator='eq' value='{expenseClaimId}'  uitype='new_srv_expense_claim' />
                                    </filter>
                                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_id' link-type='outer' alias='hb'>
                                      <attribute name='isocurrencycode' alias='isocurrencycode' groupby='true' />
                                    </link-entity>
                                  </entity>
                                </fetch>";
            EntityCollection cols = CommonHelper.QueryXmlPage(OrganizationService, fetchXml);
            EntityCollection newcols = new EntityCollection();
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity(item.LogicalName, item.Id);
                if (item.Contains("workordercount2"))
                    en["workordercount2"] = item.GetAliasAttributeValue<int>("workordercount2");
                if (item.Contains("new_withholdingmoney2"))
                    en["new_withholdingmoney2"] = item.GetAliasAttributeValue<decimal>("new_withholdingmoney2");
                if (item.Contains("new_changemoney2"))
                    en["new_changemoney2"] = item.GetAliasAttributeValue<decimal>("new_changemoney2");
                if (item.Contains("new_settlementmoney2"))
                    en["new_settlementmoney2"] = item.GetAliasAttributeValue<decimal>("new_settlementmoney2");
                if (item.Contains("sku"))
                    en["sku"] = item["sku"];
                if (item.Contains("isocurrencycode"))
                    en["isocurrencycode"] = item["isocurrencycode"];
                newcols.Entities.Add(en);
            }
            return newcols;
        }
        /// <summary>
        /// Module ID：无
        /// Author：p-liuzanxiang
        /// Create Date：2023/5/23
        /// Modify Depiction：签约主题加国家查询内部订单配置表
        /// URL:https://jira.n.xiaomi.com/browse/ISPCS-2418
        /// </summary>
        /// <param name="countryid">国家</param>
        /// <param name="contractingbody">签约主题</param>
        public string GetInternalOrder(string countryid, string contractingbody)
        {
            var fetchXml = @"<fetch distinct='false' no-lock='true' mapping='logical'>
                              <entity name='new_internalorderconfigure'>
                                <attribute name='new_internalorder' />
                                <attribute name='new_name' />
                                <attribute name='new_internalorderconfigureid' />
                                <attribute name='new_contractingbody' />
                                <filter type='and'>
                                  <condition attribute='statecode' operator='eq' value='0' />
                                  <condition attribute='new_country_id' operator='eq' value='{0}' />
                                  <condition attribute='new_contractingbody' operator='eq' value='{1}' />
                                </filter>
                              </entity>
                            </fetch>";
            EntityCollection collection = OrganizationService.RetrieveMultiple(new FetchExpression(string.Format(fetchXml, countryid, contractingbody)));
            if (collection?.Entities?.Count <= 0)
            {
                return null;
            }
            else
            {
                return collection.Entities[0]?.GetAttributeValue<string>("new_internalorder");
            }
        }
        /// <summary>
        /// 根据服务商获取服务网点，根据网点类型匹配，任取一条
        /// 根据网点类型，随机取有值的网点(签约主体，成本中心，sap id (供应商),国家 有值)；edit by p-songyongxiang 20240705
        /// </summary>
        /// <param name="new_srv_station_id">所属服务商</param>
        /// <param name="service">组织服务</param>
        /// <returns></returns>
        public Entity GetSrvStation(Guid new_srv_station_id, int site_type, IOrganizationService service)
        {
            //ESC,MSC,SIS,CP,mail-in center,小米之家:100000000,100000001,100000002,100000003,100000004,100000005
            //ESC,MSC,SIS,CP,mail-in center,小米之家:2,1,4,3,5,6
            Dictionary<int, int> sitetype_dic = new Dictionary<int, int>()
            {
                {100000000,2 }, {100000001,1 }, {100000002,4 }, {100000003,3 }, {100000004,5 },{100000005,6 }
            };
            int sitetypevalue = sitetype_dic.Where(k => k.Key == site_type).Count() > 0 ? sitetype_dic.Where(k => k.Key == site_type).FirstOrDefault().Value : 0;
            EntityCollection cols = service.RetrieveMultiple(
                new QueryExpression("new_srv_station")
                {
                    ColumnSet = new ColumnSet(true),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_srv_station_id", ConditionOperator.Equal, new_srv_station_id),
                            new ConditionExpression("new_servicetype", ConditionOperator.Equal, 2),
                            new ConditionExpression("new_stietype", ConditionOperator.Equal, sitetypevalue),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0),
                            new ConditionExpression("new_contractingbody", ConditionOperator.NotNull),
                            new ConditionExpression("new_supplier", ConditionOperator.NotNull),
                            new ConditionExpression("new_costcenter", ConditionOperator.NotNull),
                            new ConditionExpression("new_country_id", ConditionOperator.NotNull)
                        }
                    },
                    Orders =
                    {
                        new OrderExpression("modifiedon",OrderType.Ascending)
                    }
                });
            return cols.Entities.FirstOrDefault();
        }
        /// <summary>
        /// 查询服务商下所有的服务网点
        /// </summary>
        /// <param name="new_srv_station_id"></param>
        /// <param name="site_type"></param>
        /// <param name="service"></param>
        /// <returns></returns>
        public EntityCollection Getallstation(Guid new_srv_station_id, int site_type, IOrganizationService service)
        {
            //ESC,MSC,SIS,CP,mail-in center,小米之家:100000000,100000001,100000002,100000003,100000004,100000005
            //ESC,MSC,SIS,CP,mail-in center,小米之家:2,1,4,3,5,6
            Dictionary<int, int> sitetype_dic = new Dictionary<int, int>()
            {
                {100000000,2 }, {100000001,1 }, {100000002,4 }, {100000003,3 }, {100000004,5 },{100000005,6 }
            };
            int sitetypevalue = sitetype_dic.Where(k => k.Key == site_type).Count() > 0 ? sitetype_dic.Where(k => k.Key == site_type).FirstOrDefault().Value : 0;
            EntityCollection cols = service.RetrieveMultiple(
                new QueryExpression("new_srv_station")
                {
                    ColumnSet = new ColumnSet(true),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_srv_station_id", ConditionOperator.Equal, new_srv_station_id),
                            new ConditionExpression("new_servicetype", ConditionOperator.Equal, 2),
                            new ConditionExpression("new_stietype", ConditionOperator.Equal, sitetypevalue),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
            return cols;
        }
        /// <summary>
        /// 查询sku1配置表
        /// </summary>
        /// <param name="serviceAdmin"></param>
        /// <returns></returns>
        public EntityCollection GetSKU1Collection(IOrganizationService serviceAdmin)
        {
            return serviceAdmin.RetrieveMultiple(
                new QueryExpression("new_config_sku1")
                {
                    ColumnSet = new ColumnSet("new_contractingbody", "new_name", "new_infotype", "new_businesstype", "new_isvirtualservices", "new_isforceinstall", "new_acctsubjects", "new_category3_id", "new_isreversecharge"),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
        }
        /// <summary>
        /// 查询sku2配置表
        /// </summary>
        /// <param name="serviceAdmin"></param>
        /// <returns></returns>
        public EntityCollection GetSKU2Collection(IOrganizationService serviceAdmin)
        {
            return serviceAdmin.RetrieveMultiple(
                new QueryExpression("new_config_sku2")
                {
                    ColumnSet = new ColumnSet("new_contractingbody", "new_name", "new_sku1", "new_infotype"),
                    Criteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
        }
        /// <summary>
        /// 更新结算单下反冲sap批次main主数据的business date 为当前时间，保证和发票业务时间相同
        /// </summary>
        /// <param name="expense_claimid"></param>
        /// <param name="type"></param>
        /// <param name="serviceAdmin"></param>
        public void UpdateBusinessDate(string expense_claimid, int type, IOrganizationService serviceAdmin)
        {
            string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='true'>
  <entity name='new_sap_main'>
    <attribute name='new_sap_mainid' />
    <attribute name='new_name' />
    <attribute name='createdon' />
    <attribute name='new_sapbatch' />
    <order attribute='new_name' descending='false' />
    <link-entity name='new_sap_batch' from='new_sap_batchid' to='new_sapbatch' link-type='inner' alias='ac'>
      <filter type='and'>
        <condition attribute='new_expensetno' operator='eq' uitype='new_srv_expense_claim' value='{expense_claimid}' />
        <condition attribute='statecode' operator='eq' value='0' />
        <condition attribute='new_infotype' operator='eq' value='{type}' />
      </filter>
    </link-entity>
  </entity>
</fetch>";
            EntityCollection cols = serviceAdmin.RetrieveMultiple(new FetchExpression(fetchxml));
            foreach (var item in cols.Entities)
            {
                Entity sap_main = new Entity(item.LogicalName, item.Id);
                sap_main["new_businessdate"] = DateTime.UtcNow;
                serviceAdmin.Update(sap_main);
            }
            var new_sap_main = cols.Entities.FirstOrDefault();
            if (new_sap_main != null && new_sap_main.Contains("new_sapbatch"))
            {
                Entity sap_batch = new Entity("new_sap_batch", new_sap_main.ToEr("new_sapbatch").Id);
                sap_batch["new_businessdate"] = DateTime.UtcNow;
                serviceAdmin.Update(sap_batch);
            }
        }
        /// <summary>
        /// 判断字符串是否是JSON格式
        /// </summary>
        /// <param name="jsonString"></param>
        /// <returns></returns>
        public static bool IsJson(string jsonString)
        {
            return jsonString.Trim().StartsWith("{") && jsonString.Trim().EndsWith("}")
                || jsonString.Trim().StartsWith("[") && jsonString.Trim().EndsWith("]");
        }
        /// <summary>
        /// 停用new_sap_batch数据
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="expense_claimid"></param>
        /// <param name="service"></param>
        public void DisabledSapbatch(int type, string expense_claimid, IOrganizationService service)
        {
            EntityCollection cols = service.RetrieveMultiple(
                new QueryExpression("new_sap_batch")
                {
                    ColumnSet = new ColumnSet(false),
                    Criteria = new FilterExpression
                    {
                        Conditions =
                        {
                            new ConditionExpression("new_expensetno", ConditionOperator.Equal, expense_claimid),
                            new ConditionExpression("new_infotype", ConditionOperator.Equal, type),
                            new ConditionExpression("statecode", ConditionOperator.Equal, 0)
                        }
                    }
                });
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity(item.LogicalName, item.Id);
                en["statecode"] = new OptionSetValue(1);
                service.Update(en);
            }
        }
        /// <summary>
        /// 根据签约主体获取reverse charge配置数据
        /// </summary>
        /// <param name="contractingbody"></param>
        /// <returns></returns>
        public EntityCollection GetReverseCharge(string contractingbody)
        {
            EntityCollection cols = new EntityCollection();
            if (!string.IsNullOrWhiteSpace(contractingbody))
            {
                QueryExpression qe = new QueryExpression("new_reversechargeconfig");
                qe.ColumnSet = new ColumnSet("new_contractingbody", "new_type", "new_isreversecharge", "new_sku1", "new_businesstype");
                qe.Criteria.AddCondition("new_contractingbody", ConditionOperator.Equal, contractingbody);
                cols = OrganizationService.RetrieveMultiple(qe);
            }
            return cols;
        }

        /// <summary>
        /// 取选项集
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        string GetOptionsSetText(string entityName, string attributeName, int selectedValue)
        {

            RetrieveAttributeRequest retrieveAttributeRequest = new RetrieveAttributeRequest
            {
                EntityLogicalName = entityName,
                LogicalName = attributeName,
                RetrieveAsIfPublished = true
            };

            RetrieveAttributeResponse retrieveAttributeResponse = (RetrieveAttributeResponse)OrganizationService.Execute(retrieveAttributeRequest);

            Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata retrievedPicklistAttributeMetadata = (Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata)

            retrieveAttributeResponse.AttributeMetadata;
            OptionMetadata[] optionList = retrievedPicklistAttributeMetadata.OptionSet.Options.ToArray();
            string selectedOptionLabel = string.Empty;
            foreach (OptionMetadata oMD in optionList)
            {
                if (oMD.Value == selectedValue)
                {
                    foreach (var LocalizedLabel in oMD.Label.LocalizedLabels)
                    { // 优先使用中文标签
                        if (LocalizedLabel.LanguageCode == 2052)
                            selectedOptionLabel = LocalizedLabel.Label;
                    }
                    if (selectedOptionLabel == string.Empty)
                    { // 否则使用用户默认语言标签
                        selectedOptionLabel = oMD.Label.UserLocalizedLabel.Label;
                    }
                    break;
                }
            }
            return selectedOptionLabel;
        }
        /// <summary>
        /// 根据sku，三级品类，是否强安装，是否使用虚拟服务对工单进行汇总查询
        /// </summary>
        /// <param name="expense_claimId"></param>
        /// <param name="expenseclaimtype"></param>
        /// <returns></returns>
        public EntityCollection GetworkorderInstall(string expense_claimId)
        {
            QueryExpression qe = new QueryExpression("new_srv_workorder");
            qe.ColumnSet = new ColumnSet("new_isforceinstall", "new_isuseinstallrights", "new_category3_id");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link = new LinkEntity("new_srv_workorder", "new_workorder_costtable", "new_workorder_costtable_id", "new_workorder_costtableid", JoinOperator.Inner);
            link.Columns = new ColumnSet("new_withholdingmoneyinstall", "new_settlementmoneyinstall", "new_changemoneyinstall");
            link.EntityAlias = "link";
            link.LinkCriteria.AddCondition("new_expenseclaimidinstall", ConditionOperator.Equal, expense_claimId);
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link4 = new LinkEntity("new_workorder_costtable", "transactioncurrency", "new_transactioncurrency_installservice_id", "transactioncurrencyid", JoinOperator.LeftOuter);
            link4.EntityAlias = "link4";
            link4.Columns = new ColumnSet("isocurrencycode");
            LinkEntity link1 = new LinkEntity("new_srv_workorder", "new_goodsfiles", "new_goodsfiles_id", "new_goodsfilesid", JoinOperator.LeftOuter);
            link1.EntityAlias = "link1";
            link1.Columns = new ColumnSet("new_sku");
            //LinkEntity link2 = new LinkEntity("new_srv_usedinstallrights", "new_srv_workorder", "new_srv_workorder_id", "new_srv_workorderid", JoinOperator.LeftOuter);
            LinkEntity link2 = new LinkEntity("new_srv_workorder", "new_srv_usedinstallrights", "new_srv_workorderid", "new_srv_workorder_id", JoinOperator.LeftOuter);
            link2.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity link3 = new LinkEntity("new_srv_usedinstallrights", "new_goodsfiles", "new_goodsfiles_id", "new_goodsfilesid", JoinOperator.LeftOuter);
            link3.EntityAlias = "link3";
            link3.Columns = new ColumnSet("new_sku");
            LinkEntity link5 = new LinkEntity("new_srv_workorder", "new_srv_station", "new_station_id", "new_srv_stationid", JoinOperator.LeftOuter);
            link5.EntityAlias = "link5";
            link5.Columns = new ColumnSet("new_code");
            LinkEntity link6 = new LinkEntity("new_srv_workorder", "new_statusparameters", "new_saleschannel", "new_statusparametersid", JoinOperator.LeftOuter);
            link6.EntityAlias = "link6";
            link6.Columns = new ColumnSet("new_code");
            link.LinkEntities.Add(link4);
            qe.LinkEntities.Add(link);
            qe.LinkEntities.Add(link1);
            link2.LinkEntities.Add(link3);
            qe.LinkEntities.Add(link2);
            qe.LinkEntities.Add(link5);
            qe.LinkEntities.Add(link6);
            var cols = OrganizationService.RetrieveMultiple(qe);
            //根据SAP销售渠道表获取sap channel
            QueryExpression qe1 = new QueryExpression("new_sap_channel");
            qe1.ColumnSet = new ColumnSet("new_sales_code", "new_sap_code", "new_sales_name", "new_sap_name", "new_mdm_status");
            qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var sapchannellist = CommonHelper.QueryExpressionPagePassPlugin(OrganizationService, qe1);
            EntityCollection newcols = new EntityCollection();
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity();
                en["new_isforceinstall"] = item.GetAttributeValue<OptionSetValue>("new_isforceinstall");
                en["new_isuseinstallrights"] = item.GetAttributeValue<bool>("new_isuseinstallrights");
                en["new_category3_id"] = item.GetAttributeValue<EntityReference>("new_category3_id");
                if (item.GetAttributeValue<bool>("new_isuseinstallrights"))
                {
                    if (item.Contains("link3.new_sku"))
                        en["sku"] = item.GetAliasAttributeValue<string>("link3.new_sku");
                }
                else
                {
                    if (item.Contains("link1.new_sku"))
                        en["sku"] = item.GetAliasAttributeValue<string>("link1.new_sku");
                }
                en["new_withholdingmoney"] = item.GetAliasAttributeValue<decimal>("link.new_withholdingmoneyinstall");
                en["new_changemoney"] = item.GetAliasAttributeValue<decimal>("link.new_changemoneyinstall");
                en["new_settlementmoney"] = item.GetAliasAttributeValue<decimal>("link.new_settlementmoneyinstall");
                en["isocurrencycode"] = item.GetAliasAttributeValue<string>("link4.isocurrencycode");
                en["storecode"] = item.GetAliasAttributeValue<string>("link5.new_code");
                var salescode = item.GetAliasAttributeValue<string>("link6.new_code");
                var sapchannel = sapchannellist.Entities.Where(a => a.GetAttributeValue<string>("new_sales_code") == salescode).FirstOrDefault();
                en["sapchannel"] = sapchannel != null ? sapchannel.GetAttributeValue<string>("new_sap_code") : "";
                newcols.Entities.Add(en);
            }
            //根据sku，三级品类，是否强安装，是否使用虚拟服务对工单进行汇总，sku是最细分维度，多个sku可能对应相同品类，相同是否强安装，相同是否使用虚拟服务
            var colsgroup = newcols.Entities.GroupBy(a => new
            {
                sku = a.GetAttributeValue<string>("sku"),
                new_category3_id = a.GetAttributeValue<EntityReference>("new_category3_id"),
                new_isforceinstall = a.GetAttributeValue<OptionSetValue>("new_isforceinstall"),
                new_isuseinstallrights = a.GetAttributeValue<bool>("new_isuseinstallrights"),
                isocurrencycode = a.GetAttributeValue<string>("isocurrencycode"),
                storecode = a.GetAttributeValue<string>("storecode"),
                sapchannel = a.GetAttributeValue<string>("sapchannel")
            });
            EntityCollection newcolsgroup = new EntityCollection();
            foreach (var item in colsgroup)
            {
                Entity engroup = new Entity();
                engroup["sku"] = item.Key.sku;
                engroup["workordercount2"] = item.ToList().Count;
                engroup["new_category3_id"] = item.Key.new_category3_id;
                engroup["new_isforceinstall"] = item.Key.new_isforceinstall;
                engroup["new_isuseinstallrights"] = item.Key.new_isuseinstallrights;
                engroup["isocurrencycode"] = item.Key.isocurrencycode;
                engroup["sapchannel"] = item.Key.sapchannel;
                engroup["storecode"] = item.Key.storecode;
                engroup["new_withholdingmoney2"] = item.ToList().Sum(a => a.GetAttributeValue<decimal>("new_withholdingmoney"));
                engroup["new_changemoney2"] = item.ToList().Sum(a => a.GetAttributeValue<decimal>("new_changemoney"));
                engroup["new_settlementmoney2"] = item.ToList().Sum(a => a.GetAttributeValue<decimal>("new_settlementmoney"));
                newcolsgroup.Entities.Add(engroup);
            }
            return newcolsgroup;
        }
        /// <summary>
        /// 根据结算单查询高维工厂修整单明细
        /// </summary>
        /// <param name="expenseClaimId"></param>
        /// <returns></returns>
        public EntityCollection GetTrimmorderdetail(string expenseClaimId)
        {
            string fetchXml = @$"<fetch mapping='logical'  no-lock='true'  version='1.0' aggregate='true'>
                                  <entity name='new_trimming_orderdetail'>
                                    <attribute name='new_trimming_orderdetailid' alias='workordercount2' aggregate='count' />
                                    <attribute name='new_withholdingmoney' alias='new_withholdingmoney2' aggregate='sum' />
                                    <attribute name='new_changemoney' alias='new_changemoney2' aggregate='sum' />
                                    <attribute name='new_settlementmoney' alias='new_settlementmoney2' aggregate='sum' />
                                    <attribute name='new_transactioncurrency_service_id' alias='tra' groupby='true' />
                                    <attribute name='new_itemcode' alias='sku' groupby='true' />
                                    <filter type='and'>
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='new_expenseclaim_id' operator='eq' value='{expenseClaimId}'  uitype='new_srv_expense_claim' />
                                    </filter>
                                    <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_service_id' link-type='outer' alias='hb'>
                                      <attribute name='isocurrencycode' alias='isocurrencycode' groupby='true' />
                                    </link-entity>
                                  </entity>
                                </fetch>";
            EntityCollection cols = CommonHelper.QueryXmlPage(OrganizationService, fetchXml);
            EntityCollection newcols = new EntityCollection();
            foreach (var item in cols.Entities)
            {
                Entity en = new Entity(item.LogicalName, item.Id);
                if (item.Contains("workordercount2"))
                    en["workordercount2"] = item.GetAliasAttributeValue<int>("workordercount2");
                if (item.Contains("new_withholdingmoney2"))
                    en["new_withholdingmoney2"] = item.GetAliasAttributeValue<decimal>("new_withholdingmoney2");
                if (item.Contains("new_changemoney2"))
                    en["new_changemoney2"] = item.GetAliasAttributeValue<decimal>("new_changemoney2");
                if (item.Contains("new_settlementmoney2"))
                    en["new_settlementmoney2"] = item.GetAliasAttributeValue<decimal>("new_settlementmoney2");
                if (item.Contains("sku"))
                    en["sku"] = item["sku"];
                if (item.Contains("isocurrencycode"))
                    en["isocurrencycode"] = item["isocurrencycode"];
                newcols.Entities.Add(en);
            }
            return newcols;
        }
    }
}
