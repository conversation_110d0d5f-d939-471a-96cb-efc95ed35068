﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IdentityModel.Claims;
using System.Linq;
using System.Net.Mail;
using System.Runtime.ConstrainedExecution;
using System.Security.Cryptography;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using RekTec.Api.Common;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.HiddenApi;
using RekTec.Crm.OrganizationService.Common.Helper;
using RekTec.Service1.Service.Bll;
using RekTec.Service1.Service.Command;
using RekTec.Service1.Service.Helper;
using XiaoMi.Crm.BaseCommon.ApplicationInsights;
using XiaoMi.Crm.BaseCommon.Model;
using XiaoMi.Crm.CS.ApiCommon.Model;
using static RekTec.Service1.Service.Command.ApproachAndPartlineModel;
using DeleteRequest = Microsoft.Xrm.Sdk.Messages.DeleteRequest;
using UpdateRequest = Microsoft.Xrm.Sdk.Messages.UpdateRequest;

namespace RekTec.Service1.Service
{
    /// <summary>
    /// 服务单（处理方法/配件更换信息）页面
    /// </summary>
    public class ApproachAndPartlineCommand : HiddenCommand
    {
        public void LogaHao(Action func, string desc)
        {

            Stopwatch sw = new Stopwatch();
            sw.Start();
            func.Invoke();
            sw.Stop();
            Log.InfoMsg($"{desc},耗时:{sw.ElapsedMilliseconds}");
        }

        #region 服务单处理方法页面

        /// <summary>
        /// 服务单处理方法自定义页面保存按钮
        /// </summary>
        /// <param name="list"></param>
        public void CreateOrUpdateApproach(ApproachModel list)
        {

            try
            {
                if (list == null || list.approachList.Count <= 0)
                {
                    throw new Exception(GetResource("new_srv_workorder.AddData", "请添加数据"));
                }

                //保存处理方法校验【是否与其他处理方法并存】 add by Hyacinth 2010-10-30
                CheckCanSaveApproch(list.approachList.Select(x => x.new_approach_id).ToList());

                if (string.IsNullOrEmpty(list.new_workorder_id))
                {
                    throw new Exception(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                }
                foreach (var item in list.approachList)
                {
                    var approach = new Entity("new_srv_workorder_approach");
                    approach["new_name"] = item.new_name;
                    if (item.new_approach_id != null)//处理方法
                    {
                        approach["new_approach_id"] = new EntityReference("new_approach", new Guid(item.new_approach_id.id));
                    }
                    if (!string.IsNullOrEmpty(list.new_workorder_id))
                    {
                        approach["new_srv_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(list.new_workorder_id));
                    }
                    //维修级别不为空
                    if (!string.IsNullOrEmpty(item.new_repairlevelvaule))
                    {
                        approach["new_repairlevel"] = new OptionSetValue(Convert.ToInt32(item.new_repairlevelvaule));
                    }
                    //修改
                    if (!string.IsNullOrEmpty(item.new_approachid))
                    {
                        approach["new_srv_workorder_approachid"] = new Guid(item.new_approachid);
                        OrganizationService.Update(approach);
                    }
                    else
                    {
                        //if (CheckApproachIsRepeat(list.new_workorder_id, item.new_approach_id.id.ToString()))
                        //{
                        //    throw new InvalidPluginExecutionException(GetResource("Approach.hasExist", "当前处理方法已存在，无法添加"));
                        //}
                        OrganizationService.Create(approach);
                        //UpdateHaddingPriceByOrderId(list.new_workorder_id);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.InfoMsg("创建服务单处理方法异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 处理方法重复校验
        /// 创建人:esterwang
        /// 创建时间:2021-11-20
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <param name="approachId">处理方法id</param>
        /// <returns></returns>
        public bool CheckApproachIsRepeat(string orderId, string approachId)
        {
            try
            {
                bool result = false;
                if (string.IsNullOrEmpty(orderId) || string.IsNullOrEmpty(approachId))
                {
                    return result;
                }
                var approach = new QueryExpression("new_srv_workorder_approach");
                approach.ColumnSet = new ColumnSet("new_srv_workorder_approachid");
                approach.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                approach.Criteria.AddCondition("new_approach_id", ConditionOperator.Equal, approachId);
                approach.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var approachList = OrganizationService.RetrieveMultiple(approach);
                if (approachList != null && approachList.Entities != null && approachList.Entities.Count > 0)
                {
                    result = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[CheckApproachIsRepeat]" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 判断服务单是否使用活动，如果使用，则更改应付手工费
        /// </summary>
        /// <param name="orderId"></param>
        public void UpdateHaddingPriceByOrderId(string orderId)
        {
            if (string.IsNullOrEmpty(orderId))
            {
                return;
            }
            var query = new QueryExpression("new_srv_orderactivity");
            query.ColumnSet = new ColumnSet("new_srv_orderactivityid", "new_handdiscount", "new_discount", "new_feediscount");
            query.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var queryList = OrganizationService.RetrieveMultiple(query);
            if (queryList == null || queryList.Entities == null || queryList.Entities.Count <= 0)
            {
                return;
            }
            var order = OrganizationService.Retrieve("new_srv_workorder", new Guid(orderId), new ColumnSet("new_manualfee", "new_pay_manualfee", "new_type", "new_warranty"));
            if (order == null)
            {
                return;
            }
            if (!order.Contains("new_type") || !order.Contains("new_warranty"))
            {
                return;
            }
            if (order.GetAttributeValue<OptionSetValue>("new_type").Value == 1 && order.GetAttributeValue<OptionSetValue>("new_warranty").Value == 2)
            {
                var orderEntity = new Entity("new_srv_workorder", new Guid(orderId));
                orderEntity["new_pay_manualfee"] = order.GetAttributeValue<decimal>("new_manualfee") * queryList.Entities[0].GetAttributeValue<decimal>("new_handdiscount");
                OrganizationService.Update(orderEntity);
            }
        }
        /// <summary>
        /// 服务单处理方法自定义页面删除按钮
        /// </summary>
        /// <param name="approachid"></param>
        public void DeleteApproachById(string approachid)
        {
            try
            {
                if (string.IsNullOrEmpty(approachid))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder_approach.id_empty", "请提供当前要删除的处理方法id"));
                }

                //删除时，需控制，如果关联了“配件更换明细”，则不允许删除
                var approach = this.OrganizationServiceAdmin.Retrieve("new_srv_workorder_approach", new Guid(approachid), new ColumnSet("new_partline_id"));
                if (approach.Contains("new_partline_id"))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder_approach.hasPartLine", "已关联配件更换明细，不允许删除"));
                }
                else
                {
                    OrganizationService.Delete("new_srv_workorder_approach", new Guid(approachid));
                }
            }
            catch (Exception ex)
            {
                Log.InfoMsg("删除服务单处理方法异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        #endregion
        #region 根据imei,sn查出对应的sn,imei
        /// <summary>
        /// 根据imei,sn查出对应的sn,imei
        /// 创建人:esterwang 2021-10-29
        /// </summary>
        /// <param name="imeiOrsn"></param>
        /// <param name="isImeiOrSn"></param>
        /// <returns></returns>
        public string GetImeiOrSnBySnOrImei(string imeiOrsn, bool isImeiOrSn)
        {
            try
            {
                string result = null;
                if (string.IsNullOrEmpty(imeiOrsn))
                {
                    return result;
                }
                if (isImeiOrSn)
                {
                    var imeiResult = Command<IMEIInterfacebll>().ImeiSearchSn("sn", imeiOrsn.Split(' '));
                    if (imeiResult != null && imeiResult.Count > 0)
                        result = imeiResult[0].imei;
                    Log.InfoMsg("SN服务查询返回imei：" + JsonHelper.Serialize(imeiResult));
                }
                else
                {
                    var imeiResult = Command<IMEIInterfacebll>().ImeiSearchSn("imei", imeiOrsn.Split(' '));
                    if (imeiResult != null && imeiResult.Count > 0)
                        result = imeiResult[0].sn;
                    Log.InfoMsg("imei服务查询返回SN：" + JsonHelper.Serialize(imeiResult));
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg(ex.Message);
                Log.LogException(ex);
                throw new Exception(ex.Message);
            }
        }
        #endregion



        #region 服务单配件更换信息页面
        /// <summary>
        ///服务单配件更换信息自定义页面保存按钮
        /// update by esterwang
        /// update time:2021-11-29
        /// </summary>
        /// <param name="id">工单id</param>
        /// <param name="partlineList">配件集合</param>
        public void CreateOrUpdatePartlineh(string id, List<PartlineModel> partlineList)
        {
            //校验旧IMEI是否在IMEI服务接口校验池中
            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                }
                
                if (partlineList == null || partlineList.Count <= 0)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.AddData", "请添加数据"));
                }
                //查询工单
                var workOrder = OrganizationServiceAdmin.RetrieveWithBypassPlugin("new_srv_workorder", new Guid(id),
                    new ColumnSet("new_dealstatus", "new_warechangeway", "new_exchange_way", "new_station_id", "new_type", "new_goodsfiles_id", "new_imei", "new_sn"));
                if (workOrder == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                //禁止修改的状态 --防止状态乱改
                List<int> disableStatus = new List<int>()
                {
                    (int)RekTec.Service1.Service.Model.WorkOrderDealStatus.CANCELED,
                    (int)RekTec.Service1.Service.Model.WorkOrderDealStatus.PENDING_CLOSE,
                    (int)RekTec.Service1.Service.Model.WorkOrderDealStatus.CLOSED
                };
                if (!workOrder.Contains("new_dealstatus") || disableStatus.Contains(workOrder.GetAttributeValue<OptionSetValue>("new_dealstatus").Value))
                {
                    throw new InvalidPluginExecutionException(GetResource("common.new_dealstatuserroe", "此服务单工单状态不可用"));
                }
                var order = new Entity("new_srv_workorder");
                order.Id = new Guid(id);
                order["new_rowlock"] = id;
                OrganizationServiceAdmin.Update(order);

                #region 判断盘点状态--创建更换件明细 add by Hyacinthhuang 2021-12-8
                RekTec.Crm.BizCommon.InventorytaskCommon cmd = new Crm.BizCommon.InventorytaskCommon();
                LogaHao(() =>
                {
                    cmd.CheckOrderInventoryStatus(OrganizationService, id);
                }, "创建更换件明细判断盘点状态");

                #endregion
                bool hasApply = false;
                LogaHao(() =>
                {
                    hasApply = this.Command<new_srv_partlinebll>().CheckHasApply(id);
                }, "检查是否有特批明细");
               
                // 以换代修发货方式
                int wareChangeWay = workOrder.Contains("new_warechangeway") ? workOrder.GetAttributeValue<OptionSetValue>("new_warechangeway").Value : 0;
                // 换货方式
                int exchange_way = workOrder.Contains("new_exchange_way") ? workOrder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;

                //获取服务网点
                string stationId = null;
                if (workOrder.Contains("new_station_id"))
                {
                    stationId = workOrder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString();
                }
                Entity station = OrganizationServiceDisableMultLang.Retrieve("new_srv_station", new Guid(stationId), new ColumnSet("new_skipstockcheck"));
                bool skipStockCheck = station.GetAttributeValue<bool>("new_skipstockcheck");//是否跳过库存校验

                string imei = workOrder.GetAttributeValue<string>("new_imei");
                string sn = workOrder.GetAttributeValue<string>("new_sn");

                #region 判断工单三级品类是否为手机
                //判断三级品类是否为手机
                // bool allRequired = false;
                //判断三级品类为平板
                // bool isIpad = false;
                //if (workOrder.Contains("new_category3_id"))
                //{
                //    allRequired = JudegCategory3IsPhone(workOrder.GetAttributeValue<EntityReference>("new_category3_id").Id.ToString());
                //    isIpad = JudegCategory3IsIpad(workOrder.GetAttributeValue<EntityReference>("new_category3_id").Id.ToString());
                //}
                #endregion
                #region 配件集合
                //配件集合
                //List<PartlineModel> partlineList = JsonHelper.Deserialize<List<PartlineModel>>(approachList);
                //if (partlineList == null || partlineList.Count <= 0)
                //{
                //    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.AddData", "请添加数据"));
                //}
                #endregion
                //IMEI转string[]
                var sns = partlineList.Select(x => x.new_oldimei).Distinct().ToArray();
                if (sns == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.Oldimei_empty", "旧IMEI为空，无法在IMEI服务中判断"));
                }

                var queryCategory = new QueryExpression("new_materialcategory2");
                queryCategory.Criteria.AddCondition("new_code", ConditionOperator.Equal, 4);
                queryCategory.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var queryMaterialcategory = OrganizationService.RetrieveMultipleWithBypassPlugin(queryCategory);
                List<PartlineModel> batteryCoverParts = new List<PartlineModel>();
                if (queryMaterialcategory != null && queryMaterialcategory.Entities.Count > 0)
                {
                    batteryCoverParts = partlineList
                                        .Where(x => !string.IsNullOrEmpty(x.new_materialcategory2_id_vaule) &&
                                            Guid.Parse(x.new_materialcategory2_id_vaule) == queryMaterialcategory.Entities.First().Id)
                                                .ToList();
                }
                var batteryCoverPartsToModify = batteryCoverParts.Where(p => p.new_inrepairsvaules == 1).ToList();
                if (partlineList.Except(batteryCoverParts).Any(p => p.new_isdamaged))
                    batteryCoverPartsToModify.ForEach(p => p.new_inrepairsvaules = 2);

                // 是否全部保内
                bool AllInside = partlineList.All(x => x.new_inrepairsvaules != null && x.new_inrepairsvaules.Value == 1);
                // 是否全部保外
                bool AllOutside = partlineList.All(x => x.new_inrepairsvaules != null && x.new_inrepairsvaules.Value == 2);
                // 全部人损
                bool AllDamage = partlineList.All(x => x.new_isdamaged);
                // 全部非人损
                bool AllNoDamage = partlineList.All(x => !x.new_isdamaged);
                //判断存在电池盖时，工单是否需要改成保外
                bool UpdateOutByBattery = false;
                //判断存在电池盖时，工单是否需要改成保内
                bool UpdateInByBattery = false;

                //都保内，人损不统一
                if (AllInside && !AllDamage && !AllNoDamage)
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.NotAllExit", "配件更换明细不可同时存在保内、保外配件，请拆分建单"));

                // 有保内/保外 && 保内配件存在非人损
                if (!AllInside && !AllOutside && !partlineList.Where(x => x.new_inrepairsvaules == 1).All(x => x.new_isdamaged))
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.NotAllExit", "配件更换明细不可同时存在保内、保外配件，请拆分建单"));

                bool UpdateOutside = false;// 更新成保外
                                            // 全部保外 || 全部保内 && 全部人损 || 有保内、保外&&保内全部人损
                var inList = partlineList.Where(x => x.new_inrepairsvaules == 1).ToList();// 保内list
                if (AllOutside || (AllInside && AllDamage) || (!AllInside && !AllOutside && inList != null && inList.Count > 0 && inList.All(x => x.new_isdamaged)))
                    UpdateOutside = true;
                //保外物料费
                decimal countPrice = 0;
                //应付物料费
                decimal paymaterialcost = 0;
                //服务类型
                var newtype = workOrder.GetAttributeValue<OptionSetValue>("new_type").Value;
                //是否在保内
                bool isupdatewarranty = false;
                LogaHao(() =>
                {
                    isupdatewarranty = GetArconfigurAtionWarranty(id);
                }, "参与活动的是否保内");
                Log.InfoMsg("参与活动的是否保内为:" + isupdatewarranty);
                //是否存在人损修改
                bool isrensun = false;
                //电池盖数量
                int batteryCoverCount = 0;
                bool isBatteryCover = false;
                foreach (var item in partlineList)
                {
                    #region 去除sn、imei前后空格
                    if (!string.IsNullOrWhiteSpace(item.new_sn))
                    {
                        item.new_sn = item.new_sn.Trim().ToUpper();
                    }
                    if (!string.IsNullOrWhiteSpace(item.new_imei))
                    {
                        item.new_imei = item.new_imei.Trim().ToUpper();
                    }
                    if (!string.IsNullOrWhiteSpace(item.new_oldsn))
                    {
                        item.new_oldsn = item.new_oldsn.Trim().ToUpper();
                    }
                    if (!string.IsNullOrWhiteSpace(item.new_oldimei))
                    {
                        item.new_oldimei = item.new_oldimei.Trim().ToUpper();
                    }
                    #endregion 去除sn、imei前后空格
                    //已到料且是串号管理
                    if (item.new_statusvaule == 4 && item.new_isserial && item.new_isuser && !skipStockCheck)
                    {
                        Log.InfoMsg("进入串号校验");
                        #region 对非套机或机头的物料进行子串号校验 Modified By BaronKang 2022-06-13 暂时去掉主子串号校验
                        //bool isNeedChakSn = JudgeMaterialcategory2IsTaojiOrDanji(allRequired, item.new_materialcategory1_id, isIpad);
                        //Log.InfoMsg("是否需要校验sn" + isNeedChakSn);
                        //if (isNeedChakSn)
                        //{
                        //    Dictionary<string, object> parms = new Dictionary<string, object>();
                        //    List<string> oldSnList = new List<string>();
                        //    oldSnList.Add(item.new_oldsn);
                        //    parms.Add("sns", oldSnList);
                        //    QueryChildCondition queryChildCondition = new QueryChildCondition();
                        //    queryChildCondition.has_extend = true;
                        //    queryChildCondition.has_parent = true;
                        //    queryChildCondition.q_type = "child_sn";
                        //    queryChildCondition.business = "xms.laptop";
                        //    parms.Add("condition", queryChildCondition);
                        //    Log.InfoMsg("串号校验传入参数" + JsonHelper.Serialize(parms));
                        //    var checkResult = Command<Command.CreateWorkorderCommand>().CheckSn(parms);
                        //    if (checkResult == null || checkResult.Count <= 0)
                        //    {
                        //        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ImeicheckFail", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                        //    }
                        //    if (string.IsNullOrEmpty(checkResult[0].sn))
                        //    {
                        //        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ImeicheckFail", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                        //    }
                        //    if (checkResult[0].sn != workOrder.GetAttributeValue<string>("new_sn"))
                        //    {
                        //        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ImeicheckFail", "【旧IMEI/SN主子串号校验未通过，保存失败】"));
                        //    }
                        //}
                        #endregion

                        // 换货：大仓换货、维修：大仓发货不需要校验网点配件库存 add by Hyacinthhuang 2021-12-15
                        if (exchange_way != 2 && item.new_exchangeway != 2)
                        {
                            LogaHao(() =>
                            {
                                JudgeNewImeiOrSnIsInBarcodeinv(stationId, item.new_imei, item.new_sn, item.new_productid, item.new_productnew_idvalue);
                            }, "本地校验网点配件库存");
                        }
                    }
                    Entity entity = new Entity("new_srv_partline");
                    //全部人损
                    if (UpdateOutside)
                    {
                        decimal resultPrice = 0;
                        Log.InfoMsg("物料id" + item.new_productid);
                        if (workOrder.Contains("new_station_id") && !skipStockCheck)
                        {
                            //通过服务网点找配件价格模板，通过价格模板和物料找配件价格，赋值

                            LogaHao(() =>
                            {
                                resultPrice = Command<WorkOrderCommand>().SearchProductPriceByStationId(workOrder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString(), 2, item.new_productid);
                            }, "查询配件价格");
                            entity["new_price"] = resultPrice;
                            countPrice += resultPrice;
                        }

                        #region 维修保外单,计算应付物料费用
                        if (newtype == 1 && !string.IsNullOrEmpty(item.new_materialcategory2_id_vaule) && !skipStockCheck)
                        {
                            EntityCollection collection = new EntityCollection();
                            LogaHao(() =>
                            {
                                collection = Command<WorkOrderCommand>().JudgeIsUseSerByOrderId(id, item.new_materialcategory2_id_vaule, true);
                            }, "查询服务单活动明细");
                            if (collection != null && collection.Entities != null && collection.Entities.Count > 0)
                            {
                                if (collection.Entities[0].GetAttributeValue<decimal>("new_discount") != 0)
                                {
                                    paymaterialcost += (resultPrice - collection.Entities[0].GetAttributeValue<decimal>("new_discount") * resultPrice * 1 / 100);
                                }
                                else
                                {
                                    paymaterialcost += resultPrice;
                                }
                            }
                        }
                        #endregion
                    }
                    entity.Id = new Guid(item.new_srv_partlineid);
                    entity["new_oldimei"] = item.new_oldimei;
                    entity["new_imei"] = item.new_imei;
                    entity["new_oldsn"] = item.new_oldsn;
                    entity["new_sn"] = item.new_sn;
                    entity["new_isuser"] = item.new_isuser;
                    entity["new_isdamaged"] = item.new_isdamaged;
                    entity["new_product_id"] = new EntityReference("product", new Guid(item.new_product_id));
                    //判断此时是否保内,三包与有活动的不用判断
                    bool nowIsInrepairs = false;
                    //【初始保内/保外】=保内，【保内/保外】=保外，【是否人损】=否，【是否预先修改】=否
                    if (item.new_originalstate == 1 && item.new_inrepairsvaules == 2 && !item.new_isdamaged
                        && !batteryCoverPartsToModify.Contains(item))
                    {
                        isrensun = true;
                        entity["new_inrepairs"] = new OptionSetValue(1);
                        nowIsInrepairs = true;
                    }
                    else
                    {
                        if (UpdateOutside && !isupdatewarranty)
                        {
                            entity["new_inrepairs"] = new OptionSetValue(2);
                        }
                        // add by Hyacinthhuang 2022-4-6 存在三包超期特批,保存也是保内
                        if (isupdatewarranty || hasApply)
                        {
                            entity["new_inrepairs"] = new OptionSetValue(1);
                        }
                    }
                    #region 保内无特批时电池盖物料则判断保内保外
                    //判断此时是否保内
                    if (!UpdateOutside && !isupdatewarranty && item.new_inrepairsvaules == 1)
                    {
                        nowIsInrepairs = true;
                    }
                    if (!string.IsNullOrEmpty(item.new_materialcategory2_id_vaule) && !hasApply && nowIsInrepairs)
                    {
                        //是否进行白名单校验
                        bool isWhiteList = true;
                        //是否为电池盖物料
                        EntityCollection result = new EntityCollection();
                        var query = new QueryExpression("new_materialcategory2");
                        query.Criteria.AddCondition("new_code", ConditionOperator.Equal, 4);
                        query.Criteria.AddCondition("new_materialcategory2id", ConditionOperator.Equal, item.new_materialcategory2_id_vaule);
                        query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        result = OrganizationService.RetrieveMultipleWithBypassPlugin(query);
                        //判断是否满足保内条件
                        if (result?.Entities?.Count > 0)
                        {
                            if (entity.Id != null) {
                                //获取更换件明细中的商品id
                                var new_srv_partline = OrganizationService.RetrieveWithBypassPlugin("new_srv_partline", entity.Id, new ColumnSet("new_oldgoodsfiles_id"));
                                if (new_srv_partline.Contains("new_oldgoodsfiles_id") && new_srv_partline.GetValue<Guid>("new_oldgoodsfiles_id") != null)
                                {
                                    Guid oldgoodsfiles_id = new_srv_partline.GetValue<Guid>("new_oldgoodsfiles_id");
                                    if (new_srv_partline != null)
                                    {
                                        isWhiteList = JudgeBatteryCoverIsWarranty(id, oldgoodsfiles_id.ToString(), imei, sn);
                                        //判断是否存在电池盖特批与白名单
                                        if (isWhiteList)
                                        {
                                            batteryCoverCount++;
                                            isBatteryCover = true;
                                            entity["new_inrepairs"] = new OptionSetValue(1);
                                            //if (partlineList.Count == result.Entities.Count)
                                            //    UpdateInByBattery = true;
                                        }
                                        /*else
                                        {
                                            entity["new_inrepairs"] = new OptionSetValue(2);
                                            if (partlineList.Count == result.Entities.Count)
                                                UpdateOutByBattery = true;
                                        }*/
                                    }
                                }
                            }
                        }

                        #region 进行白名单校验
                        if (!isWhiteList)
                        {
                            Log.InfoMsg("进行白名单校验");
                            RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListPartLineModel part = new RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListPartLineModel();
                            part.new_srv_partlineid = item.new_srv_partlineid;
                            RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListFilterParams filterParams = RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListHelper.GetWorkorderInfo(OrganizationServiceAdmin, id, part);
                            RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListResultInfo whiteListRes = RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListHelper.WhiteListVerificationLogic(OrganizationServiceAdmin, filterParams, null, Log);
                            if (whiteListRes == null)
                            {
                                Log.InfoMsg("白名单校验失败无数据");
                                batteryCoverCount++;
                                entity["new_inrepairs"] = new OptionSetValue(2);
                                //if (partlineList.Count == result.Entities.Count)
                                //    UpdateOutByBattery = true;
                            }
                            else
                            {
                                Log.InfoMsg($"白名单校验成功whitelist:{whiteListRes.new_whitelist_id}");
                                batteryCoverCount++;
                                isBatteryCover = true;
                                entity["new_inrepairs"] = new OptionSetValue(1);
                                //if (partlineList.Count == result.Entities.Count)
                                //    UpdateInByBattery = true;
                            }

                        }
                        #endregion 进行白名单校验
                    }
                    #endregion 保内无特批时电池盖物料则判断保内保外
                    OrganizationService.Update(entity);
                }

                if (partlineList.Count == batteryCoverCount)
                {
                    if (isBatteryCover)
                        UpdateInByBattery = true;
                    else
                        UpdateOutByBattery = true;
                }
                #region 判断是否同时存在保内保外
                var que = new QueryExpression("new_srv_partline");
                que.ColumnSet = new ColumnSet("new_inrepairs");
                que.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                que.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, id);
                var EC = OrganizationService.RetrieveMultiple(que);
                if (EC?.Entities?.Count > 0)
                {
                    //保内
                    var inrepairs = EC.Entities.Where(x => x.GetAttributeValue<OptionSetValue>("new_inrepairs").Value == 1).ToList();
                    //保外
                    var repairs = EC.Entities.Where(x => x.GetAttributeValue<OptionSetValue>("new_inrepairs").Value == 2).ToList();
                    if (inrepairs != null && inrepairs.Count > 0 && repairs != null && repairs.Count > 0)
                        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.NotAllExit", "配件更换明细不可同时存在保内、保外配件，请拆分建单"));
                }
                #endregion 判断是否同时存在保内保外


                    
                if (isrensun)
                {
                    order["new_warranty"] = new OptionSetValue(1);
                }
                else if (UpdateOutside && !isupdatewarranty)
                {
                    order["new_warranty"] = new OptionSetValue(2);
                }

                // 参与活动或者更换件全部保内 ==> 工单保内
                if (isupdatewarranty || (AllInside && AllNoDamage) || hasApply)
                {
                    order["new_warranty"] = new OptionSetValue(1);
                }
                //全部人损，维修单
                if (UpdateOutside && newtype == 1)
                {
                    //保外物料费
                    order["new_materialcost"] = countPrice;
                    //保外物料费
                    decimal repaymaterialcost = countPrice - paymaterialcost;
                    order["new_pay_materialcost"] = repaymaterialcost;
                    //实付物料费
                    order["new_actualpay_materialcost"] = repaymaterialcost;
                }
                //电池盖改保内保外，更改工单状态
                if (UpdateOutByBattery)
                {
                    order["new_warranty"] = new OptionSetValue(2);
                }
                else if (UpdateInByBattery)
                {
                    order["new_warranty"] = new OptionSetValue(1);
                }
                order["new_rowlock"] = "";
                OrganizationService.Update(order);
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = id,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "CreateOrUpdatePartlineh",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = "配件更换信息自定义页面保存完成",
                    Level = LogSeverityLevel.Informational,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = id,
                    LoginUserId = Context.InitiatingUserId.ToString()
                }, AppInsightsLogType.Trace);
            }
            catch (Exception ex)
            {
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = id,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "CreateOrUpdatePartlineh",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = $"配件更换信息自定义页面保存异常:{ex}",
                    Level = LogSeverityLevel.Error,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = id,
                    LoginUserId = Context.InitiatingUserId.ToString(),
                    Code = ex.GetType().ToString() == "Microsoft.Xrm.Sdk.InvalidPluginExecutionException" ? ExceptionCode.BusinessAlertException : ExceptionCode.SystemException
                }, AppInsightsLogType.Exception);
                Log.InfoMsg("创建更新配件更换信息异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 根据服务单查询服务单活动明细
        /// 创建人:esterwang
        /// 创建时间:2022-01-21
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <returns></returns>
        public bool GetArconfigurAtionWarranty(string orderId)
        {
            try
            {
                var query = new QueryExpression("new_srv_orderactivity");
                query.ColumnSet = new ColumnSet("new_isupdatewarranty");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                var queryList = OrganizationService.RetrieveMultiple(query);
                if (queryList == null || queryList.Entities == null || queryList.Entities.Count <= 0)
                {
                    return false;
                }
                if (queryList.Entities[0].GetAttributeValue<bool>("new_isupdatewarranty"))
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[GetArconfigurAtionWarranty]方法出错" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 电池盖物料判断是否保内
        /// 创建人:panyizhang
        /// 创建时间:2024-5-14
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <param name="goodsfiles">商品档案id</param>
        /// <returns></returns>
        public bool JudgeBatteryCoverIsWarranty(string orderId, string goodsfiles, string imei, string sn){
            QueryExpression queryByWorkOrderId = new QueryExpression("new_srv_specialapply");
            queryByWorkOrderId.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
            // 类型：电池盖保内特批
            queryByWorkOrderId.Criteria.AddCondition("new_type", ConditionOperator.Equal, 7);
            queryByWorkOrderId.Criteria.AddCondition("new_approvalstatus", ConditionOperator.Equal, 3);
            queryByWorkOrderId.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            queryByWorkOrderId.ColumnSet.AddColumns("new_srv_specialapplyid");
            EntityCollection ecByWorkOrderId = OrganizationServiceAdmin.RetrieveMultiple(queryByWorkOrderId);
            if (ecByWorkOrderId?.Entities?.Count == 0)
            {
                QueryExpression queryByImeiOrSN = new QueryExpression("new_srv_specialapply");
                queryByImeiOrSN.Criteria.AddCondition("new_type", ConditionOperator.Equal, 7);
                queryByImeiOrSN.Criteria.AddCondition("new_approvalstatus", ConditionOperator.Equal, 3);
                queryByImeiOrSN.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                FilterExpression imeiOrSnFilter = new FilterExpression(LogicalOperator.Or);
                if (!string.IsNullOrEmpty(imei))
                {
                    imeiOrSnFilter.AddCondition("new_imei", ConditionOperator.Equal, imei);
                }
                if (!string.IsNullOrEmpty(sn))
                {
                    imeiOrSnFilter.AddCondition("new_sn", ConditionOperator.Equal, sn);
                }
                if (imeiOrSnFilter.Conditions.Count > 0)
                {
                    queryByImeiOrSN.Criteria.AddFilter(imeiOrSnFilter);
                }
                queryByImeiOrSN.ColumnSet.AddColumns("new_srv_specialapplyid", "new_workorder_id", "createdon");

                queryByImeiOrSN.AddOrder("createdon", OrderType.Ascending);

                EntityCollection ecByImeiOrSN = OrganizationServiceAdmin.RetrieveMultipleWithBypassPlugin(queryByImeiOrSN);

                if (ecByImeiOrSN?.Entities?.Count > 0)
                {
                    DateTime sevenDaysAgo = DateTime.Now.AddDays(-7);
                    var recentEntities = ecByImeiOrSN.Entities
                        .Where(e => e.GetAttributeValue<DateTime>("createdon") >= sevenDaysAgo)
                        .OrderBy(e => e.GetAttributeValue<DateTime>("createdon"))
                        .ToList();
                    if (recentEntities?.Count > 0)
                    {
                        var latestEntity = recentEntities.First();
                        var specialApplyToUpdate = new Entity("new_srv_specialapply", latestEntity.Id);
                        specialApplyToUpdate["new_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(orderId));
                        OrganizationServiceAdmin.Update(specialApplyToUpdate);
                        return true;
                    }
                }
                /*QueryExpression query2 = new QueryExpression("new_whitelist");
                query2.Criteria.AddCondition("new_goodsfiles_id", ConditionOperator.Equal, goodsfiles);
                // 类型：电池盖保内更换
                query2.Criteria.AddCondition("new_whitelisttype", ConditionOperator.Equal, 1);
                query2.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query2.ColumnSet.AddColumns("new_whitelistid");
                EntityCollection ec2 = OrganizationServiceAdmin.RetrieveMultiple(query2);
                if (ec2.Entities.Count == 0)*/
                return false;
            }
            return true;
        }


/// <summary>
/// 判断三级品类是否是手机
/// 创建人：esterwang
/// 创建时间:2021-12-09
/// </summary>
/// <param name="categoryId">三级品类id</param>
/// <returns></returns>
public bool JudegCategory3IsPhone(string categoryId)
        {
            try
            {
                bool result = false;
                if (string.IsNullOrEmpty(categoryId))
                {
                    return result;
                }
                //获取三级品类为手机的系统参数
                string new_category3_iphonecode = CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "new_category3_iphonecode", true);
                var categroy = OrganizationService.Retrieve("new_category3", new Guid(categoryId), new ColumnSet("new_category3id", "new_code"));
                if (categroy == null || !categroy.Contains("new_code"))
                {
                    return result;
                }
                if (categroy.GetAttributeValue<string>("new_code").Equals(new_category3_iphonecode))
                {
                    result = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[JudegCategory3IsPhone]方法错误" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 判断三级品类是否是平板
        /// 创建人：esterwang
        /// 创建时间:2022-01-21
        /// </summary>
        /// <param name="categoryId">三级品类id</param>
        /// <returns></returns>
        public bool JudegCategory3IsIpad(string categoryId)
        {
            try
            {
                bool result = false;
                if (string.IsNullOrEmpty(categoryId))
                {
                    return result;
                }
                //获取三级品类为手机的系统参数
                string new_category3_padcode = CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "new_category3_padcode", true);
                var categroy = OrganizationService.Retrieve("new_category3", new Guid(categoryId), new ColumnSet("new_category3id", "new_code"));
                if (categroy == null || !categroy.Contains("new_code"))
                {
                    return result;
                }
                if (categroy.GetAttributeValue<string>("new_code").Equals(new_category3_padcode))
                {
                    result = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[JudegCategory3IsPhone]方法错误" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 串号校验判断条件
        /// 创建人:esterwang
        /// 创建时间:2021-11-29
        /// </summary>
        /// <param name="materialcategoryId">物料类别id</param>
        /// <param name="isIpad">是否平板</param>
        /// <returns></returns>
        public bool JudgeMaterialcategory2IsTaojiOrDanji(bool isPhone, string materialcategoryId, bool isIpad)
        {
            try
            {
                bool result = false;
                if (string.IsNullOrEmpty(materialcategoryId))
                {
                    return result;
                }
                //查询物料类别编码
                var material = OrganizationService.Retrieve("new_materialcategory1", new Guid(materialcategoryId), new ColumnSet("new_code"));
                if (material == null)
                {
                    return result;
                }
                string code = material.GetAttributeValue<string>("new_code");
                //物料类别为显示屏类系统参数
                string materialcategory1_screencode = CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "materialcategory1_screencode", true);
                //物料类别为主板系统参数
                string new_materialcategory1_mainboard = CrmHelper.GetSystemParameterValue(OrganizationServiceAdmin, "new_materialcategory1_mainboard", true);
                //如果是平板，主板，则不校验主子串号
                if (isIpad && code.Equals(new_materialcategory1_mainboard))
                {
                    return false;
                }
                //如果是手机，校验屏幕类别
                if (isPhone)
                {
                    if (code.Equals(materialcategory1_screencode))
                    {
                        return true;
                    }
                }
                //非手机，校验屏幕和主板
                else
                {
                    if (code.Equals(materialcategory1_screencode) || code.Equals(new_materialcategory1_mainboard))
                    {
                        return true;
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[JudgeMaterialcategory2IsTaojiOrDanji]方法出错" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 根据物料id查询物料名称
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        public string SelectProductNameByProductId(string productId)
        {
            string result = string.Empty;
            if (string.IsNullOrEmpty(productId))
            {
                return result;
            }
            var product = OrganizationService.Retrieve("product", new Guid(productId), new ColumnSet("name"));
            if (product == null)
            {
                return result;
            }
            if (product.Contains("name"))
            {
                result = product.GetAttributeValue<string>("name");
            }
            return result;
        }
        /// <summary>
        /// 判断当前sn，imei在配件条码库存是否存在
        /// 创建人 : esterwang
        /// 创建时间: 2021-11-29
        /// </summary>
        /// <param name="stationId">服务网点id</param>
        /// <param name="imei">imei</param>
        /// <param name="sn">sn</param>
        /// <param name="productId">新配件id</param>
        /// <returns></returns>
        public void JudgeNewImeiOrSnIsInBarcodeinv(string stationId, string imei, string sn, string productId, string newProductName)
        {
            try
            {
                if (string.IsNullOrEmpty(stationId) || string.IsNullOrEmpty(sn) || string.IsNullOrEmpty(productId))
                {
                    throw new InvalidPluginExecutionException("stationId、sn、productId 中有空值");
                }
                Log.InfoMsg("查询服务仓库");
                //找到服务网点下的服务仓库 new_srv_stocksite
                var stocksite = new QueryExpression("new_srv_stocksite");
                stocksite.ColumnSet = new ColumnSet("new_srv_stocksiteid");
                stocksite.Criteria.AddCondition("new_station_id", ConditionOperator.Equal, stationId);
                stocksite.Criteria.AddCondition("new_stocktype", ConditionOperator.Equal, 1);//良品库
                stocksite.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var stocksiteList = OrganizationService.RetrieveMultiple(stocksite);
                if (stocksiteList == null || stocksiteList.Entities == null || stocksiteList.Entities.Count <= 0)
                {
                    throw new InvalidPluginExecutionException(GetResource("common.stockEmpty", "未查询到对应服务网点仓库信息"));
                }
                Log.InfoMsg("查询配件条码库存");
                var query = new QueryExpression("new_partbarcodeinv");
                query.ColumnSet = new ColumnSet("new_partbarcodeinvid", "new_product_id");
                query.Criteria.AddCondition("new_stocksite_id", ConditionOperator.Equal, stocksiteList.Entities[0].Id);
                query.Criteria.AddCondition("new_name", ConditionOperator.Equal, sn);
                if (!string.IsNullOrEmpty(imei))
                    query.Criteria.AddCondition("new_imei", ConditionOperator.Equal, imei);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_status", ConditionOperator.Equal, 1);
                //query.Criteria.AddCondition("new_product_id", ConditionOperator.Equal, productId);
                var queryList = OrganizationService.RetrieveMultiple(query);
                if (queryList == null || queryList.Entities == null || queryList.Entities.Count <= 0)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_inventorydifference_snline.notName", "串号不存在仓库中，无法保存！"));
                }
                var partBar = queryList.Entities.Where(x => x.GetAttributeValue<EntityReference>("new_product_id")?.Id.ToString() == productId).Select(x => x.Id).ToList();
                if (partBar.Count == 0)
                {
                    var productName = queryList.Entities[0].GetAttributeValue<EntityReference>("new_product_id").Name;
                    throw new InvalidPluginExecutionException(string.Format(GetResource("CreateOrUpdatePartlineh.CheckSnInStation", "保存失败！该SN关联物料为：{0}，与当前物料{1}不一致"), productName, newProductName));

                }
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[JudgeNewImeiOrSnIsInBarcodeinv]方法出错" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 服务单配件更换信息自定义页面确认申请按钮
        /// Modifier：p-liuzanxiang
        /// Modification Date：2023/9/7
        /// Modify Depiction：新增 跳过库存且推送工单校验
        /// url：https://jira.n.xiaomi.com/browse/ISPCS-2586
        /// </summary>
        public void ConfirmSublit(string orderId, string partLine)
        {
            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                #region 判断盘点状态--更换件确认申请 add by Hyacinthhuang 2021-12-8
                RekTec.Crm.BizCommon.InventorytaskCommon cmd = new Crm.BizCommon.InventorytaskCommon();
                LogaHao(() =>
                {
                    cmd.CheckOrderInventoryStatus(OrganizationService, orderId);
                }, "确认申请盘点盘点状态");

                #endregion

                #region 参数校验
                /**
                 * 校验配件明细是否存在套机(子类别=套机 || 物料是商品)，不存在就
                 * 校验维修费用是否大于商品费用的90% 
                 * **/
                if (string.IsNullOrWhiteSpace(orderId) || string.IsNullOrWhiteSpace(partLine))
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ConfirmSublitParmError", "配件更换明细确认申请参数错误"));
                // 服务单id
                List<partLineModel> partlineModel = JsonHelper.Deserialize<List<partLineModel>>(partLine);
                // 配件更换明细
                if (partlineModel == null || partlineModel.Count <= 0)
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ConfirmSublit_partLineNULL", "配件更换明细确认申请明细为空"));

                List<string> partLineIds = partlineModel.Select(x => x.new_srv_partlineid).ToList();
                if (partLineIds == null)
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ConfirmSublit_partLineIdsNULL", "配件跟换明细申请明细id为空"));

                LogaHao(() =>
                {
                    CheckPartLine(orderId);
                }, "校验新商品");
                // 校验新商品

                #endregion

                #region 大仓发货有值时，校验是否存在发货方式是否一致
                if (partlineModel.All(p => p.new_exchangeway != null))
                {
                    bool local = partlineModel.All(x => x.new_exchangeway.Value == 1);
                    bool ware = partlineModel.All(x => x.new_exchangeway.Value == 2);
                    if (!local && !ware)
                        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.ConfirmFailed", "【确认失败！当前更换明细中不支持同时存在大仓发货和本地发货的物料】"));
                }
                #endregion

                #region 使用保险，且保险类型是意外险，校验整机价格
                Entity order = OrganizationService.Retrieve("new_srv_workorder", new Guid(orderId), new ColumnSet("new_insurancetype", "new_manualfee", "new_materialcost", "new_partsoperationfee", "new_isuseinsurancecost", "new_warrantystatus", "new_warranty", "new_type", "new_exchange_way", "new_station_id", "new_servicemode", "new_servicestation_id", "new_application_time"));
                LogaHao(() =>
                {
                    UsingInsuranceCheckPrice(order, partLineIds);
                }, "使用意外险校验整机价格");

                #endregion

                #region 保外判断联系用户
                int warrantystatus = order.Contains("new_warrantystatus") ? order.GetAttributeValue<OptionSetValue>("new_warrantystatus").Value : 0;
                int warranty = order.Contains("new_warranty") ? order.GetAttributeValue<OptionSetValue>("new_warranty").Value : 0;
                if (warranty == 2 && warrantystatus != 2)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.WarrantyContactCustomer", "请点击【确认联系用户】按钮，确认已与用户联系报价后再申请物料"));
                }
                #endregion

                #region 查询工单服务商信息 add by Hyacinthhuang 2022-5-9
                // 网点类型 1：MC_consign；2：停止国家;3：buy-sell；4：Xiaomi-consign
                string stationId = order.Contains("new_station_id") ? order.GetAttributeValue<EntityReference>("new_station_id").Id.ToString() : null;
                int consignType = this.Command<WorkorderToMCCommand>().getConsignType(stationId, OrganizationServiceDisableMultLang);

                Entity station = OrganizationServiceDisableMultLang.Retrieve("new_srv_station", new Guid(stationId), new ColumnSet("new_skipstockcheck", "new_skipstockcheck_syncworkorder"));
                bool skipStockCheck = station.GetAttributeValue<bool>("new_skipstockcheck");//是否跳过库存校验
                //获取网点是否跳过库存校验-推送工单字段 add by p-liuzanxiang 2023-9-7
                bool skipStockCheckSnycOrder = station.GetAttributeValue<bool>("new_skipstockcheck_syncworkorder");//是否跳过库存校验-推送工单


                //if (stationType == 0 && order.Contains("new_servicestation_id"))
                //    stationType = this.Command<WorkorderToMCCommand>().GetSrvStationType(order.GetAttributeValue<EntityReference>("new_servicestation_id").Id.ToString());
                #endregion

                #region 更新配件更换明细
                int lackCount = 0;//缺料数量
                int servicrType = order.GetAttributeValue<OptionSetValue>("new_type").Value;//服务类型（维修、换货）
                int exchangeWay = order.Contains("new_exchange_way") ? order.GetAttributeValue<OptionSetValue>("new_exchange_way").Value : 0;//换货方式

                // 配件更换明细id字典  stirng: id, int: 占料/缺料
                Dictionary<string, int> IdsDictionary = new Dictionary<string, int>();
                foreach (var item in partlineModel)
                {
                    int new_ammaterialsstate = 0;
                    var part = OrganizationService.Retrieve("new_srv_partline", new Guid(item.new_srv_partlineid), new ColumnSet("new_ammaterialsstate"));
                    if (part != null)
                        new_ammaterialsstate = part.Contains("new_ammaterialsstate") ? part.GetAttributeValue<OptionSetValue>("new_ammaterialsstate").Value : 0;
                    LogaHao(() =>
                    {
                        Entity newline = new Entity("new_srv_partline");
                        newline.Id = new Guid(item.new_srv_partlineid);
                        newline["new_srv_workorder_id"] = new EntityReference("new_srv_workorder", new Guid(orderId));
                        if (skipStockCheck)
                        {
                            //跳过库存校验，领料状态改为已到料
                            newline["new_status"] = new OptionSetValue(4);
                        }
                        else if (item.new_iflackmaterial != null && new_ammaterialsstate != 1)
                        {
                            newline["new_status"] = item.new_iflackmaterial.Value ? new OptionSetValue(3) : new OptionSetValue(4);
                            if (item.new_iflackmaterial.Value)
                            {
                                lackCount++;
                                /*
                                #region 缺料发送邮件给总部备件

                                //换货大仓换货/维修大仓换货
                                if ((servicrType == 2 && exchangeWay == 2) || (servicrType == 1 && item.new_exchangeway == 2))
                                {
                                    LackMaterialSendEmail(orderId, item.new_srv_partlineid);
                                }

                                #endregion 缺料发送邮件给总部备件
                                */
                            }
                        }

                        OrganizationService.Update(newline);
                    }, "更新缺料状态");
                }
                #endregion

                #region 占料&缺料全量抛迈创
                if (consignType == 1 && (!skipStockCheck || skipStockCheckSnycOrder))
                {
                    LogaHao(() =>
                    {
                        // 领料全量推MC -- p-zhuhao7 2024.8.5
                        this.Command<WorkorderToMCCommand>().ThrowOrderToMC(1, orderId, 4);
                    }, "领料全量抛迈创");
                }
                #endregion

                #region 更新工单状态
                LogaHao(() =>
                {
                    Entity neworder = new Entity("new_srv_workorder");
                    neworder.Id = new Guid(orderId);
                    // 物料申请时间
                    if (!order.Contains("new_application_time"))
                    {
                        neworder["new_application_time"] = DateTime.UtcNow;
                    }
                    if (lackCount > 0)
                    {
                        neworder["new_claim_status"] = new OptionSetValue(3);
                    }
                    else
                    {
                        neworder["new_claim_status"] = new OptionSetValue(4);
                        // 物料发放时间
                        neworder["new_issuance_time"] = DateTime.UtcNow;
                    }
                    OrganizationService.Update(neworder);
                }, "更新工单状态");

                #endregion

                #region 缺料发送缺料短信 add by Hyacinthhuang 2022-6-8
                // 非维修换货不发短信
                if (lackCount > 0 && !new int[] { 1, 2 }.Contains(servicrType))
                {
                    LogaHao(() =>
                    {
                        SendLackMaterialMsg(order);
                    }, "缺料发送短信");

                }
                #endregion

                #region 非迈创抛SPM  Modified 2022-5-10
                // 是否支持急料急单
                //bool isUrgent = false;
                //Entity station = OrganizationService.Retrieve("new_srv_station", order.GetAttributeValue<EntityReference>("new_station_id").Id, new ColumnSet("new_issupporturgent"));
                //isUrgent = station.Contains("new_issupporturgent") ? station.GetAttributeValue<bool>("new_issupporturgent") : false;
                //增加判断【是否跳过库存校验】
                //Modified by p-liuzanxiang 2023-9-7 增加跳过库存校验推送工单校验
                if (new int[] { 3, 4 }.Contains(consignType) &&(!skipStockCheck || skipStockCheckSnycOrder))
                {
                    LogaHao(() =>
                    {
                        // 领料确定 抛服务单给spm add by Hyacinth 2021-11-06
                        this.Command<Service.Command.WorkOrderCommand>().ThrowOrderToSPM(1, orderId, consignType);
                    }, "占料抛SPM");

                }
                #endregion

                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = orderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "ConfirmSublit",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = "配件更换信息自定义页面确认申请完成",
                    Level = LogSeverityLevel.Informational,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = orderId,
                    LoginUserId = Context.InitiatingUserId.ToString()
                }, AppInsightsLogType.Trace);

            }
            catch (Exception ex)
            {
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = orderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "ConfirmSublit",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = $"配件更换信息自定义页面确认申请异常:{ex}",
                    Level = LogSeverityLevel.Error,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = orderId,
                    LoginUserId = Context.InitiatingUserId.ToString(),
                    Code = ex.GetType().ToString() == "Microsoft.Xrm.Sdk.InvalidPluginExecutionException" ? ExceptionCode.BusinessAlertException : ExceptionCode.SystemException
                }, AppInsightsLogType.Exception);
                Log.InfoMsg("确认申请配件更换信息异常记录");
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 服务单配件更换信息自定义页面删除按钮
        /// Modifier：p-liuzanxiang
        /// Modification Date：2023/9/7
        /// Modify Depiction：新增 跳过库存且推送工单校验
        /// url：https://jira.n.xiaomi.com/browse/ISPCS-2586
        /// </summary>
        /// <param name="partlineid"></param>
        public void DeletePartlineById(string partlineid, string workOrderId)
        {
            //查询更换件明细
            Entity entity_partline = OrganizationService.RetrieveWithBypassPlugin("new_srv_partline", Guid.Parse(partlineid), new ColumnSet("new_ismipart"));
            var ismipart = entity_partline.Contains("new_ismipart")? entity_partline.GetAttributeValue<bool>("new_ismipart"):true;//是否为小米物料
            if (!ismipart)//非小米物料调用非小米无物料的删除方法
            {
                //非小米物料配件更换明细删除
                NonXiaoMiDeletePartlineById(partlineid, workOrderId);
                return;
            }

            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {

                //处理方法、未领料
                if (string.IsNullOrEmpty(workOrderId))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                }

                //处理方法、未领料
                if (string.IsNullOrEmpty(partlineid))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.id_empty", "更换件明细id为空"));
                }
                var workorder = OrganizationService.RetrieveWithBypassPlugin("new_srv_workorder", new Guid(workOrderId), new ColumnSet("new_station_id", "new_dealstatus", "new_warranty", "new_partcost", "new_materialcost", "new_type", "new_pay_materialcost", "new_exchange_way", "new_servicestation_id", "new_whitelist_id"));
                if (workorder == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                int[] orderStatus = new int[] { 8, 9, 10, 11, 12, 13 };//不可删料的工单状态(服务完成之后的状态)
                if (!workorder.Contains("new_dealstatus") || orderStatus.Contains(workorder.GetAttributeValue<OptionSetValue>("new_dealstatus").Value))
                {
                    throw new InvalidPluginExecutionException(GetResource("addmaterial.OrderStatusNotMatch", "当前工单状态无法更新配件更换明细"));
                }
                var partline = this.OrganizationService.RetrieveWithBypassPlugin("new_srv_partline", new Guid(partlineid), new ColumnSet("new_status", "new_product_id", "new_lackmaterialnumber", "new_qty", "new_oldsn", "new_isuser", "new_inrepairs", "new_price", "new_materialcategory2_id", "new_exchangeway", "new_iflackmaterial", "new_productnew_id", "new_iswriteno", "new_islackmaterial", "new_isdamaged", "new_originalstate"));
                if (partline.Contains("new_iswriteno") && partline.GetAttributeValue<bool>("new_iswriteno"))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.iswriteno", "当前配件更换明细为写号单，不允许删除"));
                }
                bool isBatteryCover = false;
                if (partline.Contains("new_materialcategory2_id"))
                {
                    var materialcategory2 =  OrganizationService.RetrieveWithBypassPlugin("new_materialcategory2", partline.GetAttributeValue<EntityReference>("new_materialcategory2_id").Id, new ColumnSet("new_code"));
                    if (materialcategory2.Contains("new_code") && !string.IsNullOrEmpty(materialcategory2.GetAttributeValue<string>("new_code"))) {
                        string code = materialcategory2.GetAttributeValue<string>("new_code");
                        isBatteryCover = (code == "4");//是否为电池盖物料
                    }
                }

                //是否人损
                var isdamaged = partline.Contains("new_isdamaged") ? partline.GetAttributeValue<bool>("new_isdamaged") : false;

                #region 判断盘点状态--删除更换件 add by Hyacinthhuang 2021-12-8
                RekTec.Crm.BizCommon.InventorytaskCommon cmd = new Crm.BizCommon.InventorytaskCommon();
                cmd.CheckOrderInventoryStatus(OrganizationService, workOrderId);
                #endregion


                //if (!partline.Contains("new_status") || partline.GetAttributeValue<OptionSetValue>("new_status").Value == 2 || partline.GetAttributeValue<OptionSetValue>("new_status").Value == 3)
                //{
                //    throw new InvalidPluginExecutionException("缺料、待发料状态下不允许删除");
                //}
                //领料状态
                int partStatus = 0;
                if (partline.Contains("new_status"))
                {
                    partStatus = partline.GetAttributeValue<OptionSetValue>("new_status").Value;
                }


                Entity station = OrganizationServiceDisableMultLang.RetrieveWithBypassPlugin("new_srv_station", workorder.GetAttributeValue<EntityReference>("new_station_id").Id, new ColumnSet("new_skipstockcheck","new_skipstockcheck_syncworkorder"));
                bool skipStockCheck = station.GetAttributeValue<bool>("new_skipstockcheck");//是否跳过库存校验
                //获取网点是否跳过库存校验-推送工单字段 add by p-liuzanxiang 2023-9-7
                bool skipStockCheckSnycOrder = station.GetAttributeValue<bool>("new_skipstockcheck_syncworkorder");//是否跳过库存校验-推送工单

                //#region 迈创Consign 删除物料抛迈创 add by Hyacinthhuang 2022-5-10
                #region 迈创Consign 删除物料抛迈创 Modified by p-liuzanxiang 2023-9-7 增加跳过库存校验推送工单校验

                if (!skipStockCheck || skipStockCheckSnycOrder)
                {
                    DeletePartLineToMC(workorder, partlineid);
                }
                #endregion

                //查询关联配件更换明细的处理方法

                QueryExpression approach = new QueryExpression("new_srv_workorder_approach");
                approach.ColumnSet = new ColumnSet("new_srv_workorder_approachid", "new_partline_tex");
                approach.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                approach.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, new Guid(workOrderId));
                var approachList = OrganizationService.RetrieveMultipleWithBypassPlugin(approach).Entities;
                DeleteApproach(approachList, partlineid);

                OrganizationService.Delete("new_srv_partline", new Guid(partlineid));
                //服务类型
                int serviceType = 0;
                if (workorder.Contains("new_type"))
                {
                    serviceType = workorder.GetAttributeValue<OptionSetValue>("new_type").Value;
                }
                //换货方式
                int exchangeWay = 0;
                if (workorder.Contains("new_exchange_way"))
                {
                    exchangeWay = workorder.GetAttributeValue<OptionSetValue>("new_exchange_way").Value;
                }
                //配件换货方式
                int partExchangeWay = 0;
                if (partline.Contains("new_exchangeway"))
                {
                    partExchangeWay = partline.GetAttributeValue<OptionSetValue>("new_exchangeway").Value;
                }
                //是否需要修改库存
                bool isNeedUpdateStock = true;
                //缺料删除时，不涉及库存 //换货大仓换货/维修大仓换货//未领料，是缺料，缺料数量大于0时，删除不能执行库存修改
                if (partStatus == 3 || ((serviceType == 2 && exchangeWay == 2) || (serviceType == 1 && partExchangeWay == 2)) || (partStatus == 1 && partline.GetAttributeValue<decimal>("new_lackmaterialnumber") > 0 && partline.GetAttributeValue<bool>("new_iflackmaterial") && partline.GetAttributeValue<decimal>("new_qty") > 0))
                {
                    isNeedUpdateStock = false;
                }
                //POH不更改库存
                if (partline.Contains("new_islackmaterial") && partline.GetAttributeValue<OptionSetValue>("new_islackmaterial").Value == 1)
                    isNeedUpdateStock = false;
                if (isNeedUpdateStock && !skipStockCheck)
                {
                    List<string> snList = new List<string>();
                    snList.Add(partline.GetAttributeValue<string>("new_oldsn"));
                    //良品库
                    //库存占用修改
                    Command<WorkOrderCommand>().UpdateWareHouseStock(workorder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString(), partline.GetAttributeValue<EntityReference>("new_productnew_id").Id.ToString(), partline.GetAttributeValue<decimal>("new_qty").ToString(), snList, "new_srv_workorder", workOrderId, 4, true, 0);
                }
                #region 删除配件更换明细时修改工单配件费用合计或保外物料费
                //保内保外
                int warranty = 0;
                if (workorder.Contains("new_warranty"))
                {
                    warranty = workorder.GetAttributeValue<OptionSetValue>("new_warranty").Value;
                }
                //配件价格
                decimal price = partline.GetAttributeValue<decimal>("new_price");
                //工单配件费用合计
                decimal partcost = workorder.GetAttributeValue<decimal>("new_partcost");
                //保外费用合计
                decimal materialcost = workorder.GetAttributeValue<decimal>("new_materialcost");
                //应付物料费
                decimal paymaterialcost = workorder.GetAttributeValue<decimal>("new_pay_materialcost");
                //物料子类别
                string materialcategory = string.Empty;
                if (partline.Contains("new_materialcategory2_id"))
                {
                    materialcategory = partline.GetAttributeValue<EntityReference>("new_materialcategory2_id").Id.ToString();
                }
                //应付物料费折扣
                decimal payclost = 100;
                EntityCollection collection = Command<WorkOrderCommand>().JudgeIsUseSerByOrderId(workOrderId, materialcategory, true);
                if (collection != null && collection.Entities != null && collection.Entities.Count > 0)
                {
                    payclost = collection.Entities[0].GetAttributeValue<decimal>("new_discount");
                }
                Command<new_srv_partlinebll>().UpdateOrderCost(warranty, workOrderId, price, partcost, materialcost, payclost, serviceType, paymaterialcost,ref workorder);

                #region 删除配件明细时，更新工单【工单领料状态】 Add By BaronKang 2022-04-21

                Entity order = new Entity("new_srv_workorder", new Guid(workOrderId));
                QueryExpression qe_partline = new QueryExpression("new_srv_partline");
                qe_partline.ColumnSet = new ColumnSet("new_status");
                qe_partline.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, workOrderId);
                qe_partline.Criteria.AddCondition("new_srv_partlineid", ConditionOperator.NotEqual, partlineid);
                var ec_partline = OrganizationService.RetrieveMultiple(qe_partline);
                if (ec_partline != null && ec_partline.Entities != null && ec_partline.Entities.Count > 0)
                {
                    //根据剩余的更换件明细判断
                    var shortage = ec_partline.Entities.Where(i => i.GetAttributeValue<OptionSetValue>("new_status").Value == 3).ToList().Count;//缺料的更换件明细数量
                    var received = ec_partline.Entities.Where(i => i.GetAttributeValue<OptionSetValue>("new_status").Value == 4).ToList().Count;//已到料的更换件明细数量
                    if (shortage > 0)
                    {
                        //配件有[缺料]则更新工单为[缺料]
                        order["new_claim_status"] = new OptionSetValue(3);
                    }
                    else
                    {
                        if (received > 0)
                        {
                            //无[缺料]，有[已到料]则更新工单为[已到料]
                            order["new_claim_status"] = new OptionSetValue(4);
                        }
                        else
                        {
                            //无[缺料]，无[已到料]，都是[待领料]，更新工单为[待领料]
                            order["new_claim_status"] = new OptionSetValue(1);  //工单更新为未领料
                        }
                    }

                }
                else
                {
                    //服务单非保内 且 更换件明细为人损 或 为电池盖
                    if (warranty != 1 && (isdamaged || isBatteryCover))
                    {
                        //更换件明细为空且为人损，计算服务单保内外状态
                        var warranty_initi = ServiceOrderWarrantyJudgment(order, partline);
                        if (warranty_initi == 1)
                        {
                            order["new_warranty"] = new OptionSetValue(1);
                        }
                    }
                    //删除的是最后一条更换件明细
                    order["new_claim_status"] = new OptionSetValue(1);  //工单更新为未领料
                }

                #region 删除的更换件是电池盖
                if (isBatteryCover && workorder.Contains("new_whitelist_id")) 
                {
                    //变更工单上的关联白名单数据
                    RekTec.Crm.Common.XiaoMi.WhiteList.WhiteListHelper.DeletePartLineChangeWhiteList(OrganizationServiceAdmin, workorder.GetAttributeValue<EntityReference>("new_whitelist_id").Id.ToString());
                    //更新工单上的关联白名单为空
                    order["new_whitelist_id"] = null;
                }
                #endregion 删除的更换件是电池盖

                OrganizationService.Update(order);

                #endregion

                #endregion

                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workOrderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "DeletePartlineById",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = "配件更换信息自定义页面删除完成",
                    Level = LogSeverityLevel.Informational,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workOrderId,
                    LoginUserId = Context.InitiatingUserId.ToString()
                }, AppInsightsLogType.Trace);
            }
            catch (Exception ex)
            {
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workOrderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "DeletePartlineById",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = $"配件更换信息自定义页面删除异常:{ex}",
                    Level = LogSeverityLevel.Error,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workOrderId,
                    LoginUserId = Context.InitiatingUserId.ToString(),
                    Code = ex.GetType().ToString() == "Microsoft.Xrm.Sdk.InvalidPluginExecutionException" ? ExceptionCode.BusinessAlertException : ExceptionCode.SystemException
                }, AppInsightsLogType.Exception);
                Log.InfoMsg("[DeletePartlineById]" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 非小米物料服务单配件更换件明细晒出
        /// </summary>
        /// <param name="partlineid"></param>
        /// <param name="workOrderId"></param>
        public void NonXiaoMiDeletePartlineById(string partlineid, string workOrderId)
        {
            RestTelemetryClient log = new RestTelemetryClient(OrganizationServiceAdmin, Context.TracingService);
            Stopwatch sw = new Stopwatch();
            sw.Start();
            try
            {
                //处理方法、未领料
                if (string.IsNullOrEmpty(workOrderId))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.id_empty", "服务单id为空"));
                }
                //处理方法、未领料
                if (string.IsNullOrEmpty(partlineid))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.id_empty", "更换件明细id为空"));
                }
                var workorder = OrganizationService.RetrieveWithBypassPlugin("new_srv_workorder", new Guid(workOrderId), new ColumnSet("new_station_id", "new_dealstatus", "new_warranty", "new_partcost", "new_materialcost", "new_type", "new_pay_materialcost", "new_exchange_way", "new_servicestation_id"));
                if (workorder == null)
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.NotFindOrder", "未找到当前服务单"));
                }
                int[] orderStatus = new int[] { 8, 9, 10, 11, 12, 13 };//不可删料的工单状态(服务完成之后的状态)
                if (!workorder.Contains("new_dealstatus") || orderStatus.Contains(workorder.GetAttributeValue<OptionSetValue>("new_dealstatus").Value))
                {
                    throw new InvalidPluginExecutionException(GetResource("addmaterial.OrderStatusNotMatch", "当前工单状态无法更新配件更换明细"));
                }
                var partline = this.OrganizationService.RetrieveWithBypassPlugin("new_srv_partline", new Guid(partlineid), new ColumnSet("new_status", "new_product_id", "new_lackmaterialnumber", "new_qty", "new_oldsn", "new_isuser", "new_inrepairs", "new_price", "new_materialcategory2_id", "new_exchangeway", "new_iflackmaterial", "new_productnew_id", "new_iswriteno", "new_islackmaterial", "new_isdamaged", "new_originalstate"));
                if (partline.Contains("new_iswriteno") && partline.GetAttributeValue<bool>("new_iswriteno"))
                {
                    throw new InvalidPluginExecutionException(GetResource("new_srv_partline.iswriteno", "当前配件更换明细为写号单，不允许删除"));
                }
                //删除更换件明细
                OrganizationService.Delete("new_srv_partline", new Guid(partlineid));
               

                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workOrderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "DeletePartlineById",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = "非小米物料配件更换信息自定义页面删除完成",
                    Level = LogSeverityLevel.Informational,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workOrderId,
                    LoginUserId = Context.InitiatingUserId.ToString()
                }, AppInsightsLogType.Trace);
            }
            catch (Exception ex)
            {
                sw.Stop();
                log.PostTraceAsync(new AppInsightsLogModel()
                {
                    RequestId = workOrderId,
                    ModuleName = "领料",
                    ClassInfo = "ApproachAndPartlineCommand",
                    Method = "DeletePartlineById",
                    Duration = (int)sw.ElapsedMilliseconds,
                    Msg = $"非小米物料配件更换信息自定义页面删除异常:{ex}",
                    Level = LogSeverityLevel.Error,
                    Datetime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    RecordId = workOrderId,
                    LoginUserId = Context.InitiatingUserId.ToString(),
                    Code = ex.GetType().ToString() == "Microsoft.Xrm.Sdk.InvalidPluginExecutionException" ? ExceptionCode.BusinessAlertException : ExceptionCode.SystemException
                }, AppInsightsLogType.Exception);
                Log.InfoMsg("[DeletePartlineById]" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        /// <summary>
        /// 删除配件时删除对应处理方法
        /// 创建人:esterwang
        /// 创建时间:2021-11-20
        /// </summary>
        /// <param name="orderId">工单id</param>
        /// <param name="partId">配件id</param>
        public DataCollection<Entity> DeleteApproach(DataCollection<Entity> approachList, string partId)
        {
            try
            {
                if (!approachList.Any() || string.IsNullOrEmpty(partId))
                {
                    return approachList;
                }
                var deletelist= new List<Entity>();
                foreach (var item in approachList)
                {
                    //获取服务单处理方法中配件id的存储文本字段
                    string partIdListStr = item.GetAttributeValue<string>("new_partline_tex");
                    if (string.IsNullOrEmpty(partIdListStr))
                    {
                        continue;
                    }
                    partIdListStr = partIdListStr.Substring(0, partIdListStr.Length - 1);
                    string[] partIdList = partIdListStr.Split(',');
                    for (int i = 0; i < partIdList.Length; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(partIdList[i]) && partIdList[i].ToLower().Equals(partId?.ToLower()))
                        {
                            partIdList[i] = null;
                        }
                    }
                    string resultStr = string.Empty;
                    //判断是否删除完最后一个配件更换明细
                    for (int i = 0; i < partIdList.Length; i++)
                    {
                        if (!string.IsNullOrEmpty(partIdList[i]))
                        {
                            resultStr += partIdList[i] + ",";
                        }
                    }
                    if (string.IsNullOrEmpty(resultStr))
                    {
                        OrganizationService.Delete("new_srv_workorder_approach", item.Id);
                        deletelist.Add(item);
                    }
                    else
                    {
                        Entity entity = new Entity("new_srv_workorder_approach", item.Id);
                        entity["new_partline_tex"] = resultStr;
                        item["new_partline_tex"] = resultStr;
                        OrganizationService.Update(entity);

                    }
                }
                deletelist.ForEach((p) => { approachList.Remove(p); });
                return approachList;
            }
            catch (Exception ex)
            {
                Log.InfoMsg("[DeleteApproach]" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        public int ServiceOrderWarrantyJudgment(Entity order,Entity partEntity)
        {
            try
            {
                //【初始保内/保外】=保内
                var new_originalstate = partEntity.Contains("new_originalstate") ?
                    partEntity.GetAttributeValue<OptionSetValue>("new_originalstate").Value  : -1;
                if (new_originalstate ==1)
                {
                    return new_originalstate;
                }else if (GetArconfigurAtionWarranty(order.Id.ToString()))//根据服务单查询服务单活动明细
                {
                    return 1;
                }
                else
                {
                    //查询服务单是否存在三包超期类型特批单
                    QueryExpression query = new QueryExpression("new_srv_specialapply");//特批单
                    query.NoLock = true;
                    query.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, order.Id);//当前工单
                    query.Criteria.AddCondition("new_type", ConditionOperator.Equal, 1);// 类型：三包超期
                    query.Criteria.AddCondition("new_approvalstatus", ConditionOperator.Equal, 3);//审核通过
                    query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    EntityCollection ec = OrganizationServiceAdmin.RetrieveMultiple(query);
                    if (ec.Entities.Count > 0)
                    {
                        return 1;
                    }
                }
                return -1;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


        /// <summary>
        /// 确认申请缺料，发送电子邮件给总部备件
        /// 创建人：baronkang
        /// 时间：2022-02-24
        /// </summary>
        /// <param name="orderId">服务单ID</param>
        /// <param name="partLineId">配件更换明细ID</param>
        public void LackMaterialSendEmail(string orderId, string partLineId)
        {
            try
            {
                //查询工单信息
                Entity order = OrganizationService.Retrieve("new_srv_workorder", new Guid(orderId), new ColumnSet("new_name", "new_station_id", "new_srv_worker_id"));
                var orderName = order.GetAttributeValue<string>("new_name");
                var station = order.GetAttributeValue<EntityReference>("new_station_id");
                if (station == null)
                {
                    throw new InvalidPluginExecutionException("请维护处理单[ " + orderName + " ]的服务站点");
                }
                var worker = order.GetAttributeValue<EntityReference>("new_srv_worker_id");
                if (worker == null)
                {
                    throw new InvalidPluginExecutionException("请维护处理单[ " + orderName + " ]的服务人员");
                }

                //根据服务站点获取国家
                Entity etnStation = OrganizationService.Retrieve("new_srv_station", station.Id, new ColumnSet("new_country_id"));
                var country = etnStation.GetAttributeValue<EntityReference>("new_country_id");
                if (country == null)
                {
                    throw new InvalidPluginExecutionException("请维护服务站点[ " + station.Name + " ]的国家/地区");
                }

                //查询物料编码
                QueryExpression query_material = new QueryExpression("product");
                query_material.ColumnSet = new ColumnSet("productnumber");
                query_material.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                LinkEntity link = new LinkEntity("new_srv_partline", "product", "new_productnew_id", "productid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("new_srv_partlineid", ConditionOperator.Equal, new Guid(partLineId));
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query_material.LinkEntities.Add(link);
                var ec_material = OrganizationService.RetrieveMultiple(query_material);
                if (ec_material == null || ec_material.Entities == null || ec_material.Entities.Count <= 0)
                {
                    throw new InvalidPluginExecutionException("未找到对应物料");
                }
                var productnumber = ec_material.Entities.FirstOrDefault().GetAttributeValue<string>("productnumber");

                //获取邮件配置
                Mailtemp mailtemp = GetEmailtemp("workorder04_ch");
                var Emailoffice365 = CrmHelper.GetSystemParameterValue(OrganizationService, "Emailoffice365");
                string username = Emailoffice365.Split(';')[0];
                string password = Emailoffice365.Split(';')[1];
                mailtemp.emailfrom = username;
                mailtemp.username = username;
                mailtemp.password = password;

                //查询大仓发货销售渠道获取邮箱地址
                QueryExpression query_channal = new QueryExpression("new_shippingsaleschannel");
                query_channal.ColumnSet = new ColumnSet("new_reminderemail");
                query_channal.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query_channal.Criteria.AddCondition("new_country_id", ConditionOperator.Equal, country.Id);
                var ec_channal = OrganizationService.RetrieveMultiple(query_channal);
                if (ec_channal == null || ec_channal.Entities == null || ec_channal.Entities.Count <= 0)
                {
                    throw new InvalidPluginExecutionException($"请维护国家/地区[{country.Name}]对应的大仓发货销售渠道");
                }
                var mailTo = ec_channal.Entities[0].GetAttributeValue<string>("new_reminderemail");
                mailtemp.emailto = mailTo;
                //获取url
                var url = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_workorderUrl");
                url += orderId;
                string content = $"<a href='{url}'>点此</a>";

                mailtemp.Subject = mailtemp.Subject.Replace("{工单号}", orderName);
                mailtemp.Subject = mailtemp.Subject.Replace("{物料号}", productnumber);
                mailtemp.mailBody = mailtemp.mailBody.Replace("{网点名称}", station.Name);
                mailtemp.mailBody = mailtemp.mailBody.Replace("{工单号}", orderName);
                mailtemp.mailBody = mailtemp.mailBody.Replace("{物料号}", productnumber);
                mailtemp.mailBody = mailtemp.mailBody.Replace("{工程师}", worker.Name);
                mailtemp.mailBody = mailtemp.mailBody.Replace("{点此}", content);
                SendMail365(mailtemp);
            }
            catch (Exception e)
            {
                Log.InfoMsg("[LackMaterialSendEmail]方法报错" + e.Message);
                throw new InvalidPluginExecutionException(e.Message);
            }
        }

        /// <summary>
        /// 获取邮件模板
        /// </summary>
        /// <returns></returns>
        public Mailtemp GetEmailtemp(string new_code)
        {
            Mailtemp temp = new Mailtemp();
            var queryExpression = new QueryExpression("new_tmpl_mail");
            queryExpression.ColumnSet.AddColumns("new_code", "new_description", "new_type", "new_topic", "new_memo");
            queryExpression.Criteria.Conditions.Add(new ConditionExpression("new_code", ConditionOperator.Equal, new_code));
            var entityCollection = OrganizationService.RetrieveMultiple(queryExpression);
            if (entityCollection != null && entityCollection.Entities.Count > 0)
            {
                var emailtemp = entityCollection[0];
                temp.mailBody = emailtemp.GetAttributeValue<string>("new_memo");
                temp.Subject = emailtemp.GetAttributeValue<string>("new_topic");
                temp.AttachmentName = emailtemp.GetAttributeValue<string>("new_description") + ".pdf";
            }
            return temp;
            //template
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="Mailtemp"></param>
        public void SendMail365(Mailtemp Mailtemp)
        {
            SmtpClient smtpClient = new SmtpClient();
            smtpClient.UseDefaultCredentials = false;
            smtpClient.UseDefaultCredentials = false;
            smtpClient.Credentials = new System.Net.NetworkCredential(Mailtemp.username, Mailtemp.password);
            smtpClient.Port = 587; // You can use Port 25 if 587 is blocked (mine is!)
            smtpClient.Host = "smtp.office365.com";
            smtpClient.TargetName = "STARTTLS/smtp.office365.com"; // same behaviour if this lien is removed
            smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
            smtpClient.EnableSsl = true;
            var mailMessage = new MailMessage();
            mailMessage.From = new MailAddress(Mailtemp.emailfrom, "小米服务团队");
            string[] emailToArray = Mailtemp.emailto.Split(';');
            for (int i = 0; i < emailToArray.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(emailToArray[i]))
                {
                    continue;
                }
                mailMessage.To.Add(emailToArray[i]);
            }
            mailMessage.Subject = Mailtemp.Subject;
            mailMessage.Body = Mailtemp.mailBody;
            mailMessage.IsBodyHtml = true;
            smtpClient.Send(mailMessage);
        }

        /// <summary>
        /// 使用保险校验整机价格
        /// </summary>
        /// <param name="order">工单</param>
        /// <param name="partLineIds">更换件id</param>
        public void UsingInsuranceCheckPrice(Entity order, List<string> partLineIds)
        {
            try
            {
                if (order.Contains("new_insurancetype") && order.GetAttributeValue<OptionSetValue>("new_insurancetype").Value == 1)
                {
                    #region 存在套机或物料是商品：是 则不校验
                    // 编码取系统参数
                    var param = CrmHelper.GetSystemParameterValue(OrganizationService, "new_materialcategory2_code", true);
                    // 物料子类别：套机 
                    var categoryId = GetCategory2Id(param);
                    QueryExpression query = new QueryExpression("new_srv_partline");
                    query.Criteria.AddCondition("new_srv_partlineid", ConditionOperator.In, partLineIds.ToArray());
                    query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    query.ColumnSet.AddColumns("new_srv_partlineid");

                    LinkEntity link = new LinkEntity("new_srv_partline", "product", "new_productnew_id", "productid", JoinOperator.Inner);
                    link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    // Modified by Hyacinthhuang 2021-12-7 查询物料子类别：套机 || 物料是商品
                    FilterExpression filter = new FilterExpression(LogicalOperator.Or);
                    filter.AddCondition("new_isgoods", ConditionOperator.Equal, true);
                    if (!string.IsNullOrWhiteSpace(categoryId))
                    {
                        filter.AddCondition("new_materialcategory2_id", ConditionOperator.Equal, categoryId);
                    }
                    link.LinkCriteria.AddFilter(filter);
                    query.LinkEntities.Add(link);
                    EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                    if (ec == null || ec.Entities.Count <= 0)
                    {
                        #region 查询服务单总费用
                        decimal order_total = 0;
                        // 计算保险手工费
                        if (order.Contains("new_isuseinsurancecost"))
                            order_total += order.GetAttributeValue<decimal>("new_isuseinsurancecost");
                        if (order.Contains("new_materialcost"))
                            order_total += order.GetAttributeValue<decimal>("new_materialcost");
                        if (order.Contains("new_partsoperationfee"))
                            order_total += order.GetAttributeValue<decimal>("new_partsoperationfee");
                        #endregion

                        #region 查询商品价格
                        decimal product_total = 0;
                        QueryExpression expression = new QueryExpression("new_srv_productline");
                        expression.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, order.Id);
                        expression.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        expression.ColumnSet.AddColumns("new_product_price");
                        EntityCollection collection = OrganizationService.RetrieveMultiple(expression);
                        if (collection != null && collection.Entities.Count > 0)
                        {
                            product_total = collection.Entities.Where(x => x.Contains("new_product_price")).Sum(p => p.GetAttributeValue<decimal>("new_product_price"));
                        }
                        #endregion

                        Log.InfoMsg($"服务单总费用：{order_total}__商品价格：{product_total}");

                        // 判断服务总费用是否大于产品费用*0.9
                        if (order_total * 10 > product_total * 9)
                            throw new InvalidPluginExecutionException(GetResource("new_srv_workorder.PartsLineApplyFiled", "【申请失败，当前维修整体费用大于商品价值，建议以换代修处理】"));
                    }

                    #endregion
                }
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion


        #region 校验处理方法保存 add by Hyacinth 2021-10-30
        /// <summary>
        /// 校验是否能够保存 
        /// </summary>
        /// <param name="approchList"></param>
        public void CheckCanSaveApproch(List<ServiceOneLookupModel> approchList)
        {

            if (approchList == null || approchList.All(x => string.IsNullOrWhiteSpace(x.id)))
                return;

            try
            {
                QueryExpression query = new QueryExpression("new_approach");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_approachid", ConditionOperator.In, approchList.Select(x => x.id).ToList().ToArray());
                query.ColumnSet.AddColumns("new_isapproachcoexist", "new_name");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count < 0)
                    return;

                if (ec.Entities.Count == 1)
                    return;

                var list = ec.Entities.Where(x => x.Contains("new_isapproachcoexist") && !x.GetAttributeValue<bool>("new_isapproachcoexist")).Select(p => p.GetAttributeValue<string>("new_name")).ToList();
                if (list != null && list.Count > 0)
                {
                    string new_name = string.Empty;
                    foreach (var item in list)
                    {
                        new_name += item + ",";
                    }

                    throw new InvalidPluginExecutionException(GetResource("new_approach.CanntCoexistence", $"保存失败，【{new_name}】该处理方法不支持与其他处理方法并存"));
                }

            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("【CheckCanSaveApproch】错误：" + ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }
        #endregion

        /// <summary>
        /// 查询对应的物料子类别id add by Hyacinthhuang 2021-12-7
        /// </summary>
        /// <param name="code">物料子类别编码</param>
        /// <returns></returns>
        public string GetCategory2Id(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;
            try
            {
                QueryExpression query = new QueryExpression("new_materialcategory2");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.Criteria.AddCondition("new_code", ConditionOperator.Equal, code);
                query.ColumnSet.AddColumns("new_materialcategory2id");
                EntityCollection ec = OrganizationService.RetrieveMultiple(query);
                if (ec == null || ec.Entities.Count <= 0)
                    return null;

                return ec.Entities[0].Id.ToString();
            }
            catch (Exception ex)
            {
                Log.InfoMsg("查询子类别错误：" + ex.Message);
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 确认申请校验新商品
        /// add by Hyacinthhuang 2022-3-22
        /// </summary>
        /// <param name="orderId">服务单id</param>
        public void CheckPartLine(string orderId)
        {
            if (string.IsNullOrWhiteSpace(orderId))
                return;

            try
            {
                //新增校验 换货或安装且受理物品数量大于有2条且关联商品三级品类都是空调时数量不允许超过2
               
                QueryExpression qe = new QueryExpression("new_srv_partline");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_srv_workorder_id", ConditionOperator.Equal, orderId);
                qe.Criteria.AddCondition("new_goodsfiles_id", ConditionOperator.NotNull);
                qe.ColumnSet.AddColumns("new_srv_partlineid");
                EntityCollection ec = OrganizationService.RetrieveMultipleWithBypassPlugin(qe);
                if (ec != null && ec.Entities.Count > 1)
                {
                    //判断是否全是空调，如果空调的话允许2个
                    bool allIsAC = CheckAllAC(orderId, OrganizationService);// 换货/安装 受理物品是否都是空调
                    if (allIsAC)
                    { if (ec.Entities.Count > 2)
                        { throw new InvalidPluginExecutionException(GetResource("new_srv_partline.AC_Check_ConfirmFailed", "确认申请失败！空调品类同一工单下更换件明细存在两个以上商品物料")); }      
                    }
                    else
                    {
                        throw new InvalidPluginExecutionException(GetResource("new_srv_partline.Check_ConfirmFailed", "确认申请失败！同一工单下更换件明细存在两个商品物料"));
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        /// <summary>
        /// 发送缺料短信 
        /// add by Hyacinthhuang 2022-6-8
        /// </summary>
        /// <param name="order">服务单信息</param>
        public void SendLackMaterialMsg(Entity order)
        {
            try
            {
                if (!order.Contains("new_type"))
                    return;

                if (!order.Contains("new_station_id"))
                    return;
                // 工单类型
                int type = order.GetAttributeValue<OptionSetValue>("new_type").Value;
                // 工单网点
                string stationId = order.GetAttributeValue<EntityReference>("new_station_id").Id.ToString();
                // 服务方式
                int serviceMode = order.Contains("new_servicemode") ? order.GetAttributeValue<OptionSetValue>("new_servicemode").Value : 0;

                // 网点国家是否是香港
                bool isHK = CheckStationCountry(stationId, "3385");
                // 网点国家是否是台湾
                bool isTW = CheckStationCountry(stationId, "3386");

                if (!isHK && !isTW)
                {
                    return;
                }

                if (isHK && type != 1)
                {
                    Log.InfoMsg("香港非维修工单不发送短信");
                    return;
                }

                if (isTW && !new int[] { 1, 2 }.Contains(type) && !new int[] { 1, 3 }.Contains(serviceMode))
                {
                    Log.InfoMsg("台湾非到店,寄修,维修,换货 不发送短信");
                    return;
                }

                this.Command<SMSApiCommand>().SendMessage(order.Id.ToString(), 2);

            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg("创建短信记录错误：" + ex.Message);
            }
        }


        /// <summary>
        /// 校验网点地区是否是香港
        /// </summary>
        /// <param name="stationId">网点id</param>
        /// <returns></returns>
        public bool CheckStationCountry(string stationId, string countryCode)
        {
            if (string.IsNullOrWhiteSpace(stationId) || string.IsNullOrWhiteSpace(countryCode))
                return false;
            try
            {
                QueryExpression query = new QueryExpression("new_srv_station");
                query.Criteria.AddCondition("new_srv_stationid", ConditionOperator.Equal, stationId);
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                query.ColumnSet.AddColumns("new_srv_stationid");
                LinkEntity link = new LinkEntity("new_srv_station", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("new_id", ConditionOperator.Equal, countryCode);
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                query.LinkEntities.Add(link);
                EntityCollection ec = OrganizationService.RetrieveMultipleWithBypassPlugin(query);
                if (ec == null || ec.Entities.Count == 0)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                Log.InfoMsg(ex.Message);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

 
        /// <summary>
        /// 校验 换货/安装 受理物品是否都为两个且都是空调
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public static bool CheckAllAC(string orderId,IOrganizationService OrganizationService)
        {
            try
            {
                bool allIsAC = false;//多受理物品是否都是空调
                var workOrderTypes = new object[] { (int)Model.WorkOrderType.Wwap, (int)Model.WorkOrderType.Installation };

                #region 服务单工单类型需要为 换货或安装
                QueryExpression workOrderQuery = new QueryExpression("new_srv_workorder");
                workOrderQuery.ColumnSet = new ColumnSet("new_type", "statecode");
                workOrderQuery.Criteria.AddCondition("new_type", ConditionOperator.In, workOrderTypes);// 换货或安装类型
                workOrderQuery.Criteria.AddCondition("new_srv_workorderid",ConditionOperator.Equal,orderId);
                workOrderQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                EntityCollection resultsWorkOrder = OrganizationService.RetrieveMultipleWithBypassPlugin(workOrderQuery);
                if(resultsWorkOrder==null||resultsWorkOrder.Entities.Count<=0)
                {
                    return allIsAC;
                }
                #endregion

                #region  受理物品 两个且都为空调
                QueryExpression queryAcceptProduct = new QueryExpression("new_srv_productline");
                queryAcceptProduct.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryAcceptProduct.Criteria.AddCondition("new_workorder_id", ConditionOperator.Equal, orderId);
                // 关联商品档案 
                LinkEntity linkGoodsFiles = new LinkEntity
                {
                    LinkFromEntityName = "new_srv_productline",
                    LinkFromAttributeName = "new_goodsfiles_id",
                    LinkToEntityName = "new_goodsfiles",
                    LinkToAttributeName = "new_goodsfilesid",
                    JoinOperator = JoinOperator.Inner,
                    Columns = new ColumnSet("new_airconditioningtype", "statecode"),
                    EntityAlias = "goodsfiles"
                };
                linkGoodsFiles.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                linkGoodsFiles.LinkCriteria.AddCondition("new_airconditioningtype", ConditionOperator.NotNull);
                queryAcceptProduct.LinkEntities.Add(linkGoodsFiles);
                EntityCollection resultsAcceptProduct = OrganizationService.RetrieveMultipleWithBypassPlugin(queryAcceptProduct);
                if (resultsAcceptProduct != null && resultsAcceptProduct.Entities.Count == 2)
                {
                    allIsAC = true;
                }
                #endregion
                return allIsAC;
            }
            catch(Exception ex)
            {
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }


        /// <summary>
        /// 迈创Consign 取消占料 add by Hyacinthhuang 2022-5-10
        /// </summary>
        /// <param name="workorder"></param>
        /// <param name="partlineId"></param>
        public void DeletePartLineToMC(Entity workorder, string partlineId)
        {
            try
            {
                string stationId = workorder.Contains("new_station_id") ? workorder.GetAttributeValue<EntityReference>("new_station_id").Id.ToString() : null;
                // 备件服务商类型
                int consignType = this.Command<WorkorderToMCCommand>().getConsignType(stationId, OrganizationServiceDisableMultLang);

                if (consignType == 1)
                {
                    this.Command<WorkorderToMCCommand>().ThrowOrderToMC(1, workorder.Id.ToString(), 4, partlineId);
                }
            }
            catch (Exception ex)
            {
                Log.LogException(ex);
                throw new InvalidPluginExecutionException(ex.Message);
            }
        }

        public class Mailtemp
        {
            /// <summary>
            /// 
            /// </summary>
            public Guid workorderid { get; set; }
            /// <summary>
            /// 邮件附件
            /// </summary>
            public string AttachmentBase64 { get; set; }
            /// <summary>
            /// 邮件主题
            /// </summary>
            public string Subject { get; set; }
            /// <summary>
            /// 邮件内容
            /// </summary>
            public string mailBody { get; set; }
            /// <summary>
            /// 邮件发件人
            /// </summary>
            public string emailfrom { get; set; }
            /// <summary>
            /// 邮件收件人
            /// </summary>
            public string emailto { get; set; }
            /// <summary>
            /// 附件名称
            /// </summary>
            public string AttachmentName { get; set; }
            /// <summary>
            /// 邮件用户名称
            /// </summary>
            public string username { get; set; }
            /// <summary>
            /// 邮件用户密码
            /// </summary>
            public string password { get; set; }
            /// <summary>
            /// 邮件抄送者
            /// </summary>
            public string emailCC { get; set; }
        }

    }
}

