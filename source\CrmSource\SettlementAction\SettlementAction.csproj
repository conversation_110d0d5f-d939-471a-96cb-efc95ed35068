﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F1B32E98-38DA-4346-BB2D-2CEC019277A4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SettlementAction</RootNamespace>
    <AssemblyName>SettlementAction</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>XM.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Crm.Sdk.Proxy">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.9.0.2.52\lib\net462\Microsoft.Crm.Sdk.Proxy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xrm.Sdk">
      <HintPath>..\bin\Microsoft.Xrm.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RekTec.Api.Biz">
      <HintPath>..\bin\RekTec.Api.Biz.dll</HintPath>
    </Reference>
    <Reference Include="RekTec.Api.Common">
      <HintPath>..\bin\RekTec.Api.Common.dll</HintPath>
    </Reference>
    <Reference Include="RekTec.Api.Interface">
      <HintPath>..\bin\RekTec.Api.Interface.dll</HintPath>
    </Reference>
    <Reference Include="RekTec.Api.Model">
      <HintPath>..\bin\RekTec.Api.Model.dll</HintPath>
    </Reference>
    <Reference Include="RekTec.Crm.Common">
      <HintPath>..\bin\RekTec.Crm.Common.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.12.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.12.0\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Command\CriticalPushBpmCommand.cs" />
    <Compile Include="Command\SubmitBpmSettlementCommand.cs" />
    <Compile Include="Command\ImportdataCommand.cs" />
    <Compile Include="Common\CommonHelper.cs" />
    <Compile Include="Common\ServriceBusCommon.cs" />
    <Compile Include="GetOCOrderItem.cs" />
    <Compile Include="Helper\AppHelper.cs" />
    <Compile Include="ImportdataAction_Settlement.cs" />
    <Compile Include="Model\CriticalExpenseModel.cs" />
    <Compile Include="Model\FieldList.cs" />
    <Compile Include="Model\SpecialrefundapplyModel.cs" />
    <Compile Include="Model\ImportdataModel.cs" />
    <Compile Include="new_criticalPushBpmAction.cs" />
    <Compile Include="new_SubmitBpmAction_Settlement.cs" />
    <Compile Include="TrimorderdetailRematch.cs" />
    <Compile Include="WarehousingWorkorderdetailExport.cs" />
    <Compile Include="WorkorderdetailCustomizeViewAction.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="key.pfx" />
    <None Include="packages.config" />
    <None Include="XM.snk" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>