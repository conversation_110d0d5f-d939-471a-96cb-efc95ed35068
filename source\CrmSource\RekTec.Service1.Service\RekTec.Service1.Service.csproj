﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{435EAA44-1413-4A6B-8340-9F239C5F8B79}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RekTec.Service1.Service</RootNamespace>
    <AssemblyName>RekTec.Service1.Service</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>C:\ServiceOneSharedLib\RekTec.Service1.Service.xml</DocumentationFile>
    <NoWarn>1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>RekTec.Service1.Service.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Azure.Core, Version=1.34.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.34.0\lib\net461\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Maps.Common, Version=*******, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Maps.Common.1.0.0-beta.4\lib\netstandard2.0\Azure.Maps.Common.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Maps.Search, Version=*******, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Maps.Search.1.0.0-beta.4\lib\netstandard2.0\Azure.Maps.Search.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.1\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.1\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Crm.Sdk.Proxy, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.********\lib\net462\Microsoft.Crm.Sdk.Proxy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=9.3.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xrm.Sdk, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CrmSdk.CoreAssemblies.********\lib\net462\Microsoft.Xrm.Sdk.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.3.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.1\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.3.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.1\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.3.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.1\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.3.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.1\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="RekTec.Crm.Cache.Redis">
      <HintPath>..\..\..\DLL\RekTec.Crm.Cache.Redis.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.12.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.12.0\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.1\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Text.Encodings.Web, Version=4.0.5.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.7.2\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.4.7.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Workflow.Activities" />
    <Reference Include="System.Workflow.ComponentModel" />
    <Reference Include="System.Workflow.Runtime" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationInsights\AppInsightsLogEnvelope.cs" />
    <Compile Include="ApplicationInsights\AppInsightsLogModel.cs" />
    <Compile Include="ApplicationInsights\GlobalConfigurationHelper.cs" />
    <Compile Include="ApplicationInsights\RestTelemetryClient.cs" />
    <Compile Include="Bll\AppointmentCommand.cs" />
    <Compile Include="Bll\BaseBpmInterface.cs" />
    <Compile Include="Bll\COTAApiCommand.cs" />
    <Compile Include="Bll\IMEIInterfacebll.cs" />
    <Compile Include="Bll\IMEIInterfaceCommand.cs" />
    <Compile Include="Bll\new_accountbll.cs" />
    <Compile Include="Bll\new_b2x_goodsconfigCommand.cs" />
    <Compile Include="Bll\new_srv_partlinebll.cs" />
    <Compile Include="Bll\new_srv_productlinebll.cs" />
    <Compile Include="Bll\new_srv_stationbll.cs" />
    <Compile Include="Bll\new_srv_troublebll.cs" />
    <Compile Include="Bll\new_srv_workorder_approachbll.cs" />
    <Compile Include="Bll\OcApiCommand.cs" />
    <Compile Include="Bll\SAPCommom.cs" />
    <Compile Include="Bll\SearchRepairrightsrule.cs" />
    <Compile Include="Bll\SMSApiCommand.cs" />
    <Compile Include="Bll\WorkOrderCommon.cs" />
    <Compile Include="Bll\TrainingCommon.cs" />
    <Compile Include="Command\AcceptancePrintCommand.cs" />
    <Compile Include="Command\ApproachAndPartlineCommand.cs" />
    <Compile Include="Command\CancelWorkOrderCommand.cs" />
    <Compile Include="Command\ComplainCommand.cs" />
    <Compile Include="Command\ConfirmPayCommand.cs" />
    <Compile Include="Command\CrossServiceProvideQueryCommand.cs" />
    <Compile Include="Command\DigitalStoreCommand.cs" />
    <Compile Include="Command\DispatchToStationOrUserCommand.cs" />
    <Compile Include="Command\EmailCommmond.cs" />
    <Compile Include="Command\ExpenseClaimCommand.cs" />
    <Compile Include="Command\InstallRightsCommand.cs" />
    <Compile Include="Command\InsuranceConfirmCommand.cs" />
    <Compile Include="Command\Msdyn_workorderCommand.cs" />
    <Compile Include="Command\NoSerialNumberCommand.cs" />
    <Compile Include="Command\NotifyCommand.cs" />
    <Compile Include="Command\OMSCommand.cs" />
    <Compile Include="Command\PartLineWarehouseStockCommand.cs" />
    <Compile Include="Command\ProjectCommand.cs" />
    <Compile Include="Command\QueryDataCommand.cs" />
    <Compile Include="Command\ReassignmentCommand.cs" />
    <Compile Include="Command\RecoilApproveHelp.cs" />
    <Compile Include="Command\SearchInsuranceCommand.cs" />
    <Compile Include="Command\SearchStationListCommand.cs" />
    <Compile Include="Command\SearchUserprofileList.cs" />
    <Compile Include="Command\SendSignCommand.cs" />
    <Compile Include="Command\ServiceBomCommand.cs" />
    <Compile Include="Command\ServiceIsCompleteCommand.cs" />
    <Compile Include="Command\ServiceMappingCommand.cs" />
    <Compile Include="Command\ServiceSettlementCommand.cs" />
    <Compile Include="Command\SpecialApplyCommand.cs" />
    <Compile Include="Command\StationCommand.cs" />
    <Compile Include="Command\TaxCalculationCommand.cs" />
    <Compile Include="Command\TranTypeCommand.cs" />
    <Compile Include="Command\WarrantyQueryCommand.cs" />
    <Compile Include="Command\WorkOrderCommand.cs" />
    <Compile Include="Command\WorkorderCommand_close.cs" />
    <Compile Include="Command\WorkorderCommand_create.cs" />
    <Compile Include="Command\WorkorderCommand_search.cs" />
    <Compile Include="Command\WorkorderToMCCommand.cs" />
    <Compile Include="Command\WorkorderUpload\UploadCreateOrderCommand.cs" />
    <Compile Include="Command\WorkorderUpload\UploadCloseOrderCommand.cs" />
    <Compile Include="Command\WorkorderUpload\UploadGoodsidWorkOrderCommand.cs" />
    <Compile Include="Command\WorkorderUpload\UploadProcessingOrderCommand.cs" />
    <Compile Include="Command\WorkorderUpload\UploadSearchOrderCommand.cs" />
    <Compile Include="Command\WriteNoCommand.cs" />
    <Compile Include="Constants\WorkOrderConstants.cs" />
    <Compile Include="Controller\AcceptancePrintController.cs" />
    <Compile Include="Controller\AppointmentController.cs" />
    <Compile Include="Controller\ComplainController.cs" />
    <Compile Include="Controller\ConfirmPayController.cs" />
    <Compile Include="Controller\EmailController.cs" />
    <Compile Include="Controller\ExpenseClaimController.cs" />
    <Compile Include="Controller\IMEIInterfaceController.cs" />
    <Compile Include="Controller\InstallRightsController.cs" />
    <Compile Include="Controller\InsuranceConfirmController.cs" />
    <Compile Include="Controller\Msdyn_workorderController.cs" />
    <Compile Include="Controller\NoSerialNumberController.cs" />
    <Compile Include="Controller\OMSController.cs" />
    <Compile Include="Controller\ProjectController.cs" />
    <Compile Include="Controller\ReassignmentController.cs" />
    <Compile Include="Controller\SearchStationListController.cs" />
    <Compile Include="Controller\SendSignController.cs" />
    <Compile Include="Controller\ServiceBomController.cs" />
    <Compile Include="Controller\ServiceIsCompleteController.cs" />
    <Compile Include="Controller\ServiceMappingController.cs" />
    <Compile Include="Controller\ServiceSettlementController.cs" />
    <Compile Include="Controller\SMSController.cs" />
    <Compile Include="Controller\StationController.cs" />
    <Compile Include="Controller\TranTypeController.cs" />
    <Compile Include="Controller\WarrantyQueryController.cs" />
    <Compile Include="Controller\WorkorderUploadController.cs" />
    <Compile Include="DataImport\DataImportCommand.cs" />
    <Compile Include="DataImport\DataImportController.cs" />
    <Compile Include="Helper\AccessHelper.cs" />
    <Compile Include="Helper\AppHelper.cs" />
    <Compile Include="Helper\AssertUtils.cs" />
    <Compile Include="Helper\DateTimeHelper.cs" />
    <Compile Include="Helper\HttpUtils.cs" />
    <Compile Include="Helper\ObjectExtensions.cs" />
    <Compile Include="Helper\Parttransaction.cs" />
    <Compile Include="Helper\PCFHelper.cs" />
    <Compile Include="Helper\ServriceBusCommon.cs" />
    <Compile Include="Model\AcceptancePrintModel.cs" />
    <Compile Include="Model\AddressModel.cs" />
    <Compile Include="Model\ApproachAndPartlineModel.cs" />
    <Compile Include="Command\BpmCriticalPayCommand.cs" />
    <Compile Include="Command\BpmExpenseCommand.cs" />
    <Compile Include="Command\BpmCriticalCommand.cs" />
    <Compile Include="Command\BpmSpecialCommand.cs" />
    <Compile Include="Command\CreateWorkorderCommand.cs" />
    <Compile Include="Command\CrisisEventCommand.cs" />
    <Compile Include="Command\DispatchCommand.cs" />
    <Compile Include="Command\ExpenseToSAPCommand.cs" />
    <Compile Include="Command\LogisticsCommon.cs" />
    <Compile Include="Command\MapDispatchCommand.cs" />
    <Compile Include="Command\MiPublicMethodCommand.cs" />
    <Compile Include="Command\PartLineCommand.cs" />
    <Compile Include="Command\PCFCommand.cs" />
    <Compile Include="Command\RecoilOrderCommand.cs" />
    <Compile Include="Command\ServiceHandingCommand.cs" />
    <Compile Include="Command\SpareSalesCommand.cs" />
    <Compile Include="Command\WorkerleaveapplyCommand.cs" />
    <Compile Include="Command\WorkFlowCommand.cs" />
    <Compile Include="Command\DateTimeExtensions.cs" />
    <Compile Include="Command\ContactCommand.cs" />
    <Compile Include="Command\PhotoLabelCommand.cs" />
    <Compile Include="Command\PickListUtilsCommand.cs" />
    <Compile Include="Command\ToolsCommand.cs" />
    <Compile Include="Command\DeliveyRepairCommand.cs" />
    <Compile Include="Controller\BpmInterfaceController.cs" />
    <Compile Include="Controller\CreateWorkorderController.cs" />
    <Compile Include="Controller\CrisisEventController.cs" />
    <Compile Include="Controller\DispatchController.cs" />
    <Compile Include="Controller\ExpenseToSAPController.cs" />
    <Compile Include="Controller\LogisticsController.cs" />
    <Compile Include="Controller\MapDispatchController.cs" />
    <Compile Include="Controller\PartLineController.cs" />
    <Compile Include="Controller\PCFController.cs" />
    <Compile Include="Controller\ProductModuleController.cs" />
    <Compile Include="Controller\RecoilOrderController.cs" />
    <Compile Include="Controller\ServiceHandingController.cs" />
    <Compile Include="Controller\SpareSalesController.cs" />
    <Compile Include="Controller\WorkController.cs" />
    <Compile Include="Controller\WorkerleaveapplyController.cs" />
    <Compile Include="Helper\BaiduHelper.cs" />
    <Compile Include="Helper\EnumHelper.cs" />
    <Compile Include="Helper\PickListUtils.cs" />
    <Compile Include="Controller\ContactController.cs" />
    <Compile Include="Controller\MaintenancePlanController.cs" />
    <Compile Include="Model\AuditModel.cs" />
    <Compile Include="Model\BasicModel.cs" />
    <Compile Include="Model\BpmExpenseModel.cs" />
    <Compile Include="Model\CommonImportModel.cs" />
    <Compile Include="Model\ConvertStationAndLogisticsModel.cs" />
    <Compile Include="Model\ConverstationModel.cs" />
    <Compile Include="Model\CreateWorkorderModel.cs" />
    <Compile Include="Model\CriticalModel.cs" />
    <Compile Include="Model\CrossProviderSearchModel.cs" />
    <Compile Include="Model\DeliveryInfoModel.cs" />
    <Compile Include="Model\DeliveyRepair.cs" />
    <Compile Include="Model\devicemaintenanceplan.cs" />
    <Compile Include="Model\dispatchOrderMessage.cs" />
    <Compile Include="Model\DispatchStationModel.cs" />
    <Compile Include="Model\Enum.cs" />
    <Compile Include="Model\ErrorModel.cs" />
    <Compile Include="Model\IMEIBatchQueryModel.cs" />
    <Compile Include="Model\HomeModel.cs" />
    <Compile Include="Model\InstallRightsModel.cs" />
    <Compile Include="Model\MachineInfoByMachineCode.cs" />
    <Compile Include="Model\MailCompanyEnum.cs" />
    <Compile Include="Model\MailModel.cs" />
    <Compile Include="Model\MaintenancePlanModel.cs" />
    <Compile Include="Model\MapModel.cs" />
    <Compile Include="Model\OrderToCOTAModel.cs" />
    <Compile Include="Model\OrderToSpmModel.cs" />
    <Compile Include="Model\PageBaseData.cs" />
    <Compile Include="Model\PartLine.cs" />
    <Compile Include="Model\PartLineWarehouseStockModel.cs" />
    <Compile Include="Model\PCFModel.cs" />
    <Compile Include="Model\PickListModel.cs" />
    <Compile Include="Model\ProductBomModel.cs" />
    <Compile Include="Model\ProductByMachineCodeModel.cs" />
    <Compile Include="Model\ProductByMachineCodeModel1.cs" />
    <Compile Include="Model\ProductLineModel.cs" />
    <Compile Include="Model\ProductModuleModel.cs" />
    <Compile Include="Model\ProductReturnModel.cs" />
    <Compile Include="Model\ProjectModel.cs" />
    <Compile Include="Model\QueryBaseModel.cs" />
    <Compile Include="Model\QuoteModel.cs" />
    <Compile Include="Model\SaleChannelEnum.cs" />
    <Compile Include="Model\SendRevisedModel.cs" />
    <Compile Include="Model\SendSignModel.cs" />
    <Compile Include="Model\ServiceActivitiesModel.cs" />
    <Compile Include="Model\ServiceBomModel.cs" />
    <Compile Include="Model\ServiceMappingModel.cs" />
    <Compile Include="Model\ServiceTypeSecondMaterialCategoryModel.cs" />
    <Compile Include="Model\SMSModel.cs" />
    <Compile Include="Model\StateModel.cs" />
    <Compile Include="Model\SupportModel.cs" />
    <Compile Include="Model\TaxCalculationLine.cs" />
    <Compile Include="Model\TaxCalculationServiceModel.cs" />
    <Compile Include="Model\TranTypeModel.cs" />
    <Compile Include="Model\UploadCreateOrderModel.cs" />
    <Compile Include="Model\UploadProcessingModel.cs" />
    <Compile Include="Model\UserProfileModel.cs" />
    <Compile Include="Model\WarrantyQueryModel.cs" />
    <Compile Include="Model\WorkHoursModel.cs" />
    <Compile Include="Model\WorkOrderModel.cs" />
    <Compile Include="Model\WorkorderWithCustomer.cs" />
    <Compile Include="Model\IncidentWithCustomer.cs" />
    <Compile Include="Model\NewSrvWorkerModel.cs" />
    <Compile Include="Controller\PartsPriceController.cs" />
    <Compile Include="Controller\PickListUtilsController.cs" />
    <Compile Include="Controller\ProductBomController.cs" />
    <Compile Include="Controller\ProductLineController.cs" />
    <Compile Include="Controller\RepairFeeController.cs" />
    <Compile Include="Controller\IncidentController.cs" />
    <Compile Include="Model\IncidenttoworkorderModel.cs" />
    <Compile Include="Model\Models.cs" />
    <Compile Include="Controller\DeliveyRepairController.cs" />
    <Compile Include="Controller\WorkOrderController.cs" />
    <Compile Include="Model\WX_MP_Templete.cs" />
    <Compile Include="Model\WX_MP_TextMessage.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServiceHostPlugin.cs" />
    <Compile Include="Command\WarrantyQueryListCommand.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include=".editorconfig" />
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="RekTec.Service1.Service.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.Common\RekTec.Crm.Common.csproj">
      <Project>{f7c53d80-ce20-475c-abce-213386d66446}</Project>
      <Name>RekTec.Crm.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.FileStorage\RekTec.Crm.FileStorage.csproj">
      <Project>{fc84129e-7cab-4015-be57-1154494c8795}</Project>
      <Name>RekTec.Crm.FileStorage</Name>
    </ProjectReference>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.HiddenApi\RekTec.Crm.HiddenApi.csproj">
      <Project>{7692ff4a-4a0d-4b37-bd1e-96dcfb5c3c0b}</Project>
      <Name>RekTec.Crm.HiddenApi</Name>
    </ProjectReference>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.Language.Common\RekTec.Crm.Language.Common.csproj">
      <Project>{a9673262-f43a-4cd0-8a17-156697475a1d}</Project>
      <Name>RekTec.Crm.Language.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\1_RekTec.Framework\RekTec.Crm.WeixinMP\RekTec.Crm.WeixinMP.csproj">
      <Project>{8db6e173-401f-4303-88f1-886e882348cc}</Project>
      <Name>RekTec.Crm.WeixinMP</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Biz\RekTec.Api.Biz.csproj">
      <Project>{f6923484-6c78-404c-9466-f952ba5385c2}</Project>
      <Name>RekTec.Api.Biz</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Common\RekTec.Api.Common.csproj">
      <Project>{0F7870C3-987E-4A29-A7F9-B854F0E56F55}</Project>
      <Name>RekTec.Api.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Interface\RekTec.Api.Interface.csproj">
      <Project>{C18B6A42-E0D3-44A0-8B0F-D656E37E322D}</Project>
      <Name>RekTec.Api.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Api.Model\RekTec.Api.Model.csproj">
      <Project>{949872a4-fe32-4a72-a3d5-0146889e7097}</Project>
      <Name>RekTec.Api.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.BizCommon\RekTec.Crm.BizCommon.csproj">
      <Project>{0a47c8fc-6c51-4cf7-b78c-f3a46dfdc9d1}</Project>
      <Name>RekTec.Crm.BizCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.Common.XiaoMi\RekTec.Crm.Common.XiaoMi.csproj">
      <Project>{369fd852-80bb-4ca6-bd3d-19463029405d}</Project>
      <Name>RekTec.Crm.Common.XiaoMi</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.Model\RekTec.Crm.Model.csproj">
      <Project>{8999e175-3398-4a5b-80b2-180d22b4cb03}</Project>
      <Name>RekTec.Crm.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.OrganizationService.Common\RekTec.Crm.OrganizationService.Common.csproj">
      <Project>{2c567bb0-2d83-40e2-906e-7330f0afe82a}</Project>
      <Name>RekTec.Crm.OrganizationService.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\RekTec.Crm.Plugin.Common\RekTec.Crm.Plugin.Common.csproj">
      <Project>{7a420002-57c5-435a-aca8-3fa10ec477e3}</Project>
      <Name>RekTec.Crm.Plugin.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\XiaoMi.Crm.CS.ApiCommon\XiaoMi.Crm.CS.ApiCommon.csproj">
      <Project>{63D41B26-05C4-4C7D-873E-A593B5F04AD2}</Project>
      <Name>XiaoMi.Crm.CS.ApiCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\XiaoMi.Crm.Common.SendFeishuMsg\XiaoMi.Crm.Common.SendFeishuMsg.csproj">
      <Project>{d03a7a39-24bb-4b8e-bdd0-1845ecfa2805}</Project>
      <Name>XiaoMi.Crm.Common.SendFeishuMsg</Name>
    </ProjectReference>
    <ProjectReference Include="..\XiaoMi.Crm.WarrantyQueryCommon\XiaoMi.Crm.WarrantyQueryCommon.csproj">
      <Project>{A4EBB101-6F2B-4B3D-B865-D5E36F02DAAB}</Project>
      <Name>XiaoMi.Crm.WarrantyQueryCommon</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>