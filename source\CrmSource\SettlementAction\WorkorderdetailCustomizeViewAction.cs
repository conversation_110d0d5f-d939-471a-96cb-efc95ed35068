﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SettlementAction.Common;
using SettlementAction.Model;

namespace SettlementAction
{
    public class WorkorderdetailCustomizeViewAction : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(context.UserId);
            IOrganizationService adminService = serviceFactory.CreateOrganizationService(null);
            //ITracingService tracer = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                string expenseclaimid = context.InputParameters["expenseclaimid"] != null ? context.InputParameters["expenseclaimid"].ToString() : "";
                QueryExpression queryExpression = new QueryExpression("new_srv_expense_claim");
                queryExpression.NoLock = true;
                queryExpression.ColumnSet = new ColumnSet("new_businesstype");
                queryExpression.Criteria.AddCondition("new_srv_expense_claimid", ConditionOperator.Equal, expenseclaimid);
                LinkEntity link = new LinkEntity("new_srv_expense_claim", "new_srv_station", "new_srv_station_id", "new_srv_stationid", JoinOperator.Inner);
                link.EntityAlias = "link";
                link.Columns = new ColumnSet("new_providertype");
                queryExpression.LinkEntities.Add(link);
                Entity expenseclaim = adminService.RetrieveMultipleWithBypassPlugin(queryExpression).Entities.FirstOrDefault();
                int expense_type = expenseclaim.GetAttributeValue<OptionSetValue>("new_businesstype").Value;
                int providertype = -1;//服务商类型
                if (expenseclaim != null)
                {
                    if (expenseclaim.Contains("link.new_providertype"))
                        providertype = expenseclaim.GetAliasAttributeValue<OptionSetValue>("link.new_providertype").Value;
                }
                JObject joresult = new JObject();
                if (providertype == 7 || providertype == 8)
                {
                    //运营商
                    joresult = WorkorderdetialViewdataOperator(expenseclaimid, adminService, service, context);
                }
                else 
                {
                    joresult = WorkorderdetialViewdata(expenseclaimid, providertype, adminService, service, context);
                }
                if (expense_type == 9) 
                {
                    joresult = WorkorderdetialViewdataInstall(expenseclaimid, providertype, adminService, service, context);
                }
                context.OutputParameters["output"] = joresult.ToString();
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException("WorkorderdetailCustomizeViewAction Iplugin" + ex.Message);
            }
        }
        /// <summary>
        /// 根据语言id,语言配置名称获取语言配置数据
        /// </summary>
        /// <param name="langid"></param>
        /// <param name="languagename"></param>
        /// <returns></returns>
        public EntityCollection GetResourceWorkorderdetail(string langid, string languagename, IOrganizationService OrganizationServiceAdmin)
        {
            string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='new_languageconfig'>
    <attribute name='new_languageconfigid' />
    <attribute name='new_name' />
    <attribute name='createdon' />
    <attribute name='new_note' />
    <attribute name='new_content' />
    <order attribute='new_name' descending='false' />
    <filter type='and'>
      <condition attribute='new_name' operator='like' value='{languagename}' />
    </filter>
    <link-entity name='new_language' from='new_languageid' to='new_language_id' link-type='inner' alias='af'>
      <filter type='and'>
        <condition attribute='new_langid' operator='eq' value='{langid}' />
      </filter>
    </link-entity>
  </entity>
</fetch>";
            EntityCollection cols = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchxml));
            return cols;
        }
        /// <summary>
        /// 获取语言id
        /// </summary>
        /// <param name="organizationService"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public int GetCurrentUserLangId(Guid userId, IOrganizationService OrganizationServiceAdmin)
        {
            Entity entity = OrganizationServiceAdmin.Retrieve("usersettings", userId, new ColumnSet("uilanguageid", "localeid"));

            var langId = entity.GetAttributeValue<int>("uilanguageid");
            if (langId == 0)
            {
                langId = entity.GetAttributeValue<int>("localeid");
            }
            return langId;
        }
        /// <summary>
        /// 查询客户类型
        /// </summary>
        /// <param name="returnchannellist"></param>
        /// <param name="OrganizationServiceAdmin"></param>
        /// <returns></returns>
        public EntityCollection GetCustomertype(IOrganizationService OrganizationServiceAdmin) 
        {
            QueryExpression qe = new QueryExpression("new_customertypeconfiguration");
            qe.NoLock = true;
            qe.ColumnSet = new ColumnSet("new_return_channel", "new_customertype");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            EntityCollection cols = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return cols;
        }
        /// <summary>
        /// 查询结算单下所有的工单明细，以及更换件明细，处理方法
        /// </summary>
        /// <param name="expenseclaimid"></param>
        /// <param name="adminService"></param>
        /// <param name="service"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public JObject WorkorderdetialViewdata(string expenseclaimid, int providertype, IOrganizationService adminService, IOrganizationService service, IPluginExecutionContext context) 
        {
            bool isb2x = false;
            if (providertype == 6)
                isb2x = true;
            else
                isb2x = false;
            string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
          <entity name='new_srv_workorder'>
            <attribute name='new_srv_workorderid' />
            <attribute name='new_name' />
			<attribute name='new_station_id' />
			<attribute name='new_type' />
			<attribute name='new_repairfee' />
			<attribute name='new_repairfeekpi' />
			<attribute name='new_changemoney' />
			<attribute name='new_specialfeeshare' />
			<attribute name='new_withholdingmoney' />
			<attribute name='new_settlementmoney' />
			<attribute name='new_recordingfee' />
			<attribute name='createdon' />
			<attribute name='new_endtime' />
			<attribute name='new_refurbishmentfee' />
			<attribute name='new_emptycontainerfee' />
			<attribute name='new_emptycontainerlogisticsfee' />
			<attribute name='new_tvprotectionmaterialfee' />
			<attribute name='new_logisticsfeeb2x' />
			<attribute name='new_customsclearancefee' />
			<attribute name='new_externalcallfee' />
			<attribute name='new_srv_workorder_id' />
			<attribute name='new_repairstation_id' />
			<attribute name='new_warranty' />
			<attribute name='new_category3_id' />
			<attribute name='new_model2_id' />
			<attribute name='new_servicemode' />
			<attribute name='new_uploadcompletion_time' />
			<attribute name='new_iswriteno' />
			<attribute name='new_goodsfiles_id' />
			<attribute name='new_deliverytrackingnumber' />
			<attribute name='new_pullouttrackingnumber' />
			<attribute name='new_customer_type' />
			<attribute name='new_upload_orderid' />
			<attribute name='new_materialdetails' />
			<attribute name='new_imei' />
			<attribute name='new_sn' />
			<attribute name='new_transactioncurrency_service_id' />
			<attribute name='new_b2x_orderid' />
			<attribute name='new_servicestation_id' />
			<attribute name='new_sparepartscostbuysell' />
			<attribute name='new_sparepartscostmarkup' />
			<attribute name='new_localbuycostbuysell' />
			<attribute name='new_localbuymarkupcostbuysell' />
			<attribute name='new_expenseclaimid' />
			<attribute name='new_category1_id' />
			<attribute name='new_category2_id' />
			<attribute name='new_isrur' />
			<attribute name='new_closingtime' />
			<attribute name='new_sub_station_type' />
			<attribute name='new_shipment_type' />
			<attribute name='new_shipment_type2' />
			<attribute name='new_return_channel' />
			<attribute name='new_logistic_zone' />
			<attribute name='new_model1_id' />
			<attribute name='new_repairprovider_id' />
			<attribute name='new_srv_workorderid' />
			<attribute name='new_uploadcompletion_time' />
			<attribute name='new_b2xshare' />
			<attribute name='new_boxfee' />
			<attribute name='new_repairsubsidy' />
			<attribute name='new_distancesubsidy' />
			<attribute name='new_logisticsfee' />
			<attribute name='new_othercost' />
			<attribute name='new_guaranteefeeshare' />
			<attribute name='new_returnvisitfee' />
			<attribute name='new_inspect_fee' />
			<attribute name='new_ordercode' />
			<order attribute='createdon' descending='true' />
			<filter type='and'>
				<condition attribute='statecode' operator='eq' value='0' />
				<condition attribute='new_expenseclaimid' operator='eq' value='{expenseclaimid}'/>
			</filter>
	        <link-entity name='new_workorder_costtable' from='new_workorder_costtableid' to='new_workorder_costtable_id' visible='false' link-type='outer' alias='cost'>
				<attribute name='new_doabuyback' />
				<attribute name='new_aftersalesrefund' />
				<attribute name='new_swapsettlement' />
				<attribute name='new_freescreenchange' />
				<attribute name='new_aftersalesotherfee' />
				<attribute name='new_aftersalecustomsclearfee' />
				<attribute name='new_disassemblyfee' />
				<attribute name='new_semiselfemployedfee' />
				<attribute name='new_aftersaleescsubsidyfee' />
				<attribute name='new_aftersalelogisticsfee' />
				<attribute name='new_b2xsubsidyfee' />
				<attribute name='new_withholdingfeerepair' />
				<attribute name='new_withholdingfeerecoilrepair' />
			</link-entity>
			<link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_service_id' visible='false' link-type='outer' alias='at'>
				<attribute name='isocurrencycode' />
			</link-entity>
			<link-entity name='new_category1' from='new_category1id' to='new_category1_id' visible='false' link-type='outer' alias='a_c1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model1' from='new_model1id' to='new_model1_id' visible='false' link-type='outer' alias='a_m1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model2' from='new_model2id' to='new_model2_id' visible='false' link-type='outer' alias='a_m2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category3' from='new_category3id' to='new_category3_id' visible='false' link-type='outer' alias='a_c3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category2' from='new_category2id' to='new_category2_id' visible='false' link-type='outer' alias='a_c2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_station_id' visible='false' link-type='outer' alias='a_s1'>
				<attribute name='new_stietype' />
				<attribute name='new_country_id' />
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_servicestation_id' visible='false' link-type='outer' alias='a_s2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairprovider_id' visible='false' link-type='outer' alias='a_s3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairstation_id' visible='false' link-type='outer' alias='a_s4'>
				<attribute name='new_stietype' />
				<attribute name='new_country_id' />
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_workorder_approach' from='new_srv_workorder_id' to='new_srv_workorderid' visible='false' link-type='outer' alias='aw'>
				<attribute name='new_approach_id' />
				<link-entity name='new_approach' from='new_approachid' to='new_approach_id' visible='false' link-type='outer' alias='ap'>
					<attribute name='new_name' />
					<attribute name='new_settlementlevel' />
				</link-entity>
			</link-entity>
            <link-entity name='new_srv_partline' from='new_srv_workorder_id' to='new_srv_workorderid' link-type='outer' alias='ac'>
                <attribute name='new_srv_partlineid' />
                <attribute name='new_name' />
                <attribute name='createdon' />
                <attribute name='new_action_reason' />
                <attribute name='new_srv_workorder_id' />
                <filter type='and'>
                  <condition attribute='statecode' operator='eq' value='0' />
                </filter>
                <link-entity name='product' from='productid' to='new_productnew_id' visible='false' link-type='outer' alias='a_p'>
				<attribute name='productnumber' />
				<attribute name='name' />
			    </link-entity>
            </link-entity>
	</entity>
</fetch>";
            EntityCollection workorderdetailcols = Common.CommonHelper.QueryXmlPagePassPlugin(service, fetchxml);
            Dictionary<Guid, List<partline>> workorder_partline_dic = new Dictionary<Guid, List<partline>>();
            Dictionary<Guid, List<approach>> workorder_approach_dic = new Dictionary<Guid, List<approach>>();
            //根据服务单进行分组
            var workordergroupby = workorderdetailcols.Entities.GroupBy(a => a.GetAttributeValue<Guid>("new_srv_workorderid"));
            foreach (var item in workordergroupby)
            {
                var itemlist = item.ToList();
                var partline = itemlist.Where(a => a.Contains("ac.new_action_reason")
                || a.Contains("a_p.productnumber")).Select(p => new partline()
                {
                    new_action_reason = p.Contains("ac.new_action_reason") ? p.FormattedValues["ac.new_action_reason"] : "",
                    productnumber = p.Contains("a_p.productnumber") ? p.GetAliasAttributeValue<string>("a_p.productnumber") : ""
                }).ToList();
                partline = partline.GroupBy(a => new { a.productnumber, a.new_action_reason }) // 假设我们根据Id去重  
                      .Select(group => group.First()) // 选择每组中的第一个元素  
                      .ToList();
                workorder_partline_dic.Add(item.Key, partline);
                var approach = itemlist.Where(a => a.Contains("aw.new_approach_id")
                || a.Contains("ap.new_settlementlevel")).Select(p => new approach()
                {
                    new_approach_id = p.Contains("aw.new_approach_id") ? p.GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "",
                    new_settlementlevel = p.Contains("ap.new_settlementlevel") ? p.FormattedValues["ap.new_settlementlevel"] : ""
                }).ToList();
                approach = approach.GroupBy(a => new { a.new_approach_id, a.new_settlementlevel }) // 假设我们根据Id去重  
                      .Select(group => group.First()) // 选择每组中的第一个元素  
                      .ToList();
                workorder_approach_dic.Add(item.Key, approach);
            }
            EntityCollection customertypelist = GetCustomertype(adminService);
            Func<string, string> convertcustomertype = (returnchannel) =>
            {
                string customertypetext = "";
                var customertypeen = customertypelist.Entities.Where(a => a.GetAttributeValue<string>("new_return_channel") == returnchannel).FirstOrDefault();
                if (customertypeen != null)
                {
                    customertypetext = customertypeen.Contains("new_customertype") ? customertypeen.FormattedValues["new_customertype"] : "";
                }
                return customertypetext;
            };
            int langid = GetCurrentUserLangId(context.UserId, adminService);
            JArray ja = new JArray();
            JArray ja_fields = new JArray();
            List<string> jofields = new List<string>();
            var languagedata = GetResourceWorkorderdetail(langid.ToString(), "ExpenseStatement.Workorderdetail.CustomizeView.%", adminService);
            #region 工单明细字段获取多语言信息
            List<FieldList> fieldLists = new List<FieldList>
                {
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault().ToDefault<string>("new_content"):"Service Order Number", prop = "new_name" },//服务单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault().ToDefault<string>("new_content"):"Service Type", prop = "new_type" },//工单类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BNBW").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BNBW").FirstOrDefault().ToDefault<string>("new_content"):"OOW/IW", prop = "new_warranty" },//保内/保外
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault().ToDefault<string>("new_content"):"Service Mode", prop = "new_servicemode" },//服务方式
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXLWF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXLWF").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance Labor Costs", prop = "new_repairfee" },//维修劳务费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXLWFKPI").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXLWFKPI").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance Labor Costs (KPI)", prop = "new_repairfeekpi" },//维修劳务费KPI
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BJF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BJF").FirstOrDefault().ToDefault<string>("new_content"):"Spare Parts Fee", prop = "new_sparepartscostbuysell" },//备件费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BJFM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BJFM").FirstOrDefault().ToDefault<string>("new_content"):"Spare Parts Markup Fee (Buysell)", prop = "new_sparepartscostmarkup" },//备件markup费（buysell）
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.LB").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.LB").FirstOrDefault().ToDefault<string>("new_content"):"Local Buy Fee", prop = "new_localbuycostbuysell" },//Local Buy 费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.LBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.LBM").FirstOrDefault().ToDefault<string>("new_content"):"Local Buy Markup Fee", prop = "new_localbuymarkupcostbuysell" },//Local Buy 费 markup
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Fees", prop = "new_withholdingfee" },//预提费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Recoil Fees", prop = "new_withholdingfeerecoil" },//预提反冲费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault().ToDefault<string>("new_content"):"The Differential Amount", prop = "new_changemoney" },//调差金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Amount", prop = "new_withholdingmoney" },//预提金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault().ToDefault<string>("new_content"):"Total Cost", prop = "new_settlementmoney" },//结算金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault().ToDefault<string>("new_content"):"Createdon", prop = "createdon" },//创建时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault().ToDefault<string>("new_content"):"Service Finish Time", prop = "new_endtime" },//服务结束时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault().ToDefault<string>("new_content"):"Work Order Upload Completion Time", prop = "new_uploadcompletion_time" },//工单上传完成时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SFXHGD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SFXHGD").FirstOrDefault().ToDefault<string>("new_content"):"Whether To Write A Work Order", prop = "new_iswriteno" },//是否写号工单
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SPDA").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SPDA").FirstOrDefault().ToDefault<string>("new_content"):"Commodity Archives", prop = "new_goodsfiles_id" },//商品档案
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLMX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLMX").FirstOrDefault().ToDefault<string>("new_content"):"Material Details", prop = "new_materialdetails" },//物料明细
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SFFWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SFFWD").FirstOrDefault().ToDefault<string>("new_content"):"Third Service Order Number", prop = "new_upload_orderid" },//三方服务单
                    new FieldList{ label = "IMEI", prop = "new_imei" },//IMEI
                    new FieldList{ label = "SN", prop = "new_sn" },//SN
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BZ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BZ").FirstOrDefault().ToDefault<string>("new_content"):"Settlement Currency", prop = "new_transactioncurrency_service_id" },//结算币种
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault().ToDefault<string>("new_content"):"Order No.", prop = "new_ordercode" },//订单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWO").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWO").FirstOrDefault().ToDefault<string>("new_content"):"B2X Work Order Number", prop = "new_b2x_orderid" },//B2X工单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault().ToDefault<string>("new_content"):"Expense Settlement Number", prop = "new_expenseclaimid" },//结算单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category", prop = "new_category1_id" },//一级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category Id", prop = "new_category1_new_code" },//一级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category", prop = "new_category2_id" },//二级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category Id", prop = "new_category2_new_code" },//二级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_id" },//三级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_new_code" },//三级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model", prop = "new_model1_id" },//一级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model Id", prop = "new_model1_new_code" },//一级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model", prop = "new_model2_id" },//二级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model Id", prop = "new_model2_new_code" },//二级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider", prop = "new_servicestation_id" },//所属服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider Code", prop = "new_srv_station_new_code" },//所属服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault().ToDefault<string>("new_content"):"Service Station", prop = "new_station_id" },//所属网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Station Code", prop = "new_srv_station_new_code1" },//所属网点编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider", prop = "new_repairprovider_id" },//维修服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider Code", prop = "new_srv_station_new_code3" },//维修服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station", prop = "new_repairstation_id" },//维修网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station Code", prop = "new_srv_station_new_code4" },//维修网点编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDLX").FirstOrDefault().ToDefault<string>("new_content"):"Center Type (repair station)", prop = "new_srv_station_new_stietype" },//维修网点类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDGJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDGJ").FirstOrDefault().ToDefault<string>("new_content"):"Country/Region (repair station)", prop = "new_srv_station_new_country_id" },//维修网点国家
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDLX").FirstOrDefault().ToDefault<string>("new_content"):"Center Type (Service Station)", prop = "new_srv_station_new_stietype1" },//所属网点类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDGJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDGJ").FirstOrDefault().ToDefault<string>("new_content"):"Country/Region (Service Station)", prop = "new_srv_station_new_country_id1" },//所属网点国家
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.ISRUR").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.ISRUR").FirstOrDefault().ToDefault<string>("new_content"):"Whether To Mark As Rur", prop = "new_isrur" },//是否标记为RUR
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault().ToDefault<string>("new_content"):"Closing time", prop = "new_closingtime" }//关单时间
                };
            if (isb2x)
            {
                List<FieldList> fieldListsb2x = new List<FieldList>
                    {
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LDF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LDF").FirstOrDefault().ToDefault<string>("new_content"):"Recording Fee", prop = "new_recordingfee" },//录单费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DWHJF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DWHJF").FirstOrDefault().ToDefault<string>("new_content"):"Outbound call fee", prop = "new_externalcallfee" },//对外呼叫费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.GSF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.GSF").FirstOrDefault().ToDefault<string>("new_content"):"Customs Fee", prop = "new_customsclearancefee" },//关税费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWLF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWLF").FirstOrDefault().ToDefault<string>("new_content"):"Logistics Fee", prop = "new_logisticsfeeb2x" },//物流费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.TVCLBHF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.TVCLBHF").FirstOrDefault().ToDefault<string>("new_content"):"Protection material fee", prop = "new_tvprotectionmaterialfee" },//电视保护材料费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KXWLF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KXWLF").FirstOrDefault().ToDefault<string>("new_content"):"Box logistics fee", prop = "new_emptycontainerlogisticsfee" },//空箱物流费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KXF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KXF").FirstOrDefault().ToDefault<string>("new_content"):"Box fee", prop = "new_emptycontainerfee" },//空箱费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FXF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FXF").FirstOrDefault().ToDefault<string>("new_content"):"Refurbish fee", prop = "new_refurbishmentfee" },//翻新费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.B2XBT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.B2XBT").FirstOrDefault().ToDefault<string>("new_content"):"B2X Subsidy Fee", prop = "new_b2xsubsidyfee" },//B2X补贴费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JJXFY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JJXFY").FirstOrDefault().ToDefault<string>("new_content"):"Add/Subtract Fees Sharing", prop = "new_specialfeeshare" },//加减项费用
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SBLY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SBLY").FirstOrDefault().ToDefault<string>("new_content"):"Return Channel", prop = "new_return_channel" },//设备来源
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KHLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KHLX").FirstOrDefault().ToDefault<string>("new_content"):"Customer Type", prop = "new_customer_type" },//客户类型
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WLQY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WLQY").FirstOrDefault().ToDefault<string>("new_content"):"Logistic Zone", prop = "new_logistic_zone" },//物流区域
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.ZWDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.ZWDLX").FirstOrDefault().ToDefault<string>("new_content"):"Sub-service Centre Type", prop = "new_sub_station_type" },//子网点类型
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHYSLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHYSLX").FirstOrDefault().ToDefault<string>("new_content"):"Shipment Type (Import)", prop = "new_shipment_type" },//收货运输类型
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHYSLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHYSLX").FirstOrDefault().ToDefault<string>("new_content"):"Shipment Type (Export)", prop = "new_shipment_type2" },//发货运输类型
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWLDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWLDH").FirstOrDefault().ToDefault<string>("new_content"):"Receiving Goods Logistics Order Number", prop = "new_pullouttrackingnumber" },//收货物流单号
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHWLDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHWLDH").FirstOrDefault().ToDefault<string>("new_content"):"Return Machine Logistics Bill Number", prop = "new_deliverytrackingnumber" },//发货物流单号
                    };
                fieldLists.AddRange(fieldListsb2x);
            }
            else
            {
                List<FieldList> fieldListsnob2x = new List<FieldList>
                    {
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHWLDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.FHWLDH").FirstOrDefault().ToDefault<string>("new_content"):"Return Shipping Logistics Number", prop = "new_deliverytrackingnumber" },//还机物流单号
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWLDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWLDH").FirstOrDefault().ToDefault<string>("new_content"):"Receiving Goods Logistics Order Number", prop = "new_pullouttrackingnumber" },//取机物流单号
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KHLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.KHLX").FirstOrDefault().ToDefault<string>("new_content"):"Customer Type", prop = "new_customer_type" },//客户类型
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.XZF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.XZF").FirstOrDefault().ToDefault<string>("new_content"):"Box Fee", prop = "new_boxfee" },//箱子费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JXBT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JXBT").FirstOrDefault().ToDefault<string>("new_content"):"Parcel Repair Allowance", prop = "new_repairsubsidy" },//寄修补贴
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LCBT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LCBT").FirstOrDefault().ToDefault<string>("new_content"):"Travel Allowance Fee", prop = "new_distancesubsidy" },//路程补贴费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SMWLF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SMWLF").FirstOrDefault().ToDefault<string>("new_content"):"Door-to-door Logistics Fee", prop = "new_logisticsfee" },//上门物流费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LDF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.LDF").FirstOrDefault().ToDefault<string>("new_content"):"Recording Fee", prop = "new_recordingfee" },//录单费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DHHFF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DHHFF").FirstOrDefault().ToDefault<string>("new_content"):"Fee Of Phone Retumn Visit", prop = "new_returnvisitfee" },//电话回访费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JCF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JCF").FirstOrDefault().ToDefault<string>("new_content"):"inspection fee", prop = "new_inspect_fee" },//检测费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.QTFY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.QTFY").FirstOrDefault().ToDefault<string>("new_content"):"Other Costs", prop = "new_othercost" },//其他费用
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BDFFT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BDFFT").FirstOrDefault().ToDefault<string>("new_content"):"Underwriting Fee Sharing", prop = "new_guaranteefeeshare" },//保底费分摊
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JJXFY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.JJXFY").FirstOrDefault().ToDefault<string>("new_content"):"Add/Subtract Fees Sharing", prop = "new_specialfeeshare" },//加减项费用
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.CJFY").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.CJFY").FirstOrDefault().ToDefault<string>("new_content"):"Disassembly costs", prop = "new_disassemblyfee" },//拆机费用
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DOAHG").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.DOAHG").FirstOrDefault().ToDefault<string>("new_content"):"DOA Buyback Fee", prop = "new_doabuyback" },//售后网点杂费-DOA回购
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFTK").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFTK").FirstOrDefault().ToDefault<string>("new_content"):"After Sales Refund Fee", prop = "new_aftersalesrefund" },//售后网点杂费-Refund退款
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SWAPJF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SWAPJF").FirstOrDefault().ToDefault<string>("new_content"):"Swap Settlement Fee", prop = "new_swapsettlement" },//售后网点杂费-SWAP结费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BWMFHP").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BWMFHP").FirstOrDefault().ToDefault<string>("new_content"):"Free Screen Change Fee", prop = "new_freescreenchange" },//售后网点杂费-保外免费换屏
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFQT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFQT").FirstOrDefault().ToDefault<string>("new_content"):"After Sales Other Fee", prop = "new_aftersalesotherfee" },//售后网点杂费-其他
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFQGF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.WDZFQGF").FirstOrDefault().ToDefault<string>("new_content"):"Customs Clear Fee", prop = "new_aftersalecustomsclearfee" },//售后网点杂费-清关费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BZYF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.BZYF").FirstOrDefault().ToDefault<string>("new_content"):"Semi Self Operated Fee", prop = "new_semiselfemployedfee" },//售后网点-半自营费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.ESCBT").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.ESCBT").FirstOrDefault().ToDefault<string>("new_content"):"After Sales ESC Subsidy Fee", prop = "new_aftersaleescsubsidyfee" },//售后网点-ESC补贴费
                        new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDWL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                        == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDWL").FirstOrDefault().ToDefault<string>("new_content"):"After Sales Logistics Fee", prop = "new_aftersalelogisticsfee" },//售后网点-物流费
                    };
                fieldLists.AddRange(fieldListsnob2x);

            }
            #endregion
            #region 服务单更换件明细动态列
            List<partline> partlines = new List<partline>();
            partlines = workorder_partline_dic.Values.OrderByDescending(list => list.Count).FirstOrDefault();
            if (partlines != null && partlines.Count > 0)
            {
                foreach (var item in partlines)
                {
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.WLBM").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.WLBM").FirstOrDefault().ToDefault<string>("new_content") : "Part Code") + (partlines.IndexOf(item) + 1),
                        prop = "productnumber" + (partlines.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.MaitroxConsign.DZLY").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.MaitroxConsign.DZLY").FirstOrDefault().ToDefault<string>("new_content") : "Action Reason") + (partlines.IndexOf(item) + 1),
                        prop = "new_action_reason" + (partlines.IndexOf(item) + 1)
                    });
                };
            }
            #endregion
            #region 服务单处理方法动态列
            List<approach> approachlist = new List<approach>();
            approachlist = workorder_approach_dic.Values.OrderByDescending(list => list.Count).FirstOrDefault();
            if (approachlist != null && approachlist.Count > 0)
            {
                foreach (var item in approachlist)
                {
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault().ToDefault<string>("new_content") : "Processing Method") + (approachlist.IndexOf(item) + 1),
                        prop = "new_approach_id" + (approachlist.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault().ToDefault<string>("new_content") : "Settlement Level") + (approachlist.IndexOf(item) + 1),
                        prop = "new_settlementlevel" + (approachlist.IndexOf(item) + 1)
                    });
                };
            }
            #endregion
            jofields = fieldLists.Select(a => a.prop).ToList();
            foreach (var item in workordergroupby)
            {
                var firstdata = item.ToList().FirstOrDefault();
                JObject jo = new JObject();
                jo["new_srv_workorderid"] = firstdata.Contains("new_srv_workorderid") ? firstdata.GetAttributeValue<Guid>("new_srv_workorderid") : Guid.Empty;
                jo["new_name"] = firstdata.Contains("new_name") ? firstdata.GetAttributeValue<string>("new_name") : "";
                jo["new_ordercode"] = firstdata.Contains("new_ordercode") ? firstdata.GetAttributeValue<string>("new_ordercode") : "";
                jo["new_station_id"] = firstdata.Contains("new_station_id") ? firstdata.GetAttributeValue<EntityReference>("new_station_id").Name : "";
                jo["new_type"] = firstdata.Contains("new_type") && firstdata.FormattedValues.ContainsKey("new_type") ? firstdata.FormattedValues["new_type"] : "";
                jo["new_repairfee"] = firstdata.Contains("new_repairfee") ? firstdata.GetAttributeValue<decimal>("new_repairfee").ToString("#0.00") : "";
                jo["new_repairfeekpi"] = firstdata.Contains("new_repairfeekpi") ? firstdata.GetAttributeValue<decimal>("new_repairfeekpi").ToString("#0.00") : "";
                jo["new_changemoney"] = firstdata.Contains("new_changemoney") ? firstdata.GetAttributeValue<decimal>("new_changemoney").ToString("#0.00") : "";
                jo["new_specialfeeshare"] = firstdata.Contains("new_specialfeeshare") ? firstdata.GetAttributeValue<decimal>("new_specialfeeshare").ToString("#0.00") : "";
                jo["new_withholdingmoney"] = firstdata.Contains("new_withholdingmoney") ? firstdata.GetAttributeValue<decimal>("new_withholdingmoney").ToString("#0.00") : "";
                jo["new_settlementmoney"] = firstdata.Contains("new_settlementmoney") ? firstdata.GetAttributeValue<decimal>("new_settlementmoney").ToString("#0.00") : "";
                jo["new_recordingfee"] = firstdata.Contains("new_recordingfee") ? firstdata.GetAttributeValue<decimal>("new_recordingfee").ToString("#0.00") : "";
                jo["new_withholdingfee"] = firstdata.Contains("cost.new_withholdingfeerepair") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_withholdingfeerepair").ToString("#0.00") : "";
                jo["new_withholdingfeerecoil"] = firstdata.Contains("cost.new_withholdingfeerecoilrepair") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_withholdingfeerecoilrepair").ToString("#0.00") : "";
                jo["createdon"] = firstdata.Contains("createdon") ? firstdata.GetAttributeValue<DateTime>("createdon").ToString() : "";
                jo["new_endtime"] = firstdata.Contains("new_endtime") ? firstdata.GetAttributeValue<DateTime>("new_endtime").ToString() : "";
                jo["new_uploadcompletion_time"] = firstdata.Contains("new_uploadcompletion_time") ? firstdata.GetAttributeValue<DateTime>("new_uploadcompletion_time").ToString() : "";
                jo["new_srv_workorder_id"] = firstdata.Contains("new_srv_workorder_id") ? firstdata.GetAttributeValue<EntityReference>("new_srv_workorder_id").Name : "";
                jo["new_repairstation_id"] = firstdata.Contains("new_repairstation_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairstation_id").Name : "";
                jo["new_warranty"] = firstdata.Contains("new_warranty") && firstdata.FormattedValues.ContainsKey("new_warranty") ? firstdata.FormattedValues["new_warranty"] : "";
                jo["new_category3_id"] = firstdata.Contains("new_category3_id") ? firstdata.GetAttributeValue<EntityReference>("new_category3_id").Name : "";
                jo["new_model2_id"] = firstdata.Contains("new_model2_id") ? firstdata.GetAttributeValue<EntityReference>("new_model2_id").Name : "";
                jo["new_servicemode"] = firstdata.Contains("new_servicemode") && firstdata.FormattedValues.ContainsKey("new_servicemode") ? firstdata.FormattedValues["new_servicemode"] : "";
                jo["new_iswriteno"] = firstdata.Contains("new_iswriteno") ? firstdata.FormattedValues["new_iswriteno"] : "";
                jo["new_goodsfiles_id"] = firstdata.Contains("new_goodsfiles_id") ? firstdata.GetAttributeValue<EntityReference>("new_goodsfiles_id").Name : "";
                jo["new_deliverytrackingnumber"] = firstdata.Contains("new_deliverytrackingnumber") ? firstdata.GetAttributeValue<string>("new_deliverytrackingnumber") : "";
                jo["new_pullouttrackingnumber"] = firstdata.Contains("new_pullouttrackingnumber") ? firstdata.GetAttributeValue<string>("new_pullouttrackingnumber") : "";
                if (isb2x)
                {
                    jo["new_customer_type"] = firstdata.Contains("new_return_channel") ? convertcustomertype(firstdata.GetAttributeValue<string>("new_return_channel")) : "";
                }
                else
                {
                    jo["new_customer_type"] = firstdata.Contains("new_customer_type") && firstdata.FormattedValues.ContainsKey("new_customer_type") ? firstdata.FormattedValues["new_customer_type"] : "";
                }
                jo["new_upload_orderid"] = firstdata.Contains("new_upload_orderid") ? firstdata.GetAttributeValue<string>("new_upload_orderid") : "";
                jo["new_materialdetails"] = firstdata.Contains("new_materialdetails") ? firstdata.GetAttributeValue<string>("new_materialdetails") : "";
                jo["new_imei"] = firstdata.Contains("new_imei") ? firstdata.GetAttributeValue<string>("new_imei") : "";
                jo["new_sn"] = firstdata.Contains("new_sn") ? firstdata.GetAttributeValue<string>("new_sn") : "";
                jo["new_transactioncurrency_service_id"] = firstdata.Contains("at.isocurrencycode") ? firstdata.GetAliasAttributeValue<string>("at.isocurrencycode") : "";
                jo["new_b2x_orderid"] = firstdata.Contains("new_b2x_orderid") ? firstdata.GetAttributeValue<string>("new_b2x_orderid") : "";
                jo["new_servicestation_id"] = firstdata.Contains("new_servicestation_id") ? firstdata.GetAttributeValue<EntityReference>("new_servicestation_id").Name : "";
                jo["new_sparepartscostbuysell"] = firstdata.Contains("new_sparepartscostbuysell") ? firstdata.GetAttributeValue<decimal>("new_sparepartscostbuysell").ToString("#0.00") : "";
                jo["new_sparepartscostmarkup"] = firstdata.Contains("new_sparepartscostmarkup") ? firstdata.GetAttributeValue<decimal>("new_sparepartscostmarkup").ToString("#0.00") : "";
                jo["new_localbuycostbuysell"] = firstdata.Contains("new_localbuycostbuysell") ? firstdata.GetAttributeValue<decimal>("new_localbuycostbuysell").ToString("#0.00") : "";
                jo["new_localbuymarkupcostbuysell"] = firstdata.Contains("new_localbuymarkupcostbuysell") ? firstdata.GetAttributeValue<decimal>("new_localbuymarkupcostbuysell").ToString("#0.00") : "";
                jo["new_category1_id"] = firstdata.Contains("new_category1_id") ? firstdata.GetAttributeValue<EntityReference>("new_category1_id").Name : "";
                jo["new_category2_id"] = firstdata.Contains("new_category2_id") ? firstdata.GetAttributeValue<EntityReference>("new_category2_id").Name : "";
                jo["new_expenseclaimid"] = firstdata.Contains("new_expenseclaimid") ? firstdata.GetAttributeValue<EntityReference>("new_expenseclaimid").Name : "";
                jo["new_model1_id"] = firstdata.Contains("new_model1_id") ? firstdata.GetAttributeValue<EntityReference>("new_model1_id").Name : "";
                jo["new_isrur"] = firstdata.Contains("new_isrur") ? firstdata.FormattedValues["new_isrur"] : "";
                jo["new_closingtime"] = firstdata.Contains("new_closingtime") ? firstdata.GetAttributeValue<DateTime>("new_closingtime").ToString() : "";
                jo["new_repairprovider_id"] = firstdata.Contains("new_repairprovider_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairprovider_id").Name : "";
                jo["new_category1_new_code"] = firstdata.Contains("a_c1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c1.new_code") : "";
                jo["new_category2_new_code"] = firstdata.Contains("a_c2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c2.new_code") : "";
                jo["new_category3_new_code"] = firstdata.Contains("a_c3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c3.new_code") : "";
                jo["new_category3_new_code"] = firstdata.Contains("a_c3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c3.new_code") : "";
                jo["new_model1_new_code"] = firstdata.Contains("a_m1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m1.new_code") : "";
                jo["new_model2_new_code"] = firstdata.Contains("a_m2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m2.new_code") : "";
                jo["new_srv_station_new_country_id"] = firstdata.Contains("a_s4.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("a_s4.new_country_id").Name : "";
                jo["new_srv_station_new_country_id1"] = firstdata.Contains("a_s1.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("a_s1.new_country_id").Name : "";
                jo["new_srv_station_new_code"] = firstdata.Contains("a_s2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s2.new_code") : "";
                jo["new_srv_station_new_code1"] = firstdata.Contains("a_s1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s1.new_code") : "";
                jo["new_srv_station_new_code3"] = firstdata.Contains("a_s3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s3.new_code") : "";
                jo["new_srv_station_new_code4"] = firstdata.Contains("a_s4.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s4.new_code") : "";
                jo["new_srv_station_new_stietype"] = firstdata.Contains("a_s4.new_stietype") ? firstdata.FormattedValues["a_s4.new_stietype"] : "";
                jo["new_srv_station_new_stietype1"] = firstdata.Contains("a_s1.new_stietype") ? firstdata.FormattedValues["a_s1.new_stietype"] : "";
                if (!isb2x)
                {
                    jo["new_boxfee"] = firstdata.Contains("new_boxfee") ? firstdata.GetAttributeValue<decimal>("new_boxfee").ToString("#0.00") : "";
                    jo["new_repairsubsidy"] = firstdata.Contains("new_repairsubsidy") ? firstdata.GetAttributeValue<decimal>("new_repairsubsidy").ToString("#0.00") : "";
                    jo["new_distancesubsidy"] = firstdata.Contains("new_distancesubsidy") ? firstdata.GetAttributeValue<decimal>("new_distancesubsidy").ToString("#0.00") : "";
                    jo["new_logisticsfee"] = firstdata.Contains("new_logisticsfee") ? firstdata.GetAttributeValue<decimal>("new_logisticsfee").ToString("#0.00") : "";
                    jo["new_returnvisitfee"] = firstdata.Contains("new_returnvisitfee") ? firstdata.GetAttributeValue<decimal>("new_returnvisitfee").ToString("#0.00") : "";
                    jo["new_guaranteefeeshare"] = firstdata.Contains("new_guaranteefeeshare") ? firstdata.GetAttributeValue<decimal>("new_guaranteefeeshare").ToString("#0.00") : "";
                    jo["new_othercost"] = firstdata.Contains("new_othercost") ? firstdata.GetAttributeValue<decimal>("new_othercost").ToString("#0.00") : "";
                    jo["new_inspect_fee"] = firstdata.Contains("new_inspect_fee") ? firstdata.GetAttributeValue<decimal>("new_inspect_fee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_doabuyback"))
                        jo["new_doabuyback"] = firstdata.Contains("cost.new_doabuyback") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_doabuyback").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_aftersalesrefund"))
                        jo["new_aftersalesrefund"] = firstdata.Contains("cost.new_aftersalesrefund") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_aftersalesrefund").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_swapsettlement"))
                        jo["new_swapsettlement"] = firstdata.Contains("cost.new_swapsettlement") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_swapsettlement").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_freescreenchange"))
                        jo["new_freescreenchange"] = firstdata.Contains("cost.new_freescreenchange") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_freescreenchange").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_aftersalesotherfee"))
                        jo["new_aftersalesotherfee"] = firstdata.Contains("cost.new_aftersalesotherfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_aftersalesotherfee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_aftersalecustomsclearfee"))
                        jo["new_aftersalecustomsclearfee"] = firstdata.Contains("cost.new_aftersalecustomsclearfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_aftersalecustomsclearfee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_disassemblyfee"))
                        jo["new_disassemblyfee"] = firstdata.Contains("cost.new_disassemblyfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_disassemblyfee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_semiselfemployedfee"))
                        jo["new_semiselfemployedfee"] = firstdata.Contains("cost.new_semiselfemployedfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_semiselfemployedfee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_aftersaleescsubsidyfee"))
                        jo["new_aftersaleescsubsidyfee"] = firstdata.Contains("cost.new_aftersaleescsubsidyfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_aftersaleescsubsidyfee").ToString("#0.00") : "";
                    if (firstdata.Contains("cost.new_aftersalelogisticsfee"))
                        jo["new_aftersalelogisticsfee"] = firstdata.Contains("cost.new_aftersalelogisticsfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_aftersalelogisticsfee").ToString("#0.00") : "";
                }
                if (isb2x)
                {
                    jo["new_refurbishmentfee"] = firstdata.Contains("new_refurbishmentfee") ? firstdata.GetAttributeValue<decimal>("new_refurbishmentfee").ToString("#0.00") : "";
                    jo["new_emptycontainerfee"] = firstdata.Contains("new_emptycontainerfee") ? firstdata.GetAttributeValue<decimal>("new_emptycontainerfee").ToString("#0.00") : "";
                    jo["new_emptycontainerlogisticsfee"] = firstdata.Contains("new_emptycontainerlogisticsfee") ? firstdata.GetAttributeValue<decimal>("new_emptycontainerlogisticsfee").ToString("#0.00") : "";
                    jo["new_tvprotectionmaterialfee"] = firstdata.Contains("new_tvprotectionmaterialfee") ? firstdata.GetAttributeValue<decimal>("new_tvprotectionmaterialfee").ToString("#0.00") : "";
                    jo["new_logisticsfeeb2x"] = firstdata.Contains("new_logisticsfeeb2x") ? firstdata.GetAttributeValue<decimal>("new_logisticsfeeb2x").ToString("#0.00") : "";
                    jo["new_customsclearancefee"] = firstdata.Contains("new_customsclearancefee") ? firstdata.GetAttributeValue<decimal>("new_customsclearancefee").ToString("#0.00") : "";
                    jo["new_externalcallfee"] = firstdata.Contains("new_externalcallfee") ? firstdata.GetAttributeValue<decimal>("new_externalcallfee").ToString("#0.00") : "";
                    jo["new_logistic_zone"] = firstdata.Contains("new_logistic_zone") ? firstdata.GetAttributeValue<string>("new_logistic_zone") : "";
                    jo["new_shipment_type"] = firstdata.Contains("new_shipment_type") ? firstdata.GetAttributeValue<string>("new_shipment_type") : "";
                    jo["new_shipment_type2"] = firstdata.Contains("new_shipment_type2") ? firstdata.GetAttributeValue<string>("new_shipment_type2") : "";
                    jo["new_return_channel"] = firstdata.Contains("new_return_channel") ? firstdata.GetAttributeValue<string>("new_return_channel") : "";
                    jo["new_sub_station_type"] = firstdata.Contains("new_sub_station_type") ? firstdata.GetAttributeValue<string>("new_sub_station_type") : "";
                    if (firstdata.Contains("cost.new_b2xsubsidyfee"))
                        jo["new_b2xsubsidyfee"] = firstdata.Contains("cost.new_b2xsubsidyfee") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_b2xsubsidyfee").ToString("#0.00") : "";
                }
                for (int i = 0; i < partlines.Count; i++)
                {
                    if (item.ToList().Count > i)
                    {
                        jo["productnumber" + (i + 1)] = item.ToList()[i].Contains("a_p.productnumber") ? item.ToList()[i].GetAliasAttributeValue<string>("a_p.productnumber") : "";
                        jo["new_action_reason" + (i + 1)] = item.ToList()[i].Contains("ac.new_action_reason") ? item.ToList()[i].FormattedValues["ac.new_action_reason"] : "";
                    }
                    else
                    {
                        //服务单更换件明细小于最大数量的，物料编码，动作路由赋值为空
                        jo["productnumber" + (i + 1)] = "";
                        jo["new_action_reason" + (i + 1)] = "";
                    }
                };
                for (int i = 0; i < approachlist.Count; i++)
                {
                    if (item.ToList().Count > i)
                    {
                        jo["new_approach_id" + (i + 1)] = item.ToList()[i].Contains("aw.new_approach_id") ? item.ToList()[i].GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "";
                        jo["new_settlementlevel" + (i + 1)] = item.ToList()[i].Contains("ap.new_settlementlevel") ? item.ToList()[i].FormattedValues["ap.new_settlementlevel"] : "";
                    }
                    else
                    {
                        //服务单更换件明细小于最大数量的，旧件编码，新建编码赋值为空
                        jo["new_approach_id" + (i + 1)] = "";
                        jo["new_settlementlevel" + (i + 1)] = "";
                    }
                };
                ja.Add(jo);
            }
            // 遍历 JArray 中的每个 JObject
            foreach (JObject jsonObject in ja)
            {
                // 获取 JObject 的所有属性（字段）
                IEnumerable<JProperty> properties = jsonObject.Properties();

                // 遍历并打印当前 JObject 的每个属性的名称（字段名）
                foreach (JProperty property in properties)
                {
                    if (!jofields.Contains(property.Name))
                        jofields.Add(property.Name);
                }
            }
            var fieldfilter = fieldLists.Where(a => jofields.Contains(a.prop)).ToList();
            ja_fields = JArray.Parse(JsonConvert.SerializeObject(fieldfilter));
            JObject joresult = new JObject();
            joresult["data"] = ja;
            joresult["fields"] = ja_fields;
            return joresult;
        }
        /// <summary>
        /// 查询服务商类型为Operator的结算单下所有的工单明细，以及更换件明细，处理方法
        /// </summary>
        /// <param name="expenseclaimid"></param>
        /// <param name="adminService"></param>
        /// <param name="service"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public JObject WorkorderdetialViewdataOperator(string expenseclaimid,IOrganizationService adminService, IOrganizationService service, IPluginExecutionContext context)
        {
            string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
          <entity name='new_srv_workorder'>
            <attribute name='new_name' />
            <attribute name='new_type' />
            <attribute name='createdon' />
            <attribute name='new_endtime' />
            <attribute name='new_closingtime' />
            <attribute name='new_uploadcompletion_time' />
            <attribute name='new_category3_id' />
            <attribute name='new_model2_id' />
            <attribute name='new_servicemode' />
            <attribute name='new_category2_id' />
            <attribute name='new_model1_id' />
            <attribute name='new_category1_id' />
            <attribute name='new_withholdingmoneyhandling' />
            <attribute name='new_changemoneyhandling' />
            <attribute name='new_settlementmoneyhandling' />
            <attribute name='new_station_id' />
            <attribute name='new_servicestation_id' />
            <attribute name='new_repairprovider_id' />
            <attribute name='new_repairstation_id' />
            <attribute name='new_operator_id' />
            <attribute name='new_b2x_orderid' />
            <attribute name='new_handlingfee' />
            <attribute name='new_handlingfeekpi' />
            <attribute name='new_logisticsfeehandling' />
            <attribute name='new_callfeehandling' />
            <attribute name='new_othermiscellaneouschargeshandling' />
            <attribute name='new_saleamounthandling' />
            <attribute name='new_new_expenseclaimidhandling' />
            <attribute name='new_ordercode' />
            <attribute name='new_srv_workorderid' />
			<order attribute='createdon' descending='true' />
			<filter type='and'>
				<condition attribute='statecode' operator='eq' value='0' />
				<condition attribute='new_new_expenseclaimidhandling' operator='eq' value='{expenseclaimid}'/>
			</filter>
            <link-entity name='new_workorder_costtable' from='new_workorder_costtableid' to='new_workorder_costtable_id' visible='false' link-type='outer' alias='cost'>
				<attribute name='new_customsclearancefeetransfer' />
				<attribute name='new_withholdingfeehandling' />
				<attribute name='new_withholdingfeerecoilhandling' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairstation_id' visible='false' link-type='outer' alias='an'>
                <attribute name='new_country_id' />
                <attribute name='new_code' />
            </link-entity>
            <link-entity name='new_operator' from='new_operatorid' to='new_operator_id' visible='false' link-type='outer' alias='ao'>
                <attribute name='new_country_id' />
                <attribute name='new_code' />
                <attribute name='new_serviceprovider' />
            </link-entity>
			<link-entity name='new_category1' from='new_category1id' to='new_category1_id' visible='false' link-type='outer' alias='a_c1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model1' from='new_model1id' to='new_model1_id' visible='false' link-type='outer' alias='a_m1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model2' from='new_model2id' to='new_model2_id' visible='false' link-type='outer' alias='a_m2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category3' from='new_category3id' to='new_category3_id' visible='false' link-type='outer' alias='a_c3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category2' from='new_category2id' to='new_category2_id' visible='false' link-type='outer' alias='a_c2'>
				<attribute name='new_code' />
			</link-entity>
            <link-entity name='new_srv_station' from='new_srv_stationid' to='new_station_id' visible='false' link-type='outer' alias='a_s1'>
				<attribute name='new_stietype' />
				<attribute name='new_country_id' />
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_servicestation_id' visible='false' link-type='outer' alias='a_s2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairprovider_id' visible='false' link-type='outer' alias='a_s3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_workorder_approach' from='new_srv_workorder_id' to='new_srv_workorderid' visible='false' link-type='outer' alias='aw'>
				<attribute name='new_approach_id' />
				<link-entity name='new_approach' from='new_approachid' to='new_approach_id' visible='false' link-type='outer' alias='ap'>
					<attribute name='new_name' />
					<attribute name='new_code' />
					<attribute name='new_settlementlevel' />
				</link-entity>
			</link-entity>
	</entity>
</fetch>";
            EntityCollection workorderdetailcols = Common.CommonHelper.QueryXmlPagePassPlugin(service, fetchxml);
            Dictionary<Guid, List<approach>> workorder_approach_dic = new Dictionary<Guid, List<approach>>();
            //根据服务单进行分组
            var workordergroupby = workorderdetailcols.Entities.GroupBy(a => a.GetAttributeValue<Guid>("new_srv_workorderid"));
            foreach (var item in workordergroupby)
            {
                var itemlist = item.ToList();
                var approach = itemlist.Where(a => a.Contains("aw.new_approach_id")
                || a.Contains("ap.new_settlementlevel")).Select(p => new approach()
                {
                    new_approach_id = p.Contains("aw.new_approach_id") ? p.GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "",
                    new_approach_code = p.Contains("ap.new_code") ? p.GetAliasAttributeValue<string>("ap.new_code") :"",
                    new_settlementlevel = p.Contains("ap.new_settlementlevel") ? p.FormattedValues["ap.new_settlementlevel"] : ""
                }).ToList();
                approach = approach.GroupBy(a => new { a.new_approach_id, a.new_approach_code, a.new_settlementlevel }) // 假设我们根据Id去重  
                      .Select(group => group.First()) // 选择每组中的第一个元素  
                      .ToList();
                workorder_approach_dic.Add(item.Key, approach);
            }
            List<string> jofields = new List<string>();
            int langid = GetCurrentUserLangId(context.UserId, adminService);
            JArray ja = new JArray();
            JArray ja_fields = new JArray();
            var languagedata = GetResourceWorkorderdetail(langid.ToString(), "ExpenseStatement.Workorderdetail.CustomizeView.%", adminService);
            #region 工单明细字段获取多语言信息
            List<FieldList> fieldLists = new List<FieldList>
                {
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault().ToDefault<string>("new_content"):"Expense Settlement Number (Handling) ", prop = "new_new_expenseclaimidhandling" },//结算单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GJ").FirstOrDefault().ToDefault<string>("new_content"):"Country/Region", prop = "new_repairstation_id_new_country_id" },//国家/区域
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYS").FirstOrDefault().ToDefault<string>("new_content"):"Operatore", prop = "new_operator_id" },//运营商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSGJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSGJ").FirstOrDefault().ToDefault<string>("new_content"):"Operatore Country/Region", prop = "new_operator_country" },//运营商国家
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSSSFUS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSSSFUS").FirstOrDefault().ToDefault<string>("new_content"):"Operatore Service Provider", prop = "new_operator_servicestation" },//运营商所属服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YYSBM").FirstOrDefault().ToDefault<string>("new_content"):"Operatore Code", prop = "new_operator_code" },//运营商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault().ToDefault<string>("new_content"):"Service Order Number", prop = "new_name" },//服务单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault().ToDefault<string>("new_content"):"Order No.", prop = "new_ordercode" },//订单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWO").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.B2XWO").FirstOrDefault().ToDefault<string>("new_content"):"B2X Work Order Number", prop = "new_b2x_orderid" },//B2X工单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault().ToDefault<string>("new_content"):"Service Mode", prop = "new_servicemode" },//服务方式
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault().ToDefault<string>("new_content"):"Service Type", prop = "new_type" },//工单类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider", prop = "new_servicestation_id" },//所属服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider Code", prop = "new_srv_station_new_code" },//所属服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault().ToDefault<string>("new_content"):"Service Station", prop = "new_station_id" },//所属网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Station Code", prop = "new_srv_station_new_code1" },//所属网点编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider", prop = "new_repairprovider_id" },//维修服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider Code", prop = "new_srv_station_new_code3" },//维修服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station", prop = "new_repairstation_id" },//维修网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station Code", prop = "new_srv_station_new_code4" },//维修网点编码
                     new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category", prop = "new_category1_id" },//一级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category Id", prop = "new_category1_new_code" },//一级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category", prop = "new_category2_id" },//二级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category Id", prop = "new_category2_new_code" },//二级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_id" },//三级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_new_code" },//三级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model", prop = "new_model1_id" },//一级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model Id", prop = "new_model1_new_code" },//一级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model", prop = "new_model2_id" },//二级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model Id", prop = "new_model2_new_code" },//二级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJF").FirstOrDefault().ToDefault<string>("new_content"):"Handling Fee", prop = "new_handlingfee" },//收集费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJFKPI").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJFKPI").FirstOrDefault().ToDefault<string>("new_content"):"Handling Fee (KPI)", prop = "new_handlingfeekpi" },//收集费KPI
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLF").FirstOrDefault().ToDefault<string>("new_content"):"Logistics Fee (Handling)", prop = "new_logisticsfeehandling" },//物流费（handling）
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.HJF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.HJF").FirstOrDefault().ToDefault<string>("new_content"):"Calling Fee (Handling)", prop = "new_callfeehandling" },//呼叫费（handling）
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.XSF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.XSF").FirstOrDefault().ToDefault<string>("new_content"):"Sales Fee (Handling)", prop = "new_saleamounthandling" },//销售额（handling）
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TSF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TSF").FirstOrDefault().ToDefault<string>("new_content"):"Special Fee (Handling)", prop = "new_othermiscellaneouschargeshandling" },//其他特殊费用（handling）
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault().ToDefault<string>("new_content"):"Createdon", prop = "createdon" },//创建时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault().ToDefault<string>("new_content"):"Service Finish Time", prop = "new_endtime" },//服务结束时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault().ToDefault<string>("new_content"):"Work Order Upload Completion Time", prop = "new_uploadcompletion_time" },//工单上传完成时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault().ToDefault<string>("new_content"):"Closing time", prop = "new_closingtime" },//关单时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault().ToDefault<string>("new_content"):"The Differential Amount", prop = "new_changemoneyhandling" },//调差金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Amount", prop = "new_withholdingmoneyhandling" },//预提金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault().ToDefault<string>("new_content"):"Total Cost", prop = "new_settlementmoneyhandling" },//结算金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Fees", prop = "new_withholdingfeehandling" },//预提费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Recoil Fees", prop = "new_withholdingfeerecoilhandling" },//预提反冲费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.QGF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.QGF").FirstOrDefault().ToDefault<string>("new_content"):"Customs Clearance Fee (Transfer)", prop = "new_customsclearancefeetransfer" },//清关费（转派）
                };
            #endregion
            #region 服务单处理方法动态列
            List<approach> approachlist = new List<approach>();
            approachlist = workorder_approach_dic.Values.OrderByDescending(list => list.Count).FirstOrDefault();
            if (approachlist != null && approachlist.Count > 0)
            {
                foreach (var item in approachlist)
                {
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault().ToDefault<string>("new_content") : "Processing Method") + (approachlist.IndexOf(item) + 1),
                        prop = "new_approach_id" + (approachlist.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFFBM").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFFBM").FirstOrDefault().ToDefault<string>("new_content") : "Processing Method Code") + (approachlist.IndexOf(item) + 1),
                        prop = "new_approach_code" + (approachlist.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault().ToDefault<string>("new_content") : "Settlement Level") + (approachlist.IndexOf(item) + 1),
                        prop = "new_settlementlevel" + (approachlist.IndexOf(item) + 1)
                    });
                };
            }
            #endregion
            jofields = fieldLists.Select(a => a.prop).ToList();
            foreach (var item in workordergroupby)
            {
                var firstdata = item.ToList().FirstOrDefault();
                JObject jo = new JObject();
                jo["new_srv_workorderid"] = firstdata.Contains("new_srv_workorderid") ? firstdata.GetAttributeValue<Guid>("new_srv_workorderid") : Guid.Empty;
                jo["new_name"] = firstdata.Contains("new_name") ? firstdata.GetAttributeValue<string>("new_name") : "";
                jo["new_station_id"] = firstdata.Contains("new_station_id") ? firstdata.GetAttributeValue<EntityReference>("new_station_id").Name : "";
                jo["new_servicestation_id"] = firstdata.Contains("new_servicestation_id") ? firstdata.GetAttributeValue<EntityReference>("new_servicestation_id").Name : "";
                jo["new_operator_id"] = firstdata.Contains("new_operator_id") ? firstdata.GetAttributeValue<EntityReference>("new_operator_id").Name : "";
                jo["new_repairprovider_id"] = firstdata.Contains("new_repairprovider_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairprovider_id").Name : "";
                jo["new_repairstation_id"] = firstdata.Contains("new_repairstation_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairstation_id").Name : "";
                jo["new_type"] = firstdata.Contains("new_type") && firstdata.FormattedValues.ContainsKey("new_type") ? firstdata.FormattedValues["new_type"] : "";
                jo["createdon"] = firstdata.Contains("createdon") ? firstdata.GetAttributeValue<DateTime>("createdon").ToString() : "";
                jo["new_endtime"] = firstdata.Contains("new_endtime") ? firstdata.GetAttributeValue<DateTime>("new_endtime").ToString() : "";
                jo["new_closingtime"] = firstdata.Contains("new_closingtime") ? firstdata.GetAttributeValue<DateTime>("new_closingtime").ToString() : "";
                jo["new_uploadcompletion_time"] = firstdata.Contains("new_uploadcompletion_time") ? firstdata.GetAttributeValue<DateTime>("new_uploadcompletion_time").ToString() : "";
                jo["new_category3_id"] = firstdata.Contains("new_category3_id") ? firstdata.GetAttributeValue<EntityReference>("new_category3_id").Name : "";
                jo["new_category2_id"] = firstdata.Contains("new_category2_id") ? firstdata.GetAttributeValue<EntityReference>("new_category2_id").Name : "";
                jo["new_category1_id"] = firstdata.Contains("new_category1_id") ? firstdata.GetAttributeValue<EntityReference>("new_category1_id").Name : "";
                jo["new_model2_id"] = firstdata.Contains("new_model2_id") ? firstdata.GetAttributeValue<EntityReference>("new_model2_id").Name : "";
                jo["new_model1_id"] = firstdata.Contains("new_model1_id") ? firstdata.GetAttributeValue<EntityReference>("new_model1_id").Name : "";
                jo["new_servicemode"] = firstdata.Contains("new_servicemode") && firstdata.FormattedValues.ContainsKey("new_servicemode") ? firstdata.FormattedValues["new_servicemode"] : "";
                jo["new_withholdingmoneyhandling"] = firstdata.Contains("new_withholdingmoneyhandling") ? firstdata.GetAttributeValue<decimal>("new_withholdingmoneyhandling").ToString("#0.00") : "";
                jo["new_changemoneyhandling"] = firstdata.Contains("new_changemoneyhandling") ? firstdata.GetAttributeValue<decimal>("new_changemoneyhandling").ToString("#0.00") : "";
                jo["new_settlementmoneyhandling"] = firstdata.Contains("new_settlementmoneyhandling") ? firstdata.GetAttributeValue<decimal>("new_settlementmoneyhandling").ToString("#0.00") : "";
                jo["new_handlingfee"] = firstdata.Contains("new_handlingfee") ? firstdata.GetAttributeValue<decimal>("new_handlingfee").ToString("#0.00") : "";
                jo["new_handlingfeekpi"] = firstdata.Contains("new_handlingfeekpi") ? firstdata.GetAttributeValue<decimal>("new_handlingfeekpi").ToString("#0.00") : "";
                jo["new_logisticsfeehandling"] = firstdata.Contains("new_logisticsfeehandling") ? firstdata.GetAttributeValue<decimal>("new_logisticsfeehandling").ToString("#0.00") : "";
                jo["new_callfeehandling"] = firstdata.Contains("new_callfeehandling") ? firstdata.GetAttributeValue<decimal>("new_callfeehandling").ToString("#0.00") : "";
                jo["new_othermiscellaneouschargeshandling"] = firstdata.Contains("new_othermiscellaneouschargeshandling") ? firstdata.GetAttributeValue<decimal>("new_othermiscellaneouschargeshandling").ToString("#0.00") : "";
                if (firstdata.Contains("cost.new_customsclearancefeetransfer"))
                    jo["new_customsclearancefeetransfer"] = firstdata.Contains("cost.new_customsclearancefeetransfer") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_customsclearancefeetransfer").ToString("#0.00") : "";
                if (firstdata.Contains("cost.new_withholdingfeehandling"))
                    jo["new_withholdingfeehandling"] = firstdata.Contains("cost.new_withholdingfeehandling") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_withholdingfeehandling").ToString("#0.00") : "";
                if (firstdata.Contains("cost.new_withholdingfeerecoilhandling"))
                    jo["new_withholdingfeerecoilhandling"] = firstdata.Contains("cost.new_withholdingfeerecoilhandling") ? firstdata.GetAliasAttributeValue<decimal>("cost.new_withholdingfeerecoilhandling").ToString("#0.00") : "";
                jo["new_saleamounthandling"] = firstdata.Contains("new_saleamounthandling") ? firstdata.GetAttributeValue<decimal>("new_saleamounthandling").ToString("#0.00") : "";
                jo["new_ordercode"] = firstdata.Contains("new_ordercode") ? firstdata.GetAttributeValue<string>("new_ordercode") : "";
                jo["new_b2x_orderid"] = firstdata.Contains("new_b2x_orderid") ? firstdata.GetAttributeValue<string>("new_b2x_orderid") : "";
                jo["new_new_expenseclaimidhandling"] = firstdata.Contains("new_new_expenseclaimidhandling") ? firstdata.GetAttributeValue<EntityReference>("new_new_expenseclaimidhandling").Name : "";
                jo["new_repairstation_id_new_country_id"] = firstdata.Contains("an.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("an.new_country_id").Name : "";
                jo["new_operator_country"] = firstdata.Contains("ao.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("ao.new_country_id").Name : "";
                jo["new_operator_servicestation"] = firstdata.Contains("ao.new_serviceprovider") ? firstdata.GetAliasAttributeValue<EntityReference>("ao.new_serviceprovider").Name : ""; // 运营商所属服务商
                jo["new_operator_code"] = firstdata.Contains("ao.new_code") ? firstdata.GetAliasAttributeValue<string>("ao.new_code") : ""; // 运营商编码
                jo["new_srv_station_new_code"] = firstdata.Contains("a_s2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s2.new_code") : ""; // 所属服务商编码
                jo["new_srv_station_new_code1"] = firstdata.Contains("a_s2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s1.new_code") : ""; // 所属网点编码
                jo["new_srv_station_new_code3"] = firstdata.Contains("a_s3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s3.new_code") : ""; // 维修服务商编码
                jo["new_srv_station_new_code4"] = firstdata.Contains("an.new_code") ? firstdata.GetAliasAttributeValue<string>("an.new_code") : ""; // 维修网点编码
                jo["new_category1_new_code"] = firstdata.Contains("a_c1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c1.new_code") : ""; // 一级品类编码
                jo["new_category2_new_code"] = firstdata.Contains("a_c2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c2.new_code") : ""; // 二级品类编码
                jo["new_category3_new_code"] = firstdata.Contains("a_c3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c3.new_code") : ""; // 三级品类编码
                jo["new_model1_new_code"] = firstdata.Contains("a_m1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m1.new_code") : ""; // 一级机型编码
                jo["new_model2_new_code"] = firstdata.Contains("a_m2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m2.new_code") : ""; // 二级机型编码
                for (int i = 0; i < approachlist.Count; i++)
                {
                    if (item.ToList().Count > i)
                    {
                        jo["new_approach_id" + (i + 1)] = item.ToList()[i].Contains("aw.new_approach_id") ? item.ToList()[i].GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "";
                        jo["new_approach_code" + (i + 1)] = item.ToList()[i].Contains("ap.new_code") ? item.ToList()[i].GetAliasAttributeValue<string>("ap.new_code") : "";
                        jo["new_settlementlevel" + (i + 1)] = item.ToList()[i].Contains("ap.new_settlementlevel") ? item.ToList()[i].FormattedValues["ap.new_settlementlevel"] : "";
                    }
                    else
                    {
                        //服务单更处理方法最大数量的，赋值为空
                        jo["new_approach_id" + (i + 1)] = "";
                        jo["new_approach_code" + (i + 1)] = "";
                        jo["new_settlementlevel" + (i + 1)] = "";
                    }
                };
                ja.Add(jo);
            }
            // 遍历 JArray 中的每个 JObject
            foreach (JObject jsonObject in ja)
            {
                // 获取 JObject 的所有属性（字段）
                IEnumerable<JProperty> properties = jsonObject.Properties();

                // 遍历并打印当前 JObject 的每个属性的名称（字段名）
                foreach (JProperty property in properties)
                {
                    if (!jofields.Contains(property.Name))
                        jofields.Add(property.Name);
                }
            }
            var fieldfilter = fieldLists.Where(a => jofields.Contains(a.prop)).ToList();
            ja_fields = JArray.Parse(JsonConvert.SerializeObject(fieldfilter));
            JObject joresult = new JObject();
            joresult["data"] = ja;
            joresult["fields"] = ja_fields;
            return joresult;
        }
        /// <summary>
        /// 查询结算单类型 = 安装的结算单的工单明细
        /// </summary>
        /// <param name="expenseclaimid"></param>
        /// <param name="providertype"></param>
        /// <param name="adminService"></param>
        /// <param name="service"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public JObject WorkorderdetialViewdataInstall(string expenseclaimid, int providertype, IOrganizationService adminService, IOrganizationService service, IPluginExecutionContext context) 
        {
            string fetchxml = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
          <entity name='new_srv_workorder'>
            <attribute name='new_srv_workorderid' />
            <attribute name='new_name' />
			<attribute name='new_station_id' />
			<attribute name='new_type' />
			<attribute name='createdon' />
			<attribute name='new_endtime' />
			<attribute name='new_srv_workorder_id' />
			<attribute name='new_repairstation_id' />
			<attribute name='new_warranty' />
			<attribute name='new_category3_id' />
			<attribute name='new_model2_id' />
			<attribute name='new_servicemode' />
			<attribute name='new_uploadcompletion_time' />
			<attribute name='new_goodsfiles_id' />
			<attribute name='new_upload_orderid' />
			<attribute name='new_materialdetails' />
			<attribute name='new_imei' />
			<attribute name='new_sn' />
			<attribute name='new_servicestation_id' />
			<attribute name='new_category1_id' />
			<attribute name='new_category2_id' />
			<attribute name='new_closingtime' />
			<attribute name='new_sub_station_type' />
			<attribute name='new_return_channel' />
			<attribute name='new_logistic_zone' />
			<attribute name='new_model1_id' />
			<attribute name='new_repairprovider_id' />
			<attribute name='new_srv_workorderid' />
			<attribute name='new_uploadcompletion_time' />
			<attribute name='new_service_duration' />
			<attribute name='new_ordercode' />
			<order attribute='createdon' descending='true' />
			<filter type='and'>
				<condition attribute='statecode' operator='eq' value='0' />
			</filter>
			<link-entity name='new_workorder_costtable' from='new_workorder_costtableid' to='new_workorder_costtable_id' visible='false' link-type='inner' alias='az'>
				<attribute name='new_withholdingmoneyinstall' />
				<attribute name='new_settlementmoneyinstall' />
				<attribute name='new_changemoneyinstall' />
				<attribute name='new_installfee' />
				<attribute name='new_installfeekpi' />
				<attribute name='new_othermiscellaneouschargesinstall' />
				<attribute name='new_withholdingfeeinstall' />
				<attribute name='new_withholdingfeerecoilinstall' />
				<attribute name='new_fixedmonthfeeinstall' />
				<attribute name='new_expenseclaimidinstall' />
                <link-entity name='transactioncurrency' from='transactioncurrencyid' to='new_transactioncurrency_installservice_id' visible='false' link-type='outer' alias='at'>
				<attribute name='isocurrencycode' />
			    </link-entity>
                <filter type='and'>
				<condition attribute='new_expenseclaimidinstall' operator='eq' value='{expenseclaimid}' />
			    </filter>
			</link-entity>
			<link-entity name='new_category1' from='new_category1id' to='new_category1_id' visible='false' link-type='outer' alias='a_c1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model1' from='new_model1id' to='new_model1_id' visible='false' link-type='outer' alias='a_m1'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_model2' from='new_model2id' to='new_model2_id' visible='false' link-type='outer' alias='a_m2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category3' from='new_category3id' to='new_category3_id' visible='false' link-type='outer' alias='a_c3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_category2' from='new_category2id' to='new_category2_id' visible='false' link-type='outer' alias='a_c2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_station_id' visible='false' link-type='outer' alias='a_s1'>
				<attribute name='new_stietype' />
				<attribute name='new_country_id' />
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_servicestation_id' visible='false' link-type='outer' alias='a_s2'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairprovider_id' visible='false' link-type='outer' alias='a_s3'>
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_station' from='new_srv_stationid' to='new_repairstation_id' visible='false' link-type='outer' alias='a_s4'>
				<attribute name='new_stietype' />
				<attribute name='new_country_id' />
				<attribute name='new_code' />
			</link-entity>
			<link-entity name='new_srv_workorder_approach' from='new_srv_workorder_id' to='new_srv_workorderid' visible='false' link-type='outer' alias='aw'>
				<attribute name='new_approach_id' />
				<link-entity name='new_approach' from='new_approachid' to='new_approach_id' visible='false' link-type='outer' alias='ap'>
					<attribute name='new_name' />
					<attribute name='new_settlementlevel' />
				</link-entity>
			</link-entity>
            <link-entity name='new_srv_partline' from='new_srv_workorder_id' to='new_srv_workorderid' link-type='outer' alias='ac'>
                <attribute name='new_srv_partlineid' />
                <attribute name='new_name' />
                <attribute name='createdon' />
                <attribute name='new_action_reason' />
                <attribute name='new_srv_workorder_id' />
                <filter type='and'>
                  <condition attribute='statecode' operator='eq' value='0' />
                </filter>
                <link-entity name='product' from='productid' to='new_productnew_id' visible='false' link-type='outer' alias='a_p'>
				<attribute name='productnumber' />
				<attribute name='name' />
			    </link-entity>
            </link-entity>
	</entity>
</fetch>";
            EntityCollection workorderdetailcols = Common.CommonHelper.QueryXmlPagePassPlugin(service, fetchxml);
            Dictionary<Guid, List<partline>> workorder_partline_dic = new Dictionary<Guid, List<partline>>();
            Dictionary<Guid, List<approach>> workorder_approach_dic = new Dictionary<Guid, List<approach>>();
            //根据服务单进行分组
            var workordergroupby = workorderdetailcols.Entities.GroupBy(a => a.GetAttributeValue<Guid>("new_srv_workorderid"));
            foreach (var item in workordergroupby)
            {
                var itemlist = item.ToList();
                var partline = itemlist.Where(a => a.Contains("ac.new_action_reason")
                || a.Contains("a_p.productnumber")).Select(p => new partline()
                {
                    new_action_reason = p.Contains("ac.new_action_reason") ? p.FormattedValues["ac.new_action_reason"] : "",
                    productnumber = p.Contains("a_p.productnumber") ? p.GetAliasAttributeValue<string>("a_p.productnumber") : ""
                }).ToList();
                partline = partline.GroupBy(a => new { a.productnumber, a.new_action_reason }) // 假设我们根据Id去重  
                      .Select(group => group.First()) // 选择每组中的第一个元素  
                      .ToList();
                workorder_partline_dic.Add(item.Key, partline);
                var approach = itemlist.Where(a => a.Contains("aw.new_approach_id")
                || a.Contains("ap.new_settlementlevel")).Select(p => new approach()
                {
                    new_approach_id = p.Contains("aw.new_approach_id") ? p.GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "",
                    new_settlementlevel = p.Contains("ap.new_settlementlevel") ? p.FormattedValues["ap.new_settlementlevel"] : ""
                }).ToList();
                approach = approach.GroupBy(a => new { a.new_approach_id, a.new_settlementlevel }) // 假设我们根据Id去重  
                      .Select(group => group.First()) // 选择每组中的第一个元素  
                      .ToList();
                workorder_approach_dic.Add(item.Key, approach);
            }
            int langid = GetCurrentUserLangId(context.UserId, adminService);
            var languagedata = GetResourceWorkorderdetail(langid.ToString(), "ExpenseStatement.Workorderdetail.CustomizeView.%", adminService);
            #region
            List<FieldList> fieldLists = new List<FieldList>
                {
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWDH").FirstOrDefault().ToDefault<string>("new_content"):"Service Order Number", prop = "new_name" },//服务单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDLX").FirstOrDefault().ToDefault<string>("new_content"):"Service Type", prop = "new_type" },//工单类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BNBW").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BNBW").FirstOrDefault().ToDefault<string>("new_content"):"OOW/IW", prop = "new_warranty" },//保内/保外
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWFS").FirstOrDefault().ToDefault<string>("new_content"):"Service Mode", prop = "new_servicemode" },//服务方式
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZLWF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZLWF").FirstOrDefault().ToDefault<string>("new_content"):"Install Labor Costs", prop = "new_installfee" },//安装劳务费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZLWFKPI").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZLWFKPI").FirstOrDefault().ToDefault<string>("new_content"):"Install Labor Costs (KPI)", prop = "new_installfeekpi" },//安装劳务费KPI
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Fees", prop = "new_withholdingfeeinstall" },//预提费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTFCF").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Recoil Fees", prop = "new_withholdingfeerecoilinstall" },//预提反冲费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZGDYDF").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.AZGDYDF").FirstOrDefault().ToDefault<string>("new_content"):"Install Fixed monthly fee", prop = "new_fixedmonthfeeinstall" },//安装固定月度费
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.TCJE").FirstOrDefault().ToDefault<string>("new_content"):"The Differential Amount", prop = "new_changemoney" },//调差金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YTJE").FirstOrDefault().ToDefault<string>("new_content"):"Withholding Amount", prop = "new_withholdingmoney" },//预提金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSJE").FirstOrDefault().ToDefault<string>("new_content"):"Total Cost", prop = "new_settlementmoney" },//结算金额
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.CJSJ").FirstOrDefault().ToDefault<string>("new_content"):"Createdon", prop = "createdon" },//创建时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.FWJSSJ").FirstOrDefault().ToDefault<string>("new_content"):"Service Finish Time", prop = "new_endtime" },//服务结束时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SCWCSJ").FirstOrDefault().ToDefault<string>("new_content"):"Work Order Upload Completion Time", prop = "new_uploadcompletion_time" },//工单上传完成时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SPDA").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SPDA").FirstOrDefault().ToDefault<string>("new_content"):"Commodity Archives", prop = "new_goodsfiles_id" },//商品档案
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLMX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WLMX").FirstOrDefault().ToDefault<string>("new_content"):"Material Details", prop = "new_materialdetails" },//物料明细
                    new FieldList{ label = "IMEI", prop = "new_imei" },//IMEI
                    new FieldList{ label = "SN", prop = "new_sn" },//SN
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BZ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.BZ").FirstOrDefault().ToDefault<string>("new_content"):"Settlement Currency", prop = "new_transactioncurrency_service_id" },//结算币种
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.DDH").FirstOrDefault().ToDefault<string>("new_content"):"Order No.", prop = "new_ordercode" },//订单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.JSDH").FirstOrDefault().ToDefault<string>("new_content"):"Expense Settlement Number", prop = "new_expenseclaimid" },//结算单号
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category", prop = "new_category1_id" },//一级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Category Id", prop = "new_category1_new_code" },//一级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category", prop = "new_category2_id" },//二级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Category Id", prop = "new_category2_new_code" },//二级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPL").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_id" },//三级品类
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJPLBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv3 Category", prop = "new_category3_new_code" },//三级品类编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model", prop = "new_model1_id" },//一级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.YJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv1 Model Id", prop = "new_model1_new_code" },//一级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJX").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model", prop = "new_model2_id" },//二级机型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.EJJXBM").FirstOrDefault().ToDefault<string>("new_content"):"Lv2 Model Id", prop = "new_model2_new_code" },//二级机型编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWS").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider", prop = "new_servicestation_id" },//所属服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Provider Code", prop = "new_srv_station_new_code" },//所属服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWD").FirstOrDefault().ToDefault<string>("new_content"):"Service Station", prop = "new_station_id" },//所属网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SHWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Service Station Code", prop = "new_srv_station_new_code1" },//所属网点编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWS").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider", prop = "new_repairprovider_id" },//维修服务商
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXFWSBM").FirstOrDefault().ToDefault<string>("new_content"):"Maintenance provider Code", prop = "new_srv_station_new_code3" },//维修服务商编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWD").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station", prop = "new_repairstation_id" },//维修网点
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDBM").FirstOrDefault().ToDefault<string>("new_content"):"Repair Station Code", prop = "new_srv_station_new_code4" },//维修网点编码
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDLX").FirstOrDefault().ToDefault<string>("new_content"):"Center Type (repair station)", prop = "new_srv_station_new_stietype" },//维修网点类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDGJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.WXWDGJ").FirstOrDefault().ToDefault<string>("new_content"):"Country/Region (repair station)", prop = "new_srv_station_new_country_id" },//维修网点国家
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDLX").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDLX").FirstOrDefault().ToDefault<string>("new_content"):"Center Type (Service Station)", prop = "new_srv_station_new_stietype1" },//所属网点类型
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDGJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SSWDGJ").FirstOrDefault().ToDefault<string>("new_content"):"Country/Region (Service Station)", prop = "new_srv_station_new_country_id1" },//所属网点国家
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.GDSJ").FirstOrDefault().ToDefault<string>("new_content"):"Closing time", prop = "new_closingtime" },//关单时间
                    new FieldList{ label = languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJFWSC").FirstOrDefault() != null? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                    == "ExpenseStatement.Workorderdetail.CustomizeView.SJFWSC").FirstOrDefault().ToDefault<string>("new_content"):"Service hours‌", prop = "new_service_duration" }//服务时长(小时)
                };
            #endregion
            JArray ja = new JArray();
            JArray ja_fields = new JArray();
            #region 服务单更换件明细动态列
            List<partline> partlines = new List<partline>();
            partlines = workorder_partline_dic.Values.OrderByDescending(list => list.Count).FirstOrDefault();
            if (partlines != null && partlines.Count > 0)
            {
                foreach (var item in partlines)
                {
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.WLBM").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.WLBM").FirstOrDefault().ToDefault<string>("new_content") : "Part Code") + (partlines.IndexOf(item) + 1),
                        prop = "productnumber" + (partlines.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.MaitroxConsign.DZLY").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.MaitroxConsign.DZLY").FirstOrDefault().ToDefault<string>("new_content") : "Action Reason") + (partlines.IndexOf(item) + 1),
                        prop = "new_action_reason" + (partlines.IndexOf(item) + 1)
                    });
                };
            }
            #endregion
            #region 服务单处理方法动态列
            List<approach> approachlist = new List<approach>();
            approachlist = workorder_approach_dic.Values.OrderByDescending(list => list.Count).FirstOrDefault();
            if (approachlist != null && approachlist.Count > 0)
            {
                foreach (var item in approachlist)
                {
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.CLFF").FirstOrDefault().ToDefault<string>("new_content") : "Processing Method") + (approachlist.IndexOf(item) + 1),
                        prop = "new_approach_id" + (approachlist.IndexOf(item) + 1)
                    });
                    fieldLists.Add(new FieldList
                    {
                        label = (languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault() != null ? languagedata.Entities.Where(a => a.ToDefault<string>("new_name")
                     == "ExpenseStatement.Workorderdetail.CustomizeView.JFJB").FirstOrDefault().ToDefault<string>("new_content") : "Settlement Level") + (approachlist.IndexOf(item) + 1),
                        prop = "new_settlementlevel" + (approachlist.IndexOf(item) + 1)
                    });
                };
            }
            #endregion
            foreach (var item in workordergroupby)
            {
                var firstdata = item.ToList().FirstOrDefault();
                JObject jo = new JObject();
                jo["new_srv_workorderid"] = firstdata.Contains("new_srv_workorderid") ? firstdata.GetAttributeValue<Guid>("new_srv_workorderid") : Guid.Empty;
                jo["new_name"] = firstdata.Contains("new_name") ? firstdata.GetAttributeValue<string>("new_name") : "";
                jo["new_ordercode"] = firstdata.Contains("new_ordercode") ? firstdata.GetAttributeValue<string>("new_ordercode") : "";
                jo["new_station_id"] = firstdata.Contains("new_station_id") ? firstdata.GetAttributeValue<EntityReference>("new_station_id").Name : "";
                jo["new_type"] = firstdata.Contains("new_type") && firstdata.FormattedValues.ContainsKey("new_type") ? firstdata.FormattedValues["new_type"] : "";
                jo["new_installfee"] = firstdata.Contains("az.new_installfee") ? firstdata.GetAliasAttributeValue<decimal>("az.new_installfee").ToString("#0.00") : "";
                jo["new_installfeekpi"] = firstdata.Contains("az.new_installfeekpi") ? firstdata.GetAliasAttributeValue<decimal>("az.new_installfeekpi").ToString("#0.00") : "";
                jo["new_withholdingfeeinstall"] = firstdata.Contains("az.new_withholdingfeeinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_withholdingfeeinstall").ToString("#0.00") : "";
                jo["new_withholdingfeerecoilinstall"] = firstdata.Contains("az.new_withholdingfeerecoilinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_withholdingfeerecoilinstall").ToString("#0.00") : "";
                jo["new_fixedmonthfeeinstall"] = firstdata.Contains("az.new_fixedmonthfeeinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_fixedmonthfeeinstall").ToString("#0.00") : "";
                jo["new_changemoney"] = firstdata.Contains("az.new_changemoneyinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_changemoneyinstall").ToString("#0.00") : "";
                jo["new_specialfeeshare"] = firstdata.Contains("az.new_othermiscellaneouschargesinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_othermiscellaneouschargesinstall").ToString("#0.00") : "";
                jo["new_withholdingmoney"] = firstdata.Contains("az.new_withholdingmoneyinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_withholdingmoneyinstall").ToString("#0.00") : "";
                jo["new_settlementmoney"] = firstdata.Contains("az.new_settlementmoneyinstall") ? firstdata.GetAliasAttributeValue<decimal>("az.new_settlementmoneyinstall").ToString("#0.00") : "";
                jo["new_expenseclaimid"] = firstdata.Contains("az.new_expenseclaimidinstall") ? firstdata.GetAliasAttributeValue<EntityReference>("az.new_expenseclaimidinstall").Name : "";
                jo["createdon"] = firstdata.Contains("createdon") ? firstdata.GetAttributeValue<DateTime>("createdon").ToString() : "";
                jo["new_endtime"] = firstdata.Contains("new_endtime") ? firstdata.GetAttributeValue<DateTime>("new_endtime").ToString() : "";
                jo["new_uploadcompletion_time"] = firstdata.Contains("new_uploadcompletion_time") ? firstdata.GetAttributeValue<DateTime>("new_uploadcompletion_time").ToString() : "";
                jo["new_srv_workorder_id"] = firstdata.Contains("new_srv_workorder_id") ? firstdata.GetAttributeValue<EntityReference>("new_srv_workorder_id").Name : "";
                jo["new_repairstation_id"] = firstdata.Contains("new_repairstation_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairstation_id").Name : "";
                jo["new_warranty"] = firstdata.Contains("new_warranty") && firstdata.FormattedValues.ContainsKey("new_warranty") ? firstdata.FormattedValues["new_warranty"] : "";
                jo["new_category3_id"] = firstdata.Contains("new_category3_id") ? firstdata.GetAttributeValue<EntityReference>("new_category3_id").Name : "";
                jo["new_model2_id"] = firstdata.Contains("new_model2_id") ? firstdata.GetAttributeValue<EntityReference>("new_model2_id").Name : "";
                jo["new_servicemode"] = firstdata.Contains("new_servicemode") && firstdata.FormattedValues.ContainsKey("new_servicemode") ? firstdata.FormattedValues["new_servicemode"] : "";
                jo["new_iswriteno"] = firstdata.Contains("new_iswriteno") ? firstdata.FormattedValues["new_iswriteno"] : "";
                jo["new_goodsfiles_id"] = firstdata.Contains("new_goodsfiles_id") ? firstdata.GetAttributeValue<EntityReference>("new_goodsfiles_id").Name : "";
                jo["new_upload_orderid"] = firstdata.Contains("new_upload_orderid") ? firstdata.GetAttributeValue<string>("new_upload_orderid") : "";
                jo["new_materialdetails"] = firstdata.Contains("new_materialdetails") ? firstdata.GetAttributeValue<string>("new_materialdetails") : "";
                jo["new_imei"] = firstdata.Contains("new_imei") ? firstdata.GetAttributeValue<string>("new_imei") : "";
                jo["new_sn"] = firstdata.Contains("new_sn") ? firstdata.GetAttributeValue<string>("new_sn") : "";
                jo["new_service_duration"] = firstdata.Contains("new_service_duration") ? firstdata.GetAttributeValue<decimal>("new_service_duration").ToString("#0.00") : "";
                jo["new_transactioncurrency_service_id"] = firstdata.Contains("at.isocurrencycode") ? firstdata.GetAliasAttributeValue<string>("at.isocurrencycode") : "";
                jo["new_servicestation_id"] = firstdata.Contains("new_servicestation_id") ? firstdata.GetAttributeValue<EntityReference>("new_servicestation_id").Name : "";
                jo["new_category1_id"] = firstdata.Contains("new_category1_id") ? firstdata.GetAttributeValue<EntityReference>("new_category1_id").Name : "";
                jo["new_category2_id"] = firstdata.Contains("new_category2_id") ? firstdata.GetAttributeValue<EntityReference>("new_category2_id").Name : "";
                jo["new_model1_id"] = firstdata.Contains("new_model1_id") ? firstdata.GetAttributeValue<EntityReference>("new_model1_id").Name : "";
                jo["new_closingtime"] = firstdata.Contains("new_closingtime") ? firstdata.GetAttributeValue<DateTime>("new_closingtime").ToString() : "";
                jo["new_repairprovider_id"] = firstdata.Contains("new_repairprovider_id") ? firstdata.GetAttributeValue<EntityReference>("new_repairprovider_id").Name : "";
                jo["new_category1_new_code"] = firstdata.Contains("a_c1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c1.new_code") : "";
                jo["new_category2_new_code"] = firstdata.Contains("a_c2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c2.new_code") : "";
                jo["new_category3_new_code"] = firstdata.Contains("a_c3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c3.new_code") : "";
                jo["new_category3_new_code"] = firstdata.Contains("a_c3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_c3.new_code") : "";
                jo["new_model1_new_code"] = firstdata.Contains("a_m1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m1.new_code") : "";
                jo["new_model2_new_code"] = firstdata.Contains("a_m2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_m2.new_code") : "";
                jo["new_srv_station_new_country_id"] = firstdata.Contains("a_s4.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("a_s4.new_country_id").Name : "";
                jo["new_srv_station_new_country_id1"] = firstdata.Contains("a_s1.new_country_id") ? firstdata.GetAliasAttributeValue<EntityReference>("a_s1.new_country_id").Name : "";
                jo["new_srv_station_new_code"] = firstdata.Contains("a_s2.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s2.new_code") : "";
                jo["new_srv_station_new_code1"] = firstdata.Contains("a_s1.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s1.new_code") : "";
                jo["new_srv_station_new_code3"] = firstdata.Contains("a_s3.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s3.new_code") : "";
                jo["new_srv_station_new_code4"] = firstdata.Contains("a_s4.new_code") ? firstdata.GetAliasAttributeValue<string>("a_s4.new_code") : "";
                jo["new_srv_station_new_stietype"] = firstdata.Contains("a_s4.new_stietype") ? firstdata.FormattedValues["a_s4.new_stietype"] : "";
                jo["new_srv_station_new_stietype1"] = firstdata.Contains("a_s1.new_stietype") ? firstdata.FormattedValues["a_s1.new_stietype"] : "";
                
                for (int i = 0; i < partlines.Count; i++)
                {
                    if (item.ToList().Count > i)
                    {
                        jo["productnumber" + (i + 1)] = item.ToList()[i].Contains("a_p.productnumber") ? item.ToList()[i].GetAliasAttributeValue<string>("a_p.productnumber") : "";
                        jo["new_action_reason" + (i + 1)] = item.ToList()[i].Contains("ac.new_action_reason") ? item.ToList()[i].FormattedValues["ac.new_action_reason"] : "";
                    }
                    else
                    {
                        //服务单更换件明细小于最大数量的，物料编码，动作路由赋值为空
                        jo["productnumber" + (i + 1)] = "";
                        jo["new_action_reason" + (i + 1)] = "";
                    }
                };
                for (int i = 0; i < approachlist.Count; i++)
                {
                    if (item.ToList().Count > i)
                    {
                        jo["new_approach_id" + (i + 1)] = item.ToList()[i].Contains("aw.new_approach_id") ? item.ToList()[i].GetAliasAttributeValue<EntityReference>("aw.new_approach_id").Name : "";
                        jo["new_settlementlevel" + (i + 1)] = item.ToList()[i].Contains("ap.new_settlementlevel") ? item.ToList()[i].FormattedValues["ap.new_settlementlevel"] : "";
                    }
                    else
                    {
                        //服务单更换件明细小于最大数量的，旧件编码，新建编码赋值为空
                        jo["new_approach_id" + (i + 1)] = "";
                        jo["new_settlementlevel" + (i + 1)] = "";
                    }
                };
                ja.Add(jo);
            }
            ja_fields = JArray.Parse(JsonConvert.SerializeObject(fieldLists));
            JObject joresult = new JObject();
            joresult["data"] = ja;
            joresult["fields"] = ja_fields;
            return joresult;
        }
    }
}
