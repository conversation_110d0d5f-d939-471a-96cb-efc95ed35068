﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RekTec.ServiceSettlement.Function.Model
{
    public class X5DataParam
    {
        /// <summary>
        /// 头
        /// </summary>
        public X5DataHeader header { get; set; }
        /// <summary>
        /// 正文
        /// </summary>
        public string body { get; set; }
    }
    /// <summary>
    /// X5验证的header参数
    /// </summary>
    public class X5DataHeader
    {
        /// <summary>
        /// appid
        /// </summary>
        public string appid { get; set; }
        /// <summary>
        /// 签名
        /// </summary>
        public string sign { get; set; }
        public string url { get; set; }
        public string method { get; set; }
    }
    /// <summary>
    /// X5验证的header参数
    /// </summary>
    public class XMSX5DataHeader
    {
        /// <summary>
        /// appid
        /// </summary>
        public string appid { get; set; }
        /// <summary>
        /// 签名
        /// </summary>
        public string sign { get; set; }
        public string url { get; set; }
        public string id { get; set; }
        public string project { get; set; }
        public List<string> index { get; set; }
    }
    public class XMSX5DataParam
    {
        /// <summary>
        /// 头
        /// </summary>
        public XMSX5DataHeader header { get; set; }
        /// <summary>
        /// 正文
        /// </summary>
        public string body { get; set; }
    }
    public class InterfaceReturnModel
    {
        public SapResultHeader header = new SapResultHeader();
    }

    public class SapResultHeader
    {
        public string code { get; set; }
        public string desc { get; set; }
    }
    public class XMSResultHeader
    {
        public string statusCode { get; set; }
        public string message { get; set; }
        public XMSResultdata data = new XMSResultdata();
    }
    public class XMSResultdata
    {
        public string sync_result { get; set; }
        public string sync_fail_reason { get; set; }
        public string data_id { get; set; }
    }
    public class NotifyCustomerModel 
    {
        public int notify_code { get; set; }
    }
}
