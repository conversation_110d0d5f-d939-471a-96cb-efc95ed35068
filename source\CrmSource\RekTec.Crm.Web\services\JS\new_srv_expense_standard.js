﻿/// <reference path="../common/XrmPage-vsdoc.js" />
/// <reference path="../common/new_crm.common.js" />
/// <reference path="../common/new_crm.biz.js" />

//#region 文件描述
/******************************************************************
** Copyright @ 苏州瑞泰信息技术有限公司 All rights reserved. 
** 创建人   : EsterWang
** 创建时间 : 2021-9-6 22:26:33
** 说明     :    服务单
******************************************************************/
//#endregion

function form_load() {
    //节显示控制
    this.initandchange();
    //所属服务商筛选控制
    this.new_stationservice_idLookup();
    //服务网点筛选控制
    this.new_station_idLookup();
    //梯度字段显示控制
    this.gradientstartorend_showorhidden();
    //商品sku是否必填
    this.new_goodsfiles_idIsRequired();
    //服务商必填校验
    this.new_stationservice_idIsRequired();
    //网点类型必填校验
    this.new_stietypeIsRequired();
    //一级品类必填
    new_category1_idRequired();
    //国家必填
    new_country_idRequired();
    //百分比必填
    new_ratioRequired();
    //权重必填
    new_costweightRequired();
    //工单量必填
    new_workordernumberRequired();
    //币种设置非必填
    new_transactioncurrency_idNoneRequired();
    //物料名称设置必填
    new_productid_Required();
    //Return channel 设置必填
    new_return_channel_Required();
    //Action reason 设置必填
    new_action_reason_Required();
    //Direction 设置必填
    new_direction_Required();
    //服务网点 设置必填
    new_station_id_Required();
    //结费级别设置必填
    new_settlementlevel_Required();
    // 运营商Operator设置必填
    new_operator_id_Required();
    //费用标签设置必填
    new_feelabel_Required();
    //二级品类必填
    new_category2_idRequired();
    //三级品类必填
    new_category3_idRequired();
    // 物流费 - section_logistics - 字段根据服务商、费用类型实现动态显隐、必填
    section_logistics_setRequiredAndVisible();
    // 安装劳务费 - section_install - 字段根据费用类型实现动态显隐、必填
    section_install_setRequiredAndVisible();
    //安装劳务费
    var feetype = rtcrm("#new_feetype").val();
    if (feetype == 156)
        Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_install").setVisible(true);
    else
        Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_install").setVisible(false);
    //填写了处理方法，则结费级别改为非填项
    new_approach_idOnchange();
}
function new_feetype_onchange() {
    this.initandchange();
    //商品sku是否必填
    this.new_goodsfiles_idIsRequired();
    //服务商必填校验
    this.new_stationservice_idIsRequired();
    //网点类型必填校验
    this.new_stietypeIsRequired();
    //一级品类必填
    new_category1_idRequired();
    //国家必填
    new_country_idRequired();
    //百分比必填
    new_ratioRequired();
    //权重必填
    new_costweightRequired();
    //工单量必填
    new_workordernumberRequired();
    //币种设置非必填
    new_transactioncurrency_idNoneRequired();
    //物料名称设置必填
    new_productid_Required();
    //Return channel 设置必填
    new_return_channel_Required();
    //Action reason 设置必填
    new_action_reason_Required();
    //Direction 设置必填
    new_direction_Required();
    //服务网点 设置必填
    new_station_id_Required();
    //结费级别设置必填
    new_settlementlevel_Required();
    new_category2_idRequired();
    // 运营商Operator设置必填
    new_operator_id_Required();
    //三级品类必填
    new_category3_idRequired();
    // 物流费 - section_logistics - 字段根据服务商、费用类型实现动态显隐、必填
    section_logistics_setRequiredAndVisible();
    section_install_setRequiredAndVisible();
    //费用标签设置必填
    new_feelabel_Required();
}
//商品sku是否必填
function new_goodsfiles_idIsRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    if (feetype == 6) {
        rtcrm("#new_goodsfiles_id").req("required");
    } else {
        rtcrm("#new_goodsfiles_id").req("none");
    }
}
//服务商是否必填
function new_stationservice_idIsRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //保险手工费，逆向物流费，翻新费，Handling费（运营商），物流费（运营商） 服务商非必填
    if (feetype == 13 || feetype == 14 || feetype == 36 || feetype == 43 || feetype == 44) {
        rtcrm("#new_stationservice_id").req("none");
    } else {
        rtcrm("#new_stationservice_id").req("required");
    }
}
//窗体中节的显示控制
function initandchange() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //维修劳务费用  1
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_repair").setVisible(feetype == 1);
    //是否按单量结算  1,6
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_public").setVisible(feetype == 1 || feetype == 6 || feetype == 18);
    //箱子费用  2
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_box").setVisible(feetype == 2);
    //电话回访  3
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_phone").setVisible(feetype == 3);
    //寄修补贴  4
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_mailrepair").setVisible(feetype == 4);
    //路程补贴  5
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_distance").setVisible(feetype == 5);
    //收集点补贴 6
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_collectionpoint").setVisible(feetype == 6);
    //合规激活  7
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_compliance").setVisible(feetype == 7);
    //运营商HF  8
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_operator").setVisible(feetype == 8);
    //上门物流  9
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_ondoor").setVisible(feetype == 9);
    //录单费  10
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_recording").setVisible(feetype == 10);
    //保底费  11
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_minimum").setVisible(feetype == 11);
    //保外手工费  12
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_manual").setVisible(feetype == 12);
    //保险手工费  13
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_manualhadding").setVisible(feetype == 13);
    //逆向物流费  14
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_reverse").setVisible(feetype == 14);
    //检测费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_Inspectionfee").setVisible(feetype == 15);
    //印尼补贴费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_specificsubsidy").setVisible(feetype == 16);
    //远程维修费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_maintenancefee").setVisible(feetype == 18);
    //备件服务费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_partservicecost").setVisible(feetype == 20);
    //Local buy markup费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_markupreplacementcost").setVisible(feetype == 21);
    ////固定仓储费
    //Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_warehousingfee").setVisible(feetype == 22);
    //物流费markup
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_markuplogisticsfee").setVisible(feetype == 23);
    //备件补贴费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_sparepartsallowance").setVisible(feetype == 24);
    //固定服务费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_fixedservicefee").setVisible(feetype == 25);
    //资本利息费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_capitalinterestexpense").setVisible(feetype == 26);
    //最低工单量
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_minorderquantity").setVisible(feetype == 27);
    //分摊权重
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_weightconfig").setVisible(feetype == 28);
    //迈创备件价格模版
    //Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_maitroxpartprice").setVisible(feetype == 29);
    //预提费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_withholdingfee").setVisible(feetype == 41);

    //对外呼叫费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_outboundcallfee").setVisible(feetype == 32);
    //录单费（B2X）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_recordingfeeb2x").setVisible(feetype == 33);
    //箱子费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_boxfee").setVisible(feetype == 34);
    //翻新费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_refurbishfee").setVisible(feetype == 36);
    //电视保护材料费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_protection_materialfee").setVisible(feetype == 37);
    //清关费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_customsfee").setVisible(feetype == 31);
    //物流费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_logisticsfeeb2x").setVisible(feetype == 30);
    //空箱物料费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_boxlogisticsfee").setVisible(feetype == 35);
    //备件费（Buysell）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_partfeeBuysell").setVisible(feetype == 38);
    //备件费Markup（Buysell）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_partfeeBuysellMarkup").setVisible(feetype == 39);
    //local buy Markup（Buysell）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_localbuyBuysellMarkup").setVisible(feetype == 40);
    // Handling费（运营商
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_handlingfee_operator").setVisible(feetype == 43);
    // 物流费（运营商）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_logisticsfee_operator").setVisible(feetype == 44);
    // Handling费（转派）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_handlingfee_collection").setVisible(feetype == 45);
    // 物流费（转派）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_logisticsfee_collection").setVisible(feetype == 46);
    //激活费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_avtivatefee").setVisible(feetype == 42);
    //租金固定费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_fixedrendfee").setVisible(feetype == 47);
    //租金-货架单价
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_shelfrentalfee").setVisible(feetype == 48);
    //租金-扩仓单价
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_warehouseexpansionfee").setVisible(feetype == 49);
    //人力固定费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_fixedlaborcost").setVisible(feetype == 50);
    //人力-变动部分费用
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_variablelaborcost").setVisible(feetype == 51);
    //运营固定费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_fixedoperationcosts").setVisible(feetype == 52);
    //运营变动费-电视操作费、运营变动费-出入库操作费、运营变动费-大小件操作费、运营变动费-入库上架费、运营变动费-打包费、运营变动费-质检费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_operationalchangecost").setVisible(feetype == 53 || feetype == 55 || feetype == 56 || feetype == 57 || feetype == 58 || feetype == 59);
    //物流费（仓储）
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_logisticsfee_warehousing").setVisible(feetype == 54);

    // 物流费相关
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_logistics").setVisible(feetype >= 101 && feetype <= 155);
    //安装劳务费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_install").setVisible(feetype == 156);
    //售后网点-半自营费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_semiselfemployed").setVisible(feetype == 157);
    //高维工厂劳务费
    Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_dimensional").setVisible(feetype == 158);
    // 延迟执行函数（单位：毫秒）
    setTimeout(() => {
        if (feetype == 156)
            Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_install").setVisible(true);
        else
            Xrm.Page.ui.tabs.get("tab_routine").sections.get("section_install").setVisible(false);
    }, 1000);
}

function new_stationservice_id_onchange() {
    this.new_stationservice_idLookup();
    // 物流费 - section_logistics - 字段根据服务商、费用类型实现动态显隐、必填
    section_logistics_setRequiredAndVisible();
}
function new_station_idonchange() {
    this.new_station_idLookup();
    //根据服务网点带出服务商
    var servicestation = rtcrm("#new_stationservice_id").val();
    if (servicestation != null && servicestation != "") {
        return;
    }
    var station = rtcrm("#new_station_id").val();
    if (station != null && station != "") {
        var stationid = station[0].id.replace('{', '').replace('}', '');
        var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>" +
            "<entity name='new_srv_station' >" +
            "<attribute name='new_srv_station_id' />" +
            "<filter type='and'>" +
            "<condition attribute='statecode' operator='eq' value='0' />" +
            "<condition attribute='new_srv_stationid' operator='eq' value='" + stationid + "' />" +
            " </filter>" +
            "</entity>" +
            "</fetch >";
        var stationdata = rtcrm.fetch("new_srv_stations", fetchXml, true);
        if (stationdata && stationdata.length > 0) {
            rtcrm("#new_stationservice_id").val([{ id: stationdata[0]._new_srv_station_id_value, name: stationdata[0]["<EMAIL>"], entityType: "new_srv_station" }]);
        }
    } else {
        return;
    }

    // rtcrm("#new_account_id").val([{ id: "xxx-xx-xx-xxx", name: "苏州瑞泰", entityType: "account" }]);           
}
//选择服务商时，只能选择合作状态为合作中，服务网络类型为服务商的服务商
function new_stationservice_idLookup() {
    var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                            <entity name = 'new_srv_station' >\
                            <attribute name='new_srv_stationid' />\
                            <attribute name='new_name' />\
                            <attribute name='new_code' />\
                            <attribute name='createdon' />\
                            <order attribute='new_name' descending='false' />\
                            <filter type='and'>\
                              <condition attribute='new_cooperationstatus' operator='eq' value='1' />\
                              <condition attribute='new_servicetype' operator='eq' value='1' />\
                            </filter>\
                          </entity >\
                        </fetch >";

    var layoutXml = "<grid name='resultset' " + "object='1' " + "jump='name' " + "select='1' " + "icon='1' " + "preview='1'>" +
        "<row name='result' " + "id='new_srv_stationid'>" +
        "<cell name='new_name' " + "width='120' />" +
        "<cell name='new_code' " + "width='120' />" +
        "</row>" +
        "</grid>";
    try {
        rtcrm.customizeLookupView("new_stationservice_id", "new_srv_station", fetchXml, layoutXml);
    } catch (e) {
        rtcrm.alertDialog(e.message);
    }
}
//选择服务网点时，服务网络类型为服务网点
function new_station_idLookup() {
    var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>\
                            <entity name = 'new_srv_station' >\
                            <attribute name='new_srv_stationid' />\
                            <attribute name='new_name' />\
                            <attribute name='new_code' />\
                            <attribute name='createdon' />\
                            <order attribute='new_name' descending='false' />\
                            <filter type='and'>\
                              <condition attribute='new_servicetype' operator='eq' value='2' />\
                            </filter>\
                          </entity >\
                        </fetch >";

    var layoutXml = "<grid name='resultset' " + "object='1' " + "jump='name' " + "select='1' " + "icon='1' " + "preview='1'>" +
        "<row name='result' " + "id='new_srv_stationid'>" +
        "<cell name='new_name' " + "width='120' />" +
        "<cell name='new_code' " + "width='120' />" +
        "</row>" +
        "</grid>";
    try {
        rtcrm.customizeLookupView("new_station_id", "new_srv_station", fetchXml, layoutXml);
    } catch (e) {

        rtcrm.alertDialog(e.message);
    }
}
//是否按单量结算
function new_iswonumberonchange() {
    this.gradientstartorend_showorhidden();
    //网点类型必填校验
    this.new_stietypeIsRequired();
}
//根据是否按单量结算显示梯度开始梯度结束
function gradientstartorend_showorhidden() {
    var feetype = rtcrm("#new_feetype").val();
    var status = rtcrm("#new_iswonumber").val();
    if (status == 1 && (feetype == 1 || feetype == 6 || feetype == 18)) {
        rtcrm("#new_gradientstart").visible(true);
        rtcrm("#new_gradientend").visible(true);
    } else {
        rtcrm("#new_gradientstart").visible(false);
        rtcrm("#new_gradientend").visible(false);
    }
}
//梯度开始,如果梯度结束不为空，梯度开始需小于梯度结束
function new_gradientstartonchange() {
    var gradientend = rtcrm("#new_gradientend").val();
    var gradientstart = rtcrm("#new_gradientstart").val();
    if (gradientstart != null && gradientend != null) {
        if (gradientstart >= gradientend) {
            rtcrm.alertDialog("梯度开始不能大于梯度结束");
            rtcrm("#new_gradientstart").val(null);
            // Xrm.Page.data.refresh();           
        }
    }
}
//梯度结束,如果梯度开始不为空，梯度开始需小于梯度结束
function new_gradientendonchange() {
    var gradientend = rtcrm("#new_gradientend").val();
    var gradientstart = rtcrm("#new_gradientstart").val();
    if (gradientstart != null && gradientend != null) {
        if (gradientstart >= gradientend) {
            rtcrm.alertDialog("梯度结束不能小于梯度开始");
            rtcrm("#new_gradientend").val(null);
            // Xrm.Page.data.refresh();           
        }
    }
}
//二级品类，根据二级品类带出一级品类
function new_category2_idonchange() {
    var category1 = rtcrm("#new_category1_id").val();
    if (category1 != null && category1 != "") {
        return;
    }
    var category2 = rtcrm("#new_category2_id").val();
    if (category2 != null && category2 != "") {
        var category2id = category2[0].id.replace('{', '').replace('}', '');
        var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>" +
            "<entity name='new_category2' >" +
            "<attribute name='new_category1_id' />" +
            "<filter type='and'>" +
            "<condition attribute='statecode' operator='eq' value='0' />" +
            "<condition attribute='new_category2id' operator='eq' value='" + category2id + "' />" +
            " </filter>" +
            "</entity>" +
            "</fetch >";
        var category2data = rtcrm.fetch("new_category2s", fetchXml, true);
        if (category2data && category2data.length > 0) {
            rtcrm("#new_category1_id").val([{ id: category2data[0]._new_category1_id_value, name: category2data[0]["<EMAIL>"], entityType: "new_category1" }]);
        }
    }
}
//三级品类，根据三级品类带出二级品类和一级品类
function new_category3_idonchange() {
    var category1 = rtcrm("#new_category1_id").val();
    if (category1 != null && category1 != "") {
        return;
    }
    var category2 = rtcrm("#new_category2_id").val();
    if (category2 != null && category2 != "") {
        return;
    }
    var category3 = rtcrm("#new_category3_id").val();
    if (category3 != null && category3 != "") {
        var category3id = category3[0].id.replace('{', '').replace('}', '');
        var fetchXml = "<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>" +
            "<entity name='new_category3' >" +
            "<attribute name='new_category2_id' />" +
            "<filter type='and'>" +
            "<condition attribute='statecode' operator='eq' value='0' />" +
            "<condition attribute='new_category3id' operator='eq' value='" + category3id + "' />" +
            "</filter>" +
            "<link-entity name='new_category2' from='new_category2id' to='new_category2_id' link-type='inner' alias='ac'>" +
            "<attribute name='new_category1_id' />" +
            "</link-entity>" +
            "</entity>" +
            "</fetch >";
        var category3data = rtcrm.fetch("new_category3s", fetchXml, true);
        if (category3data && category3data.length > 0) {
            rtcrm("#new_category2_id").val([{ id: category3data[0]._new_category2_id_value, name: category3data[0]["<EMAIL>"], entityType: "new_category2" }]);
            rtcrm("#new_category1_id").val([{ id: category3data[0]["ac.new_category1_id"], name: category3data[0]["<EMAIL>"], entityType: "new_category1" }]);
        }
    }
}
/// <summary>
/// Module ID：无
/// Author：p-dongbinbib
/// Create Date：2023-03-21
/// Depiction：网点类型是否必填
/// URL：https://dev.azure.com/xiaomiserviceCRM/ISP%20CS/_sprints/taskboard/ISP%20CS%20Team/ISP%20CS/Sprint%2020230316-20230330?workitem=834
/// </summary>
function new_stietypeIsRequired() {
    //如果【费用类型new_feetype】=录单费且【按单结算：new_iswonumber】为是则 new_stietype网点类型必填
    var new_iswonumber = Xrm.Page.getAttribute("new_iswonumber").getValue();
    var new_feetype = Xrm.Page.getAttribute("new_feetype").getValue();
    if (new_iswonumber == 1 && new_feetype == 10) {
        rtcrm("#new_stietype").req("required");
    } else {
        rtcrm("#new_stietype").req("none");
    }
}

//一级品类必填
function new_category1_idRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //备件服务费,Local buy markup费，分摊权重，录单费（B2X），物流费（B2X）,维修劳务费，清关费，对外呼叫费，录单费，寄修补贴，激活费， Handling费（运营商），物流费（运营商），Handling费（转派），物流费（转派）
    if (feetype == 20 || feetype == 21 || feetype == 28 || feetype == 33 || feetype == 30 || feetype == 1 || feetype == 31 || feetype == 32 || feetype == 10 || feetype == 4 || feetype == 42 || feetype == 43 || feetype == 44 || feetype == 45 || feetype == 46) {
        rtcrm("#new_category1_id").req("required");
    } else {
        rtcrm("#new_category1_id").req("none");
    }
}
//国家必填
function new_country_idRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //备件服务费，Local buy markup费，物流费markup，备件补贴费，固定服务费，资本利息费，最低工单量，激活费
    if (feetype == 20 || feetype == 21 || feetype == 23 || feetype == 24 || feetype == 25 || feetype == 26 || feetype == 27 || feetype == 42) {
        rtcrm("#new_country_id").req("required");
    } else {
        rtcrm("#new_country_id").req("none");
    }
}
//百分比必填
function new_ratioRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //Local buy markup费，物流费markup，资本利息费，备件markup费（Buysell），Local buy markup费（Buysell）,激活费
    if (feetype == 21 || feetype == 23 || feetype == 26 || feetype == 39 || feetype == 40 || feetype == 42) {
        rtcrm("#new_ratio").req("required");
    } else {
        rtcrm("#new_ratio").req("none");
    }
}
//工单量必填
function new_workordernumberRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //备件补贴费，最低工单量
    if (feetype == 24 || feetype == 27) {
        rtcrm("#new_workordernumber").req("required");
    } else {
        rtcrm("#new_workordernumber").req("none");
    }
}
//权重，可选费用必填
function new_costweightRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //权重分摊
    if (feetype == 28) {
        rtcrm("#new_costweight").req("required");
        rtcrm("#new_optionalcharges").req("required");
    } else {
        rtcrm("#new_costweight").req("none");
        rtcrm("#new_optionalcharges").req("none");
    }
}
//币种设置必填
function new_transactioncurrency_idNoneRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //物流费 非必填
    if (feetype == 23) {
        rtcrm("#new_transactioncurrency_id").req("none");
    }
    //备件服务费，Local buy markup费，备件补贴费，固定服务费 必填
    if (feetype == 20 || feetype == 21 || feetype == 24 || feetype == 25) {
        rtcrm("#new_transactioncurrency_id").req("required");
    }
}
//物料名称设置必填
function new_productid_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //迈创备件价格模版，空箱费,电视材料保护费，空箱物流费
    if (feetype == 29 || feetype == 34 || feetype == 37 || feetype == 35) {
        rtcrm("#new_product_id").req("required");
    } else {
        rtcrm("#new_product_id").req("none");
    }
}
//结费级别设置必填
function new_settlementlevel_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //维修劳务费
    if (feetype == 1) {
        rtcrm("#new_settlementlevel").req("required");
    } else {
        rtcrm("#new_settlementlevel").req("none");
    }
}
//Return channel 设置必填
function new_return_channel_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //翻新费、清关费、 Handling费（转派）、物流费（转派）
    if (/*feetype == 32 || */feetype == 36 || feetype == 31 || feetype == 45 || feetype == 46) {
        rtcrm("#new_return_channel").req("required");
    } else {
        rtcrm("#new_return_channel").req("none");
    }
}
//Action reason 设置必填
function new_action_reason_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //空箱费、翻新费
    if (feetype == 34 || feetype == 36) {
        rtcrm("#new_action_reason").req("required");
    } else {
        rtcrm("#new_action_reason").req("none");
    }
}
//Direction 设置必填
function new_direction_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //清关费
    if (feetype == 31) {
        rtcrm("#new_direction").req("required");
    } else {
        rtcrm("#new_direction").req("none");
    }
}
//服务网点 设置必填
function new_station_id_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //录单费（B2X）
    if (feetype == 33) {
        rtcrm("#new_station_id").req("required");
    } else {
        rtcrm("#new_station_id").req("none");
    }
}

// 运营商Operator设置必填
function new_operator_id_Required() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //Handling费（运营商）, 物流费（运营商）
    if (feetype == 43 || feetype == 44) {
        rtcrm("#new_operator_id").req("required");
    } else {
        rtcrm("#new_operator_id").req("none");
    }
}

//二级品类必填
function new_category2_idRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //激活费
    if (feetype == 42) {
        rtcrm("#new_category2_id").req("required");
    } else {
        rtcrm("#new_category2_id").req("none");
    }
}

// 物流费 - section_logistics - 字段根据服务商、费用类型实现动态显隐、必填
// tips: 字段显隐只对首个字段生效, 后期 物流费节 字段不可再复用进行字段显隐控制
function section_logistics_setRequiredAndVisible() {
    section_logistics_init(); // 初始化物流费字段显隐性、必填性

    if (rtcrm.isNullOrWhiteSpace(rtcrm.getLookupId("new_stationservice_id"))) return;
    var new_stationservice_id = rtcrm.getLookupId("new_stationservice_id").replace("{", "").replace("}", "");
    var queryString = "new_srv_stations(" + new_stationservice_id + ")?$select=new_logitics_servicetype";
    var res = rtcrm.retrieve(queryString, true);
    
    if (res == null) return;
    var type = res["new_logitics_servicetype"]; // 物流费服务商类型
    var feetype = rtcrm("#new_feetype").val(); // 费用类型
    
    if (feetype == null || type == null) return;
    if (type == 1) { // 泰国Kerry
        if (feetype == 151) {
            // 运费
            // 字段: 重量区间(KG)、始发港、目的港、单价、金额
            Xrm.Page.getControl('new_weightinterval_thailandkerry_freight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_thailandkerry_freight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 2) { // 马来西亚GDEX
        if (feetype == 152) {
            // 运费Last mile
            // 字段：Branch、首重(KG)、首重金额、单价、系数
            Xrm.Page.getControl('new_new_priceregion_dhlldigit_branch_id')?.setVisible(true);
            Xrm.Page.getControl('new_new_priceregion_dhlletter_branch_id')?.setVisible(true);
            Xrm.Page.getControl('new_firstweight')?.setVisible(true);
            Xrm.Page.getAttribute('new_firstweight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_firstweightprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_firstweightprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_factor')?.setVisible(true);
            Xrm.Page.getAttribute('new_factor')?.setRequiredLevel('required');
        }
        else if (feetype == 153) {
            // 运费Milk Run
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 3) { // 珠海DB
        if (feetype == 142) {
            // 空运费
            // 字段：始发港、目的港、重量区间（KG）、单价、保底价
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_zhuhaidb_airfreight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_zhuhaidb_airfreight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 117 || feetype == 130) {
            // 地面操作费 或者 国外THC
            // 字段：始发港、目的港、单价、保底价
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 119) {
            // 磁检费
            // 字段：始发港、单价
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 133) {
            // 国外派送费
            // 字段：始发港、目的港、重量区间（KG）、单价、保底价、整车价
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_zhuhaidb_foreigndelivery')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_zhuhaidb_foreigndelivery')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_carloadprice')?.setVisible(true);
        }
        else if (feetype == 136) {
            // 国外杂费
            // 字段：始发港、目的港、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 129) {
            // 国内运输服务费
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
        }
    }
    else if (type == 4) { // DHL
        if (feetype == 142) {
            // 空运费
            // 字段：价格区、商品类型、重量区间（KG）、金额、单价
            Xrm.Page.getControl('new_priceregion_dhldigit')?.setVisible(true);
            Xrm.Page.getControl('new_priceregion_dhlletter')?.setVisible(true);
            Xrm.Page.getControl('new_goodstype')?.setVisible(true);
            Xrm.Page.getControl('new_weightinterval_dhl_airfreight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_dhl_airfreight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);

        }
        else if (feetype == 107 || feetype == 106) {
            // Go Green Plus 或者 燃油附加费
            // 字段：费率
            Xrm.Page.getControl('new_rate')?.setVisible(true);
            Xrm.Page.getAttribute('new_rate')?.setRequiredLevel('required');
        }
    }
    else if (type == 5) { // 深圳越海
        if (feetype == 121 || feetype == 123 || feetype == 116) {
            // 代理服务费、订舱服务费、操作服务费
            // 字段：操作类型、金额
            Xrm.Page.getControl('new_operatetype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 155) {
            // 资金服务费
            // 字段：费率
            Xrm.Page.getControl('new_rate')?.setVisible(true);
            Xrm.Page.getAttribute('new_rate')?.setRequiredLevel('required');
        }
        else if (feetype == 143) {
            // 陆运费
            // 字段：始发港、目的港、线路、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_route')?.setVisible(true);
            Xrm.Page.getAttribute('new_route')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
    }
    else if (type == 6) { // 跨越速运
        if (feetype == 128) {
            // 国内运费
            // 字段：始发地、目的地、服务方式、车型（长度）、重量区间(KG)、金额、保底价、业务类型
            Xrm.Page.getControl('new_begin_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_servicetype')?.setVisible(true);
            Xrm.Page.getAttribute('new_servicetype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_carlength')?.setVisible(true);
            Xrm.Page.getControl('new_weightinterval_straddleexpress_homefreigh')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getControl('new_businesstype')?.setVisible(true);
        }
        else if (feetype == 145) {
            // 木架费
            // 字段：费率
            Xrm.Page.getControl('new_rate')?.setVisible(true);
            Xrm.Page.getAttribute('new_rate')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 7) { // 瑞尔香港-陆运
        if (feetype == 129 || feetype == 146) {
            // 国内运输服务费、派送费
            // 字段：费率、保底费
            Xrm.Page.getControl('new_rate')?.setVisible(true);
            Xrm.Page.getAttribute('new_rate')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');

        }
        else if (feetype == 124) {
            // 服务费
            // 字段：始发港、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            
        }
        else if (feetype == 143) {
            // 陆运费
            // 字段：始发港、费率
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_rate')?.setVisible(true);
            Xrm.Page.getAttribute('new_rate')?.setRequiredLevel('required');
        }
        else if (feetype == 122) {
            // 登记停车费
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 114) { 
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
    }
    else if (type == 8) { // 瑞尔香港-海运
        if (feetype == 136 || feetype == 133 || feetype == 139 || feetype == 130 || feetype == 135 || feetype == 111 || feetype == 149 || feetype == 146 || feetype == 141) {
            // 国外杂费、国外派送费、海运费、国外THC、国外提货费、Terminal charge 地面费、卸货费、派送费、换单费
            // 字段：始发港、目的港、LCL、20GP、40GP、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_lcl')?.setVisible(true);
            Xrm.Page.getControl('new_20gp')?.setVisible(true);
            Xrm.Page.getControl('new_40gp')?.setVisible(true);
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 131 || feetype == 137 || feetype == 132 || feetype == 113 || feetype == 108 || feetype == 115 || feetype == 121 || feetype == 125) {
            // 国外操作费、国外制单费、国外电放费 、VGM费、IMO费、操作费、代理服务费、港杂费
            // 字段：始发港、目的港、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 9) { // 瑞尔香港-空运
        if (feetype == 142) {
            // 空运费
            // 字段：始发港、目的港、重量区间(KG)、货物类型、金额、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_realhongkongair_airfreight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_realhongkongair_airfreight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 138 || feetype == 124 || feetype == 122 || feetype == 118 || feetype == 147 || feetype == 110 || feetype == 103 || feetype == 104 || feetype == 136
            || feetype == 154 || feetype == 138 || feetype == 137 || feetype == 101 || feetype == 112 || feetype == 115) {
            // 过境杂费、服务费、登记停车费、出口特殊运费、危险品申报费、RAC危险品操作费、危险品检查费、危险品操作费、国外杂费
            // 备机制单跟单费、过境杂费 、国外制单费、ADF文件费、闸费、操作费
            // 字段：目的港、始发港、货物类型、成交方式、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_dealtype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 148 || feetype == 140 || feetype == 143 || feetype == 146 || feetype == 129 || feetype == 117 || feetype == 111 || feetype == 102 || feetype == 146) {
            // 香港飞附加费、含电整机9类危险品附加费、陆运费、派送费、国内运输服务费、出口地面操作费、Terminal charge地面费、CFS分拨费、派送费
            // 字段：目的港、始发港、货物类型、成交方式、金额、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_dealtype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 105) {
            // 危险品License费
            // 字段：始发港、目的港、数量区间、货物类型、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_quantityinterval')?.setVisible(true);
            Xrm.Page.getAttribute('new_quantityinterval')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getAttribute('new_cargotype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 133) {
            // 国外派送费
            // 字段：目的港、货物类型、成交方式、重量区间(KG)、金额、保底费、整车报价
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getAttribute('new_cargotype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_dealtype')?.setVisible(true);
            Xrm.Page.getAttribute('new_dealtype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_realhongkongair_foreigndel')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_realhongkongair_foreigndel')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_carloadprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_carloadprice')?.setRequiredLevel('required');
        }
        else if (feetype == 131 || feetype == 135 || feetype == 130) {
            // 国外操作费、国外提货费、国外THC
            // 字段：始发港、目的港、货物类型、成交方式、金额、保底费、单价
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getAttribute('new_cargotype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_dealtype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 119) {
            // 磁检费
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 10) { // 瑞尔香港-双清
        if (feetype == 151) {
            // 运费
            // 字段：始发港、目的港、重量区间(KG)、货物类型、首重金额(0.5KG)、续重金额（每0.5KG）、单价、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_realhongkongsq_freight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_realhongkongsq_freight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_firstweightprice05')?.setVisible(true);
            Xrm.Page.getAttribute('new_firstweightprice05')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_extraweightprice05')?.setVisible(true);
            Xrm.Page.getAttribute('new_extraweightprice05')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 115) {
            // 操作费
            // 字段：始发港、目的港、货物类型、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getAttribute('new_cargotype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
        }
    }
    else if (type == 11) { // 瑞尔香港-代垫
        if (feetype == 120) {
            // 代垫手续费
            // 字段：比例
            Xrm.Page.getControl('new_ratio_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratio_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 12) { // 瑞尔珠海-海运
        if (feetype == 139 || feetype == 126 || feetype == 144 || feetype == 127) {
            // 海运费、工厂装柜提货费、码头吊装费+LSS等码头杂费、国内港杂操作费
            // 字段：始发港、目的港、LCL、20GP、40GP、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_lcl')?.setVisible(true);
            Xrm.Page.getControl('new_20gp')?.setVisible(true);
            Xrm.Page.getControl('new_40gp')?.setVisible(true);
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
            Xrm.Page.getAttribute('new_minimum_guaranteefee')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
    }
    else if (type == 13) { // 瑞尔珠海-空运
        if (feetype == 142 || feetype == 140 || feetype == 143 || feetype == 146 || feetype == 129 || feetype == 117) {
            // 空运费、含电整机9类危险品附加费、陆运费、派送费、国内运输服务费、出口地面操作费
            // 字段：始发港、目的港、重量区间(KG)、货物类型、单价、保底费
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_weightinterval_realzhuhaiair_airfreightet')?.setVisible(true);
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_unitprice')?.setVisible(true);
            Xrm.Page.getAttribute('new_unitprice')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 138 || feetype == 124 || feetype == 122 || feetype == 118 || feetype == 147 || feetype == 110 || feetype == 103 || feetype == 104) {
            // 过境杂费、服务费、登记停车费、出口特殊交通费、危险品申报费、RAC危险品操作费、危险品检查费、危险品操作费
            // 字段：始发港、目的港、货物类型、成交方式、金额
            Xrm.Page.getControl('new_begin_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getControl('new_dealtype')?.setVisible(true);
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）、保底费
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_minimum_guaranteefee')?.setVisible(true);
        }
        else if (feetype == 105) {
            // 危险品License费
            // 字段：目的港、货物类型、件数区间、金额
            Xrm.Page.getControl('new_ending_portcode_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_portcode_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_cargotype')?.setVisible(true);
            Xrm.Page.getAttribute('new_cargotype')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_numberinterval')?.setVisible(true);
            Xrm.Page.getAttribute('new_numberinterval')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 119) {
            // 磁检费
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 14) { // INDOTAMA
        if (feetype == 153) {
            // Milk Run
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 15) { // YAMAYTO
        if (feetype == 151) {
            // 运费
            // 字段：发货地、尺寸、金额
            Xrm.Page.getControl('new_begin_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_size')?.setVisible(true);
            Xrm.Page.getAttribute('new_size')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 16) { // JNE
        if (feetype == 151) {
            // 运费
            // 字段：发货地、目的地、服务类型、服务类型、金额
            Xrm.Page.getControl('new_begin_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_servicetype2')?.setVisible(true);
            Xrm.Page.getAttribute('new_servicetype2')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 150) {
            // 印尼邮票费
            // 字段：金额
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 17) { // Speedex
        if (feetype == 151) {
            // 运费
            // 字段：出发地、目的地、业务场景、重量区间、系数、金额
            Xrm.Page.getControl('new_begin_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_businessscene')?.setVisible(true);
            Xrm.Page.getAttribute('new_businessscene')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_speedx_freight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_speedx_freight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_factor')?.setVisible(true);
            Xrm.Page.getAttribute('new_factor')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 114) {
            // 保险费
            // 字段：比例（%）
            Xrm.Page.getControl('new_ratiopercent_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_ratiopercent_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 109) {
            // ODA费
            // 字段：区域、省、市、金额
            Xrm.Page.getControl('new_region_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_region_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_province_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_province_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_city_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_city_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
    }
    else if (type == 18) { // 顺丰
        if (feetype == 151) {
            // 运费
            // 字段：出发地、目的地、重量区间(kg)、系数、扣减参数、金额
            Xrm.Page.getControl('new_begin_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_begin_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_ending_destination_config_id')?.setVisible(true);
            Xrm.Page.getAttribute('new_ending_destination_config_id')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_weightinterval_sf_freight')?.setVisible(true);
            Xrm.Page.getAttribute('new_weightinterval_sf_freight')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_factor')?.setVisible(true);
            Xrm.Page.getAttribute('new_factor')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_reduceparameter')?.setVisible(true);
            Xrm.Page.getAttribute('new_reduceparameter')?.setRequiredLevel('required');
            Xrm.Page.getControl('new_amount_logistics')?.setVisible(true);
            Xrm.Page.getAttribute('new_amount_logistics')?.setRequiredLevel('required');
        }
        else if (feetype == 106) {
            // FU_CHRG 燃油附加费
            // 字段：系数
            Xrm.Page.getControl('new_factor')?.setVisible(true);
            Xrm.Page.getAttribute('new_factor')?.setRequiredLevel('required');
        }
    }
}

function section_logistics_init() {
    var logic_name = ["new_20gp", "new_40gp", "new_new_priceregion_dhlletter_branch_id", "new_new_priceregion_dhlldigit_branch_id", "new_lcl", "new_businessscene",
        "new_numberinterval", "new_priceregion_dhlletter", "new_priceregion_dhldigit", "new_minimum_guaranteefee", "new_region_id", "new_unitprice", "new_goodstype",
        "new_begin_destination_config_id", "new_begin_portcode_config_id", "new_size", "new_city_id", "new_dealtype", "new_reduceparameter", "new_operatetype",
        "new_quantityinterval", "new_carloadprice", "new_servicetype", "new_ratio_logistics", "new_ratiopercent_logistics", "new_ending_destination_config_id", "new_ending_portcode_config_id",
        "new_province_id", "new_factor", "new_route", "new_extraweightprice05", "new_cargotype", "new_rate", "new_carlength", "new_weightinterval_dhl_airfreight",
        "new_weightinterval_speedx_freight", "new_weightinterval_thailandkerry_freight", "new_weightinterval_zhuhaidb_foreigndelivery", "new_weightinterval_zhuhaidb_airfreight",
        "new_weightinterval_realzhuhaiair_dangerousgoo", "new_weightinterval_realzhuhaiair_airfreightet", "new_weightinterval_realhongkongsq_freight", "new_weightinterval_realhongkongair_dangerousg",
        "new_weightinterval_realhongkongair_foreigndel", "new_weightinterval_realhongkongair_airfreight", "new_weightinterval_straddleexpress_homefreigh", "new_weightinterval_sf_freight",
        "new_amount_logistics", "new_firstweight", "new_firstweightprice", "new_firstweightprice05", "new_businesstype","new_servicetype2"
    ]
    logic_name.forEach(function(element){
        Xrm.Page.getControl(element)?.setVisible(false);
        Xrm.Page.getAttribute(element)?.setRequiredLevel('none');
    });
}
//安装劳务费 - section_install - 字段根据费用类型实现动态显隐、必填
function section_install_setRequiredAndVisible() {
    //服务商，结费级别，处理方法，一级品类 设置必填
    rtcrm("#new_stationservice_id").req("required");
    rtcrm("#new_settlementlevel").req("required");
    rtcrm("#new_approachid").req("required");
    rtcrm("#new_category1_id").req("required");
    isquantitychargedFiledControl();
    servicehourschargedFiledControl();
}
//是否按照数量结费 onchang事件
function isquantitychargedFiledControl() {
    var isquantitycharged = rtcrm("#new_isquantitycharged").val();
    if (isquantitycharged) {
        //是否按照数量结费 = 是，是否按服务时长结费，最高结算金额，标准服务时长(min)，标准服务时长金额隐藏
        rtcrm("#new_servicehourscharged").visible(false);
        rtcrm("#new_maxsettlementamount").visible(false);
        rtcrm("#new_standardserviceduration").visible(false);
        rtcrm("#new_servicedurationamount").visible(false);
    } else if (!isquantitycharged) {
        //是否按照数量结费 = 否，是否按服务时长结费，最高结算金额，标准服务时长(min)，标准服务时长金额显示
        rtcrm("#new_servicehourscharged").visible(true);
        //rtcrm("#new_maxsettlementamount").visible(true);
        //rtcrm("#new_standardserviceduration").visible(true);
        //rtcrm("#new_servicedurationamount").visible(true);
    }
}
//是否按服务时长结费 onchang事件
function servicehourschargedFiledControl() {
    var servicehourscharged = rtcrm("#new_servicehourscharged").val();
    if (servicehourscharged) {
        rtcrm("#new_isquantitycharged").visible(false);
        rtcrm("#new_maxsettlementamount").visible(true);
        rtcrm("#new_standardserviceduration").visible(true);
        rtcrm("#new_servicedurationamount").visible(true);
        rtcrm("#new_maxsettlementamount").req("required");
        rtcrm("#new_standardserviceduration").req("required");
        rtcrm("#new_servicedurationamount").req("required");
    } else if (!servicehourscharged) {
        rtcrm("#new_isquantitycharged").visible(true);
        rtcrm("#new_maxsettlementamount").req("none");
        rtcrm("#new_standardserviceduration").req("none");
        rtcrm("#new_servicedurationamount").req("none");
        rtcrm("#new_maxsettlementamount").visible(false);
        rtcrm("#new_standardserviceduration").visible(false);
        rtcrm("#new_servicedurationamount").visible(false);
    }
}
//费用标签设置必填
function new_feelabel_Required() {
    //费用类型 = 补贴费时，费用标签设置必填
    var feetype = rtcrm("#new_feetype").val();
    if (feetype == 16) {
        rtcrm("#new_feelabel").req("required");
    } else {
        rtcrm("#new_feelabel").req("none");
    }
}
//三级品类必填
function new_category3_idRequired() {
    //费用类型
    var feetype = rtcrm("#new_feetype").val();
    //高维工厂劳务费
    if (feetype == 158) {
        rtcrm("#new_category3_id").req("required");
    } else {
        rtcrm("#new_category3_id").req("none");
    }
}
//维修劳务费结算标准填写了处理方法，则结费级别改为非填项
function new_approach_idOnchange() {
    var approach = rtcrm("#new_approach_id").val();
    if (approach != null && approach != "") {
        rtcrm("#new_settlementlevel").req("none");
    } else {
        rtcrm("#new_settlementlevel").req("required");
    }
}