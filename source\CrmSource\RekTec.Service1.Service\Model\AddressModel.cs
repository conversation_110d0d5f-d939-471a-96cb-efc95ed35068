﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RekTec.Service1.Service.Model
{
    /// <summary>
    /// 地址级联选项模型，支持省-市-区三级嵌套
    /// </summary>
    public class AddressOptionModel
    {
        /// <summary>
        /// 选项值(guid)
        /// </summary>
        public string value { get; set; }
        /// <summary>
        /// 选项显示名(多语言)
        /// </summary>
        public string label { get; set; }
        /// <summary>
        /// 下级选项 
        /// </summary>
        public List<AddressOptionModel> children { get; set; }
    }

    /// <summary>
    /// 地址options根结构
    /// </summary>
    public class AddressOptionsRoot
    {
        /// <summary>
        /// 每个省份节点
        /// </summary>
        public List<AddressOptionModel> options { get; set; }
    }

    public class AddressApiResponse<T>
    {
        public int Code { get; set; }
        public T Value { get; set; }

        public AddressApiResponse(int code, T value)
        {
            Code = code;
            Value = value;
        }
    }
}
