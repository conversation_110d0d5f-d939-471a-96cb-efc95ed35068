﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace XiaoMi.Crm.CS.ApiCommon.Model
{
    /// <summary>
    /// XMS改派网点请求信息
    /// </summary>
    public class XMSDispatchStationRequestModel
    {
        /// <summary>
        /// 工单号
        /// </summary>
        public string serviceNo { get; set; }
        /// <summary>
        /// 网点名称
        /// </summary>
        public string stationNo { get; set; }
        /// <summary>
        /// 变更人 米聊号
        /// </summary>
        public string operateUser { get; set; }
    }


    /// <summary>
    /// ISP调用XMS取消接口请求入参
    /// </summary>
    public class XMSCancelRequestModel { 
    
        /// <summary>
        /// 工单号
        /// </summary>
        public string serviceNo { get; set; }

        /// <summary>
        /// 操作人米聊号
        /// </summary>
        public long operatePerson { get; set; }

        /// <summary>
        /// 取消原因
        /// </summary>
        public int? cancelReason { get; set; }
        /// <summary>
        /// 取消描述
        /// </summary>
        public string remark { get; set; }


    }


    public class Header
    {
        public int code { get; set; }
        public string desc { get; set; }
    }
    /// <summary>
    /// XMS返回信息
    /// </summary>
    public class XMSResponseModel
    {
        public int code { get; set; }
        public string message { get; set; }
        public string data { get; set; }
        public Header header { get; set; }
        /// <summary>
        /// 返回内容
        /// </summary>
        public object body { get; set; }
    }
    /// <summary>
    /// XMS附件信息
    /// </summary>
    public class XmsAttachmentRequestModel
    {
        public string serviceNo { get; set; }
    }


    public class XmsAttachmentResponseModel
    {
        /// <summary>
        /// 附件Id
        /// </summary>
        public int id { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string fileName { get; set; }
        /// <summary>
        /// 文件类型
        /// </summary>
        public string mimeType { get; set; }
        /// <summary>
        /// 文件大小
        /// </summary>
        public string fileSize { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string filePath { get; set; }
        /// <summary>
        /// 文件描述
        /// </summary>
        public string fileDesc { get; set; }
        /// <summary>
        /// 文件内容 
        /// </summary>
        public string documentBody { get; set; }
        /// <summary>
        /// 附件标题
        /// </summary>
        public string fileTitle { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string createdBy { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public long createdOn { get; set; }
        /// <summary>
        /// 上传附件类型
        /// </summary>
        public string fileType { get; set; }
    }
    //xms建单传参
    public class XMSCreateSerivceModel
    {
        /// <summary>
        /// 送装标识
        /// </summary>
        public bool szFlag { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string orderId { get; set; }
        /// <summary>
        /// 销售渠道
        /// </summary>
        public int saleType { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string serviceType { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string areaId { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string returnType { get; set; }
        /// <summary>
        /// 服务方式
        /// </summary>
        public string serviceWay { get; set; }
        /// <summary>
        /// 上门方式-ISP无
        /// </summary>
        public string serviceSubWay { get; set; }
        /// <summary>
        /// 故障描述
        /// </summary>
        public string faultDesc { get; set; }
        /// <summary>
        /// 网点编码
        /// </summary>
        public string acceptOrgId { get; set; }
        /// <summary>
        /// 建单方式
        /// </summary>
        public int createType { get; set; }
        /// <summary>
        /// 订单来源
        /// </summary>
        public int orderFrom { get; set; }
        /// <summary>
        /// 销售渠道
        /// </summary>
        public string buySaleChannel { get; set; }
        public string orgId { get; set; }
        /// <summary>
        /// 固定类型 "KEFU_CREATE":客服建单
        /// </summary>
        public string opFrom { get; set; }
        /// <summary>
        /// 创建人米聊号
        /// </summary>
        public long createPerson { get; set; }
        /// <summary>
        /// 客户希望上门时间
        /// </summary>
        public string visitDate { get; set; }
        /// <summary>
        /// 时间点
        /// </summary>
        public string visitTime { get; set; }
        /// <summary>
        /// 建单时间 当前的时间戳 毫秒
        /// </summary>
        public long updateTime { get; set; }
        /// <summary>
        /// 默认传这个 客服中心  "CALL_CENTER"
        /// </summary>
        public string requestFrom { get; set; }
        /// <summary>
        /// 是否同意售后回访  YES/NO
        /// </summary>
        public string isOverseaAfterSalesReturnVisit { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        public List<itemParamList> itemParamList { get; set; }
        /// <summary>
        /// 用户信息
        /// </summary>
        public srvCustomerParam srvCustomerParam { get; set; }
        /// <summary>
        /// 送装信息
        /// </summary>
        public srvDeliveryVisitParam srvDeliveryVisitParam { get; set; }

    }
    public class srvDeliveryVisitParam
    {
        /// <summary>
        /// 用户期望上门时间，毫秒级时间戳
        /// </summary>
        public long? hopeVisitTimeTs { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string tel { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string customerName { get; set; }
        /// <summary>
        /// 国家编码
        /// </summary>
        public int country { get; set; }
        /// <summary>
        /// 省份编码
        /// </summary>
        public int province { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string address { get; set; }
        /// <summary>
        /// 街道编码
        /// </summary>
        public int street { get; set; }
        /// <summary>
        /// 区县编码
        /// </summary>
        public int district { get; set; }
        /// <summary>
        /// 城市编码
        /// </summary>
        public int city { get; set; }
    }
    public class srvCustomerParam
    {
        /// <summary>
        /// 米聊号
        /// </summary>
        public long userId { get; set; }
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 用户电话
        /// </summary>
        public string tel { get; set; }
        /// <summary>
        /// 用户电话2
        /// </summary>
        public string tel2 { get; set; }
        /// <summary>
        /// 国家编码
        /// </summary>
        public string country { get; set; }
        /// <summary>
        /// 省份编码
        /// </summary>
        public string province { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string address { get; set; }
        /// <summary>
        /// 街道编码
        /// </summary>
        public string street { get; set; }
        /// <summary>
        /// 区县编码
        /// </summary>
        public string district { get; set; }
        /// <summary>
        /// 城市编码
        /// </summary>
        public string city { get; set; }

        /// <summary>
        /// 客户类型
        /// </summary>
        public string customerType { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string email { get; set; }
    }
    public class itemParamList
    {
        /// <summary>
        /// sn
        /// </summary>
        public int itemType { get; set; }
        /// <summary>
        /// sn
        /// </summary>
        public string sn { get; set; }
        /// <summary>
        /// 商品code
        /// </summary>
        public string goodsId { get; set; }
        /// <summary>
        /// imei
        /// </summary>
        public string imei { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderId { get; set; }
        /// <summary>
        /// 三级品类
        /// </summary>
        public string brandClassId { get; set; }
        /// <summary>
        /// 是否串号
        /// </summary>
        public bool? isSerial { get; set; }
        /// <summary>
        /// 送装默认故障 传【47】
        /// </summary>
        public int[] faultIds { get; set; }
        /// <summary>
        /// 发货单号
        /// </summary>
        public string waybillId { get; set; }
        /// <summary>
        /// 故障描述
        /// </summary>
        public string faultDesc { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }
        /// <summary>
        /// 发票时间
        /// </summary>
        public string invoiceDate { get; set; }
    }



    /// <summary>
    /// 更新工单请求XMS信息
    /// </summary>
    public class UpdateWorkOrderRequestModel
    {
        /// <summary>
        /// 工单号
        /// </summary>
        public string serviceNo { get; set; }
        /// <summary>
        /// 变更人 米聊号
        /// </summary>
        public string userId { get; set; }
        /// <summary>
        /// 国家/地区ID
        /// </summary>
        public string countryId { get; set; }
        /// <summary>
        /// 省份ID
        /// </summary>
        public string provinceId { get; set; }
        /// <summary>
        /// 城市ID
        /// </summary>
        public string cityId { get; set; }
        /// <summary>
        /// 区县ID
        /// </summary>
        public string countyId { get; set; }
        /// <summary>
        /// 街道ID
        /// </summary>
        public string streetId { get; set; }
        /// <summary>
        /// 详细地址
        /// </summary>
        public string address { get; set; }
        /// <summary>
        /// 电话1
        /// </summary>
        public string tel1 { get; set; }
        /// <summary>
        /// 电话2
        /// </summary>
        public string tel2 { get; set; }
        /// <summary>
        /// 客户类型
        /// </summary>
        public int accountType { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string accountName { get; set; }
        /// <summary>
        /// email
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 故障描述
        /// </summary>
        public string faultDescription { get; set; }
        /// <summary>
        /// 预计上门时间
        /// </summary>
        public long hopeVisitTime { get; set; }
    }

    /// <summary>
    /// XMS个人领用物料接口入参
    /// </summary>
    public class XMSDrawMaterialRequestModel : RevertUseMaterialVoModel
    {
        //TODO: 继承RevertUseMaterialVoModel
    }

    /// <summary>
    /// XMS个人领用物料接口返回
    /// </summary>
    public class XMSDrawMaterialResponseModel
    {
    }

    /// <summary>
    /// XMS个人归还物料接口入参
    /// </summary>
    public class XMSReturnMaterialRequestModel : RevertUseMaterialVoModel
    {
        //TODO: 继承RevertUseMaterialVoModel
    }

    /// <summary>
    /// XMS个人归还物料接口返回
    /// </summary>
    public class XMSReturnMaterialResponseModel
    {
    }

    /// <summary>
    /// XMS个人用料-占用接口入参
    /// </summary>
    public class XMSPreemptMaterialWrapRequestModel : RevertUseMaterialVoModel
    {
        //TODO: 继承RevertUseMaterialVoModel
    }

    /// <summary>
    /// XMS个人用料-占用接口返回
    /// </summary>
    public class XMSPreemptMaterialWrapResponseModel
    {
    }

    /// <summary>
    /// XMS个人用料-撤消接口入参
    /// </summary>
    public class XMSUnPreemptMaterialRequestModel : RevertUseMaterialVoModel
    {
        //TODO: 继承RevertUseMaterialVoModel
    }

    /// <summary>
    /// XMS个人用料-撤消接口返回
    /// </summary>
    public class XMSUnPreemptMaterialResponseModel
    {
    }

    /// <summary>
    /// XMS个人用料完成接口（用料会产生废品/坏品入库）入参
    /// </summary>
    public class XMSUseMaterialRequestModel
    {
        /// <summary>
        /// 入库
        /// </summary>
        public List<RevertUseMaterialVoModel> pinVos { get; set; }
        /// <summary>
        /// 出库
        /// </summary>
        public List<RevertUseMaterialVoModel> poutVos { get; set; }
    }

    /// <summary>
    /// XMS个人用料完成接口（用料会产生废品/坏品入库）返回
    /// </summary>
    public class XMSUseMaterialResponseModel
    {
    }

    /// <summary>
    /// XMS用料完成-反冲接口入参
    /// </summary>
    public class XMSRevertUseMaterialRequestModel
    {
        /// <summary>
        /// 入库
        /// </summary>
        public RevertUseMaterialVoModel pinVo { get; set; }
        /// <summary>
        /// 出库
        /// </summary>
        public RevertUseMaterialVoModel poutVo { get; set; }
    }

    /// <summary>
    /// XMS用料完成-反冲接口pinVo/poutVo
    /// </summary>
    public class RevertUseMaterialVoModel
    {
        /// <summary>
        /// 类型（“in”/”out”）
        /// </summary>
        public string inOutType { get; set; }
        /// <summary>
        /// 库存类型
        /// </summary>
        public string storageType { get; set; }
        /// <summary>
        /// 库存区枚举类型
        /// </summary>
        public string sectionType { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string serviceType { get; set; }
        /// <summary>
        /// 子业务类型
        /// </summary>
        public string subServiceType { get; set; }
        /// <summary>
        /// 业务单号
        /// </summary>
        public string serviceNo { get; set; }
        /// <summary>
        /// AS工单信息, 传此参数可以使用AS单对应的库存
        /// </summary>
        public string asId { get; set; }
        /// <summary>
        /// 归属人，共用库存请传入0
        /// </summary>
        public string opPerson { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string updatePerson { get; set; }
        /// <summary>
        /// trans2Person
        /// </summary>
        public string trans2Person { get; set; }
        /// <summary>
        /// lstItem
        /// </summary>
        public RevertUseMaterialVoLstItemModel lstItem { get; set; }
        /// <summary>
        /// id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// remark
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 原出入库库号－－记录来源的原出入库库号。支持幂等检查调用。
        /// </summary>
        public string orginStorageId { get; set; }
        /// <summary>
        /// 原出入库库位号
        /// </summary>
        public string orginSectionId { get; set; }
        /// <summary>
        /// XMS系统来源标记，区分1.0工单 2.0工单 工单一体
        /// </summary>
        public string sysFlag { get; set; }
        /// <summary>
        /// 目标系统标识. 区分米家入库 迈创 库存对接. Constants.STORAGE_TARGE_MIHOME
        /// </summary>
        public string targetFlag { get; set; }
        /// <summary>
        /// extendInfo
        /// </summary>
        public RevertUseMaterialVoExtendInfoModel extendInfo { get; set; }
        /// <summary>
        /// productType
        /// </summary>
        public int productType { get; set; }
    }

    /// <summary>
    /// XMS用料完成-反冲接口pinVo/poutVo.lstItem
    /// </summary>
    public class RevertUseMaterialVoLstItemModel
    {
        /// <summary>
        /// itemId
        /// </summary>
        public int itemId { get; set; }
        /// <summary>
        /// macNo
        /// </summary>
        public string macNo { get; set; }
        /// <summary>
        /// macName
        /// </summary>
        public string macName { get; set; }
        /// <summary>
        /// price
        /// </summary>
        public double price { get; set; }
        /// <summary>
        /// quantity
        /// </summary>
        public int quantity { get; set; }
        /// <summary>
        /// binUnitInfoVo
        /// </summary>
        public string binUnitInfoVo { get; set; }
        /// <summary>
        /// serviceItemId
        /// </summary>
        public int serviceItemId { get; set; }
        /// <summary>
        /// lstSn
        /// </summary>
        public List<RevertUseMaterialVoLstItemLstSnModel> lstSn { get; set; }
    }

    /// <summary>
    /// XMS用料完成-反冲接口pinVo/poutVo.lstItem.lstSn
    /// </summary>
    public class RevertUseMaterialVoLstItemLstSnModel
    {
        /// <summary>
        /// imei
        /// </summary>
        public string imei { get; set; }
        /// <summary>
        /// sn
        /// </summary>
        public string sn { get; set; }
        /// <summary>
        /// meid
        /// </summary>
        public string meid { get; set; }
    }

    /// <summary>
    /// XMS用料完成-反冲接口pinVo/poutVo.extendInfo
    /// </summary>
    public class RevertUseMaterialVoExtendInfoModel
    {
        /// <summary>
        /// 跳过SN处理，内部接口功能，修复数据专用.
        /// </summary>
        public bool isJumpSn { get; set; }
    }

    /// <summary>
    /// XMS用料完成-反冲接口返回
    /// </summary>
    public class XMSRevertUseMaterialResponseModel
    {
    }

    /// <summary>
    /// XMS个人库存查询接口（按人与料查询其数量）入参
    /// </summary>
    public class XMSGetPersonQuantityRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 人员
        /// </summary>
        public string person { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string macNo { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int type { get; set; }
    }

    /// <summary>
    /// XMS个人库存查询接口（按人与料查询其数量）返回
    /// </summary>
    public class XMSGetPersonQuantityResponseModel
    {
    }

    /// <summary>
    /// XMS个人库存查询接口（按人查其物料和相应的数量）入参
    /// </summary>
    public class XMSGetPersonMacQuantityRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 人员
        /// </summary>
        public string person { get; set; }
    }

    /// <summary>
    /// XMS个人库存查询接口（按人查其物料和相应的数量）返回
    /// </summary>
    public class XMSGetPersonMacQuantityResponseModel
    {
    }

    /// <summary>
    /// XMS按sn查询个人库存接口入参
    /// </summary>
    public class XMSSelectPersonSnByUniqueRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 人员
        /// </summary>
        public string person { get; set; }
        /// <summary>
        /// 串号
        /// </summary>
        public string sn { get; set; }
    }

    /// <summary>
    /// XMS按sn查询个人库存接口返回
    /// </summary>
    public class XMSSelectPersonSnByUniqueResponseModel
    {
    }

    /// <summary>
    /// XMS查看串号是否在个人库存中接口（批量）入参
    /// </summary>
    public class XMSSelectPersonSnBySnsWrapRequestModel
    {
        /// <summary>
        /// 串号List
        /// </summary>
        public List<string> sns { get; set; }
    }

    /// <summary>
    /// XMS查看串号是否在个人库存中接口（批量）返回
    /// </summary>
    public class XMSSelectPersonSnBySnsWrapResponseModel
    {
    }

    /// <summary>
    /// XMS检查sn是否在个人库存中接口（单个）入参
    /// </summary>
    public class XMSCheckSnExistPersonRequestModel
    {
        /// <summary>
        /// 串号
        /// </summary>
        public string sn { get; set; }
    }

    /// <summary>
    /// XMS检查sn是否在个人库存中接口（单个）返回
    /// </summary>
    public class XMSCheckSnExistPersonResponseModel
    {
    }

    /// <summary>
    /// XMS逆向拒收重新入库接口入参
    /// </summary>
    public class XMSRejectStorageRequestModel
    {
    }

    /// <summary>
    /// XMS逆向拒收重新入库接口返回
    /// </summary>
    public class XMSRejectStorageResponseModel
    {
    }

    /// <summary>
    /// XMS仓库信息接口入参
    /// </summary>
    public class XMSWarehouseInfoRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 仓库类型
        /// </summary>
        public string storageType { get; set; }
    }

    /// <summary>
    /// XMS仓库信息接口返回
    /// </summary>
    public class XMSWarehouseInfoResponseModel
    {
        /// <summary>
        /// 仓库代码
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 仓库名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 仓库类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 库特征（大库：分区，小库：不分区）
        /// </summary>
        public string arributes { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string org_id { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string manager { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string tel { get; set; }
        /// <summary>
        /// 是否有效
        /// </summary>
        public string enable { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string create_time { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string create_person { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public string update_time { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string update_person { get; set; }
    }

    /// <summary>
    /// XMS配件库存信息接口入参
    /// </summary>
    public class XMSInventoryInfoRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 仓库编码
        /// </summary>
        public string storageId { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
    }

    /// <summary>
    /// XMS配件库存信接口返回
    /// </summary>
    public class XMSInventoryInfoResponseModel
    {
        /// <summary>
        /// id
        /// </summary>
        [JsonProperty("id")]
        private int? _id;
        public int id => _id ?? 0;
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 仓库编码
        /// </summary>
        public string storageId { get; set; }
        /// <summary>
        /// 仓库类型
        /// </summary>
        public string storageType { get; set; }
        /// <summary>
        /// 库区ID
        /// </summary>
        public string sectionId { get; set; }
        /// <summary>
        /// 库区类型
        /// </summary>
        public string sectionType { get; set; }
        /// <summary>
        /// 库位ID
        /// </summary>
        public string binId { get; set; }
        /// <summary>
        /// 货位ID
        /// </summary>
        public string unitId { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string macname { get; set; }
        /// <summary>
        /// 库存总数量
        /// </summary>
        [JsonProperty("quantity")]
        private int? _quantity;
        public int quantity => _quantity ?? 0;
        /// <summary>
        /// 占用库存数量
        /// </summary>
        [JsonProperty("preemptQuantity")]
        private int? _preemptQuantity;
        public int preemptQuantity => _preemptQuantity ?? 0;
        /// <summary>
        /// 修改人
        /// </summary>
        public List<XMSInventoryInfoBatchListeModel> batchList { get; set; }
    }

    /// <summary>
    /// XMS配件库存信息接口返回batchList
    /// </summary>
    public class XMSInventoryInfoBatchListeModel
    {
        /// <summary>
        /// 货位使用类型：A-零货位 B-整货位 C-移动货位
        /// </summary>
        public string unitUseType { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createTime { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public string updateTime { get; set; }
    }

    /// <summary>
    /// XMS条码库存信息接口入参
    /// </summary>
    public class XMSBarcodeInventoryInfoRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 仓库编码
        /// </summary>
        public string storageId { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 串号
        /// </summary>
        public string sn { get; set; }
    }

    /// <summary>
    /// XMS配件库存信接口返回
    /// </summary>
    public class XMSBarcodeInventoryInfoResponseModel
    {
        /// <summary>
        /// imei
        /// </summary>
        public string imei { get; set; }
        /// <summary>
        /// sn
        /// </summary>
        public string sn { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string meid { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string used { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string serviceno { get; set; }
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        public string storageId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string sectionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string createTimDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string creaLong { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string updateTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string updatePerson { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string storageName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string sectionName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string imeiOrSnLike { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string macnoOrImeiOrSn { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string imei2 { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        public string phaseLevel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string binId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string unitId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string locked { get; set; }
    }

    /// <summary>
    /// XMS库存派发+占用接口入参
    /// </summary>
    public class XMSMaterialSendAndPreemptRequestModel
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        public string orgId { get; set; }
        /// <summary>
        /// 工程师米聊号
        /// </summary>
        public string engineer { get; set; }
        /// <summary>
        /// 原因，固定值 "material_send.reason.1"
        /// </summary>
        public string reason { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string managerRemark { get; set; }
        /// <summary>
        /// 工程师米聊-工程师名称，如 "1313472598-吴其顺1"
        /// </summary>
        public string engineerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<MaterialSendAndPreemptApplyItemListModel> applyItemList { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<MaterialSendAndPreemptMatchItemListModel> matchItemList { get; set; }
    }

    /// <summary>
    /// XMS库存派发+占用接口applyItemList
    /// </summary>
    public class MaterialSendAndPreemptApplyItemListModel
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string macName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int count { get; set; }
        /// <summary>
        /// 是否串号管理 （无串号false 有true）
        /// </summary>
        public bool isSerial { get; set; }
        /// <summary>
        /// 固定值 false
        /// </summary>
        public bool masterSlaveSn { get; set; }
    }

    /// <summary>
    /// XMS库存派发+占用接口matchItemList
    /// </summary>
    public class MaterialSendAndPreemptMatchItemListModel
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string macno { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string macName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int count { get; set; }
        /// <summary>
        /// 是否串号管理 （无串号false 有true）
        /// </summary>
        public bool isSerial { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        public string storageId { get; set; }
        /// <summary>
        /// 区位ID
        /// </summary>
        public string sectionId { get; set; }
        /// <summary>
        /// 库位ID
        /// </summary>
        public string binId { get; set; }
        /// <summary>
        /// 货位ID
        /// </summary>
        public string unitId { get; set; }
        /// <summary>
        /// imei
        /// </summary>
        public string imei { get; set; }
        /// <summary>
        /// sn
        /// </summary>
        public string sn { get; set; }
    }

    /// <summary>
    /// XMS仓库类型
    /// </summary>
    public class XMSStorageTypeModel
    {
        public static Dictionary<int, string> dic { get; set; } = new Dictionary<int, string>
            {
                {1,"ggs" },//良品库
                {2,"" },//保外坏品库
                {3,"" },//来料坏品库
                {4,"" },//差异库
                {5,"" },//成品DOA库
                {6,"" },//保内坏品库
                {7,"sps" }//样品库
            };
    }
}