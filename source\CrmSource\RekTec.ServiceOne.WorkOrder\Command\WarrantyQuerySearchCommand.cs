﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using RekTec.Crm.Common.Helper;
using RekTec.Crm.HiddenApi;
using RekTec.Crm.Plugin.Common.EntitySerialize;
using RekTec.ServiceOne.WorkOrder.Bll;
using RekTec.ServiceOne.WorkOrder.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;

namespace RekTec.ServiceOne.WorkOrder.Command
{
    public class WarrantyQuerySearchCommand : HiddenCommand
    {
        private static Stopwatch sw = new Stopwatch();
        public List<WarrantyReturnBody> WarrantyQuery(WarrantySerachParameter warrantySerachParameter)
        {
            try
            {
                Log.InfoMsg("IMEI维保参数：" + JsonHelper.Serialize(warrantySerachParameter));
                Log.InfoMsg("进入command");
                //接口返回数据，维保结果集合
                List<WarrantyReturnBody> warrantyReturnBodies = new List<WarrantyReturnBody>();
                foreach (var parameterData in warrantySerachParameter.listparameterData)
                {
                    Log.InfoMsg("进入数据处理");
                    if (string.IsNullOrEmpty(parameterData.country))
                        throw new InvalidProgramException(GetResource("warranty.countryempty", "參數[country]為空"));

                    if (string.IsNullOrEmpty(parameterData.item.sn))
                    {
                        if (string.IsNullOrEmpty(parameterData.item.imei))
                        {
                            throw new InvalidProgramException(GetResource("warranty.snandimeiempty", "參數[SN]或[IMEI]至少有一項不為空"));
                        }
                        else
                        {
                            var warrantyReturnBody = IsInternationalWarranty(parameterData, parameterData.item.imei, UniqueType.imei);
                            warrantyReturnBodies.Add(warrantyReturnBody);
                        }
                    }
                    else
                    {
                        var warrantyReturnBody = IsInternationalWarranty(parameterData, parameterData.item.sn, UniqueType.sn);
                        warrantyReturnBodies.Add(warrantyReturnBody);
                    }
                }

                return warrantyReturnBodies;

            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 判断是否国际维保
        /// </summary>
        /// <param name="country">国家编码</param>
        /// <param name="unique">SN/IMEI</param>
        /// <param name="uniqueType">类型</param>
        /// <param name="imei">IMEI</param>
        /// <param name="log">日志</param>
        /// <returns></returns>
        public WarrantyReturnBody IsInternationalWarranty(WarrantyParameterDataSerach parameterDataSerach, string unique, UniqueType uniqueType)
        {
            try
            {
                Log.InfoMsg("进入IsInternationalWarranty");
                WarrantyParam warrantyParam = new WarrantyParam();
                warrantyParam.country = parameterDataSerach.country;
                warrantyParam.unique = unique;
                warrantyParam.uniqueType = uniqueType;
                warrantyParam.imei = parameterDataSerach.item.imei;

                //查询该SN是否在东北欧生态链历史销售产品串号池，且区域为北欧
                sw.Restart();
                var isEunesnmanage = GetEunesnmanage(warrantyParam.unique);
                sw.Stop();
                Log.InfoMsg($"【IsInternationalWarranty】查询东北欧串号配置表 用时:{sw.ElapsedMilliseconds}");
                DateTime startTime = new DateTime();
                if (isEunesnmanage.Item1)
                {
                    if (!string.IsNullOrWhiteSpace(warrantyParam?.activeReturnData?.active_time))
                    {
                        startTime = Crm.BizCommon.CommonHelper.ConvertToDateTime(long.Parse(warrantyParam.activeReturnData.active_time));
                    }
                    else
                    {
                        startTime = isEunesnmanage.Item2.AddDays(90);
                    }
                    DateTime dateTime = DateTime.UtcNow;
                    if (startTime.AddYears(1) >= dateTime)
                    {
                        return SetWarranty(warrantyParam.unique, warrantyParam.imei, GetResource("warranty.Nord365", "此产品由Nord365经销、并在出售1年内，由Nord365负责维修，请转寄给Nord365"), (int)IsWarranty.Warranty);
                    }
                }


                if (!string.IsNullOrEmpty(parameterDataSerach.new_packetsstarttime))
                    warrantyParam.new_packetsstarttime = Crm.BizCommon.CommonHelper.ConvertTimestamp(Convert.ToDateTime(parameterDataSerach.new_packetsstarttime));

                sw.Restart();
                //调用imei服务
                warrantyParam.iMEIServiceReturnData = GetIMEIServiceOne(parameterDataSerach.item.sn, uniqueType);
                sw.Stop();
                Log.InfoMsg($"【IsInternationalWarranty】调用IMEI服务接口 用时:{sw.ElapsedMilliseconds}");

                if (warrantyParam.iMEIServiceReturnData == null)
                    return uniqueType == UniqueType.sn ? SetWarranty(unique, warrantyParam.imei, GetResource("warranty.snnotexist", "SN碼不存在"), (int)IsWarranty.NONENTITY) : SetWarranty("", warrantyParam.imei, "IMEI不存在", (int)IsWarranty.NONENTITY);

                sw.Restart();
                //将国家编码转换成id
                if (!string.IsNullOrWhiteSpace(warrantyParam.iMEIServiceReturnData.b2b_country))
                    warrantyParam.iMEIServiceReturnData.b2b_country = GetCountryId(warrantyParam.iMEIServiceReturnData.b2b_country);

                if (warrantyParam.iMEIServiceReturnData.extend != null)
                {
                    List<string> sale_region = warrantyParam.iMEIServiceReturnData.extend.Where(m => m.key.Contains("sale_region") && m.key != "sale_region_count").Select(m => m.value).ToList();
                    Log.InfoMsg("sale_region:" + JsonHelper.Serialize(sale_region));
                    warrantyParam.iMEIServiceReturnData.sales_scope = new List<string>();
                    if (sale_region.Count > 0)
                    {
                        foreach (var item in sale_region)
                        {
                            foreach (var item1 in item.Split(','))
                            {
                                warrantyParam.iMEIServiceReturnData.sales_scope.Add(item1);
                            }
                        }
                    }

                    Log.InfoMsg("warrantyParam.iMEIServiceReturnData.sales_scope:" + JsonHelper.Serialize(warrantyParam.iMEIServiceReturnData.sales_scope));
                }
                //调用OC服务
                warrantyParam.oCReturnData = new OCReturnData() { country = parameterDataSerach?.oc_country, goods_id = parameterDataSerach?.goodid };

                warrantyParam.channel = parameterDataSerach.channel;

                //调用激活服务
                warrantyParam.activeReturnData = parameterDataSerach.ActiveReturnData;

                //将激活服务国家编码转换成id
                if (warrantyParam.activeReturnData != null)
                {
                    sw.Restart();
                    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                    {
                        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                        #region 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                        if (string.IsNullOrWhiteSpace(active_country))
                        {
                            warrantyParam.activeReturnData.active_country = "";
                        }
                        else
                        {
                            warrantyParam.activeReturnData.active_country = active_country;
                        }
                        //return SetWarranty(warrantyParam.unique, warrantyParam.imei, GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                        #endregion 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                    }

                    sw.Stop();
                    Log.InfoMsg($"【IsInternationalWarranty】更新激活国家id 用时:{sw.ElapsedMilliseconds}");
                }
                sw.Stop();
                Log.InfoMsg($"【IsInternationalWarranty】查询激活国家id 用时:{sw.ElapsedMilliseconds}");

                sw.Restart();
                //判断手机/生态链
                warrantyParam.type = GetGoodsfiles(warrantyParam.iMEIServiceReturnData.goods_id);
                sw.Stop();
                Log.InfoMsg($"【IsInternationalWarranty】判断手机/生态链 用时:{sw.ElapsedMilliseconds}");

                sw.Restart();
                if (warrantyParam.type != 1)//非手机
                    return GetWarranty(warrantyParam);

                //判断 销售地/激活地(如果有)/受理地 完全一致
                if (string.IsNullOrWhiteSpace(warrantyParam.activeReturnData?.active_country))
                {

                    if (warrantyParam.iMEIServiceReturnData?.b2b_country == parameterDataSerach.country)
                        return GetWarranty(warrantyParam);
                }

                if (warrantyParam.iMEIServiceReturnData?.b2b_country == warrantyParam.activeReturnData?.active_country && warrantyParam.activeReturnData?.active_country == parameterDataSerach.country)
                    return GetWarranty(warrantyParam);

                var isConfig = IsConfig(warrantyParam.iMEIServiceReturnData.goods_id.ToString());

                //判断受理物品GoodsId是否配置国际联保权益
                if (!isConfig.Item1)
                    return GetWarranty(warrantyParam);

                //判断权益是否需要注册
                if (!isConfig.Item2)
                    return GetInternationalWarranty(warrantyParam, isConfig.Item3);

                //判断SN/IMEI是否有注册记录
                if (!GetInternationalwarrantyequityregister(isConfig.Item3, unique, warrantyParam.iMEIServiceReturnData.imei))
                    return GetWarranty(warrantyParam);


                return GetInternationalWarranty(warrantyParam, isConfig.Item3);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            finally
            {
                sw.Stop();
                Log.InfoMsg($"【IsInternationalWarranty】维保核心逻辑 用时:{sw.ElapsedMilliseconds}");
            }
        }
        /// <summary>
        /// 国际维保逻辑
        /// </summary>
        /// <param name="country"></param>
        /// <param name="unique"></param>
        /// <param name="uniqueType"></param>
        /// <param name="imei"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public WarrantyReturnBody GetInternationalWarranty(WarrantyParam warrantyParam, Guid new_interwarrantysettingid)
        {
            try
            {
                //获取销售渠道 自营/非自营
                var activeReturnData = warrantyParam.activeReturnData;
                var iMEIServiceReturnData = warrantyParam.iMEIServiceReturnData;
                var oCReturnData = warrantyParam.oCReturnData;

                bool xiaoshou = GetInternationalwarrantyequitynation(iMEIServiceReturnData.b2b_country, new_interwarrantysettingid);
                bool shouli = GetInternationalwarrantyequitynation(warrantyParam.country, new_interwarrantysettingid);
                bool jihuo = true;

                if (warrantyParam.channel == 1)//自营渠道
                {
                    //判断销售地、激活地（如果有）受理地是否在联保国家
                    if (!string.IsNullOrEmpty(activeReturnData?.active_country))
                        jihuo = GetInternationalwarrantyequitynation(activeReturnData.active_country, new_interwarrantysettingid);

                    if (xiaoshou && shouli && jihuo)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                    return GetWarranty(warrantyParam);
                }
                else if (warrantyParam.channel == 2)//非自营
                {
                    //判断是否官方销售渠道
                    //if (!GetIsOfficial(warrantyParam.country, oCReturnData.goods_id))
                    //    return GetWarranty(warrantyParam);

                    //判断是否有销售范围
                    if (iMEIServiceReturnData.sales_scope?.Count > 0)
                    {
                        //判断是否有激活地
                        if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                        {
                            //激活服务中渠道的激活可能会中文，统一转成id
                            //if (warrantyParam.activeReturnData != null)
                            //{
                            //    sw.Restart();
                            //    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                            //    {
                            //        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                            //        if (string.IsNullOrWhiteSpace(active_country))
                            //            return SetWarranty(warrantyParam.unique, warrantyParam.imei, GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                            //        warrantyParam.activeReturnData.active_country = active_country;
                            //    }

                            //    sw.Stop();
                            //    Log.InfoMsg($"查询激活国家id 用时:{sw.ElapsedMilliseconds}");
                            //}
                            //激活地是否在允许的销售范围
                            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                            {
                                //判断销售地、激活地、受理地是否在联保国家
                                jihuo = GetInternationalwarrantyequitynation(activeReturnData?.active_country, new_interwarrantysettingid);

                                if (xiaoshou && shouli && jihuo)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                                return GetWarranty(warrantyParam);
                            }

                            return GetWarranty(warrantyParam);
                        }

                        //jihuo = GetInternationalwarrantyequitynation(activeReturnData?.active_country, new_interwarrantysettingid);

                        if (xiaoshou && shouli && jihuo)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                        return GetWarranty(warrantyParam);
                    }

                    //判断销售地、激活地（如果有）受理地是否在联保国家
                    if (!string.IsNullOrEmpty(activeReturnData?.active_country))
                        jihuo = GetInternationalwarrantyequitynation(activeReturnData.active_country, new_interwarrantysettingid);

                    if (xiaoshou && shouli && jihuo)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime, true);

                    return GetWarranty(warrantyParam);
                }

                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 维保结果查询
        /// </summary>
        /// <param name="country">国家</param>
        /// <param name="unique">sn/imei</param>
        /// <returns></returns>
        public WarrantyReturnBody GetWarranty(WarrantyParam warrantyParam)
        {
            try
            {
                //获取系统参数 国家id
                string hk = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_hkid", true);
                string ru = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_ruid", true);
                string cb = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_cbid", true);
                string em = CrmHelper.GetSystemParameterValue(OrganizationService, "sys_emid", true);
                var activeReturnData = warrantyParam.activeReturnData;
                var iMEIServiceReturnData = warrantyParam.iMEIServiceReturnData;
                var oCReturnData = warrantyParam.oCReturnData;

                Log.InfoMsg("激活国家：" + activeReturnData?.active_country);
                Log.InfoMsg("IMEI国家：" + iMEIServiceReturnData?.b2b_country);
                Log.InfoMsg("受理国家：" + warrantyParam?.country);
                Log.InfoMsg("oc国家:" + oCReturnData?.country);
                Log.InfoMsg("iMEIServiceReturnData.sales_scope.Count:" + iMEIServiceReturnData?.sales_scope?.Count);
                Log.InfoMsg("品类:" + warrantyParam.type);
                Log.InfoMsg("渠道:" + warrantyParam.channel);

                if (warrantyParam.type == 1)//手机
                {

                    //判断销售地 = EM && 商品档案中根据new_sku后两位 = CN / TW / HK / IN
                    var new_sku = GetGoodssku(warrantyParam.iMEIServiceReturnData.goods_id);
                    if (!string.IsNullOrWhiteSpace(new_sku))
                    {
                        new_sku = new_sku.Substring(new_sku.Length - 2).ToUpper();
                        if ((new_sku == "CN" || new_sku == "TW" || new_sku == "HK" || new_sku == "IN") && warrantyParam.iMEIServiceReturnData.b2b_country == em)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FEMBD", "非EM本地电商售后可受理机型"), (int)IsWarranty.FALSE);
                    }

                    //激活地=受理地=俄罗斯 激活时间<2020.8 可维保
                    //if (activeReturnData != null && activeReturnData.active_country == warrantyParam?.country && warrantyParam?.country == ru)
                    if (activeReturnData != null && activeReturnData.active_country == warrantyParam?.country && warrantyParam?.country == "850000016")
                    {
                        if (Crm.BizCommon.CommonHelper.ConvertToDateTime(long.Parse(activeReturnData.active_time)) < new DateTime(2020, 8, 1))
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                    }

                    if (warrantyParam.channel == 1)//自营
                    {
                        //判断OC订单国家是否等于受理地
                        if (warrantyParam?.country == oCReturnData?.country)
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //判断受理国家和销售国家是否联保
                        if (GetIsCoinsurance(warrantyParam?.country, oCReturnData?.country))
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFDG", "受理機構非訂單所屬國家，且非聯保"), (int)IsWarranty.FALSE);
                    }
                    else if (warrantyParam.channel == 2)//非自营
                    {
                        //判断是否可保修 取sn查imei服务【是否维保】
                        if (iMEIServiceReturnData.can_repair == 1 || iMEIServiceReturnData.can_repair == 0 || GetSpecialapply((int)IsWarranty.IMEINoWarranty, iMEIServiceReturnData.sn))
                        {
                            //判断受理地是否为 激活地判保例外区域
                            if (GetIsException(warrantyParam.country))
                            {
                                //判断受理地是否等于IMEI服务销售国家
                                if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和销售国家是否联保
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }
                            //判断该imei是否在imei配置表中
                            if (GetImeiexception(iMEIServiceReturnData.imei))
                            {
                                //判断【受理地】等于销售地（IMEI服务）
                                if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                ///【受理地】是否与销售地（IMEI服务）联保 （判断销售地和【入参国家】是否在【联保国家】配置表中）
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }
                            //是否有激活地【IMEI服务-激活地是否有值】
                            if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                            {
                                //激活服务中渠道的激活可能会中文，统一转成id
                                //if (warrantyParam.activeReturnData != null)
                                //{
                                //    sw.Restart();
                                //    if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                                //    {
                                //        var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                                //        if (string.IsNullOrWhiteSpace(active_country))
                                //            return SetWarranty(warrantyParam.unique, warrantyParam.imei, GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                                //        warrantyParam.activeReturnData.active_country = active_country;
                                //    }

                                //    sw.Stop();
                                //    Log.InfoMsg($"查询激活国家id 用时:{sw.ElapsedMilliseconds}");
                                //}
                                //判断是否有销售范围
                                if (iMEIServiceReturnData.sales_scope?.Count == 0)
                                {
                                    //判断受理国家是否等于销售国家
                                    if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    //判断受理国家和销售国家是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                                }
                                /*
                                //查询受理地是否在跨境电商配置表中
                                if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                {
                                    //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, activeReturnData.active_country))
                                    {
                                        //判断受理国家是否等于激活地
                                        if (warrantyParam.country == activeReturnData.active_country)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //判断受理地是否跨境电商允许销售国家
                                        if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                        {
                                            //判断受理地是否与激活地联保
                                            if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                        }

                                        //判断受理机构是否为香港机构 
                                        if (warrantyParam.country == hk)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                    }
                                    //判断受理地是否跨境电商允许销售国家
                                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                    {
                                        //判断受理地是否与激活地联保
                                        if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //if (activeReturnData.active_country == cb)
                                        if (activeReturnData.active_country == "850036864")
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理机构是否为香港机构 
                                    //if (warrantyParam.country == hk)
                                    if (warrantyParam.country == "3385")
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                                }
                                */
                                //激活地是否在允许的销售范围
                                //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                                if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                                {
                                    //判断激活地和受理国家是否相等
                                    if (activeReturnData.active_country == warrantyParam.country)
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理国家和和激活地是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }
                                //激活地是否与允许销售区域联保
                                if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                                {
                                    //判断激活地和受理国家是否相等
                                    if (activeReturnData.active_country == warrantyParam.country)
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理国家和和激活地是否联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    {
                                        //【销售地=CB && 激活地不在CB国家】或【销售地=EM && 激活地不在EM国家】
                                        if (!((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && !GetIsCross(iMEIServiceReturnData.b2b_country, activeReturnData.active_country)))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        //销售地=CB
                                        if (iMEIServiceReturnData.b2b_country == cb)
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                                        //销售地=EM
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                                    }

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);
                            }

                            //【销售地=CB && 受理地在CB范围国家 && 允许售后】或【销售地=EM && 受理地在EM范围国家 && 允许售后】
                            if ((iMEIServiceReturnData.b2b_country == cb || iMEIServiceReturnData.b2b_country == em) && GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            //查询受理地是否在跨境电商配置表中
                            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            ////判断受理国家是否等于销售国家
                            if (GetCountryId(iMEIServiceReturnData.b2b_country) == warrantyParam.country)
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            //判断受理国家和销售国家是否联保
                            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        }

                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);

                        ////判断是否官方渠道，受理国家与商品编码是否在【官方销售配置表】中
                        //if (GetIsOfficial(warrantyParam.country, oCReturnData.goods_id))
                        //{
                        //    //判断是否可保修 取sn查imei服务【是否维保】
                        //    if (iMEIServiceReturnData.can_repair == 1 || iMEIServiceReturnData.can_repair == 0)
                        //    {
                        //        //判断受理地是否为 激活地判保例外区域
                        //        if (GetIsException(warrantyParam.country))
                        //        {
                        //            //判断受理地是否等于IMEI服务销售国家
                        //            if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            //判断受理国家和销售国家是否联保
                        //            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //        }
                        //        //判断该imei是否在imei配置表中
                        //        if (GetImeiexception(iMEIServiceReturnData.imei))
                        //        {
                        //            //判断【受理地】等于销售地（IMEI服务）
                        //            if (warrantyParam.country == iMEIServiceReturnData.b2b_country)
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            ///【受理地】是否与销售地（IMEI服务）联保 （判断销售地和【入参国家】是否在【联保国家】配置表中）
                        //            if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //        }
                        //        //是否有激活地【IMEI服务-激活地是否有值】
                        //        if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                        //        {
                        //            //判断是否有销售范围
                        //            if (iMEIServiceReturnData.sales_scope?.Count == 0)
                        //            {
                        //                //判断受理国家是否等于销售国家
                        //                if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和销售国家是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //            }
                        //            //查询受理地是否在跨境电商配置表中
                        //            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //            {
                        //                //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                        //                if (GetIsAllow(iMEIServiceReturnData.b2b_country, activeReturnData.active_country))
                        //                {
                        //                    //判断受理国家是否等于激活地
                        //                    if (warrantyParam.country == activeReturnData.active_country)
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    //判断受理地是否跨境电商允许销售国家
                        //                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //                    {
                        //                        //判断受理地是否与激活地联保
                        //                        if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                        //                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //                    }

                        //                    //判断受理机构是否为香港机构 
                        //                    if (warrantyParam.country == "3385")
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //                }
                        //                //判断受理地是否跨境电商允许销售国家
                        //                if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //                {
                        //                    //判断受理地是否与激活地联保
                        //                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                    if (activeReturnData.active_country == "850036864")
                        //                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);

                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非EM本地電商允許售後區域"), (int)IsWarranty.FALSE);
                        //                }

                        //                //判断受理机构是否为香港机构  香港编码暂时不知道，回头补上
                        //                if (warrantyParam.country == "3385")
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                        //            }
                        //            //激活地是否在允许的销售范围
                        //            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                        //            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                        //            {
                        //                //判断激活地和受理国家是否相等
                        //                if (activeReturnData.active_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和和销售地是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //            }
                        //            //激活地是否与允许销售区域联保
                        //            if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                        //            {
                        //                //判断激活地和受理国家是否相等
                        //                if (activeReturnData.active_country == warrantyParam.country)
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                //判断受理国家和和销售地是否联保
                        //                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                        //            }

                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);

                        //        }
                        //        //查询受理地是否在跨境电商配置表中
                        //        if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        //判断受理国家是否等于销售国家
                        //        if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        //判断受理国家和销售国家是否联保
                        //        if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        //            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                        //        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                        //    }

                        //    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);
                        //}
                        //return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SYSF", "當前商品與受理機構國家非官方銷售渠道"), (int)IsWarranty.FALSE);
                    }

                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);

                }
                else if (warrantyParam.type == 2)//生态链
                {
                    ////imei服务 是否可保修 为否
                    if (iMEIServiceReturnData.can_repair == 2 && !GetSpecialapply((int)IsWarranty.IMEINoWarranty, iMEIServiceReturnData.sn))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BKWB", "IMEI服務中該商品不可維保"), (int)IsWarranty.IMEINoWarranty);

                    //判断受理国家是否等于销售国家
                    if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家和销售国家是否联保
                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //受理地=澳门 && 三级品类=电视 && 销售地=中国，可维保
                    if (warrantyParam.country == "850000196" && iMEIServiceReturnData.b2b_country == "1")
                    {
                        Log.InfoMsg($"澳门特殊逻辑--受理地：{warrantyParam.country}，销售地：{iMEIServiceReturnData.b2b_country}，商品档案：{iMEIServiceReturnData.goods_id}");
                        //判断imei服务的商品的三级品类是否为电视[30000008]
                        if (GetGoodsFilesCategory3(iMEIServiceReturnData.goods_id.ToString()) == "30000008")
                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);
                    }

                    //是否有激活地【IMEI服务-激活地是否有值】
                    if (activeReturnData != null && !string.IsNullOrEmpty(activeReturnData.active_country))
                    {
                        //激活服务中渠道的激活可能会中文，统一转成id
                        if (warrantyParam.activeReturnData != null)
                        {
                            sw.Restart();
                            if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                            {
                                var active_country = GetCountryId(warrantyParam.activeReturnData.active_country);
                                #region 如果找不到国家ID，则默认为空 p-dongbinbib 2023/8/23
                                if (string.IsNullOrWhiteSpace(active_country))
                                {
                                    warrantyParam.activeReturnData.active_country = "";
                                }
                                else
                                {
                                    warrantyParam.activeReturnData.active_country = active_country;
                                }

                                //return SetWarranty(warrantyParam.unique, warrantyParam.imei, GetResource("warranty.JJWK", "激活地未匹配到地址库国家，请联系管理员"), (int)IsWarranty.FALSE);
                                #endregion

                            }

                            sw.Stop();
                            Log.InfoMsg($"【GetWarranty】查询激活国家id 用时:{sw.ElapsedMilliseconds}");
                        }
                        if (!string.IsNullOrWhiteSpace(warrantyParam.activeReturnData.active_country))
                        {
                            //判断是否有销售范围
                            if (iMEIServiceReturnData.sales_scope?.Count == 0)
                            {
                                //判断受理国家是否等于销售国家
                                if (iMEIServiceReturnData.b2b_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和销售国家是否联保
                                if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                            }

                            //查询受理地是否在跨境电商配置表中
                            if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                            {
                                //判断激活地是否在表中跨境电商配置表中，如果在表中看是否允许售后
                                if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                {
                                    //判断受理国家是否等于激活地
                                    if (warrantyParam.country == activeReturnData.active_country)
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    //判断受理地是否跨境电商允许销售国家
                                    if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                    {
                                        //判断受理地是否与激活地联保
                                        if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                    }

                                    //判断受理机构是否为香港机构 
                                    if (warrantyParam.country == "3385")
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                                }

                                //判断受理地是否跨境电商允许销售国家
                                if (GetIsAllow(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                                {
                                    //判断受理地是否与激活地联保
                                    if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFKS", "激活地非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                                }

                                //判断受理机构是否为香港机构  香港编码暂时不知道，回头补上
                                if (warrantyParam.country == "3385")
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JSFK", "激活地與受理機構非跨境電商允許售後區域"), (int)IsWarranty.FALSE);
                            }

                            //激活地是否在允许的销售范围
                            //var active_country = GetCountryCode(activeReturnData.active_country).ToLower();
                            if (iMEIServiceReturnData.sales_scope.Contains(activeReturnData.active_country))
                            {
                                //判断激活地和受理国家是否相等
                                if (activeReturnData.active_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和和激活地是否联保
                                if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                            }

                            //激活地是否与允许销售区域联保
                            if (GetIsCoinsurance(activeReturnData.active_country, string.Join(",", iMEIServiceReturnData.sales_scope)))///
                            {
                                //判断激活地和受理国家是否相等
                                if (activeReturnData.active_country == warrantyParam.country)
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                //判断受理国家和和激活地是否联保
                                if (GetIsCoinsurance(warrantyParam.country, activeReturnData.active_country))
                                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.SFJD", "受理機構非激活地，且非聯保"), (int)IsWarranty.FALSE);
                            }

                            return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.JFSB", "激活地不在銷售區域和其聯保範圍"), (int)IsWarranty.FALSE);
                        }
                    }

                    //查询受理地是否在跨境电商配置表中
                    if (GetIsCross(iMEIServiceReturnData.b2b_country, warrantyParam.country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家是否等于销售国家
                    if (GetCountryId(iMEIServiceReturnData.b2b_country) == warrantyParam.country)
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    //判断受理国家和销售国家是否联保
                    if (GetIsCoinsurance(warrantyParam.country, iMEIServiceReturnData.b2b_country))
                        return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, "", (int)IsWarranty.TRUE, warrantyParam.new_packetsstarttime);

                    return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.FBDS", "非本地區銷售"), (int)IsWarranty.FALSE);
                }

                return SetWarranty(iMEIServiceReturnData.sn, iMEIServiceReturnData.imei, GetResource("warranty.BZWB", "當前商品不支持維保"), (int)IsWarranty.FALSE);

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
                throw new Exception("错误详情：" + JsonHelper.Serialize(ex));
            }
        }

        /// <summary>
        /// 获取商品的三级品类
        /// </summary>
        /// <param name="goods_id">商品id</param>
        /// <returns></returns>
        public string GetGoodsFilesCategory3(string goods_id)
        {
            //获取商品的三级品类
            QueryExpression query = new QueryExpression("new_goodsfiles");
            query.TopCount = 1;
            query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            query.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, goods_id);
            LinkEntity category3 = new LinkEntity("new_goodsfiles", "new_category3", "new_category3_id", "new_category3id", JoinOperator.Inner);
            category3.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            category3.EntityAlias = "category3";
            category3.Columns.AddColumns("new_code");
            query.LinkEntities.Add(category3);
            var ec = OrganizationServiceDisableMultLang.RetrieveMultiple(query);
            if (ec?.Entities?.Count > 0)
                return ec.Entities[0].GetAliasAttributeValue<string>("category3.new_code");

            return "";
        }

        /// <summary>
        /// SN/IMEI是否有注册记录
        /// </summary>
        /// <param name="internationalwarrantyequityid"></param>
        /// <param name="sn"></param>
        /// <returns></returns>
        public bool GetInternationalwarrantyequityregister(Guid internationalwarrantyequityid, string unique, string imei)
        {
            var new_internationalwarrantyequityregister = GetCache("new_internationalwarrantyequityregister");
            if (new_internationalwarrantyequityregister is null || new_internationalwarrantyequityregister?.Count == 0)
            {
                QueryExpression qe = new QueryExpression("new_internationalwarrantyequityregister");
                qe.ColumnSet = new ColumnSet("new_sn", "new_imei");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var ec = OrganizationService.RetrieveMultiple(qe);

                SetCache("new_internationalwarrantyequityregister", ec.Entities.ToList());//插入缓存
                new_internationalwarrantyequityregister = ec.Entities.ToList();
            }
            new_internationalwarrantyequityregister = new_internationalwarrantyequityregister.Where(m => m.Id == internationalwarrantyequityid).ToList();
            new_internationalwarrantyequityregister = new_internationalwarrantyequityregister.Where(m => m.GetAttributeValue<string>("new_sn") == unique || m.GetAttributeValue<string>("new_imei") == imei).ToList();

            return new_internationalwarrantyequityregister?.Count > 0 ? true : false;
        }

        /// <summary>
        /// 查询国家是否在联保国家
        /// </summary>
        /// <param name="countryId"></param>
        /// <returns></returns>
        public bool GetInternationalwarrantyequitynation(string countryId, Guid new_interwarrantysettingid)
        {
            sw.Restart();
            QueryExpression qe = new QueryExpression("new_internationalwarrantyequitynation");
            qe.Criteria.AddCondition("new_interwarrantysetting_id", ConditionOperator.Equal, new_interwarrantysettingid);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            LinkEntity le = new LinkEntity("new_internationalwarrantyequitynation", "new_country", "new_country_id", "new_countryid", JoinOperator.Inner);
            le.LinkCriteria.AddCondition("new_id", ConditionOperator.Equal, countryId);
            qe.LinkEntities.Add(le);

            var ec = OrganizationService.RetrieveMultiple(qe);
            sw.Stop();
            Log.InfoMsg($"查询国家是否在联保国家 用时:{sw.ElapsedMilliseconds}");
            return ec?.Entities?.Count > 0 ? true : false;
        }

        /// <summary>
        /// 调用imei服务
        /// </summary>
        /// <param name="unique">sn或imei</param>
        /// <param name="uniqueType">类别</param>
        /// <returns></returns>
        public IMEIServiceReturnData GetIMEIServiceOne(string unique, UniqueType uniqueType)
        {
            IMEIServiceReturnData iMEIServiceReturnData = new IMEIServiceReturnData();

            IMEIRequestData iMEIRequestData = new IMEIRequestData();
            Condition condition = new Condition();
            condition.business = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "sys_IMEIBusiness");
            condition.has_extend = true;
            condition.limit = 100;
            string[] array = { unique };

            int retryCount = 3;
            int counter = 1;
            while (counter <= retryCount)
            {
                try
                {
                    condition.q_type = Enum.GetName(typeof(UniqueType), uniqueType);
                    iMEIRequestData.sns = array;
                    iMEIRequestData.condition = condition;
                    iMEIServiceReturnData = Command<IMEIInterfacebll>().ImeiSearchSn(iMEIRequestData);
                    #region 港台米家销售区域数据错误引起判保错误修复
                    List<string> HKmihomeList = new List<string>();
                    List<string> TWmihomeList = new List<string>();
                    HKmihomeList = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "HKmihomeList").Split(',').ToList();
                    TWmihomeList = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "TWmihomeList").Split(',').ToList();
                    if (iMEIServiceReturnData != null && !string.IsNullOrWhiteSpace(Cast.ConToString(iMEIServiceReturnData.mihome)))
                    {
                        var mh = Cast.ConToString(iMEIServiceReturnData.mihome);
                        if (HKmihomeList.Count > 0 && HKmihomeList.Contains(mh) && !string.IsNullOrWhiteSpace(iMEIServiceReturnData.b2b_country) && iMEIServiceReturnData.b2b_country.ToLower() == "cn")
                        {
                            iMEIServiceReturnData.b2b_country = "hk";
                        }
                        if (TWmihomeList.Count > 0 && TWmihomeList.Contains(mh) && !string.IsNullOrWhiteSpace(iMEIServiceReturnData.b2b_country) && iMEIServiceReturnData.b2b_country.ToLower() == "cn")
                        {
                            iMEIServiceReturnData.b2b_country = "tw";
                        }
                    }
                    #endregion
                    return iMEIServiceReturnData;
                }
                catch (Exception ex)
                {
                    if (counter >= retryCount)
                    {
                        Log.ErrorMsg($"IMEI查询异常:{ex}");
                        throw new Exception($"IMEI重试次数【{counter}】，IMEI error:{ex.Message}");
                    }
                }
                Thread.Sleep(500);
                counter++;
            }
            return iMEIServiceReturnData;
        }
        /// <summary>
        /// 单条维保结果
        /// </summary>
        /// <param name="msg">不维保原因</param>
        /// <param name="code">状态码  5000维保，5201不维保</param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public WarrantyReturnBody SetWarranty(string sn, string imei, string msg, int code, long startTime = 0, bool IsInternationalWarranty = false)
        {
            if (GetSpecialapply(code, sn))
            {
                code = (int)IsWarranty.TRUE;
                msg = "";
            }
            WarrantyReturnBody warrantyReturnBody = new WarrantyReturnBody();
            warrantyReturnBody.sn = sn;
            warrantyReturnBody.imei = imei;
            warrantyReturnBody.warranty = new Warranty();
            warrantyReturnBody.warranty.msg = msg;
            warrantyReturnBody.warranty.code = code;
            warrantyReturnBody.warranty.startTime = startTime;
            warrantyReturnBody.warranty.IsInternationalWarranty = IsInternationalWarranty;
            return warrantyReturnBody;
        }

        /// <summary>
        /// 特批单查询
        /// </summary>
        /// <param name="code">状态码  5013</param>
        /// <param name="sn"></param>
        /// <returns></returns>
        public bool GetSpecialapply(int code, string sn)
        {
            if ((int)IsWarranty.IMEINoWarranty == code)
            {
                sw.Restart();
                QueryExpression qe = new QueryExpression("new_srv_specialapply");
                qe.ColumnSet = new ColumnSet("new_approvalstatus");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_type", ConditionOperator.Equal, 8);
                qe.Criteria.AddCondition("new_sn", ConditionOperator.Equal, sn);
                qe.Criteria.AddCondition("new_approvalstatus", ConditionOperator.Equal, 3);//特批单状态 = 已审核
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                sw.Stop();
                Log.InfoMsg($"查询特批申请单表 用时:{sw.ElapsedMilliseconds}");
                if (ec?.Entities?.Count > 0 )
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;

        }

        //获取 根据国家中文名/英文名/二位编码/三位编码 获取国家id，
        public string GetCountryId(string country)
        {
            country = country.ToUpper();
            var new_country = Cache.GetValue(country, "WorkOrder_GetCountry");
            if (!string.IsNullOrWhiteSpace(new_country))
                return new_country;

            QueryExpression qe = new QueryExpression("new_country");
            qe.ColumnSet = new ColumnSet("new_id", "new_name", "new_englishname", "new_code", "new_code1");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            LinkEntity le1 = new LinkEntity("new_country", "new_data_languageconfig", "new_countryid", "new_data_id", JoinOperator.Inner);
            le1.Columns = new ColumnSet("new_value");
            le1.EntityAlias = "le";
            qe.LinkEntities.Add(le1);

            LinkEntity le2 = new LinkEntity("new_data_languageconfig", "new_language", "new_language_id", "new_languageid", JoinOperator.LeftOuter);
            le2.LinkCriteria.AddCondition("new_langid", ConditionOperator.Equal, "2052");
            le1.LinkEntities.Add(le2);
            var ec = OrganizationServiceDisableMultLang.RetrieveMultiple(qe);

            Log.InfoMsg("eccount:" + ec.Entities.Count);
            Entity entity = ec.Entities.Where(m => m.GetAttributeValue<string>("new_id") == country || m.GetAttributeValue<string>("new_name") == country || (!string.IsNullOrWhiteSpace(m.GetAttributeValue<string>("new_englishname")) && m.GetAttributeValue<string>("new_englishname").ToUpper() == country) || m.GetAttributeValue<string>("new_code") == country || m.GetAttributeValue<string>("new_code1") == country || (m.Contains("le.new_value") && m.GetAttributeValue<AliasedValue>("le.new_value").Value.ToString() == country)).FirstOrDefault();
            Log.InfoMsg("entity:" + JsonHelper.Serialize(entity));
            if (entity != null)
            {
                string new_id = entity.GetAttributeValue<string>("new_id");
                Cache.SetValue(country, new_id, "WorkOrder_GetCountry", (int)TimeSpan.FromHours(8).TotalSeconds);
                return new_id;
            }

            return string.Empty;
        }


        /// <summary>
        /// 根据商品编码获取 手机/生态链
        /// </summary>
        /// <param name="goods_id">商品编码</param>
        /// <returns></returns>
        public int GetGoodsfiles(long goods_id)
        {
            // modified by Hyacinthhuang 优化查询，取缓存，系统参数
            string param = CrmHelper.GetSystemParameterValue(OrganizationService, "firstcategory1_shoujipingban", true);

            QueryExpression qe = new QueryExpression("new_goodsfiles");
            qe.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, Convert.ToInt32(goods_id));
            LinkEntity le = new LinkEntity("new_goodsfiles", "new_category1", "new_new_category1_id", "new_category1id", JoinOperator.LeftOuter);
            le.EntityAlias = "station";
            le.Columns = new ColumnSet("new_code");
            qe.LinkEntities.Add(le);
            var res = OrganizationService.RetrieveMultiple(qe);
            if (res?.Entities?.Count == 0)
                throw new Exception(GetResource("warranty.goodsIdisnotfound", "當前商品不支持維保，未查詢到此商品編碼"));

            var obj = res.Entities.FirstOrDefault();

            var new_code = obj.Contains("station.new_code") ? obj.GetAliasAttributeValue<string>("station.new_code") : string.Empty;

            int type = new_code == param ? 1 : 2;



            return type;
        }
        /// <summary>
        /// 根据商品编码查询sku
        /// </summary>
        /// <param name="goods_id"></param>
        /// <returns></returns>
        public string GetGoodssku(long goods_id)
        {
            QueryExpression qe = new QueryExpression("new_goodsfiles");
            qe.ColumnSet = new ColumnSet("new_sku");
            qe.Criteria.AddCondition("new_commoditycode", ConditionOperator.Equal, Convert.ToInt32(goods_id));
            var res = OrganizationService.RetrieveMultiple(qe);
            if (res?.Entities?.Count > 0)
            {
                return res[0].GetAttributeValue<string>("new_sku");
            }
            return default;
        }

        /// <summary>
        /// 判断受理国家和销售地是否联保
        /// </summary>
        /// <param name="country">受理国家</param>
        /// <param name="b2b_country">销售国家</param>
        /// <returns></returns>
        public bool GetIsCoinsurance(string country, string b2b_country)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(country) || string.IsNullOrWhiteSpace(b2b_country)) return false;
                sw.Restart();
                bool flag = false;
                QueryExpression query = new QueryExpression("new_unprofor");
                query.ColumnSet = new ColumnSet("new_countrys");
                query.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //query.Criteria.AddCondition("new_countrys", ConditionOperator.Like, "%" + temp + "%");
                query.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var res = OrganizationService.RetrieveMultiple(query);
                if (res == null || res.Entities == null || res.Entities.Count <= 0)
                    flag = false;
                foreach (var item in b2b_country.Split(','))
                {
                    int result = 0;
                    var temp = item;
                    if (int.TryParse(item, out result))
                    {
                        temp = GetCountryCode(item).ToLower();
                    }
                    if (string.IsNullOrEmpty(temp)) return false;
                    string new_countrys = res[0].GetAttributeValue<string>("new_countrys");
                    var countrysList = new_countrys.Split(',');
                    var f = countrysList.Where(p => p == temp).ToList();
                    if (f != null && f.Count > 0)
                        flag = true;
                    if (flag)
                        break;
                }
                sw.Stop();
                Log.InfoMsg($"判断受理国家和销售地是否联保 用时:{sw.ElapsedMilliseconds}");
                return flag;
            }
            catch (Exception)
            {
                return false;
            }

        }

        /// <summary>
        /// 根据国家id获取国家编码
        /// </summary>
        /// <param name="country"></param>
        /// <returns></returns>
        public string GetCountryCode(string country)
        {
            if (string.IsNullOrWhiteSpace(country))
                return null;
            Entity e = CacheCommon.GetCountryEntityByNewId(Cache, OrganizationServiceDisableMultLang, country);
            return e?.GetAttributeValue<string>("new_code");
        }
        /// <summary>
        /// 查询受理地是否在跨境电商配置表中 并且是否允许销售为是
        /// </summary>
        /// <returns></returns>
        public bool GetIsAllow(string imeicountry, string country)
        {
            try
            {
                sw.Restart();
                var new_crosscommerce = GetCache("new_crosscommerce2");
                if (new_crosscommerce is null || new_crosscommerce?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_crosscommerce");
                    qe.ColumnSet = new ColumnSet("new_salesarea", "new_countryid", "new_aftersale");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = OrganizationService.RetrieveMultiple(qe);

                    SetCache("new_crosscommerce2", ec.Entities.ToList());//插入缓存
                    new_crosscommerce = ec.Entities.ToList();
                }

                new_crosscommerce = new_crosscommerce.Where(m => m.GetAttributeValue<string>("new_countryid") == country && m.GetAttributeValue<OptionSetValue>("new_aftersale").Value == 100000000 && m.GetAttributeValue<string>("new_salesarea") == imeicountry).ToList();
                //QueryExpression qe = new QueryExpression("new_crosscommerce");
                //qe.ColumnSet = new ColumnSet("new_aftersale");
                //qe.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = OrganizationService.RetrieveMultiple(qe);
                //return ec?.Entities?.Count > 0 && ec.Entities[0].GetAttributeValue<OptionSetValue>("new_aftersale").Value == 100000000 ? true : false;
                sw.Stop();
                Log.InfoMsg($"查询跨境电商配置表 用时:{sw.ElapsedMilliseconds}");
                return new_crosscommerce?.Count > 0 ? true : false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 查询受理地是否在跨境电商配置表中
        /// </summary>
        /// <returns></returns>
        public bool GetIsCross(string imeicountry, string country)
        {
            try
            {
                imeicountry = GetCountryId(imeicountry);
                sw.Restart();
                Log.InfoMsg("imeicountry:" + imeicountry);
                Log.InfoMsg("country:" + country);
                var new_crosscommerce = GetCache("new_crosscommerce1");
                if (new_crosscommerce is null || new_crosscommerce?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_crosscommerce");
                    qe.ColumnSet = new ColumnSet("new_salesarea", "new_name", "new_countryid");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = OrganizationService.RetrieveMultiple(qe);

                    SetCache("new_crosscommerce1", ec.Entities.ToList());//插入缓存
                    new_crosscommerce = ec.Entities.ToList();
                }
                new_crosscommerce = new_crosscommerce.Where(m => m.GetAttributeValue<string>("new_countryid") == country && m.GetAttributeValue<string>("new_salesarea") == imeicountry).ToList();


                //QueryExpression qe = new QueryExpression("new_crosscommerce");
                //qe.Criteria.AddCondition("new_countryid", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = OrganizationService.RetrieveMultiple(qe);

                sw.Stop();
                Log.InfoMsg($"查询跨境电商配置表 用时:{sw.ElapsedMilliseconds}");
                return new_crosscommerce?.Count > 0 ? true : false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 判断imei是否在imei配置表中
        /// </summary>
        /// <param name="imei"></param>
        /// <returns></returns>
        public bool GetImeiexception(string imei)
        {
            try
            {
                sw.Restart();
                QueryExpression qe = new QueryExpression("new_imeiexception");
                qe.ColumnSet = new ColumnSet("new_name");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("new_name", ConditionOperator.Equal, imei);
                var ec = OrganizationService.RetrieveMultiple(qe);
                sw.Stop();
                Log.InfoMsg($"查询imei配置表 用时:{sw.ElapsedMilliseconds}");
                return ec?.Entities?.Count > 0 ? true : false;

            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 判断受理地是否为 激活地判保例外区域
        /// </summary>
        /// <param name="country">受理国家</param>
        /// <returns></returns>
        public bool GetIsException(string country)
        {
            try
            {
                sw.Restart();
                var new_exceptioncountry = GetCache("new_exceptioncountry");
                if (new_exceptioncountry is null || new_exceptioncountry?.Count == 0)
                {
                    QueryExpression qe = new QueryExpression("new_exceptioncountry");
                    qe.ColumnSet = new ColumnSet("new_code");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    var ec = OrganizationService.RetrieveMultiple(qe);

                    SetCache("new_exceptioncountry", ec.Entities.ToList());//插入缓存
                    new_exceptioncountry = ec.Entities.ToList();
                }
                new_exceptioncountry = new_exceptioncountry.Where(m => m.GetAttributeValue<string>("new_code") == country).ToList();
                //QueryExpression qe = new QueryExpression("new_exceptioncountry");
                //qe.Criteria.AddCondition("new_code", ConditionOperator.Equal, country);
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                //var ec = OrganizationService.RetrieveMultiple(qe);
                sw.Stop();
                Log.InfoMsg($"查询判保例外区域表 用时:{sw.ElapsedMilliseconds}");
                return new_exceptioncountry?.Count > 0 ? true : false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 判断是否官方渠道，受理国家与商品编码是否在【官方销售配置表】中
        /// </summary>
        /// <returns></returns>
        public bool GetIsOfficial(string country, string goods_id)
        {
            sw.Restart();
            try
            {
                QueryExpression qe = new QueryExpression("new_salechanneltable");
                qe.Criteria.AddCondition("new_goodsid", ConditionOperator.Equal, goods_id);
                qe.Criteria.AddCondition("new_countryid", ConditionOperator.Like, "%" + country + "%");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                var ec = OrganizationService.RetrieveMultiple(qe);
                sw.Stop();
                Log.InfoMsg($"判断是否官方销售渠道 用时:{sw.ElapsedMilliseconds}");
                return ec.Entities?.Count > 0 ? true : false;
            }
            catch (Exception)
            {
                return false;
            }
        }


        /// <summary>
        /// 查询GoodsId 是否在国际联保与商品配置表中
        /// </summary>
        /// <param name="goodsid"></param>
        /// <returns></returns>
        public (bool, bool, Guid) IsConfig(string goodsid)
        {
            sw.Restart();
            QueryExpression qe = new QueryExpression("new_intewarrantyequityproduct");
            qe.Criteria.AddCondition("new_productid", ConditionOperator.Equal, goodsid);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            LinkEntity le = new LinkEntity("new_intewarrantyequityproduct", "new_interwarrantysetting", "new_interwarrantysetting_id", "new_interwarrantysettingid", JoinOperator.LeftOuter);
            le.Columns = new ColumnSet("new_isregister", "new_interwarrantysettingid");
            le.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            le.EntityAlias = "le";
            qe.LinkEntities.Add(le);

            var ec = OrganizationService.RetrieveMultiple(qe);

            bool Item1 = ec?.Entities?.Count > 0 ? true : false;
            bool Item2 = false;
            Guid Item3 = new Guid();
            if (Item1)
            {
                if (ec.Entities.First().Contains("le.new_isregister"))
                    Item2 = (bool)ec.Entities.First().GetAttributeValue<AliasedValue>("le.new_isregister").Value;

                if (ec.Entities.First().Contains("le.new_interwarrantysettingid"))
                    Item3 = (Guid)ec.Entities.First().GetAttributeValue<AliasedValue>("le.new_interwarrantysettingid").Value;
            }

            sw.Stop();
            Log.InfoMsg($"查询国际联保与商品配置表 用时:{sw.ElapsedMilliseconds}");
            return (Item1, Item2, Item3);

        }

        /// <summary>
        /// 查询东北欧串号配置表
        /// </summary>
        /// <param name="sn">sn</param>
        /// <returns></returns>
        public (bool, DateTime) GetEunesnmanage(string sn)
        {
            QueryExpression qe = new QueryExpression("new_eunesnmanage");
            qe.ColumnSet = new ColumnSet("new_sellin_time");
            qe.Criteria.AddCondition("new_sn", ConditionOperator.Equal, sn);
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

            LinkEntity le = new LinkEntity("new_eunesnmanage", "new_region", "new_region_id", "new_regionid", JoinOperator.Inner);
            le.EntityAlias = "le";
            le.Columns = new ColumnSet("new_code");
            qe.LinkEntities.Add(le);

            var ec = OrganizationService.RetrieveMultiple(qe);

            if (ec == null || ec.Entities == null || ec.Entities.Count == 0)
                return (false, new DateTime());

            string new_code = ec.Entities[0].GetAliasAttributeValue<string>("le.new_code");
            string sys_easterneuropecode = Crm.BizCommon.CommonHelper.GetSystemParameterValueFromMemoryCatch(OrganizationService, "sys_northerneurope");
            if (new_code != sys_easterneuropecode)
                return (false, new DateTime());
            return (true, ec.Entities[0].GetAttributeValue<DateTime>("new_sellin_time"));

        }
        /// <summary>
        /// 设置缓存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public void SetCache(string key, List<Entity> value)
        {
            Cache.SetValue(key, JsonConvertExt.Serialize(value), "WarranyQuery", 3600);
        }
        public List<Entity> GetCache(string key)
        {
            try
            {
                var result = Cache.GetValue(key, "WarranyQuery");
                if (!string.IsNullOrWhiteSpace(result))
                    return JsonConvertExt.DeSerialize<List<Entity>>(result);
                return default;
            }

            catch (Exception ex)
            {
                Log.InfoMsg("GetCache2" + JsonHelper.Serialize(ex));
                throw new Exception(ex.Message);
            }

        }
    }
}
